## service：本地请求远程

方法加上：@Transactional(rollbackFor = Exception.class)

```java

    
// 其他业务
    
// 生成key
try {
  
    
    // 记录开始本地日志
    log.info("请求远程接口开始--秘钥key:xxxx ...");
    
    // 执行远程接口调用逻辑（携带key）
    // ...
  	if (r.getCode() == 200) {
      throw new BusinessException(r.getCode(), r.getMessage());
  	}
    
    // 数据库保存远程接口日志
    interfaceLogsService.create(xxx);

    // 记录结束本地日志
    log.info("请求远程接口结束--秘钥key:xxxx ...");
} catch (Exception e) {
    // 记录异常本地日志
    log.error("请求远程接口异常--秘钥key:xxxx ...");
	
    // 抛出异常
    throw new BusinessException(500,"错误信息");
}
```

### controller

没有事务抛出异常不会回滚

```java
try {
    // 调用service方法
   xxxxService.xxxxMethod();
} catch (Exception e) {
    // 记录异常本地日志
    log.error("请求远程整体接口异常--秘钥key:xxxx ...");
    
    // 数据库保存异常日志
    interfaceLogsService.create(xxx);
    
    // 抛出异常
    throw new BusinessException(500,"错误信息");
}
```

## service：远程调用本地

方法加上：@Transactional(rollbackFor = Exception.class)

```java
   
	// 获取秘钥

	 // 记录开始本地日志
    log.info("远程请求本地接口开始--秘钥key:xxxx ...");

	// 执行业务
	
    // 数据库保存日志
    interfaceLogsService.create(xxx);

	// 记录结束本地日
    log.info("远程请求本地接口结束--秘钥key:xxxx ...");
```

### controller

没有事务抛出异常不会回滚

```java
try {
    // 调用service方法
   xxxxService.xxxxMethod();
} catch (Exception e) {
    // 记录异常本地日志
    log.error("远程请求本地整体接口异常--秘钥key:xxxx ...");
    
    // 数据库保存异常日志
    interfaceLogsService.create(xxx);
    
    // 抛出异常
    throw new BusinessException(500,"错误信息");
}
```



