import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

public class RabbitMQConnectionTest {
    private static Logger logger = LogManager.getLogger(RabbitMQConnectionTest.class);
    // 连接参数
    private static final String HOST = "*************";
    private static final int PORT = 80;
    private static final String VIRTUAL_HOST = "mdm";
    private static final String EXCHANGE_NAME = "MDM.V2.RYKJK";

    // 需要设置用户名和密码
    private static final String USERNAME = "wzsc";  // 请替换为实际用户名
    private static final String PASSWORD = "FABF26F6-7615-EB92-E158-09F3125DC089";  // 请替换为实际密码

    public static void main(String[] args) {
        RabbitMQConnectionTest test = new RabbitMQConnectionTest();

        System.out.println("=== RabbitMQ连接测试开始 ===");

        // 1. 测试基础连接
//        test.testConnection();
//
//        // 2. 检查Exchange
//        test.checkExchange();
//
//        // 3. 列出队列
//        test.listQueues();
        // 4. 测试消费消息
        test.testConsumer();
    }
    /**
     * 测试基础连接
     */
    public void testConnection() {
        Connection connection = null;
        Channel channel = null;

        try {
            // 创建连接工厂
            ConnectionFactory factory = new ConnectionFactory();
            factory.setHost(HOST);
            factory.setPort(PORT);
            factory.setVirtualHost(VIRTUAL_HOST);
            factory.setUsername(USERNAME);
            factory.setPassword(PASSWORD);

            // 设置连接超时
            factory.setConnectionTimeout(10000);

            // 创建连接
            connection = factory.newConnection();
            System.out.println("✓ 连接成功!");
            System.out.println("  - 主机: " + HOST + ":" + PORT);
            System.out.println("  - 虚拟主机: " + VIRTUAL_HOST);
            System.out.println("  - 连接状态: " + connection.isOpen());

            // 创建通道
            channel = connection.createChannel();
            System.out.println("✓ 通道创建成功!");

        } catch (Exception e) {
            System.err.println("✗ 连接失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeResources(channel, connection);
        }
    }

    /**
     * 检查Exchange是否存在
     */
    public void checkExchange() {
        Connection connection = null;
        Channel channel = null;

        try {
            ConnectionFactory factory = createConnectionFactory();
            connection = factory.newConnection();
            channel = connection.createChannel();

            // 检查Exchange是否存在 (passive=true表示只检查不创建)
            channel.exchangeDeclarePassive(EXCHANGE_NAME);
            System.out.println("✓ Exchange存在: " + EXCHANGE_NAME);

        } catch (Exception e) {
            System.err.println("✗ Exchange不存在或无法访问: " + EXCHANGE_NAME);
            System.err.println("  错误: " + e.getMessage());
        } finally {
            closeResources(channel, connection);
        }
    }

    /**
     * 列出队列信息
     */
    public void listQueues() {
        Connection connection = null;
        Channel channel = null;

        try {
            ConnectionFactory factory = createConnectionFactory();
            connection = factory.newConnection();
            channel = connection.createChannel();

            System.out.println("尝试获取队列信息...");
            System.out.println("注意：需要管理员权限或知道具体队列名称");

            // 如果知道具体队列名称，可以这样检查：
            // checkSpecificQueue(channel, "your-queue-name");

        } catch (Exception e) {
            System.err.println("✗ 获取队列信息失败: " + e.getMessage());
        } finally {
            closeResources(channel, connection);
        }
    }

    /**
     * 检查指定队列
     */
    public void checkSpecificQueue(Channel channel, String queueName) {
        try {
            AMQP.Queue.DeclareOk response = channel.queueDeclarePassive(queueName);
            System.out.println("✓ 队列存在: " + queueName);
            System.out.println("  - 消息数量: " + response.getMessageCount());
            System.out.println("  - 消费者数量: " + response.getConsumerCount());
        } catch (Exception e) {
            System.err.println("✗ 队列不存在或无法访问: " + queueName);
            System.err.println("  错误: " + e.getMessage());
        }
    }

    /**
     * 测试消费消息
     */
    public void testConsumer() {
//        System.setProperty("log.dir", "D:\\code_edit\\backend\\wuzi-mmcp\\mall-material\\mall-material-service\\logs\\a.txt"); // 动态设置日志目录
        Connection connection = null;
        Channel channel = null;
        try {
            ConnectionFactory factory = createConnectionFactory();
            connection = factory.newConnection();
            channel = connection.createChannel();
//            channel.exchangeDeclare(EXCHANGE_NAME, "topic", true);
            // 创建一个临时队列用于测试
            String env = "_dev";
//            env = "_formal";
            String qName = "Personinfo_Full"+env;
//            qName = "Zcinfo_Full"+env;
//            qName = "Zyzginfo_Full"+env;
//            qName = "Grhjinfo_Full"+env;
//            qName = "Rzjlinfo_Full"+env;
//            qName = "Orginfo_Full"+env;
//            qName = "Sys_Org_Full"+env;
//            qName = "Sys_ConOrg_Full"+env;
//            qName = "Project_Project_Full"+env;
//            qName = "TTAccount_Full"+env;
            String routekey = qName.split(env)[0];
            boolean durable = true;      // 持久化
            boolean exclusive = false;   // 非独占
            boolean autoDelete = false;  // 不自动删除
            Map<String, Object> arguments = null; // 无额外参数
            String queueName = channel.queueDeclare(qName, durable, exclusive, autoDelete, arguments).getQueue();
            logger.info("✓ 创建临时队列: " + queueName);
            // 绑定队列到Exchange (监听所有消息)
            AMQP.Queue.BindOk bindOk = channel.queueBind(queueName, EXCHANGE_NAME, routekey);
//            AMQP.Queue.UnbindOk unbindOk = channel.queueUnbind(queueName, EXCHANGE_NAME, "Personinfo_Full");
            logger.info("队列绑定结果: " + (bindOk != null ? "成功,✓ 队列绑定到Exchange: "+EXCHANGE_NAME : "失败"));
//            logger.info("Personinfo_Full队列解绑结果: " + (unbindOk != null ? "成功" : "失败"));
            // 创建消费者
            Channel finalChannel = channel;
            DeliverCallback deliverCallback = (consumerTag, delivery) -> {
                logger.info("Routing Key: " + delivery.getEnvelope().getRoutingKey());
                String message = new String(delivery.getBody(), "UTF-8");
                JSONArray jsonArray = JSON.parseArray(message);
                if(jsonArray == null || jsonArray.isEmpty()){
                    return;
                }
                logger.info("消息内容: " + jsonArray.get(0));
                if(true){
                    return;
                }
                for (int i = 0,count=jsonArray.size();i<count;i++){
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    String id = jsonObject.getString("id");
                    String pname = jsonObject.getString("pname");
                    String pnumber = jsonObject.getString("pnumber");
                    String orgnumber = jsonObject.getString("orgnumber");
                    String gender = jsonObject.getString("gender");
                    String deptnumber = jsonObject.getString("deptnumber");
                    Integer age = jsonObject.getInteger("age");
                    String idcard = jsonObject.getString("idcard");
                    String gw = jsonObject.getString("gw");
                    String xl = jsonObject.getString("xl");
                    Float gznx = jsonObject.getFloat("gznx");
                    String xz = jsonObject.getString("xz");
                    String bysj = jsonObject.getString("bysj");
                    String byyx = jsonObject.getString("byyx");
                    String byzy = jsonObject.getString("byzy");
                    String mobile = jsonObject.getString("mobile");
                    String sbbm = jsonObject.getString("sbbm");
                    String sbjndw = jsonObject.getString("sbjndw");
                    String yx = jsonObject.getString("yx");
                    String tc = jsonObject.getString("tc");
                    String lastupdatedate = jsonObject.getString("lastupdatetime");
                    String mdmstate = jsonObject.getString("MdmState");

                }
                logger.info("=== 收到消息 ===");
                logger.info("消息内容: " + message);
                logger.info("Routing Key: " + delivery.getEnvelope().getRoutingKey());
                logger.info("Exchange: " + delivery.getEnvelope().getExchange());
                logger.info("=================");
                // 手动确认消息
//                try {
//                    finalChannel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
//                } catch (IOException e) {
//                    System.err.println("确认消息失败: " + e.getMessage());
//                }
            };

            CancelCallback cancelCallback = consumerTag -> {
                logger.info("消费者被取消: " + consumerTag);
            };
            // 开始消费
            channel.basicConsume(queueName, false, deliverCallback, cancelCallback);
            logger.info("✓ 开始监听消息...");
            logger.info("等待消息中... (按Ctrl+C退出)");
            // 等待消息 (这里等待10秒作为演示)
            while(true){
                Thread.sleep(10000);
            }
        } catch (Exception e) {
            logger.info("✗ 消费消息失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeResources(channel, connection);
        }
    }

    /**
     * 创建连接工厂
     */
    private ConnectionFactory createConnectionFactory() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(HOST);
        factory.setPort(PORT);
        factory.setVirtualHost(VIRTUAL_HOST);
        factory.setUsername(USERNAME);
        factory.setPassword(PASSWORD);
        factory.setConnectionTimeout(10000);
        return factory;
    }

    /**
     * 关闭资源
     */
    private void closeResources(Channel channel, Connection connection) {
        try {
            if (channel != null && channel.isOpen()) {
                channel.close();
            }
            if (connection != null && connection.isOpen()) {
                connection.close();
            }
        } catch (IOException | TimeoutException e) {
            e.printStackTrace();
        }
    }
}
