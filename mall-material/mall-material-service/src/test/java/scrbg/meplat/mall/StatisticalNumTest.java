package scrbg.meplat.mall;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.entity.StatisticalNum;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Log4j2
@SpringBootTest
public class StatisticalNumTest {
    @Autowired
    public MallConfig mallConfig;
    @Autowired
    public EnterpriseInfoService enterpriseInfoService;
    @Autowired
    public ShopService shopService;

    @Autowired
    public OrdersService ordersService;

    @Autowired
    public UserService userService;

    @Autowired
    public OrderShipService orderShipService;

    @Autowired
    public ProductService productService;
    @Autowired
    public StatisticalNumService statisticalNumService;

    @Autowired
    MaterialReconciliationService materialReconciliationService;


    @Autowired
    DealOrderInfoService dealOrderInfoService;
    @Resource
    OrderShipDtlService orderShipDtlService;


    @Test
    public void sljdfsjf() {
        List<Orders> ordersList = ordersService.lambdaQuery().in(Orders::getOrderClass, 1, 2)
                .between(Orders::getGmtCreate, "2023-01-01" , "2023-12-31").list();
        BigDecimal sumAmount = new BigDecimal(0);
        for (Orders orders : ordersList) {
            if(orders.getActualAmount() != null) {
                sumAmount = sumAmount.add(orders.getActualAmount());
            }
        }
        System.out.println("物资订单数：" + ordersList.size() + "，总金额：" + sumAmount);


        List<MaterialReconciliation> list = materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getBusinessType, 1)
                .between(MaterialReconciliation::getGmtCreate, "2023-01-01" , "2023-12-31")
                .eq(MaterialReconciliation::getState, 3).list();
        BigDecimal toavbleA = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list) {
            toavbleA = toavbleA.add(materialReconciliation.getReconciliationAmount());
        }
        System.out.println("总金额：" + sumAmount.add(toavbleA));
    }

    @Test
    public void count() {
        System.out.println("当前时间：" + LocalDateTime.now());
        //供应商数
        int supplierNum = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getIsPcwp, 1).isNotNull(EnterpriseInfo::getSocialCreditCode));
        System.out.println("外部供应商数：" + supplierNum);
        //店铺数 排除审核未通过
        int shopCoounts = shopService.count(new LambdaQueryWrapper<Shop>().ne(Shop::getAuditStatus, 3));
        System.out.println("店铺数：" + shopCoounts);
        //内部用户数
        int inUserCounts = userService.count(new LambdaQueryWrapper<User>().eq(User::getIsInternalUser, 1));
        System.out.println("使用过平台路桥用户数：" + inUserCounts);
        //内部企业数
        int inEnterPrise = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>().isNotNull(EnterpriseInfo::getInteriorId));
        System.out.println("使用过平台路桥企业数据：" + inEnterPrise);
        //上架商品总数
        int upProductNum = productService.count(new LambdaQueryWrapper<Product>().eq(Product::getState, 1));
        System.out.println("上架商品总数：" + upProductNum);
        //商品总数
        int productNum = productService.count();
        System.out.println("商品总数：" + productNum);
        //零星采购订单数
        List<Orders> ordersList = ordersService.list(new LambdaQueryWrapper<Orders>().in(Orders::getOrderClass, 1, 2).eq(Orders::getProductType, 10));
        BigDecimal sumAmount = new BigDecimal(0);
        for (Orders orders : ordersList) {
            sumAmount = sumAmount.add(orders.getActualAmount());
        }
        System.out.println("零星采购订单数：" + ordersList.size());
        System.out.println("零星采购订单金额：" + sumAmount);
        List<OrderShip> orderShips = orderShipService.list(new LambdaQueryWrapper<OrderShip>()
                .eq(OrderShip::getProductType, 10)
                .in(OrderShip::getType, 2, 1));
        BigDecimal orderShipAmount = new BigDecimal(0);
        //for (OrderShip orderShip : orderShips) {
            //orderShipAmount = orderShipAmount.add(orderShip.getRateAmount());
            for (OrderShip orderShip : orderShips) {
                List<OrderShipDtl> itemList = orderShipDtlService.lambdaQuery().eq(OrderShipDtl::getBillId, orderShip.getBillId()).list();
                for (OrderShipDtl orderShipDtl : itemList) {
                    // 已退货
                    BigDecimal returnCounts = orderShipDtl.getReturnCounts();
                    // 已收货数量
                    BigDecimal shipNum = orderShipDtl.getShipNum();
                    BigDecimal productPrice = orderShipDtl.getProductPrice();
                    BigDecimal realAmount = shipNum.subtract(returnCounts).multiply(productPrice).setScale(2, BigDecimal.ROUND_UP);
                    orderShipAmount = orderShipAmount.add(realAmount);
                }
            }
        //}
        System.out.println("零星采购发货金额：" + orderShipAmount);


        List<Orders> contractPlanLists = ordersService.list(new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrderClass, 1, 2)
                .eq(Orders::getProductType, 12));
        System.out.println("大宗订单数：" + contractPlanLists.size());


        List<MaterialReconciliation> list = materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getBusinessType, 1)
                .eq(MaterialReconciliation::getState, 3).list();
        BigDecimal toavbleA = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list) {
            toavbleA = toavbleA.add(materialReconciliation.getReconciliationAmount());
        }
        System.out.println("大宗月供订单对账总金额：" + toavbleA);


//        BigDecimal bigDecimal = new BigDecimal(0);
//        for (DealOrderInfo dealOrderInfo : dealOrderInfos) {
//            bigDecimal = bigDecimal.add(dealOrderInfo.getAmount());
//        }
        // 大宗临购的订单数和大宗临购的订单金额。零星采购对账金额。总结算金额
        List<Orders> clgOrderLists = ordersService.list(new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrderClass, 1, 2)
                .eq(Orders::getProductType, 13));
        System.out.println("大宗临购订单数：" + clgOrderLists.size());

        // 大宗临购订单金额
        BigDecimal sumAmount2 = new BigDecimal(0);
        for (Orders orders : clgOrderLists) {
            sumAmount2 = sumAmount2.add(orders.getActualAmount());
        }
        System.out.println("大宗临购订单金额：" + sumAmount2);
        // 零星采购对账金额
        List<MaterialReconciliation> list2 = materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getBusinessType, 2)
                .eq(MaterialReconciliation::getState, 3).list();
        BigDecimal toavbleA2 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list2) {
            toavbleA2 = toavbleA2.add(materialReconciliation.getReconciliationAmount());
        }
        System.out.println("零星采购对账金额：" + toavbleA2);
        // 自营店对账金额
        BigDecimal selfToavble = new BigDecimal(0);
        BigDecimal stwoToavble = new BigDecimal(0);
        Integer lxSize = 0;
        Integer lxTwoSize = 0;
        for (MaterialReconciliation materialReconciliation : list2) {
            if (materialReconciliation.getOrgShort() != null && materialReconciliation.getOrgShort().equalsIgnoreCase("LQWZ")){
                selfToavble = selfToavble.add(materialReconciliation.getReconciliationAmount());
                lxSize++;

            }else {
                stwoToavble = stwoToavble.add(materialReconciliation.getReconciliationAmount());
                lxTwoSize++;
            }
        }
        System.out.println("自营店零星采购对账金额：" + selfToavble);
        System.out.println("自营店零星采购对账条数：" + lxSize);


        System.out.println("二级零星采购对账金额：" + stwoToavble);
        System.out.println("二级零星采购对账条数：" + lxTwoSize);


        // 大宗临购对账金额
        List<MaterialReconciliation> list3 = materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getBusinessType, 6)
                .eq(MaterialReconciliation::getState, 3).list();
        BigDecimal toavbleA3 = new BigDecimal(0);
        Integer lgSize = 0;
        BigDecimal toavbleTwoA3 = new BigDecimal(0);
        Integer lgTwoSize = 0;
        for (MaterialReconciliation materialReconciliation : list3) {
            if (materialReconciliation.getOrgShort() != null && materialReconciliation.getOrgShort().equalsIgnoreCase("LQWZ")) {
                toavbleA3 = toavbleA3.add(materialReconciliation.getReconciliationAmount());
                lgSize++;
            }else {
                toavbleTwoA3 = toavbleTwoA3.add(materialReconciliation.getReconciliationAmount());
                lgTwoSize++;
            }
        }
        System.out.println("自营店临购对账金额：" + toavbleA3);
        System.out.println("自营店临购对账条数:"+lgSize);
        System.out.println("二级临购对账金额：" + toavbleTwoA3);
        System.out.println("二级临购对账条数:"+lgTwoSize);

        List<DealOrderInfo> dealOrderInfos = dealOrderInfoService.lambdaQuery()
                .list();
        BigDecimal bigDecimal = new BigDecimal(0);
        for (DealOrderInfo dealOrderInfo : dealOrderInfos) {
            bigDecimal = bigDecimal.add(dealOrderInfo.getAmount());
        }
        System.out.println("结算总金额："  + bigDecimal);
    }


    /**
     * 统计物资数据
     */
    @Test
    public void mallStatisticalNum() {
//        String src = mallConfig.templateFormUrl;
//        Map<String, Object> dataMap = new HashMap<>();
//
//        StatisticalNum statisticalNumVo = new StatisticalNum();
//
//
//        //统计时间
//        statisticalNumVo.setStatisticalDate(new Date());
//        //供应商数
//        int supplierNum = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
//                .eq(EnterpriseInfo::getIsPcwp,1).isNotNull(EnterpriseInfo::getSocialCreditCode));
//        statisticalNumVo.setSupplierNum(supplierNum);
//
//        //店铺数 排除审核未通过
//        int shopCoounts = shopService.count(new LambdaQueryWrapper<Shop>().ne(Shop::getAuditStatus, 3));
//        statisticalNumVo.setShopNum(shopCoounts);
//        //内部企业数
//        int inEnterPrise = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>().isNotNull(EnterpriseInfo::getInteriorId));
//        statisticalNumVo.setInEnterprise(inEnterPrise);
//
//        //内部用户数
//        int inUserCounts = userService.count(new LambdaQueryWrapper<User>().eq(User::getIsInternalUser,1));
//        statisticalNumVo.setUserNum(inUserCounts);
//
//
//        //商品总数
//        int productNum = productService.count();
//        statisticalNumVo.setProductNum(productNum);
//        //上架商品总数
//        int upProductNum = productService.count(new LambdaQueryWrapper<Product>().eq(Product::getState,1));
//        statisticalNumVo.setUpProductNum(upProductNum);
//        //零星采购订单数
//        List<Orders> ordersList = ordersService.list(new LambdaQueryWrapper<Orders>().in(Orders::getOrderClass, 1, 2).eq(Orders::getOrderSourceType, 2));
//
//        BigDecimal sumAmount = BigDecimal.valueOf(0);
//        for (Orders orders : ordersList) {
//            sumAmount=sumAmount.add(orders.getActualAmount());
//        }
//        statisticalNumVo.setOrdersNum(ordersList.size());
//        statisticalNumVo.setOrdersTotalAmount(sumAmount);
//        List<OrderShip> orderShips = orderShipService.list(new LambdaQueryWrapper<OrderShip>().eq(OrderShip::getSourceType, 2).eq(OrderShip::getType, 2));
//        BigDecimal orderShipAmount = BigDecimal.valueOf(0);
//        for (OrderShip orderShip : orderShips) {
//            orderShipAmount=orderShipAmount.add(orderShip.getRateAmount());
//        }
//         statisticalNumVo.setOrderShipAmount(orderShipAmount);
////        statisticalNumService.save(statisticalNumVo);
////        List<StatisticalNum> list = statisticalNumService.list();


    }


}
