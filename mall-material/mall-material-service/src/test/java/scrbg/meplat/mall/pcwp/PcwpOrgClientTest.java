package scrbg.meplat.mall.pcwp;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import scrbg.meplat.mall.dto.plan.BulkRetailPlanDtlEX;
import scrbg.meplat.mall.dto.plan.BulkRetailPlanEX;
import scrbg.meplat.mall.pcwp.auth.model.TokenRes;
import scrbg.meplat.mall.pcwp.org.model.Org;
import scrbg.meplat.mall.pcwp.third.model.Material;
import scrbg.meplat.mall.pcwp.third.model.MaterialPageDto;
import scrbg.meplat.mall.util.AESUtil;

/**
 * 测试pcwp组织机构相关接口
 */
@SpringBootTest(webEnvironment = WebEnvironment.NONE, classes = PcwpConfig.class)
@ImportAutoConfiguration({
    FeignAutoConfiguration.class,
    HttpMessageConvertersAutoConfiguration.class,
    FeignClientsConfiguration.class
})
public class PcwpOrgClientTest {
    @Autowired
    private PcwpService pcwpService;
    @Test
    void testOrg() {
        // String username = "030138";
        // String password = "YUEshan.456789";
        String username = "wudaye01";
        String password = "@15108335761wxl";
        PcwpRes<TokenRes> tRes = pcwpService.signIn(username, AESUtil.encrypt(password));
        
        TokenRes t = tRes.getData(); 
        String token = t.getToken();
        String userId = t.getUserId();
        String nickname = t.getUserName();

        PcwpRes<List<Org>> orgsRes = pcwpService.getOrgByUserId(userId);

        List<Org> orgs = orgsRes.getData();
        // 用于购买
        Org buyer = null;
        for (Org org : orgs) {
            PcwpRes<List<String>> rolesRes = pcwpService.getUserHasRoles(userId, org, token);
            List<String> roles = rolesRes.getData();
            if (roles.contains("物资采购平台履约系统")) {
                buyer = org;
                break;
            }
        }

        assertNotNull(buyer);


        MaterialPageDto materialPageDto = new MaterialPageDto();
        materialPageDto.setIsActive(1);
        materialPageDto.setPageIndex(0);
        materialPageDto.setPageSize(2);
        materialPageDto.setClassId("a927247cb7b1-980c-724d-300a-7fdc7a5b");
        PcwpPageRes<Material> materialsRes = pcwpService.queryPageMaterialDtl(materialPageDto);
        List<Material> materials = materialsRes.getList();
       
        String keyId = IdWorker.getIdStr();
        String sourceBillId = uuid();
        String sourceBillNo = uuid();
        BulkRetailPlanEX a = new BulkRetailPlanEX();
        a.setFounderId(userId);
        a.setFounderName(nickname);
        a.setOrgId(buyer.getOrgId());
        a.setOrgName(buyer.getOrgName());
        a.setTaxRate(new BigDecimal(0.1));
        a.setTotalAmount(new BigDecimal(20000));
        a.setPlanDate(LocalDate.now().toString());
        a.setRemarks("单元测试使用");
        a.setPreparerId(userId);
        a.setPreparer(nickname);
        a.setType(2);
        a.setSourceBillId(sourceBillId);
        a.setSourceBillNo(sourceBillNo);
        a.setOrgShort(buyer.getShortCode());
        // TODO 暂时未知
        a.setCreditCode("915106817891367484");
        ArrayList<BulkRetailPlanDtlEX> vos = new ArrayList<>();
        for (Material material : materials) {
            BulkRetailPlanDtlEX b = new BulkRetailPlanDtlEX();
            b.setMaterialClassId(material.getClassIdPath());
            b.setMaterialClassName(material.getClassNamePath());
            b.setMaterialId(material.getBillId());
            b.setMaterialName(material.getMaterialName());
            b.setSpec(material.getSpec());
            b.setUnit(material.getUnit());
            // 材质 新需求上架的商品中会增加这个属性，现在用规格代替
            b.setTexture(material.getSpec());
            b.setQuantity(new BigDecimal(100));
            b.setNetworkPrice(null);
            b.setFactoryPrice(null);
            b.setFreight(null);
            b.setPrice(new BigDecimal(90));
            b.setTaxPrice(new BigDecimal(100));
            b.setAmount(new BigDecimal(10000));
            b.setSupplierId("supplierId");
            b.setSupplierName("供应商");
            b.setSourceDtlId(uuid());
            b.setTradeName(material.getMaterialName());
            b.setTradeId(uuid());
            b.setOrgShort("");
            b.setCreditCode("1");
            vos.add(b);
        }
        a.setDetails(vos);
        a.setAmount(new BigDecimal(18000));
        a.setTaxAmount(new BigDecimal(2000));
        KeyedPayload<BulkRetailPlanEX> BulkRetailPlan = KeyedPayload.<BulkRetailPlanEX>builder()
                                                            .keyId(keyId)
                                                            .data(a)
                                                            .build();
        PcwpRes<String> r = pcwpService.saveBulkRetailPlan(BulkRetailPlan);
        assertEquals(200, (int)r.getCode());

    }

    private String uuid(){
        return UUID.randomUUID().toString().replaceAll("-", "");
    }
}
