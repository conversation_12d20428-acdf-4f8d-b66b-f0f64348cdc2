package scrbg.meplat.mall;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.scrbg.common.utils.PageUtils;
import io.swagger.models.auth.In;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.config.MallConfig;

import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.*;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.service.impl.MaterialReconciliationServiceImpl;
import scrbg.meplat.mall.util.*;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.MathContext;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Log4j2
@SpringBootTest
class MallServiceApplicationTexts {

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    ProductService productService;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    ShopService shopService;

    @Autowired
    OrdersService ordersService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ProductSkuService productSkuService;
    @Autowired
    ProductCategoryService productCategoryService;
    @Autowired
    FileService fileService;

    @Autowired
    SystemParamService systemParamService;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    MallConfig mallConfig;
    @Autowired
    InterfaceLogsService interfaceLogsService;

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    UserService userService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    SysRoleService sysRoleService;
    @Autowired
    SysRoleMapper sysRoleMapper;

    @Autowired
    SynthesizeTemporaryService synthesizeTemporaryService;
    @Autowired
    SynthesizeTemporaryDtlService synthesizeTemporaryDtlService;


    @Autowired
    MaterialReconciliationDtlMapper materialReconciliationDtlMapper;

    @Autowired
    MaterialReconciliationService materialReconciliationService;

    private static final String SAVE_ACCEPTANCE_URL = "/thirdapi/acceptance/externalSystem/saveAcceptance";


    @Autowired
    PlatformYearFeeService platformYearFeeService;

    @Autowired
    PlatformDealFeeDtlMapper platformDealFeeDtlMapper;

    @Autowired
    PlatformDealFeeService platformDealFeeService;

    /**
     * 店铺过期
     */
    @Test
    public void sljflsd() {
        platformYearFeeService.updateYearFeeOutTime();
    }


    @Test
    public void tesjlf() {


    }

    @Autowired
    PlatformDealFeeDtlService platformDealFeeDtlService;
    @Autowired
    PlatformBalanceOperateService platformBalanceOperateService;

    private static boolean checkOutTime(EnterpriseInfo supplier, PlatformDealFee platformDealFee, BigDecimal thisFree) {
        BigDecimal arrearage = supplier.getArrearage();
        if (thisFree.compareTo(BigDecimal.ZERO) == 0) {
            return false;
        }
        boolean flag = false;
        // 判断他审核的本次的金额会不会导致欠费，只是为了处理欠费时间
        if (arrearage.compareTo(BigDecimal.ZERO) == 0) {
            flag = true;
        } else {
            if (thisFree.compareTo(arrearage) == 1) {
                flag = true;
            } else {
                if (platformDealFee.getArrearageDateTime() == null) {
                    return true;
                }
                // 未超过欠费额度,判断欠费时间
                Integer arrearageDateNum = supplier.getArrearageDateNum();
                Integer arrearageDateType = supplier.getArrearageDateType();
                if (arrearageDateNum != null && platformDealFee.getArrearageDateTime() != null) {
                    // 说明设置了欠费时间，并且已经欠费
                    Date arrearageDateTime = platformDealFee.getArrearageDateTime();
                    int day = 0;
                    if (arrearageDateType == 1) {
                        day = arrearageDateNum;
                    } else if (arrearageDateType == 2) {
                        day = arrearageDateNum * 30;
                    } else if (arrearageDateType == 3) {
                        day = arrearageDateNum * 365;
                    } else {
                        throw new BusinessException("欠费时长时间类型错误！");
                    }
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(arrearageDateTime);
                    calendar.add(Calendar.DATE, day);
                    Date newDate = calendar.getTime();

                    Date currentDate = new Date();  // 获取当前日期
                    if (currentDate.after(newDate)) { // 当前时间在欠费截止时间之后
                        flag = true;
                    } else if (currentDate.before(newDate)) {
                    } else {
                        flag = true;
                    }
                }

            }
        }
        return flag;
    }

    private static void addMCheck(EnterpriseInfo supplier, PlatformDealFee platformDealFee, BigDecimal totalAmount, BigDecimal arrearage) {
        // 说明存在带教费
        if (arrearage.compareTo(BigDecimal.ZERO) == 0) {
            throw new BusinessException("供应商存在待缴费交易服务费！已停止服务，请前往缴费中心缴费。");
        } else {
            if (totalAmount.compareTo(arrearage) == 1) {
                throw new BusinessException("供应商已超过可欠费交易服务费额度！已停止服务，请前往缴费中心缴费。");
            } else { // 未超过欠费额度
                Integer arrearageDateNum = supplier.getArrearageDateNum();
                Integer arrearageDateType = supplier.getArrearageDateType();
                if (arrearageDateNum != null && platformDealFee.getArrearageDateTime() != null) {
                    // 说明设置了欠费时间，并且已经欠费
                    Date arrearageDateTime = platformDealFee.getArrearageDateTime();
                    int day = 0;
                    if (arrearageDateType == 1) {
                        day = arrearageDateNum;
                    } else if (arrearageDateType == 2) {
                        day = arrearageDateNum * 30;
                    } else if (arrearageDateType == 3) {
                        day = arrearageDateNum * 365;
                    } else {
                        throw new BusinessException("欠费时长时间类型错误！");
                    }
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(arrearageDateTime);
                    calendar.add(Calendar.DATE, day);
                    Date newDate = calendar.getTime();

                    Date currentDate = new Date();  // 获取当前日期
                    if (currentDate.after(newDate)) { // 当前时间在欠费截止时间之后
                        throw new BusinessException("供应商已超过可欠费交易额度截止时间！已停止服务，请前往缴费中心缴费。");
                    } else if (currentDate.before(newDate)) {
                    } else {
                        throw new BusinessException("供应商已超过可欠费交易额度截止时间！已停止服务，请前往缴费中心缴费。");
                    }
                }
            }
        }
    }

    private void addDtlInfoList(MaterialReconciliation mr, EnterpriseInfo supplier, Integer serveType, PlatformDealFee platformDealFee, Integer isOE, BigDecimal yesFree) {
        PlatformDealFeeDtl dtl = new PlatformDealFeeDtl();
        dtl.setPlatformDealFeeId(platformDealFee.getPlatformDealFeeId());
        dtl.setRelevanceId(mr.getReconciliationId());
        dtl.setRelevanceNu(mr.getReconciliationNo());
        dtl.setRelevanceType(1);
        dtl.setServeType(serveType);
        EnterpriseInfo pOrg = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getInteriorId, mr.getPurchasingOrgId())
                .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getEnterpriseName).one();
        if (pOrg == null) throw new BusinessException("未查询到采购单位！");
        dtl.setProjectEnterpriseId(pOrg.getEnterpriseId());
        dtl.setProjectEnterpriseName(pOrg.getEnterpriseName());
        dtl.setEnterpriseId(supplier.getEnterpriseId());
        dtl.setEnterpriseName(supplier.getEnterpriseName());
        BigDecimal reconciliationAmount = mr.getReconciliationAmount();
        dtl.setDealAmount(reconciliationAmount);
        String keyStr1 = null;
        if (serveType == 1) {
            keyStr1 = PublicEnum.PLATFORM_SHOP_FEE_DEAL_RATIO.getRemark();
        } else {
            keyStr1 = PublicEnum.PLATFORM_CONTRACT_FEE_DEAL_RATIO.getRemark();
        }
        SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode, keyStr1).one();
        String keyValue = systemParam.getKeyValue();
        BigDecimal ratio = new BigDecimal(keyValue);
        dtl.setFeeRatio(ratio);
        BigDecimal divRatio = ratio.divide(new BigDecimal(1000), new MathContext(18)).setScale(6, BigDecimal.ROUND_HALF_UP);
        BigDecimal mFee = null;
        if (yesFree == null) {
            mFee = reconciliationAmount.multiply(divRatio).setScale(2, BigDecimal.ROUND_HALF_UP);
            dtl.setExceedFree(reconciliationAmount);
        } else {
            // 使用需要缴费金额作为收费
            mFee = yesFree.multiply(divRatio).setScale(2, BigDecimal.ROUND_HALF_UP);
            dtl.setExceedFree(yesFree);
            dtl.setUseExemptFree(reconciliationAmount.subtract(yesFree));
        }
        dtl.setServeFee(mFee);
        dtl.setPayFee(new BigDecimal(0));
        dtl.setResiduePayFee(mFee);
        dtl.setFinishPayFee(0);
        if (isOE == 0) {
            // 没有超过额度
            dtl.setUseExemptFree(reconciliationAmount);
            BigDecimal bo = new BigDecimal(0);
            dtl.setPayFee(bo);
            dtl.setResiduePayFee(bo);
            dtl.setFinishPayFee(1);
            dtl.setPayType(2);
            dtl.setServeFee(bo);
        }
        String keyStr2 = null;
        if (serveType == 1) {
            keyStr2 = PublicEnum.PLATFORM_DEAL_MAX_QUOTA_DP.getRemark();
        } else {
            keyStr2 = PublicEnum.PLATFORM_DEAL_MAX_QUOTA_LY.getRemark();
        }
        SystemParam systemParam2 = systemParamService.lambdaQuery().eq(SystemParam::getCode, keyStr2).one();
        BigDecimal notFeeMaxAmount = new BigDecimal(systemParam2.getKeyValue());
        dtl.setDealFeeAmount(notFeeMaxAmount);
        dtl.setUseBalance(new BigDecimal(0));
        dtl.setDealFeeQuota(supplier.getArrearage());
        dtl.setFeeEndDateNum(supplier.getArrearageDateNum());
        dtl.setFeeEndDateType(supplier.getArrearageDateType());


        if (isOE == 0) {
            platformDealFeeDtlService.save(dtl);
        } else {
            // 处理如果存在余额明细抵消明细
            BigDecimal remainingSum = platformDealFee.getRemainingSum();
            // 如果不存在余额，直接保存即可
            if (remainingSum.compareTo(BigDecimal.ZERO) == 0) {
                platformDealFeeDtlService.save(dtl);
            } else {
                // 如果存在余额开始消耗
                PlatformBalanceOperate pbo = new PlatformBalanceOperate();
                pbo.setTitle("审核对账单使用余额自动缴费交易");
                pbo.setRelevanceId(platformDealFee.getPlatformDealFeeId());
                pbo.setRelevanceType(1);
                pbo.setBeforeAmount(platformDealFee.getRemainingSum());
                // 余额大于服务费用
                if (remainingSum.compareTo(mFee) == 1) {
                    pbo.setAmount(mFee.negate());
                    platformDealFee.setRemainingSum(platformDealFee.getRemainingSum().subtract(mFee));
                    dtl.setPayFee(mFee);
                    dtl.setResiduePayFee(new BigDecimal(0)); // 剩余缴费金额
                    dtl.setFinishPayFee(1); // 是否完成缴费
                    dtl.setUseBalance(mFee); // 使用余额金额
                } else if (remainingSum.compareTo(mFee) == 0) {
                    pbo.setAmount(mFee.negate());
                    platformDealFee.setRemainingSum(platformDealFee.getRemainingSum().subtract(mFee));
                    dtl.setPayFee(mFee);
                    dtl.setResiduePayFee(new BigDecimal(0)); // 剩余缴费金额
                    dtl.setFinishPayFee(1); // 是否完成缴费
                    dtl.setUseBalance(mFee); // 使用余额金额
                } else {
                    pbo.setAmount(platformDealFee.getRemainingSum().negate());
                    // 余额小于需缴费金额
                    platformDealFee.setRemainingSum(new BigDecimal(0));
                    dtl.setPayFee(remainingSum);
                    dtl.setResiduePayFee(mFee.subtract(remainingSum)); // 剩余缴费金额
                    dtl.setFinishPayFee(0); // 是否完成缴费
                    dtl.setUseBalance(remainingSum); // 使用余额金额
                }
                platformDealFeeService.update(platformDealFee);
                platformDealFeeDtlService.save(dtl);
                pbo.setAfterAmount(platformDealFee.getRemainingSum());
                pbo.setOperateId(dtl.getPlatformDealFeeDtlId());
                pbo.setOperateUn(dtl.getRelevanceNu());
                pbo.setOperateType(2);
                platformBalanceOperateService.save(pbo);
            }
        }
    }
    @Test
    public void upEnterPriseParameterIsPcwp() {
        LambdaQueryWrapper<EnterpriseInfo> q = new LambdaQueryWrapper<>();
        q.isNull(EnterpriseInfo::getInteriorId)
                .eq(EnterpriseInfo::getIsPcwp,1);
        List<EnterpriseInfo> list = enterpriseInfoService.list(q);
        List<EnterpriseInfo> enterpriseInfos = new ArrayList<>();
        for (EnterpriseInfo enterpriseInfo : list) {
            // 发送请求查询是否供应商
            String url2 = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.IS_PCWP
                    + enterpriseInfo.getSocialCreditCode();
            HttpHeaders headers2 = new HttpHeaders();
            headers2.add("token", mallConfig.thirdApiToken);
            com.scrbg.common.utils.R isSupp = restTemplateUtils.get(url2, headers2, com.scrbg.common.utils.R.class).getBody();

            if (isSupp.getCode() != null && isSupp.getCode() == 200) {
                if (isSupp.getData() != null) {
                    // 有信息也是待定状态，需要后台进行审核
                    enterpriseInfo.setIsPcwp(1);

                }else {
                    enterpriseInfo.setIsPcwp(0);
                    enterpriseInfos.add(enterpriseInfo);
                }
            } else {
                enterpriseInfo.setIsPcwp(1);
                enterpriseInfos.add(enterpriseInfo);
            }

        }
        for (EnterpriseInfo enterpriseInfo : enterpriseInfos) {
            shopService.lambdaUpdate().set(Shop::getState,0).eq(Shop::getEnterpriseId,enterpriseInfo.getEnterpriseId()).update();
            log.error("查询供应商信息失败，企业ID：" + enterpriseInfo.getEnterpriseId());
        }

        enterpriseInfoService.updateBatchById(enterpriseInfos);
        System.out.println(enterpriseInfos);
    }

    @Test
    public void classMasdfange() {
        String decrypt = AESUtil.encrypt("ZYH06134945");
        System.out.println("解密：" + decrypt);

    }


    //    // 读
    @Test
    public void testExcel() throws FileNotFoundException {

        // 修改所有订单的不含税金额
//        List<Orders> list = ordersService.lambdaQuery().list();
//        for (Orders orders : list) {
//            BigDecimal taxRate = orders.getTaxRate();
//            orders.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(orders.getActualAmount(),taxRate));
//            ordersService.update(orders);
//            List<OrderItem> list1 = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orders.getOrderId()).list();
//            for (OrderItem orderItem : list1) {
//                orderItem.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(orderItem.getTotalAmount(),taxRate));
//                orderItemService.update(orderItem);
//            }
//        }


//        List<User> list = userService.lambdaQuery().eq(User::getIsInternalUser, 0).select(User::getUserMobile, User::getEnterpriseId).list();
//        for (User user : list) {
//            String userMobile = user.getUserMobile();
//            String enterpriseId = user.getEnterpriseId();
//            EnterpriseInfo one = enterpriseInfoService.lambdaQuery()
//                    .in(EnterpriseInfo::getEnterpriseType, 0, 1)
//                    .eq(EnterpriseInfo::getEnterpriseId, enterpriseId).one();
//            if(one != null) {
//                if (one.getSocialCreditCode() == null) {
//                    System.out.println(userMobile);
//                }
//            }
//        }
//        stringRedisTemplate.opsForValue().set(RedisKey.REGISTER_CODE_KEY + "15632339777", "934868" + "_" + System.currentTimeMillis(), RedisKey.REGISTER_CODE_TTL, TimeUnit.MINUTES);

        //        String s = CodeGenerator.generateUniqueCode();
//        System.out.println(s.length());
//        System.out.println(s);
//        String url = "http://192.168.100.100:6002/thirdapi/matarialpurchase/getAllCategoryLibrary";
//        R<List<Map>> r = restTemplateUtils.getPCWP2NotParams(url);
//        System.out.println(r.getData().size());
        /*
            billId	物资类别id	string
            billNo	类别编号	string
            className	类别名称	string
            isEnable	是否启用(0：停用;1：启用)	integer(int32)
            materialType	物资类型(0：一般材料;1:：周转材料)	integer(int32)
            parentClassId	上级类别id	string
            parentClassName	上级类别名称	string
            unit	单位	string
         */
//        ArrayList<ProductCategory> p = new ArrayList<>();
//        for (Map classMap : r.getData()) {
//            ProductCategory productCategory = new ProductCategory();
//            productCategory.setClassId((String) classMap.get("billId"));
//            productCategory.setClassNo((String) classMap.get("billNo"));
//            productCategory.setClassName((String) classMap.get("className"));
//            productCategory.setState((Integer) classMap.get("isEnable"));
//            productCategory.setProductType(0);
//            productCategory.setParentId((String) classMap.get("parentClassId"));
//            productCategoryService.save(productCategory);
//        }
//        productCategoryService.saveBatch(p);

        //同步读取文件内容
//        FileInputStream inputStream = new FileInputStream(new File("C:\\text\\user.xlsx"));
//        List<UserWriteEntity> list = EasyExcel.read(inputStream).head(UserWriteEntity.class).sheet().doReadSync();
//        System.out.println(JSONArray.toJSONString(list));


//        CreateDeviseDTO createDeviseDTO = new CreateDeviseDTO();
//        createDeviseDTO.setCity("1231");
//        LogUtil.writeErrorLog(IdWorker.getIdStr(),"createById",createDeviseDTO,null,"无","ls",ProductCategory.class);
////
//
//        // 发送请求
//
//        String localArgJson = JSON.toJSONString(createDeviseDTO);
//        String farArgJson = JSON.toJSONString(createDeviseDTO);
//        log.info("分布式事务日志-秘钥key:{} \n方法名:{} \n本地接口请求参数:{} \n远程接口请求参数:{} \n接口返回:{} " +
//                        "\n---------------------------------------结束---------------------------------------"
//                ,"sdfadfa","createByid",localArgJson,farArgJson,"dasdas");


//        String productClassPath = productCategoryService.getProductClassPath("1643689468291047426", new StringBuffer("1643689468291047426"));
//        System.out.println(productClassPath);
//        List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath("1643689468291047426");
//        String classPath = "";
//        if (CollectionUtils.isEmpty(categoryParentPath)) {
//            throw new BusinessException("分类不存在！");
//
//        } else {
//            if (categoryParentPath.size() == 1) {
//                System.out.println(categoryParentPath.get(0).getClassName());
//            } else {
//                for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
//                    if (!org.springframework.util.StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
//                        classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
//                    }
//                }
//                System.out.println(classPath.substring(0, classPath.length() - 1));
//            }
//        }
    }



//    // 写
//    @Test
//    public void testExcel2() throws FileNotFoundException {
//        List<UserWriteEntity> dataList = new ArrayList<>();
//        for (int i = 0; i < 10; i++) {
//            UserWriteEntity userEntity = new UserWriteEntity();
//            userEntity.setName("张三" + i);
//            userEntity.setAge(20 + i);
//            userEntity.setTime(new Date(System.currentTimeMillis() + i));
//            dataList.add(userEntity);
//        }
//        //定义文件输出位置
//        FileOutputStream outputStream = new FileOutputStream(new File("C:\\text\\user.xlsx"));
//        EasyExcel.write(outputStream, UserWriteEntity.class).sheet("用户信息").doWrite(dataList);
//    }


    @Autowired
    ProductMapper productMapper;

    @Autowired
    ProductSkuMapper productSkuMapper;

    @Autowired
    FileMapper fileMapper;

    @Autowired
    FileRecordDeleteService fileRecordDeleteService;
    @Autowired
    private RestTemplateUtils restTemplateUtils;


    @Test
    public void dsfafasdfas() {
        // 修改鑫方盛物资所有的商品都是已确认的
//        productService.lambdaUpdate()
//                .eq(Product::getSupplierName,"四川鑫方盛电子商务有限公司")
//                .set(Product::getSupplierSubmitState,3).update();
    }

    @Test
    public void updateProductType() {
//        ProductCategory productCategory = productCategoryService.lambdaQuery().eq(ProductCategory::getClassName, "低值易耗品")
//                .eq(ProductCategory::getClassLevel, 1).one();
//        boolean f = productCategoryService.lambdaUpdate().eq(ProductCategory::getClassId, productCategory.getClassId())
//                .set(ProductCategory::getProductType, 0).update();
//        if(f) {
//            List<ProductCategory> list = productCategoryService.lambdaQuery().eq(ProductCategory::getParentId, productCategory.getClassId()).list();
//            for (ProductCategory category : list) {
//                boolean f2 = productCategoryService.lambdaUpdate().eq(ProductCategory::getClassId, category.getClassId())
//                        .set(ProductCategory::getProductType, 0).update();
//                List<ProductCategory> list2 = productCategoryService.lambdaQuery().eq(ProductCategory::getParentId, category.getClassId()).list();
//                for (ProductCategory productCategory1 : list2) {
//                     productCategoryService.lambdaUpdate().eq(ProductCategory::getClassId, productCategory1.getClassId())
//                            .set(ProductCategory::getProductType, 0).update();
//
//                }
//            }
//        }


//        String classId = "1643689512608063489";
//        List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(classId);
//        System.out.println(categoryParentPath);
    }

    @Test
    public void testPss() {
//        List<File> list = fileService.lambdaQuery()
//                .last("limit 1")
//                .eq(File::getRelevanceType, 1)
//                .select(File::getFileFarId,File::getFileId).list();
//        for (File file : list) {
//            HttpHeaders headers = new HttpHeaders();
//            String url22 = "http://192.168.91.7:9009/oss/deleteFile?recordId=" + file.getFileFarId();
//            R r = restTemplateUtils.get(url22, headers, R.class).getBody();
//            System.out.println(r);
//            if(r.getCode() == 200) {
//                fileMapper.deleteFileById(file.getFileId());
//            }
//        }

//       List<String> listId =  productMapper.getProductIdBySizeIsDelete(10000,0);
//        for (String productId : listId) {
//            int xxx = productMapper.deleteByProductId(productId);
//            System.out.println(xxx);
//            int skuRes = productSkuMapper.deleteBypProductId(productId);
//            System.out.println(skuRes);
//            fileMapper.deleteBatchFileByRelevanceIdAndType(productId,1);
//            System.out.println("删除成功");
//        }
//        String url2 = mallConfig.prodPcwp2Url02 + "/thirdapi/outer/getSupplierBycreditCode?creditCode="
//                + "xxxx";
//        HttpHeaders headers2 = new HttpHeaders();
//        headers2.add("token", mallConfig.thirdApiToken);
//        R isSupp = restTemplateUtils.get(url2, headers2, R.class).getBody();
//        System.out.println(isSupp);

        // 删除未逻辑删除的数据
//        List<Product> list = productService.lambdaQuery().last("limit 5000").select(Product::getProductId).list();
//        for (Product product : list) {
//            String productId = product.getProductId();
//            int xxx = productMapper.deleteByProductId(productId);
//            System.out.println(xxx);
//            int skuRes = productSkuMapper.deleteBypProductId(productId);
//            System.out.println(skuRes);
//            fileMapper.deleteBatchFileByRelevanceIdAndType(productId,1);
//        }


//        System.out.println("12m³".equals("12M³"));
        // bug 如只查询一个列直接返回null
//        List<ProductSku> list = productSkuService.lambdaQuery().in(ProductSku::getSkuId, "1611295870618779649")
//                .select(ProductSku::getDiscounts).list();
//        systemParamService.updateIndexParameterCount();
//        737c0d41742fd771b814283f10e071cd
//        systemParamService.updateIndexParameterCount();


        /**
         * 解析密码
         */
//        ArrayList<String> strings = new ArrayList<>();
//        strings.add("c541dfca761f59dda2af7baffe791b2a");
//        System.out.println("1111111111111111111111");
//        for (String string : strings) {
//            String decrypt2 = AESUtil.decrypt(string);
//            System.out.println(decrypt2);
//        }


        /**
         * 修改分类的路径
         */

//        List<ProductCategory> list = productCategoryService.lambdaQuery().select(ProductCategory::getClassId).list();
//        for (ProductCategory productCategory : list) {
//            List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(productCategory.getClassId());
//            String classPath = "";
//            if(!CollectionUtils.isEmpty(categoryParentPath)) {
//                for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
//                    if(!StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
//                        classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
//                    }
//                }
//            }
//            productCategoryService.lambdaUpdate().eq(ProductCategory::getClassId,productCategory.getClassId())
//                    .set(ProductCategory::getClassPath,classPath.substring(0,classPath.length() -1)).update();
//        }


        /**
         * 对接pcwp1接口日期格式
         */
//        LocalDateTime localDateTime = LocalDateTime.now();
//        ZoneOffset zoneOffset = ZoneOffset.of("+8");
//        OffsetDateTime offsetDateTime = OffsetDateTime.of(localDateTime, zoneOffset);
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSXXX");
//        String thisISO8601Date = offsetDateTime.format(formatter);


        // 批量通过物资
//        productService.lambdaUpdate()
//                .set(Product::getState, 1)
//                .set(Product::getFailReason, null)
//                .set(Product::getPutawayDate, new Date())
//                .set(Product::getGmtModified, new Date())
//                .update();
//        productSkuService.lambdaUpdate()
//                .set(ProductSku::getState, PublicEnum.STATE_OPEN.getCode())
//                .set(ProductSku::getGmtModified, new Date())
//                .update();
//        System.out.println("执行成功");


    }

    @Test
    public void ttt() {
//        String socialCrede="91511181MA6282FP9P";
//        Integer mallType =0;
//        String a="http://172.17.1.216:9011/w/user/getIsSupplier?socialCreditCode="+socialCrede+"&&mallType="+mallType;
//        String forObject = restTemplate.getForObject(a, String.class);
//        System.out.println(forObject);


        // 查询不是自营店商品补充机构id

//        List<Product> list = productService.lambdaQuery()
//                .ne(Product::getShopId,"1645601878095495170")
//                .groupBy(Product::getShopId)
//                .select(Product::getShopId).list();
//        for (Product product : list) {
//            Shop byId = shopService.getById(product.getShopId());
//            if(byId != null) {
//                boolean update = productService.lambdaUpdate()
//                    .ne(Product::getShopId, "1645601878095495170")
//                        .eq(Product::getShopId, product.getShopId())
//                        .set(Product::getSupperBy, byId.getEnterpriseId()).update();
//                System.out.println(update);
//            }else {
//                System.out.println("店铺为null" + product.getShopId());
//            }
//        }

//        boolean update = productService.lambdaUpdate()
//                .eq(Product::getShopId, "1645601878095495170")
//                .eq(Product::getIsOpenImport,1)
//                .set(Product::getSupperBy,"1668851395576467459").update();
//        System.out.println(update);


        //给所有商品冗余供应商名称
//        List<Product> list = productService.lambdaQuery()
//                .groupBy(Product::getSupperBy)
//                .select(Product::getSupperBy).list();
//        for (Product product : list) {
//            EnterpriseInfo enterpriseInfo = enterpriseInfoService.getById(product.getSupperBy());
//            if(enterpriseInfo != null) {
//                boolean update = productService.lambdaUpdate()
//                        .eq(Product::getSupperBy, product.getSupperBy())
//                        .set(Product::getSupplierName, enterpriseInfo.getEnterpriseName()).update();
//                System.out.println(update);
//            }else {
//            }
//        }


//        Shop shop = new Shop();
//        shop.setGmtCreate(new Date());
//        shop.setShopName("测试");
//        Shop shop2 = new Shop();
//        try {
//            BeanUtils.copyProperties(shop, shop2);
//        } catch (IllegalAccessException e) {
//            throw new RuntimeException(e);
//        } catch (InvocationTargetException e) {
//            throw new RuntimeException(e);
//        }
//        System.out.println(shop2);
//        System.out.println(shop2.getGmtCreate());
    }

    @Test
    public void text2() {
//        List<ProductSku> list = productSkuService.list();
//        for (ProductSku productSku : list) {
//            BigDecimal multiplyPercent = CommonUtil.getMultiplyPercent("90.10%", productSku.getSellPrice());
//            boolean update = productSkuService.lambdaUpdate().eq(ProductSku::getSkuId, productSku.getSkuId()).set(ProductSku::getSettlePrice, multiplyPercent).update();
//        }
        // 查询分类路径
//        List<String> classPath = productCategoryService.getCategoryParentIdList("1613732634880909313");
//        // 处理分类路径
//        String cp = "";
//        if (!CollectionUtils.isEmpty(classPath)) {
//            for (String s : classPath) {
//                cp += s + "/";
//            }
//        }
//        String as = cp.substring(0, cp.length() - 1);
//        productService.lambdaUpdate().eq(Product::getMallType, 0)
//                .set(Product::getClassPath, as).update();

    }

    @Test
    public void testFanoutExchange() {
//        Product product = productService.getById("1611310526548774914");
//        String productId = product.getProductId();
//        List<File> list = fileService.lambdaQuery().eq(File::getRelevanceId, "1611310526548774914").list();
//        for (File file : list) {
//            file.setFileId(null);
//        }
//        ProductSku productSku = productSkuService.lambdaQuery().eq(ProductSku::getProductId, productId).one();
//        productSku.setSkuId(null);
//        String productName = product.getProductName();
//        String skuName = productSku.getSkuName();
//        // 开始插入数据
//        for (int i = 0; i < 0; i++) {
//            product.setProductId(null);
//            Random random = new Random();
//            product.setProductName(productName + "LS" + random.nextInt(100) + "FON" + random.nextInt(1000) + "LSO");
//            product.setSerialNum(CodeEnum.CODE_ZB.getRemark() + IdWorker.getId());
//            boolean save = productService.save(product);
//            if(save){
//                // 文件
//                for (File file : list) {
//                    file.setFileId(null);
//                    file.setRelevanceId(product.getProductId());
//                }
//                fileService.saveBatch(list);
//                // sku
//                productSku.setSkuId(null);
//                productSku.setProductId(product.getProductId());
//                productSku.setSkuName(skuName + IdWorker.get32UUID().substring(0,6));
//                productSkuService.save(productSku);
//            }
//        }

    }

    @Test
    public void httpPost() {
//        String url = mallConfig.prodPcwp2Url + "/thirdapi/purchase/contract/getEquipmentLeaseForPlatform";
//        HashMap<String, Object> params = new HashMap<>();
//        params.put("limit", 10);
//        params.put("page", 1);
//        params.put("socialCode", "91510000746915565B");
//        String token = "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA";
//
//        PageUtils dateList = getDateList(url, params);
//        List list = dateList.getList();

    }

    //    public PageUtils getDateList(String url, HashMap map) {
//        List listw = new ArrayList<Map<String, Object>>();
//        PageUtils<Object> pageUtils = new PageUtils<>();
//        String token = "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA";
//        JSONObject jsonObject = HttpClientUtil.doPostParams(url, map, token);
//        Map<String, Object> innerMap = jsonObject.getInnerMap();
//        Integer code = (Integer) innerMap.get("code");
//        if (code == 200) {
//            JSONObject jsonObject1 = (JSONObject) innerMap.get("data");
//            Map<String, Object> dataPages = jsonObject1.getInnerMap();
//            Integer currPage = (Integer) dataPages.get("currPage");
//            Integer totalPage = (Integer) dataPages.get("totalPage");
//            Integer pageSize = (Integer) dataPages.get("pageSize");
//            Integer totalCount = (Integer) dataPages.get("totalCount");
//
//
//            pageUtils.setCurrPage(currPage);
//            pageUtils.setPageSize(pageSize);
//            pageUtils.setTotalPage(totalPage);
//            pageUtils.setTotalCount(totalCount);
//            List<Object> objects = (List<Object>) dataPages.get("list");
//            for (Object object : objects) {
//                Map<String, Object> ret = (Map<String, Object>) object;
//                listw.add(ret);
//            }
//
//            pageUtils.setList(listw);
//
//
//        }
//        return pageUtils;
//    }
    @Test
    public void getMysqlList() {
//        List<Sheel> list = sheelServer.list();
//        ProductCategory parentInfo = productCategoryService.getById("1598151034847973231");
//        for (Sheel sheel : list) {
//           String name=sheel.getName();
//            String[] split = name.split("/");
//            if (split.length>1){
//                String s=parentInfo.getClassId();
//                ProductCategory info =null;
//                for (int i = 1; i < split.length; i++) {
//                    ProductCategory productCategory = new ProductCategory();
//                    if (i>1){
//                        String a=s;
//                        info = productCategoryService.getClassNameAndParentId(split[i],a,i+1);
//                        productCategory.setParentId(a);
//
//                    } else {
//                         info = productCategoryService.getClassNameAndParentId(split[i],s,i+1);
//                         productCategory.setParentId(s);
//                    }
//                     if (info==null){
//                         productCategory.setMallType(0);
//                         productCategory.setClassName(split[i]);
//                         productCategory.setClassLevel(i+1);
//                         productCategory.setSort(0);
//                         productCategory.setIsDelete(0);
//                         productCategory.setGmtCreate(new Date());
//                         productCategory.setState(1);
//                         productCategory.setProductType(0);
//                         productCategory.setIsExhibition(1);
//                         productCategory.setIsHaveProduct(0);
//                         productCategoryService.save(productCategory);
//                         s=productCategory.getClassId();
//                }else {
//                         s=info.getClassId();
//                     }
//            }
//        }
//
//    }}
    }

    @Test
    public void ListSort() {
    }



}
