package scrbg.meplat.mall;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import scrbg.meplat.mall.service.ProductCompareService;

import javax.annotation.Resource;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: maill_api
 * @description: 比价测试类
 * @author: 代文翰
 * @create: 2023-12-19 14:23
 **/
@SpringBootTest
@Log4j2
public class ProductCompareTest {
    @Resource
    private ProductCompareService productCompareService;
    @Test
    public void testCreate() {
            List<String> ids = new ArrayList<>();

            ids.add("1742074289319264257");
            //ids.add("1734773399579156481");
            //ids.add("1735134192804577281");
            //ids.add("1735134150073008130");
            //ids.add("1735120147305218050");
            //ids.add("1735133966878392322");
            //ids.add("1735134150073008130");
            try {
                FileOutputStream fileOutputStream = new FileOutputStream("/Volumes/westDisk/testExcel/compareAll19.pdf");
                productCompareService.mergeCompareDetailToPDF1(ids,fileOutputStream);
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            }
        }

}
