package scrbg.meplat.mall;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.JwtTokenUtil;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/7/17
 */
@SpringBootTest
public class ReceiptTest {
    @Resource
    private JwtTokenUtil jwtUtil;

    @Resource
    private WxAppLoginService wxAppLoginService;

    @Autowired
    private MaterialReconciliationService materialReconciliationService;

    @Autowired
    private MaterialReconciliationDtlService materialReconciliationDtlService;

    @Autowired
    OrdersService ordersService;

    @Autowired
    MaterialMonthSupplyPlanService materialMonthSupplyPlanService;

    @Autowired
    PlatformYearFeeService platformYearFeeService;


    @Autowired
    SystemParamService systemParamService;



    @Test
    public void getLJSLF() {
        SystemParam systemParam1 = new SystemParam();
        systemParam1.setIsDelete(0);
        systemParam1.setName("11111111111111");
        systemParamService.save(systemParam1);

    }







    @Test
    public void  testJwt() throws Exception {

//        String token = jwtUtil.generateToken("18783405317");
//        String token1 = jwtUtil.getUserNameFromToken(token);
//        boolean b = jwtUtil.canRefresh(token);
//        System.out.println(b);
//        ReceiptPerson person = wxAppLoginService.lambdaQuery().eq(ReceiptPerson::getPhone, token1).one();
//        System.out.println(person);
//        System.out.println("token:"+token);
//        System.out.println("username:"+token1);

//        System.out.println(TaxCalculator.calculateYesTarRateAmount(new BigDecimal(33.22), new BigDecimal(3.33)));
//        System.out.println(TaxCalculator.calculateYesTarRateAmount(new BigDecimal(22.66), new BigDecimal(6.66)));
//        System.out.println(TaxCalculator.calculateYesTarRateAmount(new BigDecimal(74.77), new BigDecimal(0.1)));
//        System.out.println(TaxCalculator.calculateYesTarRateAmount(new BigDecimal(723.12), new BigDecimal(0.9)));

//        for (MaterialReconciliationDtl materialReconciliationDtl : materialReconciliationDtlService.lambdaQuery().list()) {
//            MaterialReconciliation d = materialReconciliationService.getById(materialReconciliationDtl.getReconciliationId());
//            materialReconciliationDtl.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(materialReconciliationDtl.getPrice(),d.getTaxRate()));
//            materialReconciliationDtl.setAcceptanceNoRateAmount(TaxCalculator.calculateNotTarRateAmount(materialReconciliationDtl.getAcceptanceAmount(),d.getTaxRate()));
//            materialReconciliationDtlService.update(materialReconciliationDtl);
//        }


//        List<OrderSelectPlan> list = orderSelectPlanService.lambdaQuery().list();
//        for (OrderSelectPlan orderSelectPlan : list) {
//            String orderId = orderSelectPlan.getOrderId();
//            Orders byId = ordersService.getById(orderId);
//            if(byId != null) {
//                orderSelectPlan.setTaxRate(byId.getTaxRate());
//                orderSelectPlanService.update(orderSelectPlan);
//            }
//        }

//        Map<String, Object> map = new HashMap<>();
//        String f = "";
//        String f1 = "";
//        ArrayList<Map> maps = new ArrayList<>();
//
//        ExcelForWebUtil.saveExcel(f,map,f1);








    }




}
