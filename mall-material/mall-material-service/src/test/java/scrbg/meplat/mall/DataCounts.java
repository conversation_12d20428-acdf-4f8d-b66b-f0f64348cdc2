//package scrbg.meplat.mall;
//
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import scrbg.meplat.mall.service.EnterpriseInfoService;
//import scrbg.meplat.mall.service.ProductService;
//import scrbg.meplat.mall.service.ShopService;
//import scrbg.meplat.mall.service.UserService;
//import scrbg.meplat.mall.entity.StatisticalNum;
//
//@Slf4j
//@SpringBootTest
//public class DataCounts {
//    @Autowired
//    UserService userService;
//
//    @Autowired
//    ShopService shopService;
//
//    @Autowired
//    EnterpriseInfoService enterpriseInfoService;
//    @Autowired
//    ProductService productService;
//
//    @Test
//    public void addReportByExcel21() {
//        StatisticalNum statisticalNumVo = new StatisticalNum();
//    }
//
//
//
//
//
//}
