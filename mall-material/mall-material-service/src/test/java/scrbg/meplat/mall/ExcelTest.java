//package scrbg.meplat.mall;
//
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.scrbg.common.utils.R;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import scrbg.meplat.mall.config.MallConfig;
//import scrbg.meplat.mall.entity.ProductCategory;
//import scrbg.meplat.mall.exception.BusinessException;
//import scrbg.meplat.mall.service.ProductCategoryService;
//import scrbg.meplat.mall.util.PCWP2ApiUtil;
//import scrbg.meplat.mall.util.RestTemplateUtils;
//import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
//
//import javax.servlet.http.HttpServletResponse;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
////package scrbg.meplat.mall;
////
////import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
////import lombok.extern.slf4j.Slf4j;
////import org.junit.jupiter.api.Test;
////import org.springframework.beans.factory.annotation.Autowired;
////import org.springframework.boot.test.context.SpringBootTest;
////import org.apache.poi.hssf.usermodel.HSSFWorkbook;
////import org.apache.poi.ss.usermodel.Cell;
////import org.apache.poi.ss.usermodel.Row;
////import org.apache.poi.ss.usermodel.Sheet;
////import org.apache.poi.ss.usermodel.Workbook;
////import org.apache.poi.ss.util.CellRangeAddress;
////import org.apache.poi.xssf.usermodel.XSSFWorkbook;
////import scrbg.meplat.mall.entity.ProductCategory;
////import scrbg.meplat.mall.service.ProductCategoryService;
////
////import java.io.FileInputStream;
////import java.io.IOException;
////import java.io.InputStream;
////import java.util.ArrayList;
////import java.util.Arrays;
////import java.util.HashSet;
////import java.util.List;
////
////
//@Slf4j
//@SpringBootTest
//public class ExcelTest {
//    //
//    @Autowired
//    private ProductCategoryService productCategoryService;
//    @Autowired
//    public MallConfig mallConfig;
//    @Autowired
//    RestTemplateUtils restTemplateUtils;
//
//
//    public void outputExcel() {
//        String src = mallConfig.templateFormUrl;
////        Map<String, Object> innerMap = jsonObject.getInnerMap();
////        String classId =(String) innerMap.get("classId");
////        Integer state = (Integer) innerMap.get("state");
//        String classId = "a927249b2810-a00f-1d43-ef73-78cbd5be";
//        Integer state = 1;
//        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
//        ProductCategory one = productCategoryService.getOne(wrapper.eq(ProductCategory::getClassId, classId).eq(ProductCategory::getState, state));
//        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("className", one.getClassName());
//        hashMap.put("classId", one.getClassName());
//        hashMap.put("state", one.getClassName());
//        hashMap.put("classLassLevel", one.getClassLevel());
//        getChildren(state, hashMap);
//        System.out.println(hashMap);
//
//
//    }
//
//
//    public void aa() {
//        System.out.println(11);
//    }
//
//    private void getChildren(Integer state, HashMap<String, Object> one) {
//        if (Integer.parseInt(one.get("classLassLevel").toString()) < 3) {
//            LambdaQueryWrapper<ProductCategory> child = new LambdaQueryWrapper<>();
//            List<ProductCategory> childs = productCategoryService.list(child.eq(ProductCategory::getParentId, one.get("classId")).eq(ProductCategory::getState, state).eq(ProductCategory::getClassLevel, Integer.parseInt(one.get("classLassLevel").toString()) + 1));
//            ArrayList<HashMap<String, Object>> hashMaps = new ArrayList<>();
//            for (ProductCategory productCategory : childs) {
//                HashMap<String, Object> hashMap = new HashMap<>();
//                hashMap.put("className", productCategory.getClassName());
//                hashMap.put("classId", productCategory.getClassName());
//                hashMap.put("state", productCategory.getClassName());
//                hashMap.put("classLassLevel", productCategory.getClassLevel());
//                hashMaps.add(hashMap);
//            }
//            one.put("children", hashMaps);
//            if (Integer.parseInt(one.get("classLassLevel").toString()) == 3) {
//                StringBuilder stringBuilder = new StringBuilder();
//                HashMap<Object, Object> hashMap = new HashMap<>();
//                hashMap.put("classId", one.get("classId"));
//                hashMap.put("isActive", state);
//                hashMap.put("classId", one.get("classId"));
//                hashMap.put("pageIndex", 1);
//                hashMap.put("pageSize", 600);
//                String url = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.SAVE_SITE_RECEIVING;
//                R<Map<String, Object>> r = restTemplateUtils.postPCWP2(url, hashMap);
//                Map<String, Object> data = r.getData();
//                ArrayList<Map<String, Object>> list = (ArrayList) data.get("list");
//                list.stream().forEach(item -> stringBuilder.append(item.get("materialName")).append('/'));
//                one.put("classPath", stringBuilder);
//            }
//
//        }
//    }
/////**/
////    @Test
////    public void addReportByExcel21() {
//       String className = "安全材料";
//       String twoClassName = "低值易耗品";
//       String tClassName = "安全防护及环保类";
//////
//       //找到安全材料的所有分类
//       ProductCategory one = productCategoryService.getAllChildItem(className);
//////
//////
//       //安全防护及环保类的位置
//       ProductCategory anquan = getAnquan(twoClassName, tClassName,1);
//       List<ProductCategory> childrenList = one.getChildren();
//       //插入三级分类
//       for (ProductCategory productCategory : childrenList) {
//           String classPath = anquan.getClassPath();
//           StringBuffer stringBuffer = new StringBuffer(classPath + "/" + productCategory.getClassName());
//           ProductCategory newChild = new ProductCategory();
//           newChild.setClassPath(stringBuffer.toString());
//           newChild.setParentId(anquan.getClassId());
//           newChild.setClassLevel(3);
//           newChild.setClassName(productCategory.getClassName());
//           newChild.setState(1);
//           newChild.setProductType(0);
//           newChild.setIsHaveProduct(1);
//           productCategoryService.save(newChild);
//           List<ProductCategory> children = productCategory.getChildren();
//////
//           //插入四级级分类
//           if (children != null && children.size() > 0) {
//               for (ProductCategory child : children) {
//                   ProductCategory son = new ProductCategory();
//                   StringBuffer ch = stringBuffer.append("/" + child.getClassName());
//                   son.setClassPath(ch.toString());
//                   son.setParentId(newChild.getClassId());
//                   son.setClassLevel(4);
//                   son.setClassName(child.getClassName());
//                   son.setState(1);
//                   son.setProductType(0);
//                   son.setIsHaveProduct(1);
//                   productCategoryService.save(son);
//               }
//////
//           }
//       }
////
////    }
////
////    @Test
////    public void addReportByExcel22() {
////
//       List<InspectionReport> classList = addReportByExcel();
//       String twoClassName = "低值易耗品";
//////
//       //找到低值易耗平所有分类
//       ProductCategory one = productCategoryService.getAllChildItem(twoClassName);
//////
//       List<ProductCategory> list = one.getChildren();
//////
//       for (ProductCategory productCategory : list) {
//           for (InspectionReport inspectionReport : classList) {
//               //二级分类和读取数据的菜单比较
//               if (productCategory.getClassName().equals(inspectionReport.getClassName())) {
//                   List<ProductCategory> children = productCategory.getChildren();
//                   List<InspectionItem> items = inspectionReport.getItems();
//                   List<String> list1 = new ArrayList<>();
//                   children.stream().forEach(s -> list1.add(s.getClassName()));
//////
//////
//                   for (InspectionItem item : items) {
//                       if (list1.contains(item.getClassName())) {
//                           //找出三级种类和二级excel的  分类
//                           ProductCategory anquan = getAnquan(productCategory.getClassName(), item.getClassName(),2);
//                           String[] split = item.getSonClassName().split("/");
//                           HashSet<String> set = new HashSet<>();
//                           for (String s : split) {
//                               set.add(s);
//                           }
//                           ArrayList<ProductCategory> saveList = new ArrayList<>();
//                           for (String s : set) {
//                               ProductCategory son = new ProductCategory();
//                               son.setClassPath(anquan.getClassPath() + "/" + s);
//                               son.setParentId(anquan.getClassId());
//                               son.setClassLevel(4);
//                               son.setClassName(s);
//                               son.setState(1);
//                               son.setProductType(0);
//                               son.setIsHaveProduct(1);
//                               saveList.add(son);
//                           }
//                           productCategoryService.saveBatch(saveList);
//                       } else {
//                           ProductCategory son = new ProductCategory();
//                           son.setClassPath(productCategory.getClassPath() + "/" + item.getClassName());
//                           son.setParentId(productCategory.getClassId());
//                           son.setClassLevel(3);
//                           son.setClassName(item.getClassName());
//                           son.setState(1);
//                           son.setProductType(0);
//                           son.setIsHaveProduct(1);
//                           productCategoryService.save(son);
//                           String[] split = item.getSonClassName().split("/");
//                           ArrayList<ProductCategory> saveList = new ArrayList<>();
//                           HashSet<String> set1 = new HashSet<>();
//                           for (String s : split) {
//                               set1.add(s);
//                           }
//                           for (String s : set1) {
//                               ProductCategory sson = new ProductCategory();
//                               sson.setClassPath(son.getClassPath() + "/" + s);
//                               sson.setParentId(son.getClassId());
//                               sson.setClassLevel(4);
//                               sson.setClassName(s);
//                               sson.setState(1);
//                               sson.setProductType(0);
//                               sson.setIsHaveProduct(1);
//                               saveList.add(sson);
//                           }
//                           productCategoryService.saveBatch(saveList);
//                       }
//                   }
//////
//               }
//           }
//////
//////
//       }
////
////    }
////
////
////    @Test
////    private void insertProductCate() {
////
////
////    }
////
//   private ProductCategory getAnquan(String twoClassName, String tClassName,Integer leaf) {
//       LambdaQueryWrapper<ProductCategory> w = new LambdaQueryWrapper<>();
//       w.eq(ProductCategory::getClassName, twoClassName);
//       w.eq(ProductCategory::getClassLevel, leaf);
//       ProductCategory two = null;
//       try {
//           two = productCategoryService.getOne(w);
//       } catch (Exception e) {
//           e.printStackTrace();
//       }
//       LambdaQueryWrapper<ProductCategory> wq = new LambdaQueryWrapper<>();
//       wq.eq(ProductCategory::getClassName, tClassName);
//       wq.eq(ProductCategory::getParentId, two.getClassId());
//       ProductCategory anquan = productCategoryService.getOne(wq);
//       return anquan;
//   }
////
////
//   public List<InspectionReport> addReportByExcel() {
//       String message = "Import success";
//       InputStream inputStream = null;
//       try {
//           inputStream = new FileInputStream("D:\\TIB\\采购平台商城物资分类.xls");
//       } catch (IOException e) {
//           e.printStackTrace();
//       }
//////
//       String fileName = "采购平台商城物资分类.xls";
//       boolean isE2007 = false;    //格式判断
//       if (fileName.endsWith("xlsx")) {
//           isE2007 = true;
//       }
//////
//       int rowIndex = 0;
//       int columnIndex = 0;
//       try {
//           InputStream input = inputStream;  //建立输入流
//           Workbook wb = null;
//           if (isE2007) {
//               wb = new XSSFWorkbook(input);
//           } else {
//               wb = new HSSFWorkbook(input);
//           }
//           Sheet sheet = wb.getSheetAt(0);    //获得第一个表单
//////
//           //System.out.println("总行数:"+sheet.getLastRowNum());
//////
//           List<CellRangeAddress> cras = getCombineCell(sheet);
//           int count = sheet.getLastRowNum() + 1;//总行数
//////
//           List<InspectionReport> irs = new ArrayList<>();
//           for (int i = 1; i < count; i++) {
//               rowIndex = i;
//               Row row = sheet.getRow(i);
//               InspectionReport ir = new InspectionReport();
//////
//               ir.setClassName(getCellValue(row.getCell(0)));
//////
//               List<InspectionItem> items = new ArrayList<>();
//               if (isMergedRegion(sheet, i, 0)) {
//                   int lastRow = getRowNum(cras, sheet.getRow(i).getCell(0), sheet);
//////
//                   for (; i <= lastRow; i++) {
//                       row = sheet.getRow(i);
//                       InspectionItem item = new InspectionItem();
//                       item.setClassName(getCellValue(row.getCell(1)));
//                       item.setSonClassName(getCellValue(row.getCell(2)));
//                       items.add(item);
//                   }
//                   i--;
//               } else {
//                   row = sheet.getRow(i);
//                   InspectionItem item = new InspectionItem();
//                   item.setClassName(getCellValue(row.getCell(1)));
//                   item.setSonClassName(getCellValue(row.getCell(2)));
//////
//                   items.add(item);
//               }
//               ir.setItems(items);
//               irs.add(ir);
//////
//           }
//////
//////
//           irs.get(0).setClassName("工具类");
//////
//           return irs;
//////
//       } catch (Exception ex) {
//           ex.printStackTrace();
//           return null;
//       }
//////
//   }
////
////    /**
////     * 获取单元格的值
////     *
////     * @param cell
////     * @return
////     */
////    public String getCellValue(Cell cell) {
//       if (cell == null) return "";
//       if (cell.getCellType() == Cell.CELL_TYPE_STRING) {
//           return cell.getStringCellValue();
//       } else if (cell.getCellType() == Cell.CELL_TYPE_BOOLEAN) {
//           return String.valueOf(cell.getBooleanCellValue());
//       } else if (cell.getCellType() == Cell.CELL_TYPE_FORMULA) {
//           return cell.getCellFormula();
//       } else if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
//           return String.valueOf(cell.getNumericCellValue());
//       }
//       return "";
//   }
////
////    /**
////     * 合并单元格处理,获取合并行
////     *
////     * @param sheet
////     * @return List<CellRangeAddress>
////     */
//   public List<CellRangeAddress> getCombineCell(Sheet sheet) {
//       List<CellRangeAddress> list = new ArrayList<CellRangeAddress>();
//       //获得一个 sheet 中合并单元格的数量
//       int sheetmergerCount = sheet.getNumMergedRegions();
//       //遍历所有的合并单元格
//       for (int i = 0; i < sheetmergerCount; i++) {
//           //获得合并单元格保存进list中
//           CellRangeAddress ca = sheet.getMergedRegion(i);
//           list.add(ca);
//       }
//       return list;
//   }
////
//   private int getRowNum(List<CellRangeAddress> listCombineCell, Cell cell, Sheet sheet) {
//       int xr = 0;
//       int firstC = 0;
//       int lastC = 0;
//       int firstR = 0;
//       int lastR = 0;
//       for (CellRangeAddress ca : listCombineCell) {
//           //获得合并单元格的起始行, 结束行, 起始列, 结束列
//           firstC = ca.getFirstColumn();
//           lastC = ca.getLastColumn();
//           firstR = ca.getFirstRow();
//           lastR = ca.getLastRow();
//           if (cell.getRowIndex() >= firstR && cell.getRowIndex() <= lastR) {
//               if (cell.getColumnIndex() >= firstC && cell.getColumnIndex() <= lastC) {
//                   xr = lastR;
//               }
//           }
//////
//       }
//       return xr;
//////
//   }
////
////    /**
////     * 判断单元格是否为合并单元格，是的话则将单元格的值返回
////     *
////     * @param listCombineCell 存放合并单元格的list
////     * @param cell            需要判断的单元格
////     * @param sheet           sheet
////     * @return
////     */
//   public String isCombineCell(List<CellRangeAddress> listCombineCell, Cell cell, Sheet sheet)
//           throws Exception {
//       int firstC = 0;
//       int lastC = 0;
//       int firstR = 0;
//       int lastR = 0;
//       String cellValue = null;
//       for (CellRangeAddress ca : listCombineCell) {
//           //获得合并单元格的起始行, 结束行, 起始列, 结束列
//           firstC = ca.getFirstColumn();
//           lastC = ca.getLastColumn();
//           firstR = ca.getFirstRow();
//           lastR = ca.getLastRow();
//           if (cell.getRowIndex() >= firstR && cell.getRowIndex() <= lastR) {
//               if (cell.getColumnIndex() >= firstC && cell.getColumnIndex() <= lastC) {
//                   Row fRow = sheet.getRow(firstR);
//                   Cell fCell = fRow.getCell(firstC);
//                   cellValue = getCellValue(fCell);
//                   break;
//               }
//           } else {
//               cellValue = "";
//           }
//       }
//       return cellValue;
//   }
////
////    /**
////     * 获取合并单元格的值
////     *
////     * @param sheet
////     * @param row
////     * @param column
////     * @return
////     */
//   public String getMergedRegionValue(Sheet sheet, int row, int column) {
//       int sheetMergeCount = sheet.getNumMergedRegions();
//////
//       for (int i = 0; i < sheetMergeCount; i++) {
//           CellRangeAddress ca = sheet.getMergedRegion(i);
//           int firstColumn = ca.getFirstColumn();
//           int lastColumn = ca.getLastColumn();
//           int firstRow = ca.getFirstRow();
//           int lastRow = ca.getLastRow();
//////
//           if (row >= firstRow && row <= lastRow) {
//               if (column >= firstColumn && column <= lastColumn) {
//                   Row fRow = sheet.getRow(firstRow);
//                   Cell fCell = fRow.getCell(firstColumn);
//                   return getCellValue(fCell);
//               }
//           }
//       }
//////
//       return null;
//   }
////
////
////    /**
////     * 判断指定的单元格是否是合并单元格
////     *
////     * @param sheet
////     * @param row    行下标
////     * @param column 列下标
////     * @return
////     */
//   private boolean isMergedRegion(Sheet sheet, int row, int column) {
//       int sheetMergeCount = sheet.getNumMergedRegions();
//       for (int i = 0; i < sheetMergeCount; i++) {
//           CellRangeAddress range = sheet.getMergedRegion(i);
//           int firstColumn = range.getFirstColumn();
//           int lastColumn = range.getLastColumn();
//           int firstRow = range.getFirstRow();
//           int lastRow = range.getLastRow();
//           if (row >= firstRow && row <= lastRow) {
//               if (column >= firstColumn && column <= lastColumn) {
//                   return true;
//               }
//           }
//       }
//       return false;
//   }
////
//
//}
