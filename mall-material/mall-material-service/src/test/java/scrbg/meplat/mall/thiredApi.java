//package scrbg.meplat.mall;
//
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
//
//import java.math.BigDecimal;
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//
//@Slf4j
//@SpringBootTest
//public class thiredApi {
//
//    @Test
//    public   void  ListSort() {
//        Date date = new Date();
//        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
//        String dateString = "2023-07-30 00:00:00";
//        Date parse=null;
//        try {
//            parse = df.parse(dateString);
//
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        if (date.getTime()>parse.getTime()){
//            System.out.println(date);
//        }else {
//            System.out.println(parse);
//        }
//    }
//
//    public void export() {
//        try {String src ="c:\\aMySoft";
//            Map<String,Object> dataMap =new HashMap<>() ;
//            dataMap.put("createByName","叶子");
//
//
//
//            ArrayList<Map> maps = new ArrayList<>();
//            HashMap<Object, Object> m1 = new HashMap<>();
//
//            m1.put("remarks","备注信息");
//
//            HashMap<Object, Object> m2 = new HashMap<>();
//            m2.put("remarks","备注信2息");
//            maps .add(m1);
//            maps .add(m2) ;
//            dataMap.put("tist",maps);
//        } catch (Exception e) {
//            e.printStackTrace();}
//    }
//@Test
//    public void text2() {
//        BigDecimal num1 = new BigDecimal("10.5");
//        BigDecimal num2 = new BigDecimal("8.7");
//
//        int result = num1.compareTo(num2);
//        System.out.println(result);
//    }
//
//    }
//
//
//
//
//
//
//
//
//
