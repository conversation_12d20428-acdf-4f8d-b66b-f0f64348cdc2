package scrbg.meplat.mall;/**
 * <AUTHOR>
 * @date 2023/7/27
 */

import com.alibaba.fastjson.JSONObject;
import com.scrbg.common.utils.PageUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.MaterialReconciliationDtlService;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.service.SynthesizeTemporaryDtlService;
import scrbg.meplat.mall.util.HttpClientUtil;
import scrbg.meplat.mall.util.RestTemplateUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: maill_api
 *
 * @description: 测试
 *
 * @author: 代文翰
 *
 * @create: 2023-07-27 15:19
 **/
public class MyProductTest {
    //public static void main(String[] args) {
    //    String paras = "扳手/斜口钳/尖嘴钳/扁嘴钳/电工钳/断线钳/管子钳/链条管子钳/台虎钳/桌虎钳/手虎钳/一字螺钉旋具/十字螺钉旋具/游标卡尺/千分尺/皮卷尺/钢卷尺/钢直尺/美工刀/螺纹塞规/螺纹环规/台秤/天平/氧气表/乙炔表/氮气表/丙烷表/氧气瓶/乙炔瓶/氮气瓶/丙烷瓶/二氧化碳表/二氧化碳气瓶/钳型表/电子秤/锉刀/丝锥/板牙/麻花钻/羊角锤/錾子/钢锹/铁锹/榔头/斧头/二锤/锄头/十字镐/木工锯/锯条/锯片/铅锤/斗车/方铲/钢丝刷/泥掌/钻花/撬棍/八角锤子/风镐/风钎/黄油枪/螺丝刀/玻璃枪/内六角扳手/钉锤/钳子/尖铲/火钳/火铲/双头扳手套件/老虎钳/滚筒/钢钎/广线/滚筒刷/海绵/开口扳手/梅花扳手/钢丝钳/活动扳手/手锤/铁尖铲/铝合金人字梯/千斤顶/轴承/测量工具/手拉葫芦/镰刀/手油泵/钻杆/串筒/卸扣/卡环/强力环/乙炔割咀/锁具/喷火枪/胶枪/吊钩/水平尺/水位尺/导管/渣样锤/工具袋/自动脱落钩/二氧化碳加热减压器/测温线/压力表/切割片/压丝轮/气表/卡头/磨片/接杆/料斗/加油枪";
    //    String[] strings = paras.split("/");
    //    for (String s : strings) {
    //
    //    }
    //}

    public static void main(String[] args) {
//        JSONObject jsonObject = new JSONObject();
//        // 默认没有任何权限
//        jsonObject.put("isTrade",0);
//        jsonObject.put("isLease",0);
//        jsonObject.put("isRepair",0);
//        jsonObject.put("isInsurance",0);
//        jsonObject.put("isFinance",0);
//        System.out.println(JSONObject.toJSONString(jsonObject));
//        JSONObject jsonObject1 = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
//        Map<String, Object> innerMap = jsonObject1.getInnerMap();
//        System.out.println(JSONObject.toJSONString(innerMap));
        JSONObject jsonObject = new JSONObject();
        // 默认没有任何权限
        jsonObject.put("isTrade",0);
        jsonObject.put("isLease",0);
        jsonObject.put("isRepair",0);
        jsonObject.put("isInsurance",0);
        jsonObject.put("isFinance",0);
        jsonObject.put("isMaterialFlow",0);

        System.out.println(JSONObject.toJSONString(jsonObject));
        JSONObject jsonObject1 = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
        Map<String, Object> innerMap = jsonObject1.getInnerMap();
        System.out.println(JSONObject.toJSONString(innerMap));
    }


    @Autowired
    private ProductService productService;
    private static final String BASICS_MATERIAL_LIST = "/thirdapi/matarialpurchase/queryPageMaterialDtl";
    //更新商品的物资基础库信息和订单信息
    @Autowired
    public MallConfig mallConfig;
    @Autowired
    RestTemplateUtils restTemplateUtils;
    @Autowired
    OrderItemService orderItemService;
    @Autowired
    MaterialReconciliationDtlService materialReconciliationDtlService;
    @Autowired
    SynthesizeTemporaryDtlService synthesizeTemporaryDtlService;

    @Test
    public  void changMatailAndProduct() {
//        List<Product> productList = productService.lambdaQuery()
//                .select(Product::getRelevanceId,Product::getRelevanceNo,Product::getClassId)
//                .groupBy(Product::getRelevanceId).list();
        ArrayList<Product> productList = new ArrayList<>();
        Product product1 = new Product();
        product1.setRelevanceId("L.05.23.01.150");
        productList.add(product1);
        String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_LIST;
        for (Product product : productList) {
            HashMap<Object, Object> paramsMap = new HashMap<>();
            paramsMap.put("pageIndex", 0);
            paramsMap.put("pageSize", 10);
            paramsMap.put("materialNo", product.getRelevanceNo());
            PageUtils<Map> r = null;
            try {
                r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
            } catch (Exception e) {
                throw new BusinessException("【远程异常】获取基础库物资错误：" + e.getMessage());
            }
            List<Map> list = r.getList();
            if (CollectionUtils.isEmpty(list)) {
                System.out.println("没有找到物资：" + product.getRelevanceNo());
            }
            for (Map map : list) {
                productService.lambdaUpdate().set(Product::getClassId, map.get("classId"))
                        .set(Product::getClassPath, map.get("classIdPath"))
                        .set(Product::getRelevanceName, map.get("materialName"))
                        .eq(Product::getRelevanceId, map.get("billId")).update();
                synthesizeTemporaryDtlService.lambdaUpdate()
                        .set(SynthesizeTemporaryDtl::getMaterialName, map.get("materialName"))
                        .set(SynthesizeTemporaryDtl::getMaterialSn, map.get("billNo"))
                        .set(SynthesizeTemporaryDtl::getUnit, map.get("unit")!=null? map.get("unit"):null)
                        .set(SynthesizeTemporaryDtl::getSpec, map.get("spec")!=null? map.get("spec"):null)
                        .eq(SynthesizeTemporaryDtl::getMaterialId, map.get("billId")).update();
                orderItemService.lambdaUpdate()
                        .set(OrderItem::getRelevanceName, map.get("materialName"))
                        .set(OrderItem::getClassId, map.get("classId"))
                        .set(OrderItem::getRelevanceNo,  map.get("billNo"))
                        .set(OrderItem::getClassPathId, map.get("classIdPath"))
                        .set(OrderItem::getClassPathName, map.get("classNamePath"))
                        .eq(OrderItem::getRelevanceId, map.get("billId")).update();
                materialReconciliationDtlService.lambdaUpdate().eq(MaterialReconciliationDtl::getMaterialId, map.get("billId"))
                        .set(MaterialReconciliationDtl::getMaterialName, map.get("materialName"))
                        .set(MaterialReconciliationDtl::getMaterialClassId, map.get("classIdPath"))
                        .set(MaterialReconciliationDtl::getMaterialClassName, map.get("classNamePath")).update();
            }
        }


    }


}
