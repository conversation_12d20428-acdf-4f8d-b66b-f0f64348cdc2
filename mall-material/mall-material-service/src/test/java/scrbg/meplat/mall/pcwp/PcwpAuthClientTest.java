package scrbg.meplat.mall.pcwp;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;

import scrbg.meplat.mall.pcwp.auth.PcwpAuthClient;
import scrbg.meplat.mall.pcwp.auth.model.SignUp;
import scrbg.meplat.mall.pcwp.auth.model.SignUp.OrgSignUp;
import scrbg.meplat.mall.pcwp.auth.model.SignUp.UserSignUp;
import scrbg.meplat.mall.pcwp.auth.model.TokenRes;
import scrbg.meplat.mall.util.AESUtil;

/**
 * 测试pcwp 身份认证相关接口
 * 只激活必要的bean，加快运行速度
 */
@SpringBootTest(webEnvironment = WebEnvironment.NONE, classes = PcwpConfig.class)
@ImportAutoConfiguration({
    FeignAutoConfiguration.class,
    HttpMessageConvertersAutoConfiguration.class,
    FeignClientsConfiguration.class
})
public class PcwpAuthClientTest {

    @Autowired
    private PcwpAuthClient pcwpAuthClient;

    private String phone = "18885566501";
    private String password = "111111";

    /**
     * 注册
     * 不能重复测试，测试一次后再次注册会失败
     */
    // @Test
    void testUserOrgSignUp() {
        UserSignUp userSignUp = new UserSignUp();
        // 这里只有phoneNo好像没用
        userSignUp.setUserName(phone);
        userSignUp.setPassword(AESUtil.encrypt(password));
        userSignUp.setPhoneNo(phone);
        
        OrgSignUp orgSignUp = new OrgSignUp();
        orgSignUp.setOrgType(1);
        // 没用随便传的
        orgSignUp.setOrgName(phone);
        
        SignUp signUp = new SignUp();
        signUp.setUser(userSignUp);
        signUp.setOrg(orgSignUp);
        // 固定值
        signUp.setSysCode("msp");

        // 调用服务
        PcwpRes<Boolean> result = pcwpAuthClient.userOrgSignUp(signUp);

        // 断言结果
        assertTrue(result.getData());
    }

    /**
     * 登录
     */
    @Test
    void testUserSignIp() {

        PcwpRes<TokenRes> tRes = pcwpAuthClient.signIn(phone,AESUtil.encrypt(password),"5","msp");
        // 响应正常
        assertEquals(200, (int)tRes.getCode());
        // 登录主要目的是获取token
        assertNotNull(tRes.getData().getToken());
        // 外部用户
        assertEquals(1, (int)tRes.getData().getIsExternal());
        // 用户名是注册时使用的手机号
        assertEquals(phone, tRes.getData().getUserName());
    }
}
