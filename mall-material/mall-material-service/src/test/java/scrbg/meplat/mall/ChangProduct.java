//package scrbg.meplat.mall;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import scrbg.meplat.mall.entity.Product;
//import scrbg.meplat.mall.entity.ProductCategory;
//import scrbg.meplat.mall.service.ProductCategoryService;
//import scrbg.meplat.mall.service.ProductService;
//
//import java.util.*;
//
//@Slf4j
//@SpringBootTest
//public class ChangProduct {
//    @Autowired
//    private ProductCategoryService productCategoryService;
//    @Autowired
//    private ProductService productService;
//
//    @Test
//    public void changCatePostion() {
//
//        String parentClassId = "1598151034847973231";
//        Integer classLeavel = 2;
//        String tweparentName = "";
//        List<String> sonlist = new ArrayList<>();
//        sonlist.add("其它类");
//        sonlist.add("劳保类");
////        sonlist.add("防护制品");
////        sonlist.add("防坠落");
////        sonlist.add("呼吸防护");
////        sonlist.add("身体防护");
//       List<ProductCategory>  oldList =productCategoryService.getchildItemList(parentClassId,sonlist,classLeavel);
//
//       List<ProductCategory>  newlist =productCategoryService.getNowchildItemList(parentClassId,sonlist,classLeavel);
//       if (newlist==null&&newlist.size()==0){
//           return;
//       }
//        ArrayList<Map<String,String>> oldClassName = new ArrayList<>();
//        ArrayList<String> newClassName = new ArrayList<>();
//        ArrayList<Map<String,String>> sameList = new ArrayList<>();
//        ArrayList<Map<String,String>> nowSameList = new ArrayList<>();
//        oldList.stream().forEach(i->
//                {
//                    HashMap<String, String> map = new HashMap<>();
//                    map.put("classId",i.getClassId());
//                    map.put("className",i.getClassName());
//                    oldClassName.add(map);
//                });
//
//        newlist.stream().forEach(i->newClassName.add(i.getClassName()));
//        for (Map<String, String> map : oldClassName) {
//            if (newClassName.contains(map.get("className"))){
//                HashMap<String, String> mapA = new HashMap<>();
//                mapA.put("classId",map.get("classId"));
//                mapA.put("className",map.get("className"));
//
//                String classpathName=productCategoryService.getProductClassPath(map.get("classId"),new StringBuffer(map.get("classId")));
//                mapA.put("classpathName",classpathName);
//                sameList.add(mapA);
//
//            }else {
//
//                //不同的分内，没有关联商品的，删除，关联商品的，保存打印
//
//                List<Product> classId = productService.getDataByClassIdList(map.get("classId"));
//                if (classId!=null&&classId.size()>0){
//                    HashMap<String, String> mapA = new HashMap<>();
//                    mapA.put("classId",map.get("classId"));
//                    mapA.put("className",map.get("className"));
////                String classpathName=productCategoryService.getProductClassPath(map.get("classId"),new StringBuffer(map.get("classId")));
////                mapA.put("classpathName",classpathName);
//                    nowSameList.add(mapA);
//                }else {
//                    productCategoryService.delete(map.get("classId"));
//                }
//
//            }
//        }
////        for (Map<String, String> map : nowSameList) {
////            i++;
////            System.out.println("----种类"+i+"-------种类名称：===="+map.get("className"));
////            List<Product> classId = productService.findAllByClassId(map.get("classId"));
////            if (classId!=null&&classId.size()>0){
////                System.out.println("-------商品：===开始=====");
////                for (Product product : classId) {
////                    System.out.print(product.getProductName()+"====");
////                }
////                System.out.println();
////                System.out.println();
////                System.out.println("--------商品：===结束=====");
////            }
////
////        }
//
////        ArrayList<Map<String,String>> changClass = new ArrayList<>();
//
//        /*
//        找出重复的分类，转移商品，删除旧分类
//         */
//        for (Map<String, String> map : sameList) {
//            for (ProductCategory productCategory : newlist) {
//                if (productCategory.getClassName().equals(map.get("className"))){
//                    /**
//                     * 如果没有重复的分类  启用1
//                     */
//                    productCategoryService.shiftProduct(map.get("classId"),productCategory.getClassId());
//
//
//                    //查看是否有重复分类  数据打印查看
////                    HashMap<String> map2 = new HashMap<>();
////                    map2.put("oldClassId",map.get("classId"));
////                    map2.put("className",map.get("className"));
////                    map2.put("classpathName",map.get("classpathName"));
////                    map2.put("nowClassId",productCategory.getClassId());
////                    map2.put("nowparentId",productCategory.getParentId());
////                    changClass.add(map2);
//                }
//            }
//        }
//
////        for (Map<String, String> map2 : changClass) {
////
////            productCategoryService.shiftProduct(map2.get("oldClassId"),map2.get("nowClassId"));
////        }
//
//        //  查看相同的集合
////        for (Map<String, String> aClass : changClass) {
////            System.out.println(aClass);
////        }
//
////        ArrayList<Product> saveList = new ArrayList<>();
////        for (Map<String, String> aClass : changClass) {
////
////
////            List<Product> products = productService.getDataByClassIdList(aClass.get("oldClassId"));
////            if (products!=null&&products.size()>0){
////                for (Product product : products) {
////                  String pathName=  productCategoryService.getProductClassPath(aClass.get("nowparentId"),
////                            new StringBuffer(aClass.get("nowparentId")));
////                    product.setClassPath(pathName);
////                    product.setClassId(aClass.get("nowClassId"));
////                    saveList.add(product);
////
////                }
////            }
////            productCategoryService.delete(aClass.get("oldClassId"));
////        }
////        productService.updateBatch(saveList);
//
//
//
//
//    }
//
//
//    @Test
//    public void shiftProduct() {
//        String  oldClassId="1673501274668634124";
//        String  nowClassId="1674693574815387650";
//
//        productCategoryService.shiftProduct(oldClassId,nowClassId);
//
////        String  oldClassName="";
////        String  nowClassName="";
////        productCategoryService.shiftProduct(oldClassName,nowClassName);
//    }
//
//
//    @Test
//    public void getPathName() {
//     String a="扳手/斜口钳/尖嘴钳/扁嘴钳/电工钳/断线钳/管子钳/链条管子钳/台虎钳/桌虎钳/手虎钳/一字螺钉旋具/十字螺钉旋具/游标卡尺/千分尺/皮卷尺/钢卷尺/钢直尺/美工刀/螺纹塞规/螺纹环规/台秤/天  平/氧气表/乙炔表/氮气表/丙烷表/氧气瓶/乙炔瓶/氮气瓶/丙烷瓶/二氧化碳表/二氧化碳气瓶/兆欧级电阻表/钳型表/电子秤/锉刀/丝锥/板牙/麻花钻/羊角锤/錾子/钢锹/铁锹/榔头/斧头/二锤/锄头/十字镐/扫把/木工锯/锯条/锯片/铅锤/斗车/方铲/钢丝刷/泥掌/抓钉/钻花/撬棍/八角锤子/风镐/风钎/黄油枪/螺丝刀/玻璃枪";
//        ArrayList<ProductCategory> list = new ArrayList<>();
//        String[] split = a.split("/");
//        for (String s : split) {
//            ProductCategory sson = new ProductCategory();
//            sson.setClassName(s);
//            sson.setClassPath( "低值易耗品/工具类/手动工具/" + s);
//            sson.setParentId("1674093769646936065");
//            sson.setState(1);
//            sson.setProductType(0);
//            sson.setClassLevel(4);
//            list.add(sson);
//        }
//        productCategoryService.saveBatch(list);
//
//    }
//
//    @Test
//    public void addReportByExcel25() {
//        String className = "五金类";
//        String son = "紧固件";
//
//
//        List<ProductCategory> list = productCategoryService.selectThreeCate(className);
//        ProductCategory one = productCategoryService.getDataName(list.get(0).getParentId(), son);
//
//        if (one != null) {
//            ArrayList<Product> productList = new ArrayList<>();
//            ArrayList<Product> productList2 = new ArrayList<>();
//            for (ProductCategory info : list) {
//                ProductCategory sson = new ProductCategory();
//                sson.setClassName(info.getClassName());
//                sson.setClassPath(one.getClassPath() + "/" + info.getClassName());
//                sson.setParentId(one.getClassId());
//                sson.setState(info.getState());
//                sson.setProductType(0);
//                sson.setClassLevel(info.getClassLevel() + 1);
//                //保存新的种类
//                productCategoryService.save(sson);
//
//                //根据种类id查询关联的所有商品
//                List<Product> products = productService.getDataByClassIdList(info.getClassId());
//                if (products != null && products.size() > 0) {
//                    for (Product product : products) {
//                        product.setClassId(sson.getClassId());
//                        product.setClassPath(one.getParentId() + "/" + one.getClassId() + "/" + sson.getClassId());
//
//                        if (productList.size() > 1000) {
//                            productList2.add(product);
//                        } else {
//                            productList.add(product);
//                        }
//
//                    }
//                }
//
//                productCategoryService.delete(info.getClassId());
//
//
//            }
//            productService.updateBatch(productList);
//            productService.updateBatch(productList2);
//        }
//
//        //保存修改种类的数据
//
//
//        System.out.println(list);
//    }
//
//    @Test
//    public void changApp() {
//
//String a="扳手/斜口钳/尖嘴钳/扁嘴钳/电工/断线/管子/链条管子/台虎/桌虎钳/手虎钳/一字螺钉旋具/-字螺钉旋具/游标卡尺/千分尺/皮卷尺/钢卷尺/钢直/美工刀/螺纹塞规/螺纹环规/台秤/天 平/氧气表/亿快表/氮气表/丙烷表/氧气瓶/乙快瓶/氮气瓶/丙烷瓶/二氧化碳表/二氧化碳气瓶/兆欧级电阻表/型表/电子秤" +
//        "/牲刀/丝锥/板牙/麻花钻/羊角锤/整子/钢锹/铁/榔头/斧头/二锤/头/十字镐/扫把/木工锯/锯条/锯片/铅锤/斗车/方铲/" ;
//
//        String[] split = a.split("/");
//        System.out.println(split.length);
//    }
//
//
//
//    @Test
//    public void changApp1() {
//        List<String> sonlist = new ArrayList<>();
//        sonlist.add("手动工具");
//
//        List<String> sonlist2 = new ArrayList<>();
//        sonlist2.add("电动工具");
//        String parentId="1643689489816215554";
//      List<ProductCategory> newList=  productCategoryService.getchildItemList(parentId,sonlist,3);
//        List<ProductCategory> oldList=productCategoryService.getchildItemList(parentId,sonlist2,3);
//        ArrayList<Map<String,String>> oldClassName = new ArrayList<>();
//        ArrayList<String> newClassName = new ArrayList<>();
//        ArrayList<Map<String,String>> sameList = new ArrayList<>();
//        ArrayList<Map<String,String>> nowSameList = new ArrayList<>();
//        oldList.stream().forEach(i->
//        {
//            HashMap<String, String> map = new HashMap<>();
//            map.put("classId",i.getClassId());
//            map.put("className",i.getClassName());
//            oldClassName.add(map);
//        });
//        newList.stream().forEach(i->newClassName.add(i.getClassName()));
//        for (Map<String, String> map : oldClassName) {
//            if (newClassName.contains(map.get("className"))) {
//                HashMap<String, String> mapA = new HashMap<>();
//                mapA.put("classId", map.get("classId"));
//                mapA.put("className", map.get("className"));
//                String classpathName = productCategoryService.getProductClassPath(map.get("classId"), new StringBuffer(map.get("classId")));
//                mapA.put("classpathName", classpathName);
//                sameList.add(mapA);
//            }
//
//        }
//
//        ArrayList<HashMap<String,String>> changClass = new ArrayList<>();
//        for (Map<String, String> map : sameList) {
//            for (ProductCategory productCategory : newList) {
//                if (productCategory.getClassName().equals(map.get("className"))){
//                    /**
//                     * 如果没有重复的分类  启用1
//                     */
//                    productCategoryService.shiftProduct(map.get("classId"),productCategory.getClassId());
//
//
////                    //查看是否有重复分类  数据打印查看
////                    HashMap<String,String> map2 = new HashMap<>();
////                    map2.put("oldClassId",map.get("classId"));
////                    map2.put("className",map.get("className"));
////                    map2.put("classpathName",map.get("classpathName"));
////                    map2.put("nowClassId",productCategory.getClassId());
////                    map2.put("nowparentId",productCategory.getParentId());
////                    changClass.add(map2);
//                }
//            }
//        }
////        for (HashMap<String, String> aClass : changClass) {
////            System.out.println(aClass);
////        }
//    }
//
//}
//
//
