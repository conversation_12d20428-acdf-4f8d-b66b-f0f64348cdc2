package scrbg.meplat.mall.pcwp;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;

import scrbg.meplat.mall.pcwp.auth.PcwpAuthClient;
import scrbg.meplat.mall.pcwp.auth.PcwpOrganizationClient;
import scrbg.meplat.mall.pcwp.auth.model.TokenRes;
import scrbg.meplat.mall.util.AESUtil;

/**
 * 测试pcwp 组织机构相关接口
 * 只激活必要的bean，加快运行速度
 */
@SpringBootTest(webEnvironment = WebEnvironment.NONE, classes = PcwpConfig.class)
@ImportAutoConfiguration({
    FeignAutoConfiguration.class,
    HttpMessageConvertersAutoConfiguration.class,
    FeignClientsConfiguration.class
})
public class PcwpOrganizationClientTest {

    @Autowired
    private PcwpAuthClient pcwpAuthClient;

    @Autowired
    private PcwpOrganizationClient pcwpOrganizationClient;

    private String phone = "18885566501";
    private String password = "111111";

    @Value("${app.pcwp.org-header}")
    private String orgHeader;
    /**
     * 找回密码或重置密码功能
     * 注意这里这样实现基于本系统的设计与一般的系统相比，有两个特殊的地方
     * 1是密码以对称加密的形式存入数据库，原密码可以字面意思上的“找回”
     * 2是pcwp端提供了以手机号码登录的接口，这个接口只需要手机号码就可以登录获取token，然后可以使用这个token进行后续操作
     */
    @Test
    void testUserSignIp() {
        PcwpRes<TokenRes> tRes = pcwpAuthClient.signIn(phone,AESUtil.encrypt(password),"5","msp");
        // 能够正常登录
        assertEquals(200, (int)tRes.getCode());
        assertNotNull(tRes.getData().getToken());
        // 不知道密码的情况下，可以这样登录，与上边的登录效果一样
        // TODO 只有手机号就能登录，有漏洞吗
        tRes = pcwpAuthClient.createExternalToken(phone,"msp");
        assertEquals(200, (int)tRes.getCode());
        assertNotNull(tRes.getData().getToken());

        // 修改密码为222222
        String newPwd = "222222";
        String token = tRes.getData().getToken();
        String userId = tRes.getData().getUserId();

        PcwpRes<Void> res = pcwpOrganizationClient.changeUserPassword(userId,AESUtil.encrypt(password),AESUtil.encrypt(newPwd), orgHeader, "msp", token);
        // 修改成功
        assertEquals(200, (int)res.getCode());

        tRes = pcwpAuthClient.signIn(phone,AESUtil.encrypt(newPwd),"5","msp");
        // 用新密码登录成功
        assertEquals(200, (int)tRes.getCode());
        assertNotNull(tRes.getData().getToken());

        // 再把密码改回来
        res = pcwpOrganizationClient.changeUserPassword(userId,AESUtil.encrypt(newPwd),AESUtil.encrypt(password), orgHeader, "msp", token);
        // 修改成功
        assertEquals(200, (int)res.getCode());

    }
}
