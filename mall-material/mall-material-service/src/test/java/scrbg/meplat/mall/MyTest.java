package scrbg.meplat.mall;

import com.alibaba.fastjson.JSON;
//import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.toolkit.SqlRunner;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import lombok.extern.log4j.Log4j2;
import org.apache.catalina.realm.UserDatabaseRealm;
import org.apache.ibatis.jdbc.SqlBuilder;
import org.checkerframework.checker.units.qual.A;
import org.docx4j.wml.Id;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.controller.MaterialReconciliationController;
import scrbg.meplat.mall.controller.website.userCenter.UserCenterMaterialMonthSupplyPlanController;
import scrbg.meplat.mall.dto.plan.SaveConsumeRecordDTO;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.CodeEnum;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.*;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.service.impl.MaterialMonthSupplyPlanServiceImpl;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.vo.product.material.CategoryClassIdAndClassNameVO;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-07-14 14:19
 */
@Log4j2
@SpringBootTest
public class MyTest {


    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    ProductService productService;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    ShopService shopService;

    @Autowired
    OrdersService ordersService;

    @Autowired
    ProductSkuService productSkuService;
    @Autowired
    ProductCategoryService productCategoryService;

    @Autowired
    ProductCategory1Service productCategory1Service;

    @Autowired
    FileService fileService;

    @Autowired
    SystemParamService systemParamService;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    MallConfig mallConfig;

    @Autowired
    InterfaceLogsService interfaceLogsService;

    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    private RestTemplateUtils restTemplateUtils;

    @Autowired
    ProductMapper productMapper;

    @Autowired
    ProductSkuMapper productSkuMapper;

    @Autowired
    FloorGoodsMapper floorGoodsMapper;

    @Autowired
    FileMapper fileMapper;

    @Autowired
    OrderSelectPlanService orderSelectPlanService;
    @Autowired
    OrderItemService orderItemService;

    @Autowired
    MaterialReconciliationService materialReconciliationService;
    @Autowired
    MaterialReconciliationDtlService materialReconciliationDtlService;

    @Autowired
    DealOrderInfoService dealOrderInfoService;

    @Autowired
    UserService userService;

    @Autowired
    SysMenuService sysMenuService;

    private static String URL2 = "/thirdapi/material/saveConsumeRecord";
    // 回滚反写大宗合同数量接口
    private static String YGURL1 = "/thirdapi/material/rollBackInventory";
    @Autowired
    MaterialMonthSupplyPlanDtlService materialMonthSupplyPlanDtlService;

    @Autowired
    MaterialMonthSupplyPlanService materialMonthSupplyPlanService;
    @Autowired
    FloorGoodsService floorGoodsService;

    @Autowired
    ShopSupplierReleService shopSupplierReleService;



    @Test
    public void shjlfjs() {
        materialReconciliationService.materialReconciliationPushAcceptance("1764942036576358402");
    }


    //切换自营店商品转移
    @Test
    public void changeBusinessOrg() {
//        List<Product> list = productService.lambdaQuery().eq(Product::getShopId, "1645601878095495170").list();
        ArrayList<ProductSku> productSkuList = new ArrayList<>();
        ArrayList<FloorGoods> floorGoodsLists = new ArrayList<>();
        ArrayList<File> files = new ArrayList<>();
//        ArrayList<Product> productList = new ArrayList<>();
//        for (Product product : list) {
//            Product newProduct = new Product();
//            BeanUtils.copyProperties(product, newProduct);
//
//            newProduct.setOutKeyId(product.getProductId());
//            newProduct.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
//            newProduct.setProductId(null);
//            if (product.getSupperBy().equals("1663065245837488130")) {
//                //测试
////                newProduct.setSupperBy("1880074382960799747");
////                //正式
//                newProduct.setSupperBy("1846748312419028995");
//                newProduct.setSupplierName("四川路桥建设集团物资有限责任公司");
//            }
//
//            //正式店铺id 1858777977810120705
//            //测试店铺id 1878734518961074177
//            newProduct.setShopId("1858777977810120705");
////            newProduct.setShopId("1858777977810120705");
//            productService.save(newProduct);
//            productList.add(newProduct);
        List<Product> list = productService.lambdaQuery().isNotNull(Product::getOutKeyId).list();
//
        List<String> collect = list.stream().map(Product::getOutKeyId).collect(Collectors.toList());
        List<File> oldFiles = fileService.lambdaQuery().eq(File::getRelevanceId, collect).eq( File::getRelevanceType, 1).list();
        for (File oldFile : oldFiles) {
            for (Product product : list) {
                if (oldFile.getRelevanceId().equals(product.getOutKeyId())){
                    File newfile = new File();
                    BeanUtils.copyProperties(oldFile, newfile);
                    newfile.setFileId(null);
                    newfile.setRelevanceId(product.getProductId());
                    files.add(newfile);
                }
            }

        }
        fileService.saveBatch(files);



//
//        List<ProductSku> skuList = productSkuService.lambdaQuery().in(ProductSku::getProductId, collect).list();
//        List<String> productIdList = skuList.stream().map(ProductSku::getProductId).collect(Collectors.toList());
//        List<Product> resultList = list.stream()
//                .filter(product -> !productIdList.contains(product.getProductId()))
//                .collect(Collectors.toList());
//
//        System.out.println(resultList.size());
//        List<String> newIds = resultList.stream().map(Product::getOutKeyId).collect(Collectors.toList());
//        List<ProductSku> nullskuList = productSkuService.lambdaQuery().in(ProductSku::getProductId, newIds).list();
//        for (ProductSku sku : nullskuList) {
//            for (Product product : resultList) {
//                if (sku.getProductId().equals(product.getOutKeyId())) {
//                    ProductSku newProductSku = new ProductSku();
//                    BeanUtils.copyProperties(sku, newProductSku);
//                    newProductSku.setProductId(product.getProductId());
//                    newProductSku.setSkuId(null);
//                    productSkuList.add(newProductSku);
//                    break;
//                }
//            }
//
//        }
//        productSkuService.saveBatch(productSkuList);


//        for (ProductSku productSku : skuList) {
//
//            for (Product product : list) {
//                if (productSku.getProductId().equals(product.getOutKeyId())) {
//                    ProductSku newProductSku = new ProductSku();
//                    BeanUtils.copyProperties(productSku, newProductSku);
//                    newProductSku.setProductId(product.getProductId());
//                    newProductSku.setSkuId(null);
//                    productSkuList.add(newProductSku);
//                    break;
//                }
//            }
//        }
//            productSkuService.saveBatch(productSkuList);

//
//
//

//
//        List<FloorGoods> floorGoodsList = floorGoodsService.lambdaQuery().in(FloorGoods::getGoodsId, collect).list();
//        Map<String, List<FloorGoods>> collect1 = floorGoodsList.stream().collect(Collectors.groupingBy(item -> item.getFloorGoodsId()));
//        collect1.forEach((key,value)->{
//            for (FloorGoods floorGoods : value) {
//                for (Product product : list) {
//                    if (floorGoods.getGoodsId().equals(product.getOutKeyId())){
//                        floorGoods.setGoodsId(product.getProductId());
//                        floorGoodsLists.add(floorGoods);
//                        break;
//                    }
//                }
//            }
//        });
//        floorGoodsService.updateBatchById(floorGoodsLists);
    }
//        Map<String, List<FloorGoods>> stringListMap = floorGoodsList.stream().collect(Collectors.groupingBy(item -> item.getFloorGoodsId()));
//        stringListMap.forEach((key, value) -> {
//            Product one = productService.lambdaQuery().eq(Product::getOutKeyId, key).one();
//            for (FloorGoods floorGoods : value) {
//                if (one!=null){
//                    if (one.getProductId()!=null){
//                        floorGoods.setGoodsId(one.getProductId());
//                        floorGoodsLists.add(floorGoods);
//                    }
//                }
//
//
//            }
//        });
//        productSkuService.saveBatch(productSkuList);


    @Test
    public void bachUpdatesdfddas() {
        //7890
        //1从第三方查询所有的分类数据
        //2.从分类数据中获得所有的（主要材料）放在集合newList[] 里面
        //3.从本地数据库中查询所有的（主要材料）分类分类数据，放在oldList[] 里面
        //4.遍历oldList集合中的数据，如果在newList里面，修改商品表中的分类，查询商品表的物资基础库是否存在，如果不存在，记录商品编号，放在集合materList[]集合中，下架商品
        //5 如果不在newList里面，下架商品，清除分类，记录商品编号，下架商品，放在noCateList中

////        String url = "http://***************:6002/thirdapi/matarialpurchase/getAllCategoryLibrary";
        String url = "http://pcwp2.scrbg.com/thirdApi/thirdapi/matarialpurchase/getAllCategoryLibrary";
        R<List<Map>> r = restTemplateUtils.getPCWP2NotParams(url);
        System.out.println(r.getData().size());
        /*
            billId	物资类别id	string
            billNo	类别编号	string
            className	类别名称	string
            isEnable	是否启用(0：停用;1：启用)	integer(int32)
            materialType	物资类型(0：一般材料;1:：周转材料)	integer(int32)
            parentClassId	上级类别id	string
            parentClassName	上级类别名称	string
            unit	单位	string
         */
        ArrayList<ProductCategory> newList = new ArrayList<>();
        List<Map> data = r.getData();
        for (Map datum : data) {

        }
        int i = 0;
        for (Map classMap : r.getData()) {
            Integer isEnable = (Integer) classMap.get("isEnable");
            if (isEnable == 1) {
                String billId = (String) classMap.get("billId");
                Integer count1 = productCategoryService.lambdaQuery().eq(ProductCategory::getClassId, billId).count();
                if (count1 > 0) {
//                    Integer count = productCategoryService.lambdaQuery().eq(ProductCategory::getClassId, billId)
//                            .eq(ProductCategory::getState, isEnable).count();
//                    if(count > 0) {
//
//                    }else {
//                        i ++ ;
////                        productCategoryService.lambdaUpdate().eq(ProductCategory::getClassId,billId)
////                                .set(ProductCategory::getState,isEnable).update();
//                    }
                } else {
                    i++;
                    System.out.println("找不到分类！！！：" + billId);
                }

            }
        }

        System.out.println("总数：" + i);
    }

    /**
     * 检查商城商品分类和基础库不一致问题，
     */
    @Test
    public void batchProduct() {
        List<Product> list = productService.lambdaQuery().eq(Product::getProductType, 1).list();
        System.out.println("商品数量：" + list.size());
        for (Product product : list) {
            String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + "/thirdapi/matarialpurchase/queryPageMaterialDtl";
            HashMap<Object, Object> paramsMap = new HashMap<>();
            paramsMap.put("pageIndex", 0);
            paramsMap.put("pageSize", 10);
            paramsMap.put("materialNo", product.getRelevanceNo());
            PageUtils<Map> r = null;
            try {
                r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
            } catch (Exception e) {
                System.out.println("【远程异常】获取基础库物资错误：" + e.getMessage());
            }
            if (r == null || r.getList() == null) {
                System.out.println("找不到基础库：" + r);
                continue;
            }
            if (CollectionUtils.isEmpty(r.getList())) {
                System.out.println("物资基础库该下不存在该物资，商品编号：" + product.getSerialNum() + "，物资基础库编号：" + product.getRelevanceNo());
                continue;
            }
            Map map = r.getList().get(0);
            String classNamePath = (String) map.get("classNamePath");
            String classIdPath = (String) map.get("classIdPath");
            Integer classQueryCount = productCategoryService.lambdaQuery().eq(ProductCategory::getClassPath, classNamePath).count();
            if (classQueryCount == 0) {
                System.out.println("分类路径：" + classNamePath + "，在商城不存在");
            } else {
                if (!product.getClassPath().equals(classIdPath)) {
                    System.out.println("商品编号：" + product.getSerialNum() + "，商品分类不一致，原分类：" + product.getClassPath() + "，新分类：" + classIdPath + "，新分类名称：" + classNamePath);
                }
            }
        }
    }


    @Autowired
    PlatformDealFeeDtlMapper platformDealFeeDtlMapper;
    @Autowired
    PlatformDealFeeService platformDealFeeService;

    // 处理大宗月供已审核的数据
    @Test
    public void updateSaslf() throws InterruptedException {
        List<SystemParam> list = systemParamService.lambdaQuery().like(SystemParam::getGmtCreate, "2023-01-15").list();
        for (SystemParam systemParam : list) {
            System.out.println(systemParam.getGmtCreate());
        }
//        QueryWrapper<User> queryWrapper = Wrappers.query();
//        queryWrapper.select("id", "name")
//                .eq("age", 18);
//
//        String sql = "SELECT id, name FROM user" + queryWrapper.getSqlSegment();
//
//        List<Map<String, Object>> maps = SqlRunner.db().selectList(sql, SystemParam.class);


//        platformDealFeeService.lambdaUpdate().eq(PlatformDealFee::getPlatformDealFeeId,"1752509365543735298")
//                .set(PlatformDealFee::getArrearageDateTime,null).update();

//        List<MaterialMonthSupplyPlan> list = materialMonthSupplyPlanService
//                .lambdaQuery().eq(MaterialMonthSupplyPlan::getState, 2).list();
//        for (MaterialMonthSupplyPlan byId : list) {
//            Thread.sleep(5000); // 休眠3秒
//            String idStr = IdWorker.getIdStr();
//                List<MaterialMonthSupplyPlanDtl> dtls = materialMonthSupplyPlanDtlService.lambdaQuery()
//                        .eq(MaterialMonthSupplyPlanDtl::getPlanId, byId.getPlanId())
//                        .list();
////                System.out.println(dtls.size());
//                List<Map> maps = new ArrayList<>();
//                SaveConsumeRecordDTO redto = new SaveConsumeRecordDTO();
//                redto.setVersion(byId.getPcwpVersion());
//                redto.setContractId(byId.getContractId());
//                redto.setCredential(idStr);
//                redto.setType("add");
//                for (MaterialMonthSupplyPlanDtl dtl : dtls) {
//                    HashMap<String, Object> map = new HashMap<>();
//                    map.put("listId", dtl.getContractDtlId());
//                    map.put("quantity", dtl.getThisPlanQty());
//                    maps.add(map);
//                }
//                redto.setListInfo(maps);
//                // 发起反写计划请求
//                String content = JSON.toJSONString(redto);
//                // 发送请求
//                String url = mallConfig.prodPcwp2Url02 + URL2;
////                LogUtil.writeInfoLog(idStr, "auditPlan", byId, redto, null, MaterialMonthSupplyPlanServiceImpl.class);
//                R rMap = null;
//                try {
//                    rMap = restTemplateUtils.postPCWP2(url, redto);
//                } catch (Exception e) {
//                    LogUtil.writeErrorLog(idStr, "auditPlan", byId, redto, rMap, e.getMessage(), MaterialMonthSupplyPlanServiceImpl.class);
//                    System.out.println("失败id：" + byId.getPlanId());
//                    continue;
//                }
//                if (rMap.getCode() == null || rMap.getCode() != 200) {
//                    LogUtil.writeErrorLog(idStr, "auditPlan", byId, redto, rMap, rMap.getMessage(), MaterialMonthSupplyPlanServiceImpl.class);
//                    System.out.println("失败id：" + byId.getPlanId());
//                    continue;
//                }
//
//                InterfaceLogs iLog = new InterfaceLogs();
//                iLog.setSecretKey(idStr);
//                iLog.setClassPackage(MaterialMonthSupplyPlanServiceImpl.class.getName());
//                iLog.setMethodName("auditPlan");
//                iLog.setLocalArguments(JSON.toJSONString(byId));
//                iLog.setFarArguments(content);
//                iLog.setResult(JSON.toJSONString(rMap));
//                iLog.setIsSuccess(1);
//                iLog.setLogType(1);
//                iLog.setErrorInfo(null);
//                interfaceLogsService.create(iLog);
//                System.out.println("成功id：" + byId.getPlanId() + "，返回：" +rMap );
//        }
    }


    @Test
    public void updateUser() {

//        BigDecimal sendProductMaxNumByOrderItemId = orderItemService.getSendProductMaxNumByOrderItemId("1736622729945571329");
//        System.out.println(sendProductMaxNumByOrderItemId);


        // 统计利润
//        List<Orders> list = ordersService.lambdaQuery().eq(Orders::getOrderClass, 2)
//                .eq(Orders::getState,10)
//                .list();
//        System.out.println("订单数据：" + list.size());
//        BigDecimal b = new BigDecimal(0);
//        for (Orders orders : list) {
//            for (OrderItem orderItem : orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orders.getOrderId()).list()) {
//                if(orderItem.getCostAmount().compareTo(BigDecimal.ZERO) == 0) {
//                    System.out.println(orderItem);
//                }
//                b = b.add(orderItem.getTotalAmount().subtract(orderItem.getCostAmount()));
//            }
//        }
//        System.out.println(b);

//        List<Orders> list = ordersService.lambdaQuery()
////                .eq(Orders::getOrderClass,2)
//                .in(Orders::getProductType, 10,13)
//                .list();
//        for (Orders orders : list) {
//            BigDecimal costPriceTotal = new BigDecimal(0);
//            List<OrderItem> list1 = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orders.getOrderId()).list();
//            for (OrderItem orderItem : list1) {
//                costPriceTotal = costPriceTotal.add(orderItem.getCostAmount());
//            }
//            BigDecimal profitPriceTotal = orders.getActualAmount().subtract(costPriceTotal);
//
//            System.out.println("成本：" + costPriceTotal);
//            System.out.println("利润：" + profitPriceTotal);
//            ordersService.lambdaUpdate().eq(Orders::getOrderId,orders.getOrderId())
//                    .set(Orders::getCostPriceTotal,costPriceTotal)
//                    .set(Orders::getProfitPriceTotal,profitPriceTotal).update();
//
//        }


    }

    @Test
    public void updateOrderAmount() {
//        List<User> list = userService.lambdaQuery()
//                .eq(User::getIsInternalUser, 0)
////                .eq(User::getInteriorId,"1648518870367715330")
//                .list();
//        for (User user : list) {
//            if(user.getPassword() != null) {
//                String decrypt = AESUtil.decrypt(user.getPassword());
//                if(decrypt == null) {
//                    System.out.println("=======================================手机号：" + user.getAccount());
//                }else {
//                    if(!PasswordUtils.password(decrypt)) {
//                        String lastSixChars = user.getAccount().substring(user.getAccount().length() - 6);
//                        String str = "m@s3A%.#12F-" + lastSixChars;
////                        String url ="http://***************:6002/hr/user/resetUserPassword";
//                        String url ="http://192.168.90.181:6002/hr/user/resetUserPassword";
//                        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
//                        String pass = AESUtil.encrypt(str);
//                        postParameters.add("resetPwd",pass);
//                        postParameters.add("userId", user.getInteriorId());
//                        HttpHeaders headers = new HttpHeaders();
//                        headers.add("Content-Type", "application/x-www-form-urlencoded");
//                        headers.add("token", "**************************.lWswCMpG4b2Cr2Bz1er--a-joeFSmdJDw9rPIBB3Q2o8WvGwjAbhmg");
//                        headers.add("sysCode", "msp");
//                        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(postParameters, headers);
//                        R r = restTemplate.postForObject(url, entity, R.class);
//                        if (r.getCode() == 200) {
//                            System.out.println("手机号密码修改成功：" + user.getAccount() + "，新密码：" + str + "，加密后：" + pass);
//                            userService.lambdaUpdate()
//                                    .eq(User::getUserId,user.getUserId())
//                                    .set(User::getPassword,pass)
//                                    .set(User::getAttrOne,1).update();
//                        }else {
//                            System.out.println(r);
//                            System.out.println("手机号修改密码异常：" + user.getAccount());
//                        }
//                    }
//                }
//            }else {
//                System.out.println("密码为null，手机号：" + user.getAccount());
//            }
//        }


        // 修改密码
//        List<User> list = userService.lambdaQuery()
//                .eq(User::getIsInternalUser, 0)
//                .select(User::getUserId,User::getUserMobile,User::getPassword,User::getAccount)
//                .list();
//        for (User user : list) {
//            String password = user.getPassword();
//            if(password != null) {
//                String decrypt = AESUtil.decrypt(user.getPassword());
//                if(decrypt == null) {
//                    System.out.println("=======================================：" + user.getAccount());
//                }else {
//                    if(!PasswordUtils.password(decrypt)) {
//                        System.out.println(user.getAccount() + "不符合规则："  + decrypt);
//                    }else {
//                    }
//                }
//            }else {
//                System.out.println("手机号为null：" + user.getAccount());
//            }

//        }


        // 获取全部的结算金额
//        List<MaterialReconciliation> list = materialReconciliationService.lambdaQuery().list();
//        BigDecimal b1 = new BigDecimal(0);
//        for (MaterialReconciliation materialReconciliation : list) {
//            b1 = b1.add(materialReconciliation.getSettleAmount());
//        }
//        System.out.println("对账单总结算金额：" + b1);
//
//        List<DealOrderInfo> list1 = dealOrderInfoService.lambdaQuery().list();
//        BigDecimal b2 = new BigDecimal(0);
//        for (DealOrderInfo dealOrderInfo : list1) {
//            b2 = b2.add(dealOrderInfo.getAmount());
//        }
//        System.out.println("总结算金额：" + b2);


        // 修改所有订单的不含税金额
//        List<Orders> list = ordersService.lambdaQuery().select(Orders::getOrderId).list();
//        for (Orders orders : list) {
//            BigDecimal noRateAmount = new BigDecimal(0);
//            List<OrderItem> list1 = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orders.getOrderId()).list();
//            for (OrderItem orderItem : list1) {
//                if(orderItem.getNoRateAmount() == null) {
//                    continue;
//                }
//                noRateAmount = noRateAmount.add(orderItem.getNoRateAmount());
//            }
//            ordersService.lambdaUpdate().eq(Orders::getOrderId,orders.getOrderId())
//                            .set(Orders::getNoRateAmount,noRateAmount).update();
//        }
////

        // 修改对账单金额
//        List<MaterialReconciliation> list1 = materialReconciliationService.lambdaQuery().list();
//        for (MaterialReconciliation m : list1) {
//            BigDecimal noRateAmount = new BigDecimal(0);
//            List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, m.getReconciliationId()).list();
//            for (MaterialReconciliationDtl md : list) {
//                noRateAmount = noRateAmount.add(md.getAcceptanceNoRateAmount());
//            }
//            if(noRateAmount.compareTo(m.getReconciliationNoRateAmount()) != 0) {
//                System.out.println("对账单id：" + m.getReconciliationId() + "，验收单id："
//                        + m.getRelevanceId() + "，原不含税总金额：" + m.getReconciliationNoRateAmount() + "，新不含税总金额：" + noRateAmount);
////                materialReconciliationService.lambdaUpdate().eq(MaterialReconciliation::getReconciliationId,m.getReconciliationId())
////                        .set(MaterialReconciliation::getReconciliationNoRateAmount,noRateAmount).update();
//            }
//
//        }

    }

    @Test
    public void updateProductClass() {
        List<Product> list2 = productService.lambdaQuery().notLike(Product::getClassPath, "%/%/%")
                .like(Product::getClassPath, "%/%")
                .select(Product::getProductId, Product::getRelevanceNo)
                .list();
        for (Product product : list2) {
            String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + "/thirdapi/matarialpurchase/queryPageMaterialDtl";
            HashMap<Object, Object> paramsMap = new HashMap<>();
            paramsMap.put("pageIndex", 0);
            paramsMap.put("pageSize", 10);
            paramsMap.put("materialNo", product.getRelevanceNo());
            PageUtils<Map> r = null;
            try {
                r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
            } catch (Exception e) {
                throw new BusinessException("【远程异常】获取基础库物资错误：" + e.getMessage());
            }
            List<Map> list = r.getList();
            if (CollectionUtils.isEmpty(list)) {
                throw new BusinessException("物资基础库该下不存在该物资！");
            }
            Map map = list.get(0);
            System.out.println(map);
//            Integer classQueryCount = productCategoryService.lambdaQuery().eq(ProductCategory::getClassPath, map.get("classNamePath")).count();
//            if (classQueryCount == 0) {
//                System.out.println(map.get("classNamePath"));
////                throw new BusinessException(500085, "和pcwp分类不统一！");
//            }else {
//                boolean update = productService.lambdaUpdate()
//                        .eq(Product::getProductId, product.getProductId())
//                        .set(Product::getClassPath, map.get("classIdPath"))
//                        .set(Product::getClassId, map.get("classId")).update();
//                System.out.println(update);
//            }
        }
    }


    @Test
    public void deleteOrder() {
//       ordersService.deleteYGOrderByOrderId("1722158816412405761");
//       ordersService.deleteYGOrderByOrderId("1722156733630091265");
//       ordersService.deleteYGOrderByOrderId("1712033502776627201");
        // 不含税单价都对的上

        // pcwp推送验收单推送，重新计算明细不含税金额
//        List<MaterialReconciliation> mr = materialReconciliationService.lambdaQuery()
//                .eq(MaterialReconciliation::getCreateType, 3)
//                .list();
//        for (MaterialReconciliation d : mr) {
//            BigDecimal toAmount = new BigDecimal(0);
//            List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, d.getReconciliationId()).list();
//            for (int i = 0; i < list.size(); i++) {
//                MaterialReconciliationDtl md = list.get(i);
//                // 计算含税金额
//                if (i == list.size() - 1) {
//                    BigDecimal amount = d.getReconciliationAmount().subtract(toAmount);
//                    md.setAcceptanceAmount(amount);
//                    md.setPrice(amount.divide(md.getQuantity(), new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP));
//                } else {
//                    BigDecimal amount = TaxCalculator.calculateYesTarRateAmount(md.getAcceptanceNoRateAmount(), d.getTaxRate());
//                    md.setAcceptanceAmount(amount);
//                    md.setPrice(amount.divide(md.getQuantity(), new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP));
//                    toAmount = toAmount.add(amount);
//                }
//                boolean update = materialReconciliationDtlService
//                        .lambdaUpdate().eq(MaterialReconciliationDtl::getReconciliationDtlId, md.getReconciliationDtlId())
//                        .set(MaterialReconciliationDtl::getAcceptanceAmount, md.getAcceptanceAmount())
//                        .set(MaterialReconciliationDtl::getPrice, md.getPrice()).update();
//                System.out.println(update);
//            }
//        }

    }

    /**
     * 修改对账单
     */
    @Test
    public void updateR() {

        // 不含税单价都对的上
//        List<MaterialReconciliation> list = materialReconciliationService.lambdaQuery()
//                .ne(MaterialReconciliation::getCreateType, 3)
//                .list();
//        for (MaterialReconciliation m : list) {
//            // 查询所有含税单价不等现在的明细
//            List<MaterialReconciliationDtl> list1 = materialReconciliationDtlService.lambdaQuery()
//                    .eq(MaterialReconciliationDtl::getReconciliationId, m.getReconciliationId())
//                    .eq(MaterialReconciliationDtl::getSourceQuantity,new BigDecimal(0))
//                    .list();
//            for (MaterialReconciliationDtl dt : list1) {
//                materialReconciliationDtlService
//                        .lambdaUpdate()
//                        .eq(MaterialReconciliationDtl::getReconciliationDtlId, dt.getReconciliationDtlId())
//                        .set(MaterialReconciliationDtl::getSourceQuantity, dt.getQuantity()).update();
//            }
//        }
//
//            BigDecimal taxRate = m.getTaxRate();
//            BigDecimal reconciliationAmount = m.getReconciliationAmount();
//            BigDecimal reconciliationNoRateAmount = m.getReconciliationNoRateAmount();
//            BigDecimal newP = TaxCalculator.calculateNotTarRateAmount(reconciliationAmount, taxRate);
//            if(newP.compareTo(reconciliationNoRateAmount) != 0) {
//                System.out.println("111111111111111111111111111111111111");
//                System.out.println("对账单id：" + m.getReconciliationId());
//                System.out.println("pcwp验收单id：" + m.getRelevanceId());
//                System.out.println("原不含税总金额：" + reconciliationNoRateAmount + "，新不含税总金额：" + newP);
////                System.out.println("验收单id："+ m.getRelevanceId() +"原不含税总金额：" + reconciliationNoRateAmount + "，新不含税总金额：" + newP);
//            }

//            List<MaterialReconciliationDtl> list1 = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, m.getReconciliationId()).list();
//            for (MaterialReconciliationDtl dt : list1) {
//                BigDecimal acceptanceAmount = dt.getAcceptanceAmount();
//                BigDecimal acceptanceNoRateAmount = dt.getAcceptanceNoRateAmount();
//                BigDecimal newP = TaxCalculator.calculateNotTarRateAmount(acceptanceAmount, taxRate);
//                if (newP.compareTo(acceptanceNoRateAmount) != 0) {
////                    System.out.println("验收单id：】" + m.getRelevanceId()
////                    + "】 物资：【" + dt.getMaterialName() + "】 规格型号：【" + dt.getSpec()
////                     + "】 材质：【" + dt.getTexture() + "】 计量单位：【" + dt.getUnit() + "】。原不含税明细金额：" + acceptanceNoRateAmount + "，新不含税明细金额：" + newP);
////                    System.out.println("111111111111111111111111111111111111");
////                    System.out.println("对账单明细id：" + dt.getReconciliationDtlId());
////                    System.out.println("原不含税总金额：" + acceptanceNoRateAmount + "，新不含税总金额：" + newP);
////                    materialReconciliationDtlService
////                            .lambdaUpdate().eq(MaterialReconciliationDtl::getReconciliationDtlId,dt.getReconciliationDtlId())
////                            .set(MaterialReconciliationDtl::getAcceptanceNoRateAmount,newP).update();
//                }
//            }


//        System.out.println(list.size());
//        for (MaterialReconciliation d : list) {
//            List<MaterialReconciliationDtl> list1 = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, d.getReconciliationId()).list();
//            for (MaterialReconciliationDtl dt : list1) {
//                BigDecimal price = dt.getNoRatePrice();
//                OrderItem one = orderItemService.lambdaQuery()
//                        .eq(OrderItem::getOrderSn, dt.getOrderSn())
//                        .eq(OrderItem::getUnit, dt.getUnit())
//                        .eq(OrderItem::getRelevanceName, dt.getMaterialName())
//                        .like(OrderItem::getSkuName, dt.getSpec())
//                        .eq(OrderItem::getProductName, dt.getTexture())
//                        .one();
//                if (one == null) {
//                    System.out.println(dt);
//                }
//                BigDecimal productPrice = one.getNoRatePrice();
//                if (price.compareTo(productPrice) != 0) {
//                    System.out.println("对账单金额：" + price + "，订单单价：" + productPrice);
//                    System.out.println("对账单id：" + d.getReconciliationId() + " 对账单明细id：" + dt.getReconciliationDtlId());
//                }
//            }

    }

    @Test
    public void getClassPath() {

        BigDecimal bigDecimal = TaxCalculator.calculateNotTarRateAmount(new BigDecimal(22), new BigDecimal(0));
        System.out.println(bigDecimal);
//        List<String> classPath = productCategoryService.getCategoryParentIdList("1683990597898792962");
//        String cp = "";
//        if (!CollectionUtils.isEmpty(classPath)) {
//            for (String s : classPath) {
//                cp += s + "/";
//            }
//        }
//        System.out.println(cp.substring(0, cp.length() - 1));


        // 修改正式订单数据的商品关联编号

//        List<OrderItem> list = orderItemService.lambdaQuery()
//                .select(OrderItem::getProductId,OrderItem::getOrderItemId).list();
//        for (OrderItem orderItem : list) {
//            Product one = productService.lambdaQuery().eq(Product::getProductId, orderItem.getProductId())
//                    .select(Product::getRelevanceName, Product::getRelevanceNo).one();
//            if(one != null) {
//                orderItemService.lambdaUpdate().eq(OrderItem::getOrderItemId,orderItem.getOrderItemId())
//                        .set(OrderItem::getRelevanceName,one.getRelevanceName())
//                        .set(OrderItem::getRelevanceNo,one.getRelevanceNo()).update();
//            }
//        }
    }


    /**
     * 填充orderID
     */
    @Test
    public void test223423() {
//        MaterialReconciliation one = materialReconciliationService.lambdaQuery().eq(MaterialReconciliation::getReconciliationNo, "1698518807059431425").one();
//        materialReconciliationService.pushMaterialReconciliationDataOne(one);

//        boolean update = productService.lambdaUpdate()
//                .eq(Product::getSupperBy, "1664555765421461505")
//                .set(Product::getSupperBy, "1697053639719763969").update();


//        List<OrderSelectPlan> list = orderSelectPlanService.lambdaQuery().select(OrderSelectPlan::getOrderSn,OrderSelectPlan::getOrderSelectPlanId).list();
//        for (OrderSelectPlan orderSelectPlan : list) {
//            String orderSn = orderSelectPlan.getOrderSn();
//            if(orderSn != null) {
//                Orders one = ordersService.lambdaQuery().eq(Orders::getOrderSn, orderSn).select(Orders::getOrderId).one();
//                if(one != null) {
//                    orderSelectPlan.setOrderId(one.getOrderId());
//                    orderSelectPlanService.update(orderSelectPlan);
//                }
//            }
//        }
    }


    /**
     * 将PCWP2分类同步数据库
     */
    @Test
    public void insertClas() {
//        String url = "http://***************:6002/thirdapi/matarialpurchase/getAllCategoryLibrary";
        String url = "http://pcwp2.scrbg.com/thirdApi/thirdapi/matarialpurchase/getAllCategoryLibrary";
        R<List<Map>> r = restTemplateUtils.getPCWP2NotParams(url);
        System.out.println(r.getData().size());
        /*
            billId	物资类别id	string
            billNo	类别编号	string
            className	类别名称	string
            isEnable	是否启用(0：停用;1：启用)	integer(int32)
            materialType	物资类型(0：一般材料;1:：周转材料)	integer(int32)
            parentClassId	上级类别id	string
            parentClassName	上级类别名称	string
            unit	单位	string
         */
        ArrayList<ProductCategory> newList = new ArrayList<>();
        List<Map> dataList = r.getData();
        List<Map> result = dataList.stream()
                .filter(map -> "主要材料".equals(map.get("className")))
                .collect(Collectors.toList());

        for (Map<String, Object> classMap : result) {
            int i = 1;
            ProductCategory productCategory = new ProductCategory();
            productCategory.setClassId((String) classMap.get("billId"));
            productCategory.setClassNo((String) classMap.get("billNo"));
            productCategory.setClassName((String) classMap.get("className"));
            productCategory.setState((Integer) classMap.get("isEnable"));
            productCategory.setProductType(0);
            productCategory.setParentId((String) classMap.get("parentClassId"));
            productCategory.setClassPath((String) classMap.get("className"));
            productCategory.setClassLevel(i);
            newList.add(productCategory);
            treeChild(dataList, productCategory, newList, i);
//            List<Map> twoList = dataList.stream()
//                    .filter(map -> productCategory.getClassId().equals(map.get("parentClassId").toString()))
//                    .collect(Collectors.toList());
//            if (twoList.size()>0){
//                for (Map<String, Object> twoMap : twoList) {
//                    ProductCategory twoProductCategory = new ProductCategory();
//                    twoProductCategory.setClassId((String) twoMap.get("billId"));
//                    twoProductCategory.setClassNo((String) twoMap.get("billNo"));
//                    twoProductCategory.setClassName((String) twoMap.get("className"));
//                    twoProductCategory.setState(Integer.parseInt(twoMap.get("isEnable").toString()));
//                    twoProductCategory.setProductType(0);
//                    twoProductCategory.setParentId((String) twoMap.get("parentClassId"));
//                    twoProductCategory.setClassPath((String) classMap.get("className")+"/"+twoMap.get("className"));
//                    twoProductCategory.setClassLevel(2);
//                    newList.add(twoProductCategory);
//                    List<Map> threeList = dataList.stream()
//                            .filter(map -> twoProductCategory.getClassId().equals(map.get("parentClassId").toString()))
//                            .collect(Collectors.toList());
//                    if (threeList.size()>0){
//                        for (Map<String, Object> threeMap : threeList) {
//                            ProductCategory threeProductCategory = new ProductCategory();
//                            threeProductCategory.setClassId((String) threeMap.get("billId"));
//                            threeProductCategory.setClassNo((String) threeMap.get("billNo"));
//                            threeProductCategory.setClassName((String) threeMap.get("className"));
//                            threeProductCategory.setState(Integer.parseInt(threeMap.get("isEnable").toString()));
//                            threeProductCategory.setProductType(0);
//                            threeProductCategory.setParentId((String) threeMap.get("parentClassId"));
//                            threeProductCategory.setClassPath((String) twoProductCategory.getClassPath()+"/"+threeMap.get("className"));
//                            threeProductCategory.setClassLevel(3);
//                            newList.add(threeProductCategory);
//                        }
//
//                    }
//                }
//            }
        }

        String json = JSONObject.toJSONString(newList);
        System.out.println("主要物资类别有：");
        System.out.println(json);
        List<ProductCategory> oldList = productCategoryService.lambdaQuery().like(ProductCategory::getClassPath, "主要材料").list();
        List<String> list = newList.stream().map(ProductCategory::getClassId).collect(Collectors.toList());
        ArrayList<String> delList = new ArrayList<>();
        ArrayList<String> updateList = new ArrayList<>();
        for (ProductCategory productCategory : oldList) {
            if (!list.contains(productCategory.getClassId())) {
                delList.add(productCategory.getClassId());
            } else {
                updateList.add(productCategory.getClassId());
            }
        }
        productCategoryService.saveOrUpdateBatch(newList);
        ArrayList<HashMap<String, String>> productList = new ArrayList<>();
        if (delList.size() > 0) {
            productCategoryService.removeByIds(delList);
            List<Product> delProduct = productService.lambdaQuery().in(Product::getClassId, delList).list();
            if (!delProduct.isEmpty()) {
                for (Product product : delProduct) {
                    product.setClassId(null);
                    product.setClassPath(null);
                    product.setRelevanceId(null);
                    product.setRelevanceName(null);
                    product.setState(2);
                    product.setFailReason("数据库分类更改，导致商品分类不存在，请重新上架");
                    HashMap<String, String> productMap = new HashMap<>();
                    productMap.put(product.getSerialNum(), product.getFailReason());
                    productList.add(productMap);
                }
                productService.updateBatch(delProduct);
            }

        }
        List<Product> upProductCate = productService.lambdaQuery().in(Product::getClassId, updateList)
                .groupBy(Product::getClassId, Product::getRelevanceNo, Product::getRelevanceId)
                .select(Product::getClassId, Product::getRelevanceNo, Product::getRelevanceId).list();

        if (upProductCate.size() > 0) {
            updateMaterInfo(upProductCate, productList);
        }
        System.out.println("需要修改的商品有");
        for (HashMap<String, String> vo : productList) {
            System.out.println(vo);

        }
//        for (Map classMap : r.getData()) {
//            ProductCategory productCategory = new ProductCategory();
//            productCategory.setClassId((String) classMap.get("billId"));
//            productCategory.setClassNo((String) classMap.get("billNo"));
//            productCategory.setClassName((String) classMap.get("className"));
//            productCategory.setState((Integer) classMap.get("isEnable"));
//            productCategory.setProductType(0);
//            productCategory.setParentId((String) classMap.get("parentClassId"));
//            productCategoryService.save(productCategory);
//        }
    }

    private static void treeChild(List<Map> dataList, ProductCategory threeProductCategory, ArrayList<ProductCategory> newList, Integer level) {
        int n = level + 1;
        List<Map> fourList = dataList.stream()
                .filter(map -> threeProductCategory.getClassId().equals(map.get("parentClassId").toString()))
                .collect(Collectors.toList());
        if (fourList.size() > 0) {
            for (Map<String, Object> fourMap : fourList) {
                ProductCategory fourProductCategory = new ProductCategory();
                fourProductCategory.setClassId((String) fourMap.get("billId"));
                fourProductCategory.setClassNo((String) fourMap.get("billNo"));
                fourProductCategory.setClassName((String) fourMap.get("className"));
                fourProductCategory.setState(Integer.parseInt(fourMap.get("isEnable").toString()));
                fourProductCategory.setProductType(0);
                fourProductCategory.setParentId((String) fourMap.get("parentClassId"));
                fourProductCategory.setClassPath((String) threeProductCategory.getClassPath() + "/" + fourMap.get("className"));
                fourProductCategory.setClassLevel(n);
                newList.add(fourProductCategory);
                treeChild(dataList, fourProductCategory, newList, n);
            }
        }
    }

    private static final String BASICS_MATERIAL_LIST = "/thirdapi/matarialpurchase/queryPageMaterialDtl";

    private void updateMaterInfo(List<Product> upProduct, ArrayList<HashMap<String, String>> productList) {
        ArrayList<Product> products = new ArrayList<>();
        String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_LIST;
        for (Product product : upProduct) {
            HashMap<Object, Object> paramsMap = new HashMap<>();
            paramsMap.put("pageIndex", 0);
            paramsMap.put("pageSize", 10);
            paramsMap.put("materialNo", product.getRelevanceNo());
            paramsMap.put("classId", product.getClassId());
            PageUtils<Map> r = null;
            try {
                r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
            } catch (Exception e) {
                updateMaterial(productList, product);
            }
            List<Map> list = r.getList();
            if (CollectionUtils.isEmpty(list)) {
                updateMaterial(productList, product);
            } else {
                for (Map map : list) {
                    List<Product> updateList = productService.lambdaQuery().eq(Product::getClassId, product.getClassId())
                            .eq(Product::getRelevanceNo, product.getRelevanceNo()).list();
                    for (Product info : updateList) {
                        info.setRelevanceName(map.get("materialName").toString());
                        info.setRelevanceId(map.get("billId").toString());
                        products.add(info);
                    }
                }
            }
        }
        if (products.size() > 0) {
            productService.updateBatch(products);
        }
    }

    private void updateMaterial(ArrayList<HashMap<String, String>> productList, Product product) {
        List<Product> updateList = productService.lambdaQuery().eq(Product::getClassId, product.getClassId())
                .eq(Product::getRelevanceNo, product.getRelevanceNo()).list();
        if (updateList.size() > 0) {
            for (Product info : updateList) {
                info.setRelevanceId(null);
                info.setRelevanceName(null);
                info.setRelevanceNo(null);
                info.setState(2);
                info.setFailReason("数据基础库不存在，请重新上架");
                HashMap<String, String> productMap = new HashMap<>();
                productMap.put(info.getSerialNum(), info.getFailReason());
                productList.add(productMap);
            }
        }
    }


    /**
     * 修改class路径
     */
    @Test
    public void updateClassPath() {
        // 验证路径是否正确
//        List<ProductCategory> list = productCategoryService.lambdaQuery()
//                .select(ProductCategory::getClassId,ProductCategory::getClassPath)
//                .list();
//        for (ProductCategory productCategory : list) {
//            List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(productCategory.getClassId());
//            String classPath = "";
//            if(!CollectionUtils.isEmpty(categoryParentPath)) {
//                for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
//                    if(!StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
//                        classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
//                    }
//                }
//            }
//            // 修改classPath
//            productCategoryService.lambdaUpdate().eq(ProductCategory::getClassId,productCategory.getClassId())
//                    .set(ProductCategory::getClassPath,classPath.substring(0,classPath.length() -1)).update();
//        }
    }

    /**
     * 修改层级
     */
    @Test
    public void updateClassLevel() {
        // 获取所有的classId
//        List<ProductCategory> list = productCategoryService.lambdaQuery().select(ProductCategory::getClassPath,ProductCategory::getClassId).list();
//
//        for (ProductCategory productCategory : list) {
//            String classPath = productCategory.getClassPath();
//            String[] split = classPath.split("/");
//            // 修改classPath
//            productCategoryService.lambdaUpdate().eq(ProductCategory::getClassId,productCategory.getClassId())
//                    .set(ProductCategory::getClassLevel,split.length).update();
//        }
    }


    /**
     * 将商品所有下架，设置分类为空！
     */
    @Test
    public void updateProductState() {
        /**
         //         * 下架所有商品
         //         */
//        productService.lambdaUpdate()
//                .set(Product::getState, 0)
//                .set(Product::getFailReason, null)
//                .update();
//        /**
//         * 让商品分类都为空
//         */
//        boolean update = productService.lambdaUpdate()
//                .set(Product::getClassId, null)
//                .set(Product::getClassPath, null).update();
//        System.out.println(update);
//

        // 启用全部品牌
//        boolean update1 = productCategoryService.lambdaUpdate()
//                .set(ProductCategory::getState, 1).update();
//        System.out.println(update1);
    }


    /**
     * 定制删除分类
     */
    @Test
    public void deleteProductClass2() {
//        List<ProductCategory> productCategoryList = productCategoryService.lambdaQuery()
//                .eq(ProductCategory::getParentId,"1683780842626412546")
//                .list();
//        for (ProductCategory productCategory : productCategoryList) {
//            productCategoryService.delete(productCategory.getClassId());
//        }
    }

    /**
     * 删除指定一级所有分类
     */
    @Test
    public void deleteProductClass() {
//        List<ProductCategory> list1 = productCategoryService.lambdaQuery()
//                .eq(ProductCategory::getClassName, "低值易耗品")
////                .in(ProductCategory::getClassName, "主要材料", "其它材料", "周转材料", "机械配件", "库存商品")
//                .eq(ProductCategory::getClassLevel, 1)
//                .list();
//
//        for (ProductCategory productCategory : list1) {
////            productCategoryService.delete(productCategory.getClassId());
//////            if(1== 1) {
//////                continue;
//////            }
//            String classId = productCategory.getClassId();
//            List<ProductCategory> list = productCategoryService.lambdaQuery().eq(ProductCategory::getParentId, classId).list();
//            for (ProductCategory category : list) {
//                productCategoryService.delete(category.getClassId());
//                List<ProductCategory> list2 = productCategoryService.lambdaQuery().eq(ProductCategory::getParentId, category.getClassId()).list();
//                if(!CollectionUtils.isEmpty(list2)) {
//                    for (ProductCategory productCategory1 : list2) {
//                        productCategoryService.delete(productCategory1.getClassId());
//                        List<ProductCategory> list3 = productCategoryService.lambdaQuery().eq(ProductCategory::getParentId, productCategory1.getClassId()).list();
//                        if(!CollectionUtils.isEmpty(list3)) {
//                            for (ProductCategory productCategory2 : list3) {
//                                System.out.println("删除3");
//                                productCategoryService.delete(productCategory2.getClassId());
//                                List<ProductCategory> list4 = productCategoryService.lambdaQuery().eq(ProductCategory::getParentId, productCategory1.getClassId()).list();
//                                if(!CollectionUtils.isEmpty(list4)) {
//                                    for (ProductCategory productCategory3 : list4) {
//                                        System.out.println("删除4");
//                                        productCategoryService.delete(productCategory3.getClassId());
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }
    }

    /**
     * 删除指定机构的所有商品
     */
    @Test
    public void deleteProduct() {
//        QueryWrapper<Product> p = new QueryWrapper<>();
//        p.eq("supplier_name","四川鑫方盛电子商务有限公司");
//        p.select("product_id");
//        List<Product> products = productMapper.selectList(p);
//        List<String> ids = products.stream( ).map(t -> t.getProductId()).collect(Collectors.toList());
//
////        List<String> ids = productMapper.getDeleteProductIds();
//        System.out.println(ids.size());
//        productMapper.removeRealByIds(ids);
//        productSkuMapper.removeRealByProductIds(ids);
//        floorGoodsMapper.removeRealByProductIdIds(ids);
//        fileMapper.removeRealByProductIdIds(1,ids);


    }


    /**
     * 获取已经逻辑删除的id
     */
    @Test
    public void getYesDeleteIds() {
//        List<String> ids = productMapper.getDeleteProductIds();
//        System.out.println(ids);
//        productMapper.removeRealByIds(ids);
//        productSkuMapper.removeRealByProductIds(ids);
//        floorGoodsMapper.removeRealByProductIdIds(ids);
//        fileMapper.removeRealByProductIdIds(1,ids);


//        List<String> deleteIds = productSkuMapper.getDeleteIds();
//        System.out.println(deleteIds);

//        List<String> deleteIds = fileMapper.getDeleteIds();
//        System.out.println(deleteIds);
//        System.out.println(deleteIds.size());

    }


    /**
     * 上架全部商品
     */
    @Test
    public void putProduct() {
//        productService.lambdaUpdate()
//                .set(Product::getState, 1)
//                .set(Product::getFailReason, null)
//                .set(Product::getPutawayDate, new Date())
//                .set(Product::getGmtModified, new Date())
//                .update();
    }

    @Test
    public void TestProduct() {
//        // 文档地址：http://***************:6002/doc.html#/%E7%AC%AC%E4%B8%89%E6%96%B9%E6%8E%A5%E5%8F%A3/%E5%90%88%E5%90%8C%E6%8E%A5%E5%8F%A3/equipmentLeaseZcToContractUsingPOST
//        String basicsMaterialUrl = "http://***************:6002/thirdapi/matarialpurchase/queryPageMaterialDtl";
//        HashMap<Object, Object> paramsMap = new HashMap<>();
//        paramsMap.put("pageIndex",0);
//        paramsMap.put("pageSize",10);
//        paramsMap.put("isActive",true);
//        paramsMap.put("classId","a927249b2810-a00f-1d43-ef73-78cbd5be");
//        String content = JSON.toJSONString(paramsMap);
//        HttpHeaders headers = new HttpHeaders();
//        headers.add("Content-Type", "application/json");
//        headers.add("token", "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA");
//        HttpEntity<String> request = new HttpEntity<>(content, headers);
//        R r = restTemplate.postForObject(basicsMaterialUrl, request, R.class);
//        System.out.println(r);
    }


    /**
     * 填充机构数据
     */
    @Test
    public void updateOrderEnterpriseName() {
//        List<Orders> list = ordersService.lambdaQuery().select(Orders::getOrderId, Orders::getEnterpriseId).list();
//        for (Orders orders : list) {
//            String enterpriseId = orders.getEnterpriseId();
//            if(enterpriseId != null) {
//                EnterpriseInfo byId = enterpriseInfoService.getById(enterpriseId);
//                if(byId != null) {
//                    orders.setEnterpriseName(byId.getEnterpriseName());
//                    ordersService.update(orders);
//                }
//            }
//        }
    }


    /**
     * 商品填充供应商名称
     */
    @Test
    public void sjldflajfas() {
//        List<Product> list = productService.lambdaQuery()
//                .in(Product::getProductId, "1668459544637870082"
//                        , "1668460774168727554"
//                        , "1668464372235636737"
//                        , "1668469040621817857"
//                        , "1668486929458401281"
//                        , "1668507501663883266"
//                        , "1668512835375730689"
//                ).list();
//        for (Product product : list) {
//            String shopId = product.getShopId();
//            Shop byId = shopService.getById(shopId);
//            if(byId == null) continue;
//            EnterpriseInfo byId1 = enterpriseInfoService.getById(byId.getEnterpriseId());
//            if(byId1 == null) continue;;
//            boolean update = productService.lambdaUpdate().eq(Product::getProductId, product.getProductId())
//                    .set(Product::getSupperBy, byId1.getEnterpriseId())
//                    .set(Product::getSupplierName, byId1.getEnterpriseName()).update();
//            System.out.println(update);
//        }
    }

    @Test
    public void addSystemParm() {
        SystemParam s = new SystemParam();
        s.setName("大宗临购最大金额");
        s.setCode(PublicEnum.SYNTHESIZE_TEMPORARY_AMOUNT_MAX.getRemark());
        s.setKeyValue("100000");
        systemParamService.save(s);
    }
}

