package scrbg.meplat.mall.util;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.springframework.beans.factory.annotation.Autowired;

import java.security.SecureRandom;
import java.util.HashSet;
import java.util.Set;

public class CodeGenerator {
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghyjklmnopqrstuvwxyz0123456789";
    private static final int CODE_LENGTH = 25;

    private static String generateCode() {
//        StringBuilder code = new StringBuilder();
//        SecureRandom random = new SecureRandom();
//
//        while (code.length() < CODE_LENGTH) {
//            int randomIndex = random.nextInt(CHARACTERS.length());
//            char randomChar = CHARACTERS.charAt(randomIndex);
//            code.append(randomChar);
//        }
//
//        return code.toString();
        // 用时间id
        return IdWorker.getIdStr();
    }

    public static String generateUniqueCode() {
//        Set<String> generatedCodes = new HashSet<>();
//        String code = generateCode();
//
//        while (generatedCodes.contains(code)) {
//            code = generateCode();
//        }
//
//        generatedCodes.add(code);
//        return code;
        return IdWorker.getIdStr();
    }


    //竞价生成编号
    public static String generateBidCode(Integer type) {

        switch (type) {
            case 10:
                return "DSMYLXKJJJ" + DateUtil.getLastTwoYear();
            case 12:
                return "DSMYDZKJJJ" + DateUtil.getLastTwoYear();
            case 13:
                return "DSMYDZKJJJ" + DateUtil.getLastTwoYear();
            default:
                return IdWorker.getIdStr();
        }
//        Set<String> generatedCodes = new HashSet<>();
//        String code = generateCode();
//
//        while (generatedCodes.contains(code)) {
//            code = generateCode();
//        }
//
//        generatedCodes.add(code);
//        return code;
    }

    public static String generateBidCodeNum(String no, int count) {
        String number = "";
        number = String.format("%03d",count+1);
        return no + number;

        //String number = "";
        //if (count == 0) {
        //    number = "001";
        //} else if (count > 0 && count < 9) {
        //    ++count;
        //    number = "00" + count;
        //} else if (count > 9 && count < 99) {
        //    ++count;
        //    number = "0" + count;
        //} else if (count > 99) {
        //    ++count;
        //    number = "" + count;
        //}
        //return no + number;

    }
}
