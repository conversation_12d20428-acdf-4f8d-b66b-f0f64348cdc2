package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.ProductComment;
import scrbg.meplat.mall.vo.user.userCenter.UserOrderPageListVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @描述：商品评价 Mapper 接口
 * @作者: y
 * @日期: 2022-11-02
 */
@Mapper
@Repository
public interface ProductCommentMapper extends BaseMapper<ProductComment> {

    /**
     * 删除评价
     * @param orderItemId
     */
    @Delete("delete from product_comment where order_id = #{orderId}")
    void deleteCommentById(String orderId);

    /**
     * 通过店铺id查询订单评价
     * @param shopId
     */
    @Select("SELECT a.comment_level,a.comment_supply,a.comment_integrity,a.comment_service FROM product_comment a LEFT JOIN orders b ON a.order_id = b.order_id WHERE a.is_delete='0' and a.product_id is null and b.shop_id IN (#{shopId}) and a.gmt_create>=#{startDate} and a.gmt_create<=#{endDate}")
    List<ProductComment> getProductCommentByShopId(@Param("shopId") String shopId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 供应商查询商品评价
     *
     * @param pages
     * @param dto
     * @return
     */
    List<ProductComment> commentManageList(Page<ProductComment> pages, @Param("dto") Map<String, Object> dto);

    /**
     * 供应商查询商品评价总数
     * @param dto
     * @return
     */
    int commentManageListCount(@Param("dto") Map<String, Object> dto);
}
