package scrbg.meplat.mall.vo.reconciliation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @描述：计划对账列表VO
 * @作者: tanfei
 * @日期: 2025-06-19
 */
@ApiModel(value = "计划对账列表VO")
@Data
public class PlanReconciliationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计划编号")
    private String billNo;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value= "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "收货单位名称")
    private String orgName;

    @ApiModelProperty(value = "PCWP计划编号")
    private String pBillNo;

    @ApiModelProperty(value = "PCWP计划ID")
    private String pBillId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "收货单位组织机构id")
    private String orgId;

    @ApiModelProperty(value = "总退货数量")
    private BigDecimal totalReturnCount;

    @ApiModelProperty(value = "待处理退货数量")
    private BigDecimal pendingReturnCount;

    @ApiModelProperty(value = "已批准退货数量")
    private BigDecimal approvedReturnCount;

    @ApiModelProperty(value = "创建日期")
    private Date gmtCreate;
}
