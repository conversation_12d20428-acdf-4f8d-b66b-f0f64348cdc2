package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：商城角色表
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "商城角色表")
@Data
@TableName("role")
public class Role extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "类型id")

    private String roleId;

    @ApiModelProperty(value = "用于查询")

    private String roleKey;

    @ApiModelProperty(value = "角色名称")

    private String roleName;

    @ApiModelProperty(value = "是否可选   1：可选 0：不可选")

    private Integer isSelective;


    @ApiModelProperty(value = "状态")

    private Integer state;


}
