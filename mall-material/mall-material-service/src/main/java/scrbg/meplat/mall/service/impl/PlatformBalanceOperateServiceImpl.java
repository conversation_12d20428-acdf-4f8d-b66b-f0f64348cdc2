package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.PlatformBalanceOperate;
import scrbg.meplat.mall.mapper.PlatformBalanceOperateMapper;
import scrbg.meplat.mall.service.PlatformBalanceOperateService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：平台交易余额操作记录 服务类
 * @作者: ye
 * @日期: 2024-01-31
 */
@Service
public class PlatformBalanceOperateServiceImpl extends ServiceImpl<PlatformBalanceOperateMapper, PlatformBalanceOperate> implements PlatformBalanceOperateService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformBalanceOperate> queryWrapper) {
        IPage<PlatformBalanceOperate> page = this.page(
        new Query<PlatformBalanceOperate>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(PlatformBalanceOperate platformBalanceOperate) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(platformBalanceOperate);
    }

    @Override
    public void update(PlatformBalanceOperate platformBalanceOperate) {
        super.updateById(platformBalanceOperate);
    }


    @Override
    public PlatformBalanceOperate getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
