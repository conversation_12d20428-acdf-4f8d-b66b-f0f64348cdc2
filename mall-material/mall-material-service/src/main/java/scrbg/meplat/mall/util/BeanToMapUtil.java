package scrbg.meplat.mall.util;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.util.HashMap;
import java.util.Map;

public class BeanToMapUtil {

    public static Map<String, Object> convertToMap(Object bean) {
        Map<String, Object> map = new HashMap<>();
        if (bean == null) {
            return map;
        }

        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(bean.getClass(), Object.class); // 忽略 Object.class 的属性
            for (PropertyDescriptor descriptor : beanInfo.getPropertyDescriptors()) {
                String name = descriptor.getName();
                Object value = descriptor.getReadMethod().invoke(bean); // 调用 getter
                map.put(name, value);
            }
        } catch (Exception e) {
            throw new RuntimeException("转换 bean 到 map 失败", e);
        }

        return map;
    }
}


