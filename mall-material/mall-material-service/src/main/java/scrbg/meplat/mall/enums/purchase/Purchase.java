package scrbg.meplat.mall.enums.purchase;

/**
零星采购计划错误码
 */
public enum Purchase {

    SHIPCOUNTSMAX_1001(1001,"发货数量大于购买数量"),
    VALIDATE_FAILED(1002, "参数检验失败!"),


    /**
     * 短信校验错误码
     */
    RESULT_CODE_500200(1003,""),
    RESULT_SAVE_ERROR(1004, "保存失败！"),

    MATERIALS(1005,"物资商城"),
    DEVICE(1006,"设备商城"),
    ;


    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    Purchase(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    Purchase() {
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


}

