package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.InvoiceRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.InvoiceRecord;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.vo.invoice.ChangInvoiceStateVo;

import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-11-16
 */
public interface InvoiceRecordService extends IService<InvoiceRecord> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<InvoiceRecord> queryWrapper);

        void create(InvoiceRecord invoiceRecord);
        void update(InvoiceRecord invoiceRecord);
        InvoiceRecord getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

        void updateState(ChangInvoiceStateVo changInvoiceStateVo);

        InvoiceRecord getData();

        InvoiceRecord getInvoiceRecord(String id);

}
