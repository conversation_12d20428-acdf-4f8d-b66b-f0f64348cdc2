package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：合同
 * @作者: ye
 * @日期: 2025-03-04
 */
@ApiModel(value="合同")
@Data
@TableName("contract")
public class Contract extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "合同编号")

    private String contractNo;


    @ApiModelProperty(value = "合同名称")

    private String name;


    @ApiModelProperty(value = "合同类型（1入驻合同）")

    private Integer type;


    @ApiModelProperty(value = "甲方机构id")

    private String partyAOrgId;


    @ApiModelProperty(value = "甲方机构名称")

    private String partyAOrgName;


    @ApiModelProperty(value = "乙方机构id")

    private String partyBOrgId;


    @ApiModelProperty(value = "乙方机构名称")

    private String partyBOrgName;
















    @ApiModelProperty(value = "状态")

    private Integer state;




    @ApiModelProperty(value = "合同JSON完整数据")

    private String contractJson;


    @ApiModelProperty(value = "合同签订年份")

    private Integer contractYear;


    @ApiModelProperty(value = "当年合同签订序号")

    private Integer contractYearIndex;



}