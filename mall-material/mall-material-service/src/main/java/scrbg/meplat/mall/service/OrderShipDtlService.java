package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.dto.order.OrderShipmentsQtyIsOkDTO;
import scrbg.meplat.mall.dto.ship.ReconcilableMaterialDTO;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.OrderShipDtl;
import scrbg.meplat.mall.vo.ship.MaterialShipDtlVo;
import scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-05-22
 */
public interface OrderShipDtlService extends IService<OrderShipDtl> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderShipDtl> queryWrapper);

        void create(OrderShipDtl orderShipDtl);

        void update(OrderShipDtl orderShipDtl);

        OrderShipDtl getById(String id);

        void delete(String id);

        PageUtils purchaseShipDtl(JSONObject jsonObject, LambdaQueryWrapper<OrderShipDtl> orderShipDtlLambdaQueryWrapper);

        void updateShipCountsByDtlIds(List<OrderShipDtl> dtls);

        /**
         * 根据发货单查询所有的发货单项
         * @param billId
         * @return
         */
        List<OrderShipDtl> getDataByOrderShipId(String billId);

    List<OrderShipDtl> getListByOrderId(String orderId);

        /**
         * 查询物资交易量信息
         * @param jsonObject
         * @return
         */
    PageUtils listByAffirmList(JSONObject jsonObject);

    /**
     * 导出数据平台
     * @param jsonObject
     * @param response
     */
    void platformOutputExcel(JSONObject jsonObject, HttpServletResponse response);

    /**
     * 查询物资交易量信息（供应商）
     * @param jsonObject
     * @return
     */
    PageUtils shopListByAffirmList(JSONObject jsonObject);

    /**
     * 导出数据店铺
     * @param jsonObject
     * @param response
     */
    void shopManageOutputExcel(JSONObject jsonObject, HttpServletResponse response);

    /**
     * 根据发货单Id导出发货单项数据
     * @param id
     */
    void exportDataByBillId(String id);

    List<OrderShipDtl> getTwoOrderShipByBillid(String id);

    OrderShipDtl getDtlListByShipIdAndProductId(String billId, String productIdList);

    /**
     * 根据收货单项id数据查询收货单项信息（按照确认收货时间经行降序）
     * @param ids
     */
    List<OrderShipDtl> getListByIds(String[] ids);



    /**
     * 查询发货单可对账数据
     * @param sourceDtlId
     * @param i  0可对账  1不可对账
     * @return  IsReconciliation
     */
    OrderShipDtl getDataByIdAndIsReconciliation(String sourceDtlId, int i);

    /**
     * 零星采购发货单项根据发货数量后确认发货数量生成 金额 和单价
     * @param orderItem   订单项
     * @param dtl    发货单
     * @param count  发货数量后确认发货数量
     */
    void updateAmount(OrderItem orderItem, OrderShipDtl dtl, BigDecimal count);

    /**
     *
     * @param jsonObject
     *
     * @param wrapper
     * @return
     */
    PageUtils getShopManageMaterial(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> wrapper);

    /**
     * 查询一级大宗临购可对账物资列表
     * @param dto 查询参数
     * @return 分页结果
     */
    PageUtils getReconcilableMaterialList(ReconcilableMaterialDTO dto);


    /**
     * 根据订单项和发货单状态获取发货单项的数量和已收货数量
     * @param orderItemId
     * @param type
     * @return
     */
    BigDecimal getCountByOrderItemIdAndType(String orderItemId, int type);


    /**
     * 发货/收货总量：如果没有收货数量：发货量减去PCWP退货量（如果有收货数量：收货数量减去PCWP退货量）
     * @return
     */

    BigDecimal getShipCounts(List<String> orderItemIds);

    /**
     *
     * @param orderItemId  订单项

     * @return
     */
    BigDecimal getDataByOrderItmIdAndType(String orderItemId);


    List<MaterialShipDtlVo> getMaterialShipDtlsByOrderShipId(String id);


    /**
     * 统计有效数量
     * @param orderItemIds
     * @return
     */
    BigDecimal getShipCounts2(List<String> orderItemIds);

    /**
     * 统计数量，只是本身
     * @param orderItemIds
     * @return
     */
    BigDecimal getShipCountsIsThis(String orderItemIds, BigDecimal thisQty);

    List<MaterialShipDtlVo> getMaterialShipDzDtlsByOrderShipId(String id);

    PageUtils wxqueryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderShipDtl> orderShipDtlLambdaQueryWrapper);

    /**
     * 更新发货数量
     * @param ids 发货单项id
     */
    void updateShipCounts(List<String> ids);
}
