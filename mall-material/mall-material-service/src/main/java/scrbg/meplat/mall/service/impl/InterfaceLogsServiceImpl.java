package scrbg.meplat.mall.service.impl;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.mapper.InterfaceLogsMapper;
import scrbg.meplat.mall.service.InterfaceLogsService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.util.Date;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-07-07
 */
@Service
public class InterfaceLogsServiceImpl extends ServiceImpl<InterfaceLogsMapper, InterfaceLogs> implements InterfaceLogsService{
    private InterfaceLogs interfaceLogs;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<InterfaceLogs> queryWrapper) {
        IPage<InterfaceLogs> page = this.page(
                new Query<InterfaceLogs>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void create(InterfaceLogs interfaceLogs) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if(user != null) {
            interfaceLogs.setFounderId(user.getUserId());
            interfaceLogs.setFounderName(user.getUserName());
        }
        interfaceLogs.setGmtCreate(new Date());
        super.save(interfaceLogs);
    }

    @Override
    public void update(InterfaceLogs interfaceLogs) {
        super.updateById(interfaceLogs);
    }


    @Override
    public InterfaceLogs getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }


    @Override
    public void createSuccessLog(InterfaceLogs interfaceLogs) {
        interfaceLogs.setIsSuccess(1);
        interfaceLogs.setErrorInfo(null);
        interfaceLogs.setLogType(1);
        create(interfaceLogs);
    }

    @Override
    public void createFailLog(InterfaceLogs interfaceLogs) {
        interfaceLogs.setIsSuccess(0);
        interfaceLogs.setLogType(1);

        create(interfaceLogs);
    }

    @Override
    public void rollBackSuccessLog(InterfaceLogs interfaceLogs) {
        interfaceLogs.setIsSuccess(1);
        interfaceLogs.setErrorInfo(null);
        interfaceLogs.setLogType(2);
        create(interfaceLogs);
    }


    @Override
    public void rollBackFailLog(InterfaceLogs interfaceLogs) {
        interfaceLogs.setIsSuccess(1);
        interfaceLogs.setLogType(2);
        create(interfaceLogs);

    }
}