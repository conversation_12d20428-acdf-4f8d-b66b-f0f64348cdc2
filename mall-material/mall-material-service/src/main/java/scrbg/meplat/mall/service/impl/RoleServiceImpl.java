package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.Role;
import scrbg.meplat.mall.entity.ShopRoleAssociation;
import scrbg.meplat.mall.enums.role.RoleCommonEnum;
import scrbg.meplat.mall.mapper.RoleMapper;
import scrbg.meplat.mall.service.RoleService;
import scrbg.meplat.mall.service.ShopRoleAssociationService;

import java.util.ArrayList;
import java.util.List;

/**
 * @描述：角色表 服务类
 * @作者: y
 * @日期: 2022-11-07
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {


    @Autowired
    ShopRoleAssociationService shopRoleAssociationService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Role> queryWrapper) {
        IPage<Role> page = this.page(
                new Query<Role>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(Role role) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(role);
    }

    @Override
    public R createList(List<Role> roleList) {
        for (Role role : roleList) {
            boolean save = save(role);
            if (!save) {
                return R.failed("角色更新失败！");
            }
        }
        return R.success("", "角色跟新成功");
    }

    @Override
    public void update(Role role) {
        super.updateById(role);
    }

    @Override
    public Role getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public R findAllByMallType(int mallType) {
        QueryWrapper<Role> queryWrapper = new QueryWrapper();
        queryWrapper.eq("mall_type", mallType).eq("is_selective", RoleCommonEnum.IS_SELECTIVE_YES.getCode());
        List<Role> roles = list(queryWrapper);
        return R.success(roles, "获取角色成功！");
    }

    @Override
    public R insertRoleToShop(List<Role> shopRoles, String shopId) {
        ArrayList<ShopRoleAssociation> list = new ArrayList<>();
        //删除当前店铺所有角色
        boolean b1 = removeById(shopId);
        //重新分配角色
        for (Role role : shopRoles) {
            ShopRoleAssociation shopRoleAssociation = new ShopRoleAssociation();
            shopRoleAssociation.setShopId(shopId);
            shopRoleAssociation.setRoleId(role.getRoleId());
            list.add(shopRoleAssociation);
        }
        boolean b = shopRoleAssociationService.saveBatch(list);
        if (b) {
            return R.success(null, "添加成功！");
        }
        return R.failed("添加角色失败！");
    }
}
