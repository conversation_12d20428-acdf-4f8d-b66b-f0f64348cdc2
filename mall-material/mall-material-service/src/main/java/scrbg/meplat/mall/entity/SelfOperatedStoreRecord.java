package scrbg.meplat.mall.entity;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value = "仓库管理-自营店商品管理")
@Data
@TableName("self_operated_record")
public class SelfOperatedStoreRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "记录id")
    private String recordId;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "分类id")
    private String classId;

    @ApiModelProperty(value = "分类名称")
    private String classPathName;

    @ApiModelProperty(value = "商品编码")
    private String serialNum;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "差价")
    private BigDecimal profitPrice;

    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "库房id")
    private String warehouseId;

    @ApiModelProperty(value = "商品小图")
    private String productMinImg;

    /** 操作人员 */
    @ApiModelProperty(name = "操作人")
    private String operationUser;

    @ApiModelProperty(value = "商品主图")
    @TableField(exist = false)
    private List<File> adminFile;

    @ApiModelProperty(value = "商品小图")
    @TableField(exist = false)
    private List<File> minFile;

    @ApiModelProperty(value = "商品图片")
    @TableField(exist = false)
    private List<File> productFiles;

    @ApiModelProperty(value = "店铺id")
    @TableField(exist = false)
    private String shopId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;




}
