package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.ImportSuppler;
import scrbg.meplat.mall.service.ImportSupplerService;

import java.util.List;

/**
 * @描述：导入供应商失败的结果控制类
 * @作者: ye
 * @日期: 2023-05-09
 */
@RestController
@RequestMapping("/importSuppler")
@Api(tags = "导入供应商失败的结果")
public class ImportSupplerController{

@Autowired
public ImportSupplerService importSupplerService;

@PostMapping("/listByEntity")
@ApiOperation(value = "根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<ImportSuppler> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= importSupplerService.queryPage(jsonObject,new LambdaQueryWrapper<ImportSuppler>());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<ImportSuppler> findById(String id){
    ImportSuppler importSuppler = importSupplerService.getById(id);
        return R.success(importSuppler);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
public R save(@RequestBody ImportSuppler importSuppler){
    importSupplerService.create(importSuppler);
        return R.success();
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
public R update(@RequestBody ImportSuppler importSuppler){
    importSupplerService.update(importSuppler);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    importSupplerService.delete(id);
        return R.success();
        }


@PostMapping("/deleteBatch")
@ApiOperation(value = "根据主键批量删除")
public R deleteBatch(@RequestBody List<String> ids){
    importSupplerService.removeByIds(ids);
        return R.success();
        }
        }

