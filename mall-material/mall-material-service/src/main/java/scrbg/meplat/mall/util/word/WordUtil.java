//package scrbg.meplat.mall.util.word;
//import com.deepoove.poi.XWPFTemplate;
//import java.io.OutputStream;
//import java.util.Map;
//
///**
// * word工具类
// * Poi-tl模板引擎官方文档：http://deepoove.com/poi-tl/
// */
//public class WordUtil {
//
//    /**
//     * 根据模板填充内容生成word，并下载
//     * @param templatePath word模板文件路径
//     * @param paramMap     替换的参数集合
//     */
//    public static void downloadWord(OutputStream out,String templatePath, Map<String, Object> paramMap) {
//        // 读取模板templatePath并将paramMap的内容填充进模板，即编辑模板(compile)+渲染数据(render)
//        XWPFTemplate template = XWPFTemplate.compile(templatePath).render(paramMap);
//        try {
//            template.write(out);//将template写到OutputStream中
//        	out.flush();
//        	out.close();
//        	template.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//}
//
