package scrbg.meplat.mall.interceptor;


import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.util.IpUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2022-12-14 16:39
 */
@Log4j2
@Component
public class CheckTokenInterceptor1 implements HandlerInterceptor {

    @Autowired
    private RedisTemplate redisTemplate;

    private final String[] urls = {
            "/w/user/loginSendCode",
    };

    /**
     * 防止频繁请求
     * @param request current HTTP request
     * @param response current HTTP response
     * @param handler chosen handler to execute, for type and/or instance evaluation
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String xxxx = request.getServletPath();
        for (int i = 0; i < urls.length; i++) {
            if(xxxx.equals(urls[i])) {
                log.info("拦截了请求：" + xxxx);
                return false;
            }
        }
        // 放行options请求
        String method = request.getMethod();
        if ("OPTIONS".equalsIgnoreCase(method)) {
            return true;
        }
        try {
            String ipAddress = request.getHeader("X-Forwarded-For").split(":")[0];
            log.info(request.getServletPath() + "对应ip地址：" + ipAddress);
        }catch (Exception e) {
        }
//        int seconds = 1;
//        int maxCount = 3;
//        String ip = IpUtil.getIpAddr(request);
//        String key = request.getServletPath() + ":" + ip ;
//        log.info("key的值：" + key);
//        boolean flag = false;
//        Integer count = (Integer) redisTemplate.opsForValue().get(key);
//        if (null == count || -1 == count) {
//            redisTemplate.opsForValue().set(key, 1,seconds, TimeUnit.SECONDS);
//            flag =  true;
//        }else {
//            if (count < maxCount) {
//                count = count+1;
//                redisTemplate.opsForValue().set(key, count,0);
//                flag =  true;
//            }
//        }
//        if(!flag) {
//            throw new BusinessException("操作过于频繁");
//        }
        return true;
    }
}
