package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * @描述：系统参数表
 * @作者: sund
 * @日期: 2022-12-08
 */
@ApiModel(value="系统参数表")
@Data
@TableName("system_param")
public class SystemParam implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "系统参数表id")
    private String systemId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "code")
    private String  code;

    @ApiModelProperty(value = "值")
    private String keyValue;

    @ApiModelProperty(value = "值2")
    private String keyValue2;

    @ApiModelProperty(value = "说明")
    private String remarks;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    @ApiModelProperty(value = "商城类型：0物资商场, 1设备商城 ")
    @TableField(value = "mall_type", fill = FieldFill.INSERT)
    private Integer mallType;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "0参数, 1字典  ")
    @TableField(value = "type", fill = FieldFill.INSERT)
    private Integer type;

    @ApiModelProperty(value = "是否可维护：0不可维护, 1可维护 ")
    private Integer maintain;

    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")
    @TableField(value = "is_delete", fill = FieldFill.INSERT)
    private Integer isDelete;

    @ApiModelProperty(value = "是否可见（1是0否）")
    private Integer isShow;
}
