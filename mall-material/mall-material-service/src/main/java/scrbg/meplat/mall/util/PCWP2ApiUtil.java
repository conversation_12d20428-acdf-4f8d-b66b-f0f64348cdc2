package scrbg.meplat.mall.util;

public  class PCWP2ApiUtil {
    //现场收料收料接口 保存现场收料(提供给物资贸易平台)
    public static String  SAVE_SITE_RECEIVING="/thirdapi/siteReceiving/saveSiteReceiving";


    public static String  SAVE_PLAN="/thirdapi/sporadicPurchasePlan/savePlan";
    //现场收料收料接口 回滚保存现场收料(提供给物资贸易平台)
    public static String  ROLLBACK_SAVE_SITE_RECEIVING="/thirdapi/siteReceiving/rollBackSaveSiteReceiving?keyId=";
    //现场收料收料接口 回滚保存现场收料(提供给物资贸易平台)
    public static String  MATERIAL_PURCHASE_DTL="/thirdapi/matarialpurchase/queryPageMaterialDtl";
    // 是否可以推送对账单/收料单
    public static  String IS_CON_OPERA_BILL_URL = "/thirdapi/monthlySettlement/isCanOperaBill";
  //批量更新物资使用状态（提供给物资贸易平台）
    public static  String BATCH_UPDATE_MATARIAL_DTL_STATE = "/thirdapi/matarialpurchase/batchUpdateMaterialDtlState";

    //批量更新物资分类使用状态（提供给物资贸易平台）
    public static  String BATCH_UPDATE_CATEGORY_LIBRARY_STATE = "/thirdapi/matarialpurchase/batchUpdateCategoryLibraryState";

    // 分页查询物资信息
    public static  String SELECT_ALL_MATERIAL_INFO_URL = "/thirdapi/matarialpurchase/queryPageMaterialDtl";

    //批量保存物资信息（提供给物资贸易平台）
    public static  String BATH_SAVE_MATERIAL_INFO_URL = "/thirdapi/matarialpurchase/saveBatchMaterialInfo";
    /**
     * 判断用户是否是pcwp供应商
     */
    public static  String IS_PCWP = "/thirdapi/outer/getSupplierBycreditCode?creditCode=";

    //保存大宗零购计划（提供给物资贸易平台）
    public static  String PullDzPlan = "/thirdapi/bulkRetailPlan/saveBulkRetailPlan";
    //保存大宗零购计划回滚（提供给物资贸易平台）
    public static  String PullDzPlanReturn = "/thirdapi/bulkRetailPlan/getBulkRetailPlanById?id=";

    //反写大宗临购计划接口请求参数
    public static  String PULL_DZ_NUMBER = "/thirdapi/bulkRetailPlan/updateBulkRetailPlanDtl";

    //反写零星采购计划接口请求参数
    public static  String PULL_LX_NUMBER = "/thirdapi/sporadicPurchasePlan/savePlan";

}
