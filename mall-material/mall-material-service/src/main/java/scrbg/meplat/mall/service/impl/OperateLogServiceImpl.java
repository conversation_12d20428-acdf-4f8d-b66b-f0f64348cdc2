package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.OperateLog;
import scrbg.meplat.mall.mapper.OperateLogMapper;
import scrbg.meplat.mall.service.OperateLogService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2024-01-04
 */
@Service
public class OperateLogServiceImpl extends ServiceImpl<OperateLogMapper, OperateLog> implements OperateLogService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OperateLog> queryWrapper) {
        IPage<OperateLog> page = this.page(
        new Query<OperateLog>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(OperateLog operateLog) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(operateLog);
    }

    @Override
    public void update(OperateLog operateLog) {
        super.updateById(operateLog);
    }


    @Override
    public OperateLog getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
