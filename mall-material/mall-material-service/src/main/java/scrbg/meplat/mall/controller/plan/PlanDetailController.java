package scrbg.meplat.mall.controller.plan;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.plan.PlanDetailService;
import scrbg.meplat.mall.entity.plan.PlanDetail;
import scrbg.meplat.mall.dto.plan.PlanDetailDTO;

import javax.validation.Valid;
import java.util.List;

/**
 * @描述：采购计划明细表控制类
 * @作者: ye
 * @日期: 2025-05-27
 */
@RestController
@RequestMapping("/planDetail")
@Api(tags = "采购计划明细表")
public class PlanDetailController {

    @Autowired
    public PlanDetailService planDetailService;

    @GetMapping("/page")
    @ApiOperation(value = "根据实体属性分页查询")
    public PageR<PlanDetail> listByPage(@RequestParam PlanDetailDTO dto) {
        PageUtils page = planDetailService.queryPage(dto);
        return PageR.success(page);
    }

    @GetMapping("/findId")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "path")
    })
    public R<PlanDetail> findById(String id) {
        PlanDetail planDetail = planDetailService.getById(id);
        return R.success(planDetail);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody @Valid PlanDetail planDetail) {
        planDetailService.create(planDetail);
        return R.success();
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody @Valid PlanDetail planDetail) {
        planDetailService.update(planDetail);
        return R.success();
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "path")
    })
    public R delete(@PathVariable String id) {
        planDetailService.delete(id);
        return R.success();
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        planDetailService.deleteBatch(ids);
        return R.success();
    }
}

