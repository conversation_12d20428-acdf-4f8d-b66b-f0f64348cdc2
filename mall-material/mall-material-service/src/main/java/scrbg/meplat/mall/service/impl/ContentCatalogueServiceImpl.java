package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.ContentCatalogue;
import scrbg.meplat.mall.mapper.ContentCatalogueMapper;
import scrbg.meplat.mall.service.ContentCatalogueService;

/**
 * @描述：内容目录表 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
@Service
public class ContentCatalogueServiceImpl extends ServiceImpl<ContentCatalogueMapper, ContentCatalogue> implements ContentCatalogueService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ContentCatalogue> queryWrapper) {
        IPage<ContentCatalogue> page = this.page(
                new Query<ContentCatalogue>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ContentCatalogue contentCatalogue) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(contentCatalogue);
    }

    @Override
    public void update(ContentCatalogue contentCatalogue) {
        super.updateById(contentCatalogue);
    }


    @Override
    public ContentCatalogue getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
