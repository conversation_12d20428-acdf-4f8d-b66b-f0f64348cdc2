package scrbg.meplat.mall.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import scrbg.meplat.mall.entity.DealOrderInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.vo.w.DealOrderInfoDTO;

import javax.servlet.http.HttpServletResponse;

/**
 * @描述：结算订单明细 服务类
 * @作者: ye
 * @日期: 2023-06-16
 */
public interface DealOrderInfoService extends IService<DealOrderInfo> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<DealOrderInfo> queryWrapper);

        void create(DealOrderInfo dealOrderInfo);

        void update(DealOrderInfo dealOrderInfo);

        DealOrderInfo getById(String id);

        void delete(String id);

        /**
         * 店铺查询供应结算信息
         * @param jsonObject
         * @param dealOrderInfoLambdaQueryWrapper
         * @return
         */
        PageUtils shopListByEntity(JSONObject jsonObject, LambdaQueryWrapper<DealOrderInfo> dealOrderInfoLambdaQueryWrapper);


        /**
         * 导出数据
         *
         * @param jsonObject
         * @param dealOrderInfoLambdaQueryWrapper
         * @param response
         */
        void outputExcel(JSONObject jsonObject, QueryWrapper<DealOrderInfo> dealOrderInfoLambdaQueryWrapper, HttpServletResponse response);

        /**
         * 平台查询结算信息
         * @param jsonObject
         * @param dealOrderInfoLambdaQueryWrapper
         * @return
         */
    PageUtils platformListByEntity(JSONObject jsonObject, LambdaQueryWrapper<DealOrderInfo> dealOrderInfoLambdaQueryWrapper);



    /**
         * 导出数据平台
         * @param jsonObject
         * @param dealOrderInfoQueryWrapper
         * @param response
         */
        void platformOutputExcel(JSONObject jsonObject, QueryWrapper<DealOrderInfo> dealOrderInfoQueryWrapper, HttpServletResponse response);

    /**
     * 供应商平台查询结算信息
     * @param jsonObject
     * @param dealOrderInfoLambdaQueryWrapper
     * @return
     */
    PageUtils vendorListByEntity(JSONObject jsonObject, LambdaQueryWrapper<DealOrderInfo> dealOrderInfoLambdaQueryWrapper);

    /**
     * 导出供应数据平台
     * @param jsonObject
     * @param dealOrderInfoQueryWrapper
     * @param response
     */
    void supplyPlatformOutputExcel(JSONObject jsonObject, QueryWrapper<DealOrderInfo> dealOrderInfoQueryWrapper, HttpServletResponse response);


    /**
     * 外部新增
     *
     * @param dto
     * @param keyId
     */
    void batchOutAdd(DealOrderInfoDTO dto, String keyId);


    void platformOutputExcel2(JSONObject jsonObject, QueryWrapper<DealOrderInfo> dealOrderInfoQueryWrapper, HttpServletResponse response);
}
