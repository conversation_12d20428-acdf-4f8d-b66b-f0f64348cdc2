package scrbg.meplat.mall.mapper.pcwpmq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.dto.pcwpmq.*;
import scrbg.meplat.mall.entity.pcwpmq.PcwpPersonPermissions;

import java.util.List;

@Mapper
@Repository
public interface MqMessageMapper  extends BaseMapper<MqMessageDTO> {

    // 2. 批量更新消费状态
    @Update("<script>" +
            "UPDATE pcwp_mq_data SET is_get = '1' WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchMarkAsConsumed(@Param("ids") List<Integer> ids);

    @Delete("delete from ${tableName} ")
    int deleteTableInfo(String tableName);

    @Update("UPDATE pcwp_mq_data SET is_get = '1' WHERE type=#{type}")
    int updateStatus(String type);

    @Delete("delete from pcwp_mq_data where is_get = '1' and type= #{type} and delivery_tag < #{delivery_tag}")
    int deleteUnUseDada(long delivery_tag,String type);

    // 3. 按类型查询未消费消息
    @Select("SELECT id,type,delivery_tag,time,is_get FROM pcwp_mq_data WHERE type = #{type} AND is_get = '0'")
    List<MqMessageDTO> selectUnconsumedByType(String type);

    // 3. 按类型查询未消费消息
    @Select("SELECT id,type,delivery_tag,time,is_get FROM pcwp_mq_data WHERE type = #{type} AND delivery_tag = #{deliveryTag}")
    List<MqMessageDTO> selectUnconsumedByTypeAndTag(String type,Long deliveryTag);

    @Select("SELECT id,json_text,type,delivery_tag,time,is_get FROM pcwp_mq_data WHERE id = #{id}")
    List<MqMessageDTO> selectUnconsumedById(int id);

    @Select("SELECT id,json_text,type,delivery_tag,patch,time,is_get FROM pcwp_mq_data WHERE type = #{type} and delivery_tag = #{delivery_tag} and patch = #{patch} limit 1 ")
    List<MqMessageDTO> selectUnconsumedBydeliveryTag(long delivery_tag,String type,int patch);

    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("INSERT INTO pcwp_mq_data(json_text, type, delivery_tag, patch, time, is_get) " +
            "VALUES (#{jsonText}, #{type}, #{deliveryTag}, #{patch}, #{time}, #{isGet})")
    int insertWithId(MqMessageDTO entity);

    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("<script>" +
            "INSERT INTO pcwp_mq_data (json_text, type, delivery_tag, patch, time, is_get) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.jsonText}, #{item.type}, #{item.deliveryTag}, #{item.patch}, #{item.time}, #{item.isGet})" +
            "</foreach>" +
            "</script>")
    int batchPatchInsertWithId(List<MqMessageDTO> list);

    @Select("select type,max(delivery_tag) delivery_tag from pcwp_mq_data where is_get='0' group by type")
//    @Select("select type,max(delivery_tag) delivery_tag from pcwp_mq_data where type='Personinfo_Full' and is_get='0' group by type")
    List<MqMessageDTO> getLastestMqData();

    @Insert("INSERT INTO `pcwp_personinfos` (`id`, `pname`, `pnumber`, `orgnumber`, `gender`, `deptnumber`, `age`, `idcard`, `gw`, `xl`, `gznx`, `xz`, `bysj`, `byyx`, `byzy`, `mobile`, `sbbm`, `sbjndw`, `yx`, `tc`, `lastupdatetime`, `mdmstate`) " +
            "VALUES (#{id}, #{pname}, #{pnumber}, #{orgnumber}, #{gender}, #{deptnumber}, #{age}, #{idcard}, #{gw}, #{xl}, #{gznx}, #{xz}, #{bysj}, #{byyx}, #{byzy}, #{mobile}, #{sbbm}, #{sbjndw}, #{yx}, #{tc}, #{lastupdatetime}, #{MdmState})")
    int insertPcwpPersoninfos(PcwpPersoninfos2DTO pcwpPersoninfosDTO);

    @Insert("<script>" +
            "INSERT INTO `pcwp_personinfos` (`id`, `pname`, `pnumber`, `orgnumber`, `gender`, `deptnumber`, `age`, `idcard`, `gw`, `xl`, `gznx`, `xz`, `bysj`, `byyx`, `byzy`, `mobile`, `sbbm`, `sbjndw`, `yx`, `tc`, `lastupdatetime`, `mdmstate`) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.pname}, #{item.pnumber}, #{item.orgnumber}, #{item.gender}, #{item.deptnumber}, #{item.age}, #{item.idcard}, #{item.gw}, #{item.xl}, #{item.gznx}, #{item.xz}, #{item.bysj}, #{item.byyx}, #{item.byzy}, #{item.mobile}, #{item.sbbm}, #{item.sbjndw}, #{item.yx}, #{item.tc}, #{item.lastupdatetime}, #{item.MdmState})" +
            "</foreach>" +
            "</script>")
    int batchInsertPcwpPersoninfos(List list);

    @Insert("INSERT INTO `pcwp_zcinfos` (`id`, `pname`, `pnumber`, `zclbmc`, `zcdj`, `zyfl`, `zymc`, `zsnumber`, `fzdate`, `lastupdatetime`, `mdmstate`) " +
            "VALUES (#{id}, #{pname}, #{pnumber}, #{zclbmc}, #{zcdj}, #{zyfl}, #{zymc}, #{zsnumber}, #{fzdate}, #{lastupdatetime}, #{MdmState})")
    int insertPcwpZcinfos(PcwpZcinfos2DTO pcwpZcinfosDTO);

    @Insert("<script>" +
            "INSERT INTO `pcwp_zcinfos` (`id`, `pname`, `pnumber`, `zclbmc`, `zcdj`, `zyfl`, `zymc`, `zsnumber`, `fzdate`, `lastupdatetime`, `mdmstate`) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.pname}, #{item.pnumber}, #{item.zclbmc}, #{item.zcdj}, #{item.zyfl}, #{item.zymc}, #{item.zsnumber}, #{item.fzdate}, #{item.lastupdatetime}, #{item.MdmState})" +
            "</foreach>" +
            "</script>")
    int batchInsertPcwpZcinfos(List list);

    @Insert("INSERT INTO `pcwp_zyzginfos` (`id`, `pname`, `pnumber`, `zyzgzsmc`, `zydj`, `zsnumber`, `fzdate`, `lastupdatetime`, `mdmstate`) " +
            "VALUES (#{id}, #{pname}, #{pnumber}, #{zyzgzsmc}, #{zydj}, #{zsnumber}, #{fzdate}, #{lastupdatetime}, #{MdmState})")
    int insertPcwpZyzginfos(PcwpZyzginfos2DTO pcwpZyzginfosDTO);

    @Insert("<script>" +
            "INSERT INTO `pcwp_zyzginfos` (`id`, `pname`, `pnumber`, `zyzgzsmc`, `zydj`, `zsnumber`, `fzdate`, `lastupdatetime`, `mdmstate`) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.pname}, #{item.pnumber}, #{item.zyzgzsmc}, #{item.zydj}, #{item.zsnumber}, #{item.fzdate}, #{item.lastupdatetime}, #{item.MdmState})" +
            "</foreach>" +
            "</script>")
    int batchInsertPcwpZyzginfos(List list);

    @Insert("INSERT INTO `pcwp_grhjinfos` (`id`, `pname`, `pnumber`, `hjmc`, `hjsj`, `bfdw`, `lastupdatetime`, `mdmstate`) " +
            "VALUES (#{id}, #{pname}, #{pnumber}, #{hjmc}, #{hjsj}, #{bfdw}, #{lastupdatetime}, #{MdmState})")
    int insertPcwpGrhjinfos(PcwpGrhjinfos2DTO pcwpGrhjinfosDTO);

    @Insert("<script>" +
            "INSERT INTO `pcwp_grhjinfos` (`id`, `pname`, `pnumber`, `hjmc`, `hjsj`, `bfdw`, `lastupdatetime`, `mdmstate`) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.pname}, #{item.pnumber}, #{item.hjmc}, #{item.hjsj}, #{item.bfdw}, #{item.lastupdatetime}, #{item.MdmState})" +
            "</foreach>" +
            "</script>")
    int batchInsertPcwpGrhjinfos(List list);

    @Insert("INSERT INTO `pcwp_rzjlinfos` (`id`, `pname`, `pnumber`, `rzxs`, `rzgw`, `bmbm`, `bmmc`, `xmmc`, `xmbm`, `startdate`, `enddate`, `lastupdatetime`, `mdmstate`) " +
            "VALUES (#{id}, #{pname}, #{pnumber}, #{rzxs}, #{rzgw}, #{bmbm}, #{bmmc}, #{xmmc}, #{xmbm}, #{StartDate}, #{EndDate}, #{lastupdatetime}, #{MdmState})")
    int insertPcwpRzjlinfos(PcwpRzjlinfos2DTO pcwpRzjlinfosDTO);

    @Insert("<script>" +
            "INSERT INTO `pcwp_rzjlinfos` (`id`, `pname`, `pnumber`, `rzxs`, `rzgw`, `bmbm`, `bmmc`, `xmmc`, `xmbm`, `startdate`, `enddate`, `lastupdatetime`, `mdmstate`) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.pname}, #{item.pnumber}, #{item.rzxs}, #{item.rzgw}, #{item.bmbm}, #{item.bmmc}, #{item.xmmc}, #{item.xmbm}, #{item.StartDate}, #{item.EndDate}, #{item.lastupdatetime}, #{item.MdmState})" +
            "</foreach>" +
            "</script>")
    int batchInsertPcwpRzjlinfos(List list);

    @Insert("INSERT INTO `pcwp_orginfos` (`id`, `name`, `number`, `parentid`, `type`, `createtime`, `flastupdatetime`, `orglayertypeid`, `orglayertypenumber`, `orglayertypename`, `djlbid`, `djlbnumber`, `djlbname`, `glzzid`, `glzznumber`, `glzzname`, `sortcode`, `issealup`, `mdmstate`) " +
            "VALUES (#{id}, #{name}, #{number}, #{parentid}, #{type}, #{createtime}, #{fLastUpdateTime}, #{orgLayerTypeid}, #{orgLayerTypenumber}, #{orgLayerTypeName}, #{djlbid}, #{djlbnumber}, #{djlbname}, #{glzzid}, #{glzznumber}, #{glzzname}, #{sortCode}, #{isSealUp}, #{MdmState})")
    int insertPcwpOrginfos(PcwpOrginfos2DTO pcwpOrginfosDTO);

    @Insert("<script>" +
            "INSERT INTO `pcwp_orginfos` (`id`, `name`, `number`, `parentid`, `type`, `createtime`, `flastupdatetime`, `orglayertypeid`, `orglayertypenumber`, `orglayertypename`, `djlbid`, `djlbnumber`, `djlbname`, `glzzid`, `glzznumber`, `glzzname`, `sortcode`, `issealup`, `mdmstate`) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.name}, #{item.number}, #{item.parentid}, #{item.type}, #{item.createtime}, #{item.fLastUpdateTime}, #{item.orgLayerTypeid}, #{item.orgLayerTypenumber}, #{item.orgLayerTypeName}, #{item.djlbid}, #{item.djlbnumber}, #{item.djlbname}, #{item.glzzid}, #{item.glzznumber}, #{item.glzzname}, #{item.sortCode}, #{item.isSealUp}, #{item.MdmState})" +
            "</foreach>" +
            "</script>")
    int batchInsertPcwpOrginfos(List list);

    @Insert("INSERT INTO `pcwp_org` (`orgid`, `orgname`, `parentorgid`, `orgtype`, `sort`, `status`, `lastmodifytime`, `createtime`, `hrorgid`, `hrparentorgid`, `shortname`, `shortcode`, `orgproperty`, `oldorgid`, `oldorgname`, `mdmstate`) " +
            "VALUES (#{OrgId}, #{OrgName}, #{ParentOrgId}, #{OrgType}, #{Sort}, #{Status}, #{LastModifyTime}, #{CreateTime}, #{HROrgId}, #{HRParentOrgId}, #{ShortName}, #{ShortCode}, #{OrgProperty}, #{OldOrgId}, #{OldOrgName}, #{MdmState})")
    int insertPcwpOrg(PcwpOrg2DTO pcwpOrgDTO);

    @Insert("INSERT INTO `pcwp_conorg` (`recordid`, `orgid`, `orgname`, `parentorgid`, `parentorgname`, `createtime`, `mdmstate`) " +
            "VALUES (#{RecordId}, #{OrgId}, #{OrgName}, #{ParentOrgId}, #{ParentOrgName}, #{CreateTime}, #{MdmState})")
    int insertPcwpConorg(PcwpConorg2DTO pcwpConorgDTO);

    @Insert("INSERT INTO `pcwp_project` (`projectid`, `projectno`, `abbreviationname`, `projectname`, `startdate`, " +
            "`enddate`, `totalmonth`, `proptypeid1`, `proptypename1`, `proptypeid2`, `proptypename2`, `classid`, `class`, " +
            "`gradeid`, `gread`, `bownerid`, `bownername`, `constructiontypeid`, `constructiontype`, `designerid`, " +
            "`designername`, `supervisorid`, `supervisorname`, `begincol`, `endcol`, `long`, `width`, `address`, " +
            "`createdate`, `orgid`, `orgname`, `agentid`, `agentname`, `remark`, `attachid`, `state`, `recorderid`, " +
            "`recordername`, `recordertime`, `lastmodifierid`, `lastmodifier`, `lastmodifytime`, `lastauditorid`, " +
            "`lastauditor`, `lastaudittime`, `workid`, `oldworkid`, `contractamount`, `contractid`, `contractorid`, " +
            "`contractor`, `oldkeyid`, `budgetcost`, `completedtime`, `finishedtime`, `Isfinishedevaluation`, " +
            "`finishedevaluationtime`, `mdmstate`) " +
            "VALUES (#{ProjectID}, #{ProjectNo}, #{AbbreviationName}, #{ProjectName}, #{StartDate}, " +
            "#{EndDate}, #{TotalMonth}, #{ProptypeId1}, #{ProptypeName1}, #{ProptypeId2}, #{ProptypeIdName2}, #{ClassId}, #{ProjectClass}, " +
            "#{GradeId}, #{Gread}, #{BOwnerId}, #{BOwnerName}, #{ConstructionTypeId}, #{ConstructionType}, #{DesignerId}, " +
            "#{DesignerName}, #{SupervisorId}, #{SupervisorName}, #{BeginCol}, #{EndCol}, #{Long}, #{Width}, #{Address}, " +
            "#{CreateDate}, #{OrgId}, #{OrgName}, #{AgentId}, #{AgentName}, #{Remark}, #{AttachId}, #{State}, #{RecorderId}, " +
            "#{RecorderName}, #{RecorderTime}, #{LastModifierId}, #{LastModifier}, #{LastModifyTime}, #{LastAuditorId}, " +
            "#{LastAuditor}, #{LastAuditTime}, #{WorkId}, #{OldWorkId}, #{ContractAmount}, #{ContractId}, #{ContractorId}, " +
            "#{Contractor}, #{OldKeyId}, #{BudgetCost}, #{CompletedTime}, #{FinishedTime}, #{IsFinishedEvaluation}, " +
            "#{FinishedEvaluationTime}, #{MdmState})")
    int insertPcwpProject(PcwpProject2DTO pcwpProjectDTO);

    @Insert("INSERT INTO `pcwp_tt_account` (`id`, `name`, `username`, `employeenumber`, `created`, `mdmstate`) " +
            "VALUES (#{Id}, #{Name}, #{UserName}, #{EmployeeNumber}, #{Created}, #{MdmState})")
    int insertPcwpTtAccount(PcwpTtAccount2DTO pcwpTtAccountDTO);

    @Insert("<script>" +
            "INSERT INTO `pcwp_tt_account` (`id`, `name`, `username`, `employeenumber`, `created`, `mdmstate`) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.Id}, #{item.Name}, #{item.UserName}, #{item.EmployeeNumber}, #{item.Created}, #{item.MdmState})" +
            "</foreach>" +
            "</script>")
    int batchInsertPcwpTtAccount(List list);

    @Insert("INSERT INTO `pcwp_person_permissions` (`user_id`, `user_name`, `org_id`, `org_name`, `short_code`, `role_id`, `role_name`) " +
            "VALUES (#{userId}, #{userName}, #{orgId}, #{orgName}, #{shortCode}, #{roleId}, #{roleName})")
    int insertPcwpPersonPermissions(PcwpPersonPermissionsDTO pcwpPersonPermissionsDTO);

    @Insert("<script>" +
            "INSERT INTO `pcwp_person_permissions` (`user_id`, `user_name`, `org_id`, `org_name`, `short_code`, `role_id`, `role_name`) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.userId}, #{item.userName}, #{item.orgId}, #{item.orgName}, #{item.shortCode}, #{item.roleId}, #{item.roleName})" +
            "</foreach>" +
            "</script>")
    int batchInsertPcwpPersonPermissions(List<PcwpPersonPermissions> list);
}
