package scrbg.meplat.mall.entity.pcwpmq;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * PCWP大经理部表实体类
 */
@Data
@TableName("pcwp_conorg")
public class PcwpConorg {
    /**
     * 主键（记录ID）
     */
    @TableField("recordid")
    private String recordid;

    /**
     * 机构ID
     */
    @TableField("orgid")
    private String orgid;

    /**
     * 机构名称
     */
    @TableField("orgname")
    private String orgname;

    /**
     * 上级机构ID(承建机构)
     */
    @TableField("parentorgid")
    private String parentorgid;

    /**
     * 上级机构名称(承建机构)
     */
    @TableField("parentorgname")
    private String parentorgname;

    /**
     * 创建时间
     */
    @TableField("createtime")
    private String createtime;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    @TableField("mdmstate")
    private Integer mdmstate;
}