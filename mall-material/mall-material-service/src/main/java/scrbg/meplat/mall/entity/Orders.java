package scrbg.meplat.mall.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：商品订单
 * @作者: y
 * @日期: 2022-11-25
 */
@ApiModel(value = "商品订单")
@Data
@TableName("orders")
public class Orders extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "订单号")
    private String orderSn;

    @ApiModelProperty(value = "计划id")
    private String planId;

    @ApiModelProperty(value = "计划编号")
    private String planNo;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "产品名称（多个产品用,隔开）")
    private String untitled;

    @ApiModelProperty(value = "收货人快照")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机号快照")
    private String receiverMobile;

    @ApiModelProperty(value = "收货地址快照")
    private String receiverAddress;

    @ApiModelProperty(value = "订单总价格（原价总金额）")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "实际总价格")
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "交易类型，店铺对用户：1外->外 2内-外 3外-内 4内-内（废弃）")
    private Integer dealType;

    @ApiModelProperty(value = "线上支付方式 1:微信 2:支付宝 3:银联 ")
    private Integer payType;

    @ApiModelProperty(value = "支付方式 1线上支付 2内部结算 3线下转账")
    private Integer payWay;

    @ApiModelProperty(value = "订单备注")
    private String orderRemark;

    @ApiModelProperty(value = "订单状态：0草稿 1已提交 2待确认 3已确认 4待签订合同 5已签合同 6待发货 7已关闭 8 发货中 9待收货 10 已完成")
    private Integer state;

    @ApiModelProperty(value = "是否开发票 0初始 1已申请 2已开票")
    private Integer orderBillState;

    @ApiModelProperty(value = "配送方式（0:普通物流）")
    private Integer deliveryType;

    @ApiModelProperty(value = "物流单号")
    private String deliveryFlowId;

    @ApiModelProperty(value = "物流公司")
    private String logisticsCompany;

    @ApiModelProperty(value = "订单运费 默认可以为零，代表包邮")
    private BigDecimal orderFreight;

    @ApiModelProperty(value = "总利润")
    private BigDecimal profitPriceTotal;

    @ApiModelProperty(value = "总成本价")
    private BigDecimal costPriceTotal;

    @ApiModelProperty(value = "付款时间")
    private Date payTime;

    @ApiModelProperty(value = "发货时间")
    private Date deliveryTime;

    @ApiModelProperty(value = "订单确认时间")
    private Date flishTime;

    @ApiModelProperty(value = "取消时间")
    private Date cancelTime;

    @ApiModelProperty(value = "订单关闭类型1-超时未支付 2-退款关闭 4-买家取消 5-数量全退关闭 15-已通过货到付款交易")
    private Integer closeType;

    @ApiModelProperty(value = "支付交易号")
    private String tradeNo;

    @ApiModelProperty(value = "商品类型：0 低值易耗品 1大宗临购 2、周转材料")
    private Integer productType;

    @ApiModelProperty(value = "完成时间")
    private Date successDate;

    @ApiModelProperty(value = "供应商id（本地机构id）")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称（本地机构名称）")
    private String supplierName;

    @ApiModelProperty(value = "当前远程企业id，采购员")
    private String orgId;

    @ApiModelProperty(value = "本地机构id，采购员")
    private String enterpriseId;

    @ApiModelProperty(value = "本地机构名称，采购员")
    private String enterpriseName;

    @ApiModelProperty(value = "本地二级机构名称，采购员")
    @TableField(exist = false)
    private String twoEnterpriseName;

    @ApiModelProperty(value = "订单类别（1普通订单2多供方订单3已拆分子订单）")
    private Integer orderClass;

    @ApiModelProperty(value = "父订单id（是拆单订单这里才有值）")
    private String parentOrderId;

    @ApiModelProperty(value = "拆单供方确认状态（0默认1已确认2已拒绝3未选择供方）")
    private Integer affirmState;

    @ApiModelProperty(value = "主订单方确认拆单状态（0待确认1已确认）")
    private Integer masterAffirmState;

    @ApiModelProperty(value = "派单状态(0未派单1已派单)")
    private Integer assignState;
    
    @ApiModelProperty(value = "接单人id")
    private String assigneeId;

    @ApiModelProperty(value = "接单人姓名")
    private String assigneeName;

    @ApiModelProperty(value = "接单时间")
    private LocalDateTime assigneeTime;

    @ApiModelProperty(value = "退单原因")
    private String rejectReason;

    @ApiModelProperty(value = "订单来源（1零星采购供应商商品2竞价3大宗合同4大宗临购计划5大宗临购父级6大宗月供父级7周转材料父级）（基本没用）")
    private Integer orderSourceType;

    // 拆单存储的是父id，竞价是竞价记录的id 大宗是合同id，子订单保存的是父订单id  大宗临购是计划的id
    @ApiModelProperty(value = "订单来源id")
    private String orderSourceId;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal noRateAmount;

    @ApiModelProperty(value = "清单类型（1浮动价格2固定价格）")
    private Integer billType;

    @ApiModelProperty(value = "货款支付周期（单位月）")
    private Integer paymentWeek;

    @ApiModelProperty(value = "超期垫资利息（%）")
    private BigDecimal outPhaseInterest;

    @ApiModelProperty(value = "订单完成类型（0发货完成1手动完成）")
    private Integer finishType;

    @ApiModelProperty(value = "源单编号（计划编号或合同编号）")
    @TableField(exist = false)
    private String billNo;

    @Version
    private Integer version;//乐观锁

    @ApiModelProperty(value = "图标题（保存在数组第一个）")
    @TableField(exist = false)
    List<String> labelTitle;

    @ApiModelProperty(value = "图数量（保存在数组第一个）")
    @TableField(exist = false)
    List<Integer> count;

    @ApiModelProperty(value = "图总利润（保存在数组第一个）")
    @TableField(exist = false)
    List<BigDecimal> profitPriceTotals;

    @ApiModelProperty(value = "订单来源id")
    @TableField(exist = false)
    private String twoSupplierName;

    @ApiModelProperty(value = "订单项")
    @TableField(exist = false)
    private List<OrderItem> orderItems;
    
    @ApiModelProperty(value = "账期")
    @TableField(exist = false)
    private Integer paymentPeriod;

    @ApiModelProperty(value = "推送日志id")
    private String outKeyId;

    @ApiModelProperty(value = "评论状态：0 未评价 1 已评价")
    private Integer isComment;
    @Override
    public Orders clone() {
        try {
            return (Orders) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError(); // 永远不会发生
        }
    }
}
