package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.SysOrg;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SysOrg;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：机构表 服务类
 * @作者: ye
 * @日期: 2024-04-30
 */
public interface SysOrgService extends IService<SysOrg> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysOrg> queryWrapper);

        void create(SysOrg sysOrg);
        void update(SysOrg sysOrg);
        SysOrg getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
