package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.SysUserOrg;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SysUserOrg;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2024-04-30
 */
public interface SysUserOrgService extends IService<SysUserOrg> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysUserOrg> queryWrapper);

        void create(SysUserOrg sysUserOrg);
        void update(SysUserOrg sysUserOrg);
        SysUserOrg getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
