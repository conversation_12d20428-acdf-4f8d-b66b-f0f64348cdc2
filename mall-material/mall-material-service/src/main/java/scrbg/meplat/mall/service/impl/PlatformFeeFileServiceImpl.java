package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.PlatformFeeFile;
import scrbg.meplat.mall.mapper.PlatformFeeFileMapper;
import scrbg.meplat.mall.service.PlatformFeeFileService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：通用附件 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
@Service
public class PlatformFeeFileServiceImpl extends ServiceImpl<PlatformFeeFileMapper, PlatformFeeFile> implements PlatformFeeFileService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformFeeFile> queryWrapper) {
        IPage<PlatformFeeFile> page = this.page(
        new Query<PlatformFeeFile>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(PlatformFeeFile platformFeeFile) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(platformFeeFile);
    }

    @Override
    public void update(PlatformFeeFile platformFeeFile) {
        super.updateById(platformFeeFile);
    }


    @Override
    public PlatformFeeFile getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
