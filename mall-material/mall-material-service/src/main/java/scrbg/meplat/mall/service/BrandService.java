package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.Brand;

import java.util.List;

/**
 * @描述：品牌 服务类
 * @作者: y
 * @日期: 2022-11-10
 */
public interface BrandService extends IService<Brand> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Brand> queryWrapper);

    void create(Brand brand);

    void update(Brand brand);

    Brand getById(String id);

    void delete(String id);

    /**
     * 根据分类id修改分类名称
     * @param classId
     * @param className
     */
    boolean updateClassNameByClassId(String classId,String className);

    PageUtils queryBrandLogo(JSONObject jsonObject, LambdaQueryWrapper<Brand> queryWrapper);

    void updateByPublish(List<String> ids, String type);


    PageUtils pageList(JSONObject jsonObject, QueryWrapper<Brand> LambdaQueryWrapper);

    /**
     * 根据主键批量删除
     * @param ids
     */
    void deleteBatch(List<String> ids);


    /**
     * 获取品牌列表
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils getBrandPageList(JSONObject jsonObject, LambdaQueryWrapper<Brand> lambdaQuery);


    /**
     * 根据品牌名称查询品牌
     * @param BrandName
     * @return
     */
    Brand getBrandByBrandName(String BrandName);

    /**
     * 根据根据分类id查询品牌集合
     * @param classId
     * @return
     */

    List<Brand> selectAllByClassId(String classId);

    /**
     * 根据根据分类id和品牌名称查询品牌集合
     * @param classId
     * @return
     */

    Brand findByClassIdAndBrandName(String classId, String brandName);
}
