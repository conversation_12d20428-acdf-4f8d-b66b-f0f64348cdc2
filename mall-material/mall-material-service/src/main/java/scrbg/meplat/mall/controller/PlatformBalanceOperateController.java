package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.dto.fee.FeeManagementQueryDto;
import scrbg.meplat.mall.entity.PlatformDealFeeDtl;
import scrbg.meplat.mall.entity.PlatformYearFeeRecord;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.PlatformBalanceOperateService;
import scrbg.meplat.mall.entity.PlatformBalanceOperate;
import scrbg.meplat.mall.service.PlatformDealFeeDtlService;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @描述：平台交易余额操作记录控制类
 * @作者: ye
 * @日期: 2024-01-31
 */
@RestController
@RequestMapping("/platformBalanceOperate")
@Api(tags = "平台交易余额操作记录")
public class PlatformBalanceOperateController {

    @Autowired
    public PlatformBalanceOperateService platformBalanceOperateService;

    @Autowired
    PlatformDealFeeDtlService platformDealFeeDtlService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<PlatformBalanceOperate> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = platformBalanceOperateService.queryPage(jsonObject, new LambdaQueryWrapper<PlatformBalanceOperate>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "query")
    })
    public R<PlatformBalanceOperate> findById(String id) {
        PlatformBalanceOperate platformBalanceOperate = platformBalanceOperateService.getById(id);
        return R.success(platformBalanceOperate);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody PlatformBalanceOperate platformBalanceOperate) {
        platformBalanceOperateService.create(platformBalanceOperate);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody PlatformBalanceOperate platformBalanceOperate) {
        platformBalanceOperateService.update(platformBalanceOperate);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        platformBalanceOperateService.delete(id);
        return R.success();
    }
    
    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        platformBalanceOperateService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("/batchQuery")
    @ApiOperation(value = "根据交易服务费明细查询平台交易余额操作记录")
    public R<List<PlatformBalanceOperate>> queryByShopName(@RequestBody JSONObject jsonObject) {
        String periodStartDate = (String) jsonObject.get("periodStartDate");
        String periodEndDate = (String) jsonObject.get("periodEndDate");
        String enterpriseId = (String) jsonObject.get("enterpriseId");
        List<PlatformDealFeeDtl> dealFeeDtlList = platformDealFeeDtlService.lambdaQuery()
                .eq(PlatformDealFeeDtl::getEnterpriseId,enterpriseId)
                .eq(PlatformDealFeeDtl::getServeType, 1)
                .eq(PlatformDealFeeDtl::getState, 0)
                .ge(periodStartDate != null, PlatformDealFeeDtl::getGmtCreate, periodStartDate)  // 大于等于季度开始日期
                .le(periodEndDate != null, PlatformDealFeeDtl::getGmtCreate, periodEndDate)
                .orderByAsc(PlatformDealFeeDtl::getGmtCreate)
                .list();
        List<String> operateIds =  dealFeeDtlList.stream().map(PlatformDealFeeDtl::getPlatformDealFeeDtlId).collect(Collectors.toList());
        List<PlatformBalanceOperate> records = platformBalanceOperateService.lambdaQuery()
                .in(PlatformBalanceOperate::getRelevanceId, operateIds)
                .eq(PlatformBalanceOperate::getIsDelete, 0)
                .orderByDesc(PlatformBalanceOperate::getGmtCreate)
                .list();
        return R.success(records);
    }


}

