package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.content.UpdateContentStateDTO;
import scrbg.meplat.mall.entity.Content;
import scrbg.meplat.mall.entity.ContentLog;
import scrbg.meplat.mall.mapper.ContentLogMapper;
import scrbg.meplat.mall.service.ContentService;
import scrbg.meplat.mall.service.FileService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @描述：内容控制类
 * @作者: sund
 * @日期: 2022-11-07
 */
@RestController
@RequestMapping("/platform/richContent")
@ApiSort(value = 500)
@Api(tags = "内容")
public class ContentController {
    @Autowired
    FileService fileService;

    @Autowired
    MallConfig mallConfig;
    @Autowired
    public ContentService contentService;

    @GetMapping("/findByProgramaKey")
    @ApiOperation(value = "ProgramaKey")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "programaKey", value = "programaKey", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Content> findByProgramaKey(String programaKey) {
        List<Content> results = contentService.findAllContent(programaKey,null);
        if(CollectionUtils.isEmpty(results)){
            return R.success();
        }
        return R.success(results.get(0));
    }


    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Content> findById(String id) {
        Content content = contentService.getById(id);
        return R.success(content);
    }

    @GetMapping("/findListByContentId/{id}")
    public R<List<ContentLog>> findListByContentId(@PathVariable("id")String id) {
        List<ContentLog> content = contentService.findListByContentId(id);
        return R.success(content);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody Content content) {
        QueryWrapper<Content> wrapper = new QueryWrapper();
        wrapper.eq("title", content.getTitle());
        wrapper.eq("programa_key", content.getProgramaKey());
        wrapper.eq("mall_type", mallConfig.mallType);
        List<Content> results = contentService.list(wrapper);
        if (results.size() > 0) {
            if (StringUtils.isNotEmpty(content.getContentId()) && !results.get(0).getContentId().equals(content.getContentId())) {
                return R.failed(400, "标题重复");
            }
            if (StringUtils.isEmpty(content.getContentId())) {
                return R.failed(400, "标题重复");
            }
        }
        content.setMallType(mallConfig.mallType);
        //设置发布时间，发布为发布中
        if (content.getGmtRelease()!=null){
            content.setState(1);
        }else {
            content.setState(2);
        }
        contentService.saveContent(content);

        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody Content content) {
        QueryWrapper<Content> wrapper = new QueryWrapper();
        if (content.getTitle()!=null){
            wrapper.eq("title", content.getTitle());
        }
        wrapper.eq("programa_key", content.getProgramaKey());
        wrapper.eq("mall_type", mallConfig.mallType);
        List<Content> results = contentService.list(wrapper);
        if (results.size() > 0) {
            if (StringUtils.isNotEmpty(content.getContentId()) && !results.get(0).getContentId().equals(content.getContentId())) {
                return R.failed(400, "标题重复");
            }
//            if (StringUtils.isEmpty(content.getContentId())) {
//                return R.failed(400, "标题重复");
//            }

        }
        content.setMallType(mallConfig.mallType);
        content.setState(2);
        if(StringUtils.isEmpty(content.getContentId())){
            contentService.saveContent(content);
        }else {
            contentService.updateContent(content);
        }
        return R.success();
    }



    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        contentService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        contentService.removeByIds(ids);
        return R.success();
    }

    /**
     * Description: 批量导入内容
     *
     * @since JDK 1.8
     */
    @PostMapping("/importBatchContent")
    @ApiOperation(value = "批量导入内容")
    public R importBatchContent(@RequestBody List<Content> contents) {
        contentService.importBatchContent(contents);
        return R.success();
    }

    /**
     * Description: 根据内容id修改内容信息
     * date: 2022/11/7 14:08
     */
    @PostMapping("/updateContentInfo")
    @ApiOperation(value = "根据内容id修改内容信息")
    public R updateContentInfo(@RequestBody List<Content> contents) {
        contentService.updateBatchById(contents);
        return R.success();
    }

    /**
     * Description:  根据ids进行逻辑删除
     * date: 2022/11/7 14:24
     */
    @PostMapping("/deleteBatchContent")
    @ApiOperation(value = "根据主键批量逻辑删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "ids", value = "IDS", required = true,
//                    dataType = "list", paramType = "query")
//    })
    public R deleteBatchContent(@RequestBody List<String> ids) {
        contentService.removeLogicBatch(ids);
        return R.success();
    }

    /**
     * Description:  根据内容id集合批量修改内容发布状态
     * date: 2022/11/7 15:40
     */
    @PostMapping("/updateContentState")
    @ApiOperation(value = "根据内容id集合批量修改内容发布状态")
    public R updateContentState(@Valid @RequestBody UpdateContentStateDTO dto) {
        contentService.updateContentState(dto);
        return R.success();
    }


    /**
     * 批量发布
     *
     * @param ids
     * @return
     */
    @PostMapping("/updateByPublish")
    @ApiOperation(value = "批量发布")
    public R updatePublish(@RequestBody List<String> ids) {
        contentService.updateByPublish(ids, "1");
        return R.success();
    }

    /**
     * 批量取消发布
     *
     * @param ids
     * @return
     */
    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量取消发布")
    @ApiImplicitParam(name = "type", value = "1：发布  2：未发布", required = true,
            dataType = "Integer", paramType = "query")
    public R updateNotPublish(@RequestBody List<String> ids) {
        contentService.updateByPublish(ids, "2");
        return R.success();
    }

    /**
     * 通过图片条件获取该条件所有图片并分页
     *
     * @param
     * @return
     */
    @PostMapping("/findByConditionByPage")
    @ApiOperation(value = "根据条件获取图片并分页")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "title", value = "标题名称", dataTypeClass = String.class),
            @DynamicParameter(name = "home", value = "首页显示", dataTypeClass = Integer.class),
            @DynamicParameter(name = "top", value = "顶部显示", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "发布状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "mallType", value = "商城类型", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<Content> findByConditionByPage(@RequestBody JSONObject jsonObject) {
        PageUtils page = contentService.queryContentPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }
}

