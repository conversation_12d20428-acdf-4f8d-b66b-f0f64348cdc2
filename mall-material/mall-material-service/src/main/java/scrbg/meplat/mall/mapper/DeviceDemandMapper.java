package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.DeviceDemand;
import scrbg.meplat.mall.vo.product.website.demand.DeviceDemandHomeVO;

import java.util.List;
import java.util.Map;

/**
 * @描述：设备需求 Mapper 接口
 * @作者: y
 * @日期: 2022-11-21
 */
@Mapper
@Repository
public interface DeviceDemandMapper extends BaseMapper<DeviceDemand> {

    /**
     * 获取前台需求列表
     *
     * @param pages
     * @param innerMap
     * @return
     */
    List<DeviceDemandHomeVO> listHomeDemandPage(Page<DeviceDemandHomeVO> pages, @Param("dto") Map<String, Object> innerMap);

    /**
     * 获取前台首页列表
     * @param innerMap
     * @return
     */
    int listHomeDemandPageCount(@Param("dto") Map<String, Object> innerMap);

    List<DeviceDemandHomeVO> attendList(@Param("dto") Map<String, Object> innerMap);

    List<DeviceDemand> attendLists(IPage<DeviceDemand> pages, @Param("dto") Map<String, Object> innerMap);
}
