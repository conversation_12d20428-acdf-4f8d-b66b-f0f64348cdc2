package scrbg.meplat.mall.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：平台交易费明细
 * @作者: ye
 * @日期: 2024-01-24
 */
@ApiModel(value = "平台交易费明细")
@Data
@TableName("platform_deal_fee_dtl")
public class PlatformDealFeeDtl extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "平台交易费明细id")
    private String platformDealFeeDtlId;

    @ApiModelProperty(value = "平台计费id")
    private String platformDealFeeId;

    @ApiModelProperty(value = "关联id")
    private String relevanceId;

    @ApiModelProperty(value = "关联编号")
    private String relevanceNu;

    @ApiModelProperty(value = "关联类型（1对账单）")
    private Integer relevanceType;

    @ApiModelProperty(value = "服务类型（1店铺交易服务费2合同履约服务费用）")
    private Integer serveType;

    @ApiModelProperty(value = "服务类型（店铺交易服务费合同履约服务费用）")
    @TableField(exist = false)
    private String serveTypeStr;

    public String getServeTypeStr() {
        if (serveType == 1) {
            return "店铺交易服务费";
        } else if (serveType == 2) {
            return "合同履约服务费用";
        } else {
            return null;
        }
    }

    @ApiModelProperty(value = "是否欠费")
    @TableField(exist = false)
    private String finishPayFeeStr;

    public String getFinishPayFeeStr() {
        if (finishPayFee == 1) {
            return "否";
        } else {
            return "是";
        }
    }

    @ApiModelProperty(value = "采购单位企业id")
    private String projectEnterpriseId;

    @ApiModelProperty(value = "采购单位企业名称")
    private String projectEnterpriseName;

    @ApiModelProperty(value = "企业id")
    private String enterpriseId;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "服务费用（计算出来的费用、超出免费额度的费用）")
    private BigDecimal serveFee;

    @ApiModelProperty(value = "收取比例（‰）")
    private BigDecimal feeRatio;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal dealAmount;

    @ApiModelProperty(value = "已缴费金额")
    private BigDecimal payFee;

    @ApiModelProperty(value = "剩余缴费金额")
    private BigDecimal residuePayFee;

    @ApiModelProperty(value = "平台免费交易额度")
    private BigDecimal dealFeeAmount;

    @ApiModelProperty(value = "缴费使用余额金额")
    private BigDecimal useBalance;

    @ApiModelProperty(value = "需缴费交易总交易金额")
    private BigDecimal exceedFree;

    @ApiModelProperty(value = "占用免费总交易金额")
    private BigDecimal useExemptFree;

    @ApiModelProperty(value = "可欠费额度")
    private BigDecimal dealFeeQuota;

    @ApiModelProperty(value = "可欠费时长")
    private Integer feeEndDateNum;

    @ApiModelProperty(value = "可欠费时长类型(1天2月3年)")
    private Integer feeEndDateType;

    @ApiModelProperty(value = "缴费类型（1正常缴费2免费额度）")
    private Integer payType;

    @ApiModelProperty(value = "缴费类型（1正常缴费2免费额度）")
    @TableField(exist = false)
    private String payTypeStr;

    public String getPayTypeStr() {
        if (payType == 1) {
            return "正常缴费";
        } else {
            return "免费额度";
        }
    }

    @ApiModelProperty(value = "是否已完成缴费 1是  0 否")
    private Integer finishPayFee;

    @ApiModelProperty(value = "缴费状态（0初始1已选择缴费）（废弃）")
    private Integer payFeeState;

    @ApiModelProperty(value = "状态（0正常1对账单已作废）")
    private Integer state;

    @ApiModelProperty(value = "作废明细ID（记录被作废的交易明细id），仅存在作废时候才存在")
    private String prePlatformDealFeeDtlId;

    @ApiModelProperty(value = "乐观锁")
    @Version
    private Integer version;

    @ApiModelProperty(value = "总费用金额")
    @TableField(exist = false)
    private BigDecimal totalFree;

    @ApiModelProperty(value = "总欠费金额")
    @TableField(exist = false)
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "不含税金额")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;
}