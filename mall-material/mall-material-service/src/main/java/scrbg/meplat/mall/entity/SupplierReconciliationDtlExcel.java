package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @描述：物资验收明细
 * @作者: ye
 * @日期: 2023-08-16
 */
@ApiModel(value = "二级对账明细")
@Data
@TableName("supplier_reconciliation_dtl")
public class SupplierReconciliationDtlExcel extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "对账明细ID")
    private String dtlId;

    @TableField(exist = false)
    @ApiModelProperty(value = "序号")
    private int index;

    @ApiModelProperty(value = "对账ID")
    private String billId;

    @ApiModelProperty(value = "对账编号")
    private String billNo;

    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "订单项id")
    private String orderItemId;

    @ApiModelProperty(value = "物资id")
    private String materialId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "物资名称")
    private String materialName;

    @ApiModelProperty(value = "规格型号")
    private String spec;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "网价")
    private BigDecimal netPrice;

    @ApiModelProperty(value = "出厂价")
    private BigDecimal outFactoryPrice;

    @ApiModelProperty(value = "固定价")
    private BigDecimal fixationPrice;

    @ApiModelProperty(value = "运费")
    private BigDecimal transportPrice;

    @ApiModelProperty(value = "材质")
    private String texture;

    @ApiModelProperty(value = "对账单价（不含税）")
    private BigDecimal noRatePrice;

    @ApiModelProperty(value = "对账单价（不含税）")
    private BigDecimal ratePrice;

    @ApiModelProperty(value = "对账单价")
    private BigDecimal price;

    @ApiModelProperty(value = "对账数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "对账总金额（不含税）")
    private BigDecimal noRateAmount;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "对账总金额（含税）")
    private BigDecimal rateAmount;

    @ApiModelProperty(value = "状态")
    private Integer state;

    @ApiModelProperty(value = "是否对账")
    private Integer isReconciliation;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "原单id")
    private String sourceDtlId;

    @ApiModelProperty(value = "业务类型(1发货单对账，2退货单对账)")
    private Integer reconciliationType;

    @ApiModelProperty(value = "供应商")
    @TableField(exist = false)
    private String twoSupplierOrgId;

    @ApiModelProperty(value = "二级供应商")
    @TableField(exist = false)
    private String supplierOrgId;

    @ApiModelProperty(value = "单据日期")
    private Date receivingDate;

    @ApiModelProperty(value = "单据日期")
    @TableField(exist = false)
    private String receivingDateStr;

    @ApiModelProperty(value = "物资类别id(1级类别id/2级类别id/..)")
    private String materialClassId;

    @ApiModelProperty(value = "物资类别名称(1级类别名称/2级类别名称/..)")
    private String materialClassName;

    @ApiModelProperty(value = "二级供应商")
    @TableField(exist = false)
    private BigDecimal taxRate;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "pcwp收料单编号")
    private String receiptBillSn;
}
