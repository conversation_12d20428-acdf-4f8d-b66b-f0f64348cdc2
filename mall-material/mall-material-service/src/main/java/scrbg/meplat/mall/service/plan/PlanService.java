package scrbg.meplat.mall.service.plan;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;

import scrbg.meplat.mall.dto.plan.PlanDTO;
import scrbg.meplat.mall.dto.plan.PlanExcelDto;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.entity.plan.PlanDetail;
import scrbg.meplat.mall.vo.product.ShoppingCartShopIdVO;
/**
 * @描述：采购计划主表 服务类
 * @作者: ye
 * @日期: 2025-05-27
 */
public interface PlanService extends IService<Plan> {

        PageUtils<Plan> queryPage(PlanDTO dto);

        void create(Plan plan);

        void update(Plan plan);

        Plan getById(String id);

        Plan getByBillId(String billId);

        void delete(String id);

        void deletePlanAndDetails(String id);

        void deleteBatch(List<String> ids);

        Plan mapToPlan(List<ShoppingCartShopIdVO> cartVOS);

        void savePlanAndDetails(Plan plan, List<PlanDetail> planDetails);

        PlanExcelDto mapToPlanExcel(Plan plan);
}
