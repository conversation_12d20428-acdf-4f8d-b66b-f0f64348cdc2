package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.product.CreateBatchByBillIdDTO;
import scrbg.meplat.mall.dto.product.UpdateProductInventoryStateDTO;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.ProductInventory;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ProductInventoryMapper;
import scrbg.meplat.mall.service.ProductInventoryService;
import scrbg.meplat.mall.service.ProductService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @描述：商品库 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class ProductInventoryServiceImpl extends ServiceImpl<ProductInventoryMapper, ProductInventory> implements ProductInventoryService {

    @Autowired
    ProductInventoryMapper productInventoryMapper;
    @Autowired
    public MallConfig mallConfig;
    @Autowired
    ProductService productService;




    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductInventory> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer productType = (Integer) innerMap.get("productType");
        String keywords = (String) innerMap.get("keywords");
        String productTitle = (String) innerMap.get("productTitle");
        String classId = (String) innerMap.get("classId");
        Integer state = (Integer) innerMap.get("state");
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        q.eq(ProductInventory::getIsDelete, PublicEnum.IS_DELETE_NO.getCode());
        q.eq(ProductInventory::getMallType, mallConfig.mallType);
        q.eq(StringUtils.isNotBlank(classId),ProductInventory::getClassId, classId);
        q.eq( ProductInventory::getProductType, PublicEnum.TYPE_MATERIAL.getCode());
        q.like(StringUtils.isNotBlank(keywords), ProductInventory::getProductName, keywords);
        q.like(StringUtils.isNotBlank(productTitle), ProductInventory::getProductName, productTitle);
        q.eq(state != null, ProductInventory::getState, state);
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), ProductInventory::getGmtCreate, startDate, endDate);
        q.orderByDesc(orderBy != null && orderBy == 1, ProductInventory::getSort,ProductInventory::getGmtCreate);
        q.orderByDesc(orderBy != null && orderBy == 2, ProductInventory::getGmtCreate);
        IPage<ProductInventory> page = this.page(
                new Query<ProductInventory>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ProductInventory productInventory) {
        Integer count = lambdaQuery()
                .eq(ProductInventory::getProductName, productInventory.getProductName())
                .eq(ProductInventory::getMallType, mallConfig.mallType)
                .eq(ProductInventory::getProductType, productInventory.getProductType())
                .eq(ProductInventory::getIsDelete, PublicEnum.IS_DELETE_NO.getCode()).count();
        if(count > 0 ){
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品名称重复！");
        }
        //调用父类方法即可
        //也可以baseMapper.insert
        productInventory.setState(PublicEnum.STATE_STOP.getCode());
        boolean save = save(productInventory);
        if(!save){
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "新增失败！");
        }
    }

    @Override
    public void update(ProductInventory productInventory) {
        Integer count = lambdaQuery()
                .eq(ProductInventory::getProductName, productInventory.getProductName())
                .eq(ProductInventory::getProductType, productInventory.getProductType())
                .eq(ProductInventory::getMallType, mallConfig.mallType)
                .ne(ProductInventory::getProductInventoryId,productInventory.getProductInventoryId())
                .eq(ProductInventory::getIsDelete, PublicEnum.IS_DELETE_NO.getCode()).count();
        if(count > 0 ){
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品名称重复！");
        }
        boolean b = super.updateById(productInventory);
        if(!b){
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "修改失败！");
        }
    }


    @Override
    public ProductInventory getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 根据物资id集合批量导入到商品库
     *
     * @param billIds
     * @return
     */
    @Override
    @Transactional()
    public void createBatchByBillIds(String[] billIds) {
//        R<List<BaseLibrary>> baseLibraryR = iMaterialFengin.baseLibraryListByIds(billIds);
//        List<BaseLibrary> data = baseLibraryR.getData();
//        if (!CollectionUtils.isEmpty(data)) {
//            data.stream().forEach((entity) -> {
//                ProductInventory p = new ProductInventory();
//                BeanUtils.copyProperties(entity, p);
//                p.setProductName(entity.getMaterialName());
//                p.setRelevanceId(entity.getBillId());
//                p.setProductType(PublicEnum.TYPE_MATERIAL.getCode());
//                p.setState(PublicEnum.STATE_STOP.getCode());
//                p.setGmtCreate(null);
//                p.setGmtModified(null);
//                p.setFounderId(null);
//                p.setFounderName(null);
//                baseMapper.insert(p);
//            });
//        }
    }

    /**
     * 根据ids批量逻辑删除
     *
     * @param ids
     */
    @Override
    public void removeLogicBatch(List<String> ids) {
        for (String id : ids) {
            Integer count = productService.lambdaQuery().eq(Product::getProductInventoryId, id).eq(Product::getIsDelete, PublicEnum.IS_DELETE_NO.getCode()).count();
            if (count > 0) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "该商品库商品有对应的店铺商品正在使用不能删除！");
            }
        }
        lambdaUpdate().in(ProductInventory::getProductInventoryId, ids).set(ProductInventory::getIsDelete, PublicEnum.IS_DELETE_YES.getCode()).update();
    }

    /**
     * 批量修改商品状态
     * @param dto
     */
    @Override
    public void updateProductInventoryState(UpdateProductInventoryStateDTO dto) {
        List<String> ids = dto.getProductInventoryIds();
        int state = dto.getState();
        // 启用
        int code1 = PublicEnum.STATE_OPEN.getCode();
        // 停用
        int code0 = PublicEnum.STATE_STOP.getCode();
        if(state == code0){
            boolean update = lambdaUpdate().in(ProductInventory::getProductInventoryId, ids).set(ProductInventory::getState, code0).update();
            if(!update){
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "修改失败！");
            }
        }
        if(state == code1){
            boolean update = lambdaUpdate().in(ProductInventory::getProductInventoryId, ids).set(ProductInventory::getState, code1).update();
            if(!update){
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "修改失败！");
            }
        }
    }

    /**
     * 批量导入物资到商品库
     * @param dtos
     */
    @Override
    public void importBatch(List<CreateBatchByBillIdDTO> dtos) {
        List<ProductInventory> list = new ArrayList<>();
        for (CreateBatchByBillIdDTO dto : dtos) {
            Integer count = lambdaQuery().eq(ProductInventory::getRelevanceId, dto.getBillId())
                    .eq(ProductInventory::getProductType, PublicEnum.TYPE_MATERIAL.getCode()).count();
            if(count > 0) continue;
            ProductInventory p = new ProductInventory();
            BeanUtils.copyProperties(dto,p);
            p.setProductName(dto.getMaterialName());
            p.setProductTitle(dto.getMaterialName());
            p.setSpecTitle(dto.getSpec());
            p.setRelevanceId(dto.getBillId());
            p.setRelevanceNo(dto.getBillNo());
            p.setProductType(PublicEnum.TYPE_MATERIAL.getCode());
            p.setState(PublicEnum.STATE_STOP.getCode());
            list.add(p);
        }
       saveBatch(list);
    }


}
