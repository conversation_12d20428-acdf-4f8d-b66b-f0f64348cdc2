package scrbg.meplat.mall.enums.product;

/**
 * <AUTHOR>
 * @create 2022-11-03 10:55
 */
public enum ProductEnum {


    RESULT_CODE_500101(500101,"商品不存在！"),
    RESULT_CODE_500102(500102,"商品已下架！"),

    STATE_STAY_PUTAWAY(0,"待上架"),
    STATE_PUTAWAY(1,"已上架"),
    STATE_SOLD_OUT(2,"已下架"),

    IS_COMPLETION_1(1,"是"),
    IS_COMPLETION_0(0,"否");
    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    ProductEnum() {
    }

    ProductEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
