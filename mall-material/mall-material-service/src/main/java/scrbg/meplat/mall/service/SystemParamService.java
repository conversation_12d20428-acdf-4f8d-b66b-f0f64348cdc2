package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SystemParam;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @描述：系统参数表 服务类
 * @作者: sund
 * @日期: 2022-12-05
 */
public interface SystemParamService extends IService<SystemParam> {
    PageUtils SysQueryPage(JSONObject jsonObject, LambdaQueryWrapper<SystemParam> queryWrapper);
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SystemParam> queryWrapper);

    void create(SystemParam systemParam);
    void createSysParam(SystemParam systemParam);

    void update(SystemParam systemParam);

    void updateByBatch(List<String> ids);

    SystemParam getById(String id);

    void delete(String id);

    void batchDelete(List<String> ids);

    /**
     * 根据编码获取参数列表
     * @param code
     * @param size
     * @return
     */
    List<SystemParam> listByCode(String code, Integer size);

    /**
     * 获取所有系统参数
     *
     * @return 系统参数列表
     */
    List<SystemParam> listAllParams();

    /**
     * 修改首页统计
     */
    void updateIndexParameterCount();

    double listByCode(String excessNum);

    //获取当前机构是否为自营店数据
    Boolean getIsBusinessOrg(String org);
}
