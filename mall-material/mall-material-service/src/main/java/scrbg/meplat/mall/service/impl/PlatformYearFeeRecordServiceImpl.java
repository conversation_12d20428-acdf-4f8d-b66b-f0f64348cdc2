package scrbg.meplat.mall.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

import scrbg.meplat.mall.common.constant.FeeConstants;
import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.fee.FeeManagementQueryDto;
import scrbg.meplat.mall.dto.fee.FeeManagementResultVO;
import scrbg.meplat.mall.dto.fee.PlatformYearFeeRecordDto;
import scrbg.meplat.mall.dto.free.FeeInputTemplateDTO;
import scrbg.meplat.mall.dto.free.FeeInputTemplateVO;
import scrbg.meplat.mall.dto.free.MyQueryPayFreeListByEntity;
import scrbg.meplat.mall.entity.AuditRecord;
import scrbg.meplat.mall.entity.Contract;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.PlatformDealFeeDtl;
import scrbg.meplat.mall.entity.PlatformDealFeeRecord;
import scrbg.meplat.mall.entity.PlatformFeeFile;
import scrbg.meplat.mall.entity.PlatformYearFee;
import scrbg.meplat.mall.entity.PlatformYearFeeRecord;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.PlatformDealFeeDtlMapper;
import scrbg.meplat.mall.mapper.PlatformYearFeeRecordMapper;
import scrbg.meplat.mall.service.AuditRecordService;
import scrbg.meplat.mall.service.ContractService;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.PlatformDealFeeRecordService;
import scrbg.meplat.mall.service.PlatformFeeFileService;
import scrbg.meplat.mall.service.PlatformYearFeeDtlService;
import scrbg.meplat.mall.service.PlatformYearFeeRecordService;
import scrbg.meplat.mall.service.PlatformYearFeeService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.SystemParamService;
import scrbg.meplat.mall.util.SpringBeanUtil;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;

/**
 * @描述：年费缴费记录 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
@Service
public class PlatformYearFeeRecordServiceImpl extends ServiceImpl<PlatformYearFeeRecordMapper, PlatformYearFeeRecord> implements PlatformYearFeeRecordService {

    @Autowired
    private ContractService contractService;

    @Autowired
    private SystemParamService systemParamService;

    @Autowired
    private PlatformYearFeeDtlService platformYearFeeDtlService;

    @Autowired
    private PlatformYearFeeService platformYearFeeService;

    @Autowired
    private ProductService productService;

    @Autowired
    PlatformYearFeeRecordMapper platformYearFeeRecordMapper;

    @Override
    public PageUtils platformQueryListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformYearFeeRecord> q) {

        List<Integer> states = (ArrayList<Integer>) jsonObject.get("states");
        String shopName = (String) jsonObject.get("shopName");
        String enterpriseName = (String) jsonObject.get("enterpriseName");
        String paymentRecordUn = (String) jsonObject.get("paymentRecordUn");
        String keywords = (String) jsonObject.get("keywords");
        String startGmtCreate = (String) jsonObject.get("startGmtCreate");
        String endGmtCreate = (String) jsonObject.get("endGmtCreate");
        String startGmtModified = (String) jsonObject.get("startGmtModified");
        String endGmtModified = (String) jsonObject.get("endGmtModified");
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        Integer recordType = (Integer) jsonObject.get("recordType");
        String startAuditTime = (String) jsonObject.get("startAuditTime");
        String endAuditTime = (String) jsonObject.get("endAuditTime");

        q.orderByDesc(orderBy == 0, PlatformYearFeeRecord::getGmtCreate);
        q.orderByDesc(orderBy == 1, PlatformYearFeeRecord::getGmtModified);
        q.orderByDesc(orderBy == 2, PlatformYearFeeRecord::getAuditOpenTime);
        q.between(StringUtils.isNotEmpty(startGmtCreate) && StringUtils.isNotEmpty(endGmtCreate), PlatformYearFeeRecord::getGmtCreate, startGmtCreate, endGmtCreate);
        q.between(StringUtils.isNotEmpty(startGmtModified) && StringUtils.isNotEmpty(endGmtModified), PlatformYearFeeRecord::getGmtModified, startGmtModified, endGmtModified);
        q.between(StringUtils.isNotEmpty(startAuditTime) && StringUtils.isNotEmpty(endAuditTime), PlatformYearFeeRecord::getAuditOpenTime, startAuditTime, endAuditTime);

        q.eq(StringUtils.isNotBlank(shopName), PlatformYearFeeRecord::getShopName, shopName);
        q.eq(StringUtils.isNotBlank(paymentRecordUn), PlatformYearFeeRecord::getPaymentRecordUn, paymentRecordUn);
        q.eq(StringUtils.isNotBlank(enterpriseName), PlatformYearFeeRecord::getEnterpriseName, enterpriseName);
        q.eq(recordType != null, PlatformYearFeeRecord::getRecordType, recordType);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(PlatformYearFeeRecord::getShopName, keywords)
                        .or()
                        .like(PlatformYearFeeRecord::getEnterpriseName, keywords)
                        .or()
                        .like(PlatformYearFeeRecord::getPaymentRecordUn, keywords);
            });
        }
        q.ne(PlatformYearFeeRecord::getState, 0); // 不包括草稿
        q.in(!CollectionUtils.isEmpty(states), PlatformYearFeeRecord::getState, states);

        IPage<PlatformYearFeeRecord> page = this.page(
                new Query<PlatformYearFeeRecord>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Override
    public void create(PlatformYearFeeRecord platformYearFeeRecord) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(platformYearFeeRecord);
    }

    @Override
    public void update(PlatformYearFeeRecord platformYearFeeRecord) {
        super.updateById(platformYearFeeRecord);
    }


    @Override
    public PlatformYearFeeRecord getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        PlatformYearFeeRecord byId = getById(id);
        if (byId.getState() == 1 || byId.getState() == 2) {
            return;
        }
        super.removeById(id);
//        删除附件
        platformFeeFileService.lambdaUpdate().eq(PlatformFeeFile::getRelevanceId, id)
                .eq(PlatformFeeFile::getRelevanceType, 1).remove();

        auditRecordService.lambdaUpdate().eq(AuditRecord::getRelevanceId, id)
                .eq(AuditRecord::getRelevanceType, 10).remove();
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    /**
     * 查詢我的繳費记录
     *
     * @return
     */
    @Override
    public IPage<PlatformYearFeeRecord> myQueryListByEntity(PlatformYearFeeRecordDto pyfrDto) {
        Page<PlatformYearFeeRecord> page = new Page<>(pyfrDto.getPage(), pyfrDto.getLimit());

        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        LambdaQueryChainWrapper<PlatformYearFeeRecord> wrapper = this.lambdaQuery();
        wrapper.eq(PlatformYearFeeRecord::getEnterpriseId, enterpriseId)
                .le(pyfrDto.getStartserveEndTime() != null, PlatformYearFeeRecord::getServeEndTime, pyfrDto.getStartserveEndTime())
                .ge(pyfrDto.getEndserveEndTime() != null, PlatformYearFeeRecord::getServeEndTime, pyfrDto.getEndserveEndTime());
        wrapper.orderByDesc(PlatformYearFeeRecord::getServeStartTime);
        return wrapper.page(page);
    }

    @Autowired
    private PlatformFeeFileService platformFeeFileService;

    @Autowired
    private AuditRecordService auditRecordService;

    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    /**
     * 供应商新增缴费记录
     *
     * @param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String supplierCreateFee(PlatformYearFeeRecord p) {
        p.setPaymentRecordUn(IdWorker.getIdStr());
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        p.setShopId(user.getShopId());
        p.setShopName(user.getShopName());
        p.setEnterpriseId(user.getEnterpriseId());
        EnterpriseInfo byId = enterpriseInfoService.getById(user.getEnterpriseId());
        if (byId.getShuDaoFlag() == 1 || byId.getInteriorId() != null) {
            throw new BusinessException(500, "蜀道或内部企业无需缴费！");
        }
        p.setEnterpriseName(user.getEnterpriseName());
        p.setState(0);
        if (p.getSubmitAud() == 1) {
            p.setState(1);
        }
        save(p);
        for (PlatformFeeFile platformFeeFile : p.getFiles()) {
            platformFeeFile.setRelevanceId(p.getPaymentRecordId());
            platformFeeFile.setRelevanceType(1);
            platformFeeFileService.save(platformFeeFile);
        }
        return p.getPaymentRecordUn();
    }

    /**
     * 根据编号查询
     *
     * @param s
     * @return
     */
    @Override
    public PlatformYearFeeRecord findBySn(String s) {
        PlatformYearFeeRecord one = lambdaQuery().eq(PlatformYearFeeRecord::getPaymentRecordUn, s).one();
        if (one == null) {
            return one;
        } else {
            List<PlatformFeeFile> list = platformFeeFileService.lambdaQuery().eq(PlatformFeeFile::getRelevanceId, one.getPaymentRecordId())
                    .eq(PlatformFeeFile::getRelevanceType, 1)
                    .list();
            one.setFiles(list);
            List<AuditRecord> list1 = auditRecordService.lambdaQuery().eq(AuditRecord::getRelevanceId, one.getPaymentRecordId())
                    .eq(AuditRecord::getRelevanceType, 10).list();
            one.setAuditRecords(list1);
            fillPreviousFeeInfo(one);//上期年费信息(上期年费有效期截止日期,上期年费状态)
            return one;
        }
    }

    /**
     * 填充上期年费信息
     * @param record 当前年费缴费记录
     */
//    public void fillPreviousFeeInfo(PlatformYearFeeRecord record) {
//        try {
//            // 判断是否为首次入驻
//            if (record.getPayStatus() != null && record.getPayStatus() == 0) {
//                // 首次入驻
//                record.setPreviousFeeEndDate("无记录");
//                record.setPreviousFeeStatus("首次入驻");
//                return;
//            }
//
//            // 非首次入驻，需要查询上期年费信息
//            LambdaQueryWrapper<PlatformYearFee> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(PlatformYearFee::getShopId, record.getShopId());
//            queryWrapper.eq(PlatformYearFee::getServeType, record.getRecordType()); // 确保类型一致
//            queryWrapper.eq(PlatformYearFee::getState, 2); // 状态2表示审核通过
//            queryWrapper.eq(PlatformYearFee::getIsDelete, 0);
//            queryWrapper.orderByDesc(PlatformYearFee::getServeEndTime);
//            queryWrapper.last("LIMIT 1");
//
//            // 使用platformYearFeeService而不是platformYearFeeMapper
//            PlatformYearFee previousFee = platformYearFeeService.getOne(queryWrapper);
//
//            if (previousFee != null) {
//                // 格式化日期
//                if (previousFee.getServeEndTime() != null) {
//                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//                    record.setPreviousFeeEndDate(previousFee.getServeEndTime().format(formatter));
//                } else {
//                    record.setPreviousFeeEndDate("无记录");
//                }
//                // 设置状态
//                record.setPreviousFeeStatus(previousFee.getOutTime() == 1 ? "正常" : "已到期");
//            } else {
//                // 没有找到上期记录（可能是数据异常）
//                record.setPreviousFeeEndDate("无记录");
//                record.setPreviousFeeStatus("数据异常");
//                log.warn("没有找到店铺ID为"+record.getShopId()+"的上期年费记录");
//            }
//        } catch (Exception e) {
//            // 发生异常时设置默认值
//            record.setPreviousFeeEndDate("无记录");
//            record.setPreviousFeeStatus("数据异常");
//            log.error("获取上期年费信息失败", e);
//        }
//    }

    /**
     * 填充上期年费信息
     *
     * @param record 当前年费缴费记录
     */
    public void fillPreviousFeeInfo(PlatformYearFeeRecord record) {
        try {
            // 判断是否为首次入驻（根据payStatus字段）
            if (record.getPayStatus() != null && record.getPayStatus() == 0) {
                // 首次入驻
                record.setPreviousFeeEndDate("无记录");
                record.setPreviousFeeStatus("首次入驻");
                return;
            }

            // 非首次入驻，查询上期年费信息
            // 查询该店铺所有年费记录，按创建时间排序
            List<PlatformYearFeeRecord> allRecords = lambdaQuery().eq(PlatformYearFeeRecord::getShopId, record.getShopId())
                    .eq(PlatformYearFeeRecord::getRecordType, record.getRecordType()) // 确保记录类型一致
                    .orderByAsc(PlatformYearFeeRecord::getGmtCreate) // 按创建时间正序排列
                    .list();

            // 如果没有记录或只有一条记录，说明是首次缴费
            if (allRecords.size() <= 1) {
                record.setPreviousFeeEndDate("无记录");
                record.setPreviousFeeStatus("首次缴费");
                return;
            }

            // 找到当前记录在列表中的位置
            int currentIndex = -1;
            for (int i = 0; i < allRecords.size(); i++) {
                if (allRecords.get(i).getPaymentRecordId().equals(record.getPaymentRecordId())) {
                    currentIndex = i;
                    break;
                }
            }

            // 如果找不到当前记录或者是第一条记录，说明是首次缴费
            if (currentIndex <= 0) {
                record.setPreviousFeeEndDate("无记录");
                record.setPreviousFeeStatus("首次缴费");
                return;
            }

            // 获取上一条记录作为上期记录
            PlatformYearFeeRecord previousRecord = allRecords.get(currentIndex - 1);

            // 赋值serveEndTime给previousFeeEndDate
            if (previousRecord.getServeEndTime() != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                record.setPreviousFeeEndDate(previousRecord.getServeEndTime().format(formatter));
            } else {
                record.setPreviousFeeEndDate("无记录");
            }

            // 根据state判断previousFeeStatus
            if (previousRecord.getState() != null && previousRecord.getState() == 2) {
                record.setPreviousFeeStatus("已完成缴费");
            } else {
                record.setPreviousFeeStatus("未完成缴费");
            }

        } catch (Exception e) {
            // 发生异常时设置默认值
            record.setPreviousFeeEndDate("无记录");
            record.setPreviousFeeStatus("数据异常");
            log.error("获取上期年费信息失败", e);
        }
    }

    /**
     * 修改缴费
     *
     * @param p
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void supplierUpdateFee(PlatformYearFeeRecord p) {
        PlatformYearFeeRecord byId = getById(p.getPaymentRecordId());
        if (byId == null || byId.getState() == 1 || byId.getState() == 2) {
            return;
        } else {
            p.setVersion(byId.getVersion());
        }
        if (p.getSubmitAud() == 1) {
            p.setState(1);
        }
        update(p);
        platformFeeFileService.lambdaUpdate().eq(PlatformFeeFile::getRelevanceId, p.getPaymentRecordId())
                .eq(PlatformFeeFile::getRelevanceType, 1).remove();
        List<PlatformFeeFile> files = p.getFiles();
        for (PlatformFeeFile file : files) {
            file.setRelevanceId(p.getPaymentRecordId());
            file.setRelevanceType(1);
            file.setFileId(null);
            platformFeeFileService.save(file);
        }

    }


    /**
     * 审核年费
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditFee(AuditDTO dto) {
        Integer isOpen = dto.getIsOpen();
        String id = dto.getId();
        PlatformYearFeeRecord byId = getById(id);
        if (byId == null || byId.getState() != 1) {
        } else {
            if (isOpen != null && isOpen == 1) {
                AuditRecord auditRecord = new AuditRecord();
                auditRecord.setRelevanceType(10);
                auditRecord.setRelevanceId(id);
                auditRecord.setResultType(1);
                auditRecord.setAuditType(1);
                auditRecord.setAuditResult("【同意】");
                auditRecordService.create(auditRecord);
                byId.setState(2);
                byId.setAuditOpenTime(new Date());

                // 设置服务时间（从AuditDTO传入）
                if (dto.getServeStartTime() != null) {
                    byId.setServeStartTime(dto.getServeStartTime());
                }
                if (dto.getServeEndTime() != null) {
                    byId.setServeEndTime(dto.getServeEndTime());
                }

                update(byId);

                // 未续费
                PlatformYearFee one = platformYearFeeService.lambdaQuery()
                        .eq(PlatformYearFee::getEnterpriseId, byId.getEnterpriseId())
                        .eq(PlatformYearFee::getServeType, byId.getRecordType())
                        .one();
                if (one == null) {
                    PlatformYearFee pt = new PlatformYearFee();
                    pt.setPlatformYearFeeNu(IdWorker.getIdStr());
                    pt.setShopId(byId.getShopId());
                    pt.setShopName(byId.getShopName());
                    pt.setEnterpriseId(byId.getEnterpriseId());
                    pt.setEnterpriseName(byId.getEnterpriseName());
                    pt.setServeType(byId.getRecordType());
                    pt.setOutTime(0);
                    platformYearFeeService.save(pt);
                    one = pt;
                }
                if (byId.getRecordType() == 1) {
                    Integer paymentDuration = byId.getPaymentDuration();
                    Integer paymentDurationType = byId.getPaymentDurationType();
                    if (paymentDurationType == 4) {
                        int day = paymentDuration * 365;
                        if (one.getServeEndTime() == null) {
                            // 获取增加后的日期
                            LocalDate newDate = LocalDate.now().plusDays(day);
                            one.setServeEndTime(newDate);
                            one.setOutTime(1);
                            platformYearFeeService.update(one);
                        } else {
                            LocalDate newDate = one.getServeEndTime().plusDays(day);
                            one.setServeEndTime(newDate);
                            LocalDate currentDate = LocalDate.now();
                            if (currentDate.isAfter(newDate)) { // 之后
                                one.setOutTime(0);
                            } else {
                                one.setOutTime(1); // 未过期
                                if (byId.getRecordType() == 1) {
                                    // 商品恢复
                                    productService.lambdaUpdate().eq(Product::getShopId, byId.getShopId())
                                            .set(Product::getShowState, 0).update();
                                    // 启用店铺
                                    shopService.lambdaUpdate().eq(Shop::getShopId, byId.getShopId())
                                            .set(Shop::getState, 1).update();
                                }
                            }
                            platformYearFeeService.update(one);
                        }
                    } else {
                        throw new BusinessException("当前仅支持年度缴费！");
                    }
                } else if (byId.getRecordType() == 2) {
                    Integer paymentDuration = byId.getPaymentDuration();
                    Integer paymentDurationType = byId.getPaymentDurationType();
                    int day = 0;
                    if (paymentDurationType == 4) {
                        day = paymentDuration * 365;
                    } else {
                        throw new BusinessException("当前仅支持年度缴费！");
                    }
                    if (one == null) {
                        PlatformYearFee platformYearFee = new PlatformYearFee();
                        platformYearFee.setPlatformYearFeeNu(IdWorker.getIdStr());
                        platformYearFee.setEnterpriseId(byId.getEnterpriseId());
                        platformYearFee.setEnterpriseName(byId.getEnterpriseName());
                        platformYearFee.setServeType(2);
                        LocalDate newDate = LocalDate.now().plusDays(day);
                        platformYearFee.setServeEndTime(newDate);
                        platformYearFee.setOutTime(1);
                        platformYearFeeService.save(platformYearFee);
                    } else {
                        LocalDate serveEndTime = one.getServeEndTime();
                        if (serveEndTime == null) serveEndTime = LocalDate.now();
                        LocalDate newDate = serveEndTime.plusDays(day);
                        one.setServeEndTime(newDate);
                        LocalDate currentDate = LocalDate.now();
                        if (currentDate.isAfter(newDate)) { // 之后
                            one.setOutTime(0);
                        } else {
                            one.setOutTime(1); // 未过期
                        }
                        platformYearFeeService.update(one);
                    }
                } else {
                    throw new BusinessException(500, "缴费服务类型错误！");
                }
            } else {
                AuditRecord auditRecord = new AuditRecord();
                auditRecord.setRelevanceType(10);
                auditRecord.setRelevanceId(id);
                auditRecord.setResultType(2);
                auditRecord.setAuditType(1);
                auditRecord.setAuditResult("【拒绝】" + dto.getAuditResult());
                auditRecordService.create(auditRecord);
                byId.setState(3);
                byId.setAuditOpenTime(new Date());
                update(byId);
            }
        }

    }

    /**
     * 供应商查询缴费管理列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils myQueryPayFreeListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformYearFeeRecord> q) {
        Page<MyQueryPayFreeListByEntity> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        IPage<MyQueryPayFreeListByEntity> resIPage = baseMapper.myQueryPayFreeListByEntity(pages, jsonObject);
        return new PageUtils<>(resIPage);
    }

    @Autowired
    PlatformDealFeeRecordService platformDealFeeRecordService;
    @Autowired
    PlatformDealFeeDtlMapper platformDealFeeDtlMapper;

    /**
     * 供应商新增缴费（包括交易和年费）
     *
     * @param p
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createFeeAndDealFree(PlatformYearFeeRecord p) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        p.setPaymentRecordUn(IdWorker.getIdStr());
        EnterpriseInfo byId = enterpriseInfoService.getById(user.getEnterpriseId());
        if (byId.getShuDaoFlag() == 1 || byId.getInteriorId() != null) {
            throw new BusinessException(500, "蜀道或内部企业无需缴费！");
        }
        byId.setZcstate("3");
        enterpriseInfoService.updateById(byId);
        Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, user.getEnterpriseId())
                .select(Shop::getShopId, Shop::getShopName).one();
        p.setShopId(shop.getShopId());
        p.setShopName(shop.getShopName());
        p.setEnterpriseId(user.getEnterpriseId());
        p.setEnterpriseName(user.getEnterpriseName());
//        p.setRemarks(p.getEnterpriseName() + " " + p.getPayAmount() + "（元）");
        p.setState(0);
        if (p.getSubmitAud() == 1) {
            p.setState(1);
        }
        Integer recordType = p.getRecordType();
        if (recordType == 1) {
            Integer count = shopService.lambdaQuery().eq(Shop::getEnterpriseId, user.getEnterpriseId())
                    .eq(Shop::getAuditStatus, 1).count();
            if (count == 0) {
                throw new BusinessException(500, "店铺不存在或店铺未审核通过不能缴费！");
            }
        }
        if (recordType == 1 || recordType == 2) {
            save(p);
            for (PlatformFeeFile platformFeeFile : p.getFiles()) {
                platformFeeFile.setRelevanceId(p.getPaymentRecordId());
                platformFeeFile.setRelevanceType(1);
                platformFeeFileService.save(platformFeeFile);
            }
        }
        if (recordType == 3 || recordType == 4) {
            PlatformDealFeeRecord p2 = new PlatformDealFeeRecord();
            if (recordType == 3) {
                p2.setRecordType(1);
            }
            if (recordType == 4) {
                p2.setRecordType(2);
            }
            // 待缴费金额（不包含作废的）
            QueryWrapper<PlatformDealFeeDtl> q1 = new QueryWrapper<>();
            q1.eq("serve_type", p2.getRecordType())
                    .eq("enterprise_id", p.getEnterpriseId())
                    .eq("finish_pay_fee", 0)
                    .eq("state", 0) // 排除作废
                    .select("sum(residue_pay_fee) as residuePayFee");
            Map<String, Object> map1 = platformDealFeeDtlMapper.selectMaps(q1).get(0);
            BigDecimal residuePayFee = new BigDecimal(0);
            if (map1 != null && map1.get("residuePayFee") != null) {
                residuePayFee = new BigDecimal(map1.get("residuePayFee").toString());
            }
            p2.setPayAmount(p.getPayAmount());
            if (p2.getPayAmount().compareTo(residuePayFee) == 1) {
                throw new BusinessException(500, "缴费金额不能超过总欠费金额！");
            }
            p2.setDealFeeRecordUn(p.getPaymentRecordUn());
            p2.setShopId(p.getShopId());
            p2.setShopName(p.getShopName());
            p2.setEnterpriseId(p.getEnterpriseId());
            p2.setEnterpriseName(p.getEnterpriseName());
            p2.setPayType(p.getPayType());
            p2.setState(p.getState());
            p2.setRemarks(p.getRemarks());
//            p2.setRemarks(p.getEnterpriseName() + " " + p.getPayAmount() + "（元）");
            platformDealFeeRecordService.save(p2);
            for (PlatformFeeFile platformFeeFile : p.getFiles()) {
                platformFeeFile.setRelevanceId(p2.getDealFeeRecordId());
                platformFeeFile.setRelevanceType(2);
                platformFeeFileService.save(platformFeeFile);
            }
        }
        return p.getPaymentRecordUn();
    }

    /**
     * 平台查询缴费记录（包括交易）
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils platformQueryYearAndDealFreeListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformYearFeeRecord> q) {
        Page<MyQueryPayFreeListByEntity> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        IPage<MyQueryPayFreeListByEntity> resIPage = baseMapper.myQueryPayFreeListByEntity(pages, jsonObject);
        return new PageUtils<>(resIPage);
    }

    @Autowired
    ShopService shopService;

    /**
     * 导入缴费信息
     *
     * @param file
     * @param response
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void feeInputData(MultipartFile file, HttpServletResponse response) {
        List<FeeInputTemplateDTO> dtos = null;
        try {
            dtos = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), FeeInputTemplateDTO.class);
        } catch (IOException e) {
            throw new BusinessException(e.getMessage());
        }
        if (CollectionUtils.isEmpty(dtos)) throw new BusinessException(500, "导入数据为空！");
        List<FeeInputTemplateVO> vos = new ArrayList<>();
        PlatformYearFeeRecordService bean = SpringBeanUtil.getBean(PlatformYearFeeRecordService.class);
        for (FeeInputTemplateDTO dto : dtos) {
            FeeInputTemplateVO vo = new FeeInputTemplateVO();
            BeanUtils.copyProperties(dto, vo);
            vo.setResult("成功");
            try {
                bean.feeInputDataOne(dto);
            } catch (Exception e) {
                vo.setResult("失败");
                vo.setFailReason(e.getMessage());
            }
            vos.add(vo);
        }
        if (!CollectionUtils.isEmpty(vos)) {
            try {
                EasyExcelUtils.writeWeb2("导入缴费结果", FeeInputTemplateVO.class, vos, "导入缴费结果", response);
            } catch (IOException e) {
                throw new BusinessException(e.getMessage());
            }
        }

    }


    /**
     * 导入缴费信息
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void feeInputDataOne(FeeInputTemplateDTO dto) {
        Integer recordType = dto.getRecordType();
        dto.setEnterpriseName(dto.getEnterpriseName().trim());
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseName, dto.getEnterpriseName())
                .select(EnterpriseInfo::getEnterpriseId,
                        EnterpriseInfo::getEnterpriseName,
                        EnterpriseInfo::getShuDaoFlag,
                        EnterpriseInfo::getInteriorId).one();
        if (recordType == 1 || recordType == 2) {
            PlatformYearFeeRecord p = new PlatformYearFeeRecord();
            p.setPaymentRecordUn(IdWorker.getIdStr());
            p.setPayAmount(dto.getPayAmount());
            p.setPayType(1);
            p.setPaymentDuration(dto.getPaymentDuration());
            p.setPaymentDurationType(4);
            p.setRecordType(recordType);
            p.setState(1);
            if (enterpriseInfo == null) {
                throw new BusinessException("企业不存在！");
            }
            Shop shop = shopService.lambdaQuery()
                    .eq(Shop::getEnterpriseId, enterpriseInfo.getEnterpriseId())
                    .eq(Shop::getAuditStatus, 1)
                    .select(Shop::getShopId, Shop::getShopName).one();
            if (recordType == 1) {
                if (shop == null) {
                    throw new BusinessException("店铺不存在或店铺未审核通过！");
                }
            }
            if (shop != null) {
                p.setShopId(shop.getShopId());
                p.setShopName(shop.getShopName());
            }
            p.setEnterpriseId(enterpriseInfo.getEnterpriseId());
            p.setEnterpriseName(enterpriseInfo.getEnterpriseName());
            if (enterpriseInfo.getShuDaoFlag() == 1 || enterpriseInfo.getInteriorId() != null) {
                throw new BusinessException("蜀道或内部企业无需缴费！");
            }
            save(p);

            // 自动审核通过

            AuditDTO auditDTO = new AuditDTO();
            auditDTO.setIsOpen(1);
            auditDTO.setId(p.getPaymentRecordId());
            auditFee(auditDTO);

        } else {
            Integer serverType = null;
            if (recordType == 3) serverType = 1;
            if (recordType == 4) serverType = 2;
            PlatformDealFeeRecord p2 = new PlatformDealFeeRecord();
            p2.setRecordType(serverType);
            p2.setPayAmount(dto.getPayAmount());
            p2.setDealFeeRecordUn(IdWorker.getIdStr());
            if (enterpriseInfo == null) {
                throw new BusinessException("企业不存在！");
            }
            // 待缴费金额（不包含作废的）
            QueryWrapper<PlatformDealFeeDtl> q1 = new QueryWrapper<>();
            q1.eq("serve_type", p2.getRecordType())
                    .eq("enterprise_id", enterpriseInfo.getEnterpriseId())
                    .eq("finish_pay_fee", 0)
                    .eq("state", 0) // 排除作废
                    .select("sum(residue_pay_fee) as residuePayFee");
            Map<String, Object> map1 = platformDealFeeDtlMapper.selectMaps(q1).get(0);
            BigDecimal residuePayFee = new BigDecimal(0);
            if (map1 != null && map1.get("residuePayFee") != null) {
                residuePayFee = new BigDecimal(map1.get("residuePayFee").toString());
            }
            if (p2.getPayAmount().compareTo(residuePayFee) == 1) {
                throw new BusinessException("缴费金额不能超过总欠费金额！当前总欠费金额：" + residuePayFee);
            }

            Shop shop = shopService.lambdaQuery()
                    .eq(Shop::getEnterpriseId, enterpriseInfo.getEnterpriseId())
                    .eq(Shop::getAuditStatus, 1)
                    .select(Shop::getShopId, Shop::getShopName).one();
            if (enterpriseInfo.getShuDaoFlag() == 1 || enterpriseInfo.getInteriorId() != null) {
                throw new BusinessException("蜀道或内部企业无需缴费！");
            }
            if (shop != null) {
                p2.setShopId(shop.getShopId());
                p2.setShopName(shop.getShopName());
            }
            p2.setEnterpriseId(enterpriseInfo.getEnterpriseId());
            p2.setEnterpriseName(enterpriseInfo.getEnterpriseName());
            p2.setPayType(1);
            p2.setState(1);
            platformDealFeeRecordService.save(p2);


            // 自动审核过去

            AuditDTO auditDTO = new AuditDTO();
            auditDTO.setIsOpen(1);
            auditDTO.setId(p2.getDealFeeRecordId());
            platformDealFeeRecordService.audit(auditDTO);

        }
    }

    @Override
    public String reCreateContract() {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        EnterpriseInfo byId = enterpriseInfoService.getById(user.getEnterpriseId());
        if (byId.getShuDaoFlag() == 1 || byId.getInteriorId() != null) {
            throw new BusinessException(500, "蜀道或内部企业无需缴费！");
        }
        Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, user.getEnterpriseId())
                .select(Shop::getShopId, Shop::getShopName).one();
        PlatformYearFeeRecord one = lambdaQuery().eq(PlatformYearFeeRecord::getShopId, shop.getShopId()).eq(PlatformYearFeeRecord::getPayStatus, 0).one();
        if (one == null) {
            throw new BusinessException("店铺缴费状态异常，请联系管理员！");
        }
        if (org.apache.commons.lang.StringUtils.isBlank(one.getContractNo())) {
            // 生成一个最新的合同编号
            Contract contractNo = contractService.createContractNo(one.getEnterpriseId());
            one.setContractNo(contractNo.getContractNo());
        }
        one.setContractDate(new Date());
        return null;
    }

    @Override
    public LocalDate getNextAnnuaFeeCycle(String enterpriseId) {
        List<PlatformYearFeeRecord> records = this.lambdaQuery()
                .eq(PlatformYearFeeRecord::getEnterpriseId, enterpriseId)
                .orderByDesc(PlatformYearFeeRecord::getServeStartTime)
                .list();
        if (records.isEmpty()) {
            return LocalDate.now();
        }
        PlatformYearFeeRecord latest = records.get(0);
        // 全都是审核通过的，就加一年
        if (latest.getState() == FeeConstants.YEAR_APPROVED) {
            return latest.getServeStartTime().plusYears(1);
        }
        // 最近一条不是审核通过的，就取最近一条的时间
        return latest.getServeStartTime();
    }

    @Override
    @Transactional
    public void createOrUpdate(PlatformYearFeeRecord platformYearFeeRecord) {
        String enterpriseId = platformYearFeeRecord.getEnterpriseId();
        List<PlatformYearFeeRecord> records = lambdaQuery()
                .eq(PlatformYearFeeRecord::getEnterpriseId, enterpriseId)
                .orderByDesc(PlatformYearFeeRecord::getServeStartTime)
                .list();
        PlatformFeeFile pff = platformYearFeeRecord.getFiles().get(0);
        PlatformFeeFile platformFeeFile = new PlatformFeeFile();
        platformFeeFile.setFileFarId(pff.getFileFarId());
        platformFeeFile.setFileType(1);
        platformFeeFile.setFounderId("");
        platformFeeFile.setFounderName("");
        platformFeeFile.setGmtCreate(new Date());
        platformFeeFile.setGmtModified(new Date());
        platformFeeFile.setIsDelete(0);
        platformFeeFile.setMallType(0);
        platformFeeFile.setModifyId("");
        platformFeeFile.setModifyName("");
        platformFeeFile.setName(pff.getName());
        platformFeeFile.setRelevanceType(1);
        platformFeeFile.setRemarks("");
        platformFeeFile.setSort(0);
        platformFeeFile.setUrl(""); // 前端没用
        // 之前没有记录
        if (records.isEmpty()) {
            PlatformYearFeeRecord record = genRecord(enterpriseId, platformYearFeeRecord);
            this.save(record);
            platformFeeFile.setRelevanceId(record.getPaymentRecordId());
            platformFeeFileService.save(platformFeeFile);
        } else {
            PlatformYearFeeRecord latest = records.get(0);
            // 以前的记录都是审核完成的
            if (latest.getState() == FeeConstants.YEAR_APPROVED) {
                PlatformYearFeeRecord record = genRecord(enterpriseId, platformYearFeeRecord);
                this.save(record);
                platformFeeFile.setRelevanceId(record.getPaymentRecordId());
                platformFeeFileService.save(platformFeeFile);
            } else {
                // 最近的记录不是审核完成的，则修改这条记录
                PlatformYearFeeRecord record = genRecord(enterpriseId, platformYearFeeRecord);
                latest.setState(FeeConstants.YEAR_PENDING_REVIEW);
                latest.setPaymentDuration(record.getPaymentDuration());//缴费时长
                latest.setServeEndTime(latest.getServeStartTime().plusYears(platformYearFeeRecord.getPaymentDuration()));
                latest.setRemarks(record.getRemarks());
                latest.setPayAmount(record.getPayAmount());
                updateById(latest);
                platformFeeFileService.remove(new LambdaQueryWrapper<PlatformFeeFile>()
                        .eq(PlatformFeeFile::getRelevanceType, 1)
                        .eq(PlatformFeeFile::getRelevanceId, latest.getPaymentRecordId()));
                platformFeeFile.setRelevanceId(latest.getPaymentRecordId());
                platformFeeFileService.save(platformFeeFile);
            }
        }
    }

    private PlatformYearFeeRecord genRecord(String enterpriseId, PlatformYearFeeRecord platformYearFeeRecord) {
        PlatformYearFee pyf = platformYearFeeService.lambdaQuery().eq(PlatformYearFee::getEnterpriseId, enterpriseId).one();
        PlatformYearFeeRecord record = new PlatformYearFeeRecord();
//                    record.setPaymentRecordId(IdWorker.getIdStr()); // 生成主键ID
        record.setPaymentRecordUn(IdWorker.getIdStr()); // 生成缴费编号
        record.setShopId(pyf.getShopId());
        record.setShopName(pyf.getShopName());
        record.setEnterpriseId(pyf.getEnterpriseId());
        record.setEnterpriseName(pyf.getEnterpriseName());
        record.setRecordType(1);
        // 审核中
        record.setState(FeeConstants.YEAR_PENDING_REVIEW);
        LocalDate today = LocalDate.now();
        record.setServeStartTime(today); // 从今天开始算
        record.setServeEndTime(today.plusYears(platformYearFeeRecord.getPaymentDuration()));
        SystemParam systemParam = systemParamService
                .lambdaQuery()
                .eq(SystemParam::getCode, PublicEnum.PlatformShopYearFeeAmount.getRemark()).one();
        BigDecimal yearFeeAmount = new BigDecimal(systemParam.getKeyValue()).multiply(new BigDecimal(platformYearFeeRecord.getPaymentDuration()));
        record.setPayAmount(yearFeeAmount);
        record.setPaymentDuration(platformYearFeeRecord.getPaymentDuration());//缴费时长
        record.setPaymentDurationType(4);//1天2周3月4年
        // TODO 是否是有效字段
        record.setPayStatus(1); // 假设1表示续费
        record.setPayType(1);// 假设1表示线下支付方式
        // 记录备注
        record.setRemarks(platformYearFeeRecord.getRemarks());
        return record;
    }

    @Override
    public PageUtils getFeeManagementList(FeeManagementQueryDto queryDto) {
        // 设置默认分页参数
        Long page = queryDto.getPage() != null ? queryDto.getPage() : 1L;
        Long limit = queryDto.getLimit() != null ? queryDto.getLimit() : 10L;

        Page<FeeManagementResultVO> pageParam = new Page<>(page, limit);
        IPage<FeeManagementResultVO> resIPage = platformYearFeeRecordMapper.getFeeManagementList(pageParam, queryDto);
        return new PageUtils<>(resIPage);
    }
}
