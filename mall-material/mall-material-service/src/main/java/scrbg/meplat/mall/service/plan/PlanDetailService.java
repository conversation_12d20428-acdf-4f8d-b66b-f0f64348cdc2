package scrbg.meplat.mall.service.plan;

import scrbg.meplat.mall.entity.plan.PlanDetail;
import scrbg.meplat.mall.dto.plan.PlanDetailDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;

import java.util.List;
/**
 * @描述：采购计划明细表 服务类
 * @作者: ye
 * @日期: 2025-05-27
 */
public interface PlanDetailService extends IService<PlanDetail> {

        PageUtils queryPage(PlanDetailDTO dto);

        void create(PlanDetail planDetail);

        void update(PlanDetail planDetail);

        PlanDetail getById(String id);

        void delete(String id);

        void deleteBatch(List<String> ids);
}
