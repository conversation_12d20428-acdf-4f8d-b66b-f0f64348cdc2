package scrbg.meplat.mall.common.constant;
/**
 * 采购计划状态常量类。
 * 定义了采购计划在不同生命周期阶段的状态值及其描述。
 */
public final class PlanConstants {

    // 私有构造函数，防止实例化
    private PlanConstants() {
        // 这是一个工具类，不应被实例化
    }

    /**
     * 计划状态：已作废
     */
    public static final String STATE_CANCELED = "-1";

    /**
     * 计划状态：待提交（已撤回）
     */
    public static final String STATE_PENDING_SUBMISSION = "0";

    /**
     * 计划状态：待审核
     */
    public static final String STATE_PENDING_REVIEW = "1";

    /**
     * 计划状态：已审核
     */
    public static final String STATE_REVIEWED = "2";

    /**
     * 计划状态：已完成
     */
    public static final String STATE_COMPLETED = "3";

    /**
     * 计划状态：审核不通过
     */
    public static final String STATE_REVIEW_REJECTED = "4";

    /**
     * 根据状态值获取状态描述。
     * 可以根据需要扩展此方法。
     * @param state 状态值
     * @return 状态描述字符串
     */
    public static String getStateDescription(String state) {
        switch (state) {
            case STATE_CANCELED:
                return "已作废";
            case STATE_PENDING_SUBMISSION:
                return "待提交（已撤回）";
            case STATE_PENDING_REVIEW:
                return "待审核";
            case STATE_REVIEWED:
                return "已审核";
            case STATE_COMPLETED:
                return "已完成";
            case STATE_REVIEW_REJECTED:
                return "审核不通过";
            default:
                return "未知状态";
        }
    }
}
