package scrbg.meplat.mall.openInterface.pcwp2;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.order.AddSettlementAmountDTO;
import scrbg.meplat.mall.dto.order.AddSettlementAmountParentDTO;
import scrbg.meplat.mall.dto.order.CheckReconciliationIsCancellationVO;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.service.impl.MaterialReconciliationServiceImpl;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.vo.order.GetMaterialContractUseMonthPlanQtyVO;
import scrbg.meplat.mall.vo.w.DealOrderInfoDTO;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-26 9:49
 */
@RestController
@RequestMapping("/w/thirdApi/order")
@Api(tags = "订单相关（对接）")
public class ThirdOrderController {

    @Autowired
    private InterfaceLogsService interfaceLogsService;
    @Autowired
    public DealOrderInfoService dealOrderInfoService;
    @Autowired
    public MaterialReconciliationService materialReconciliationService;
    @Autowired
    public MaterialMonthSupplyPlanService materialMonthSupplyPlanService;

    @Autowired
    public SynthesizeTemporaryService synthesizeTemporaryService;

    @Autowired
    public MaterialMonthSupplyPlanDtlService materialMonthSupplyPlanDtlService;


    @PostMapping("/materialReconciliation/create")
    @ApiOperation(value = "新增对账单")
    public R<String> createMaterialReconciliation(@RequestBody MaterialReconciliation dto) {
        String idStr = dto.getKeyId();
        String id = null;
        try {
            id = materialReconciliationService.createMaterialReconciliation(dto);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "createMaterialReconciliation", dto, null, id, e.getMessage(), ThirdOrderController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(ThirdOrderController.class.getName());
            iLog.setMethodName("createMaterialReconciliation");
            iLog.setLocalArguments(JSON.toJSONString(dto));
            iLog.setResult(id);
            iLog.setIsSuccess(0);
            iLog.setLogType(3);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(e.getMessage());
        }
        return R.success(id);
    }

    @GetMapping("/materialReconciliation/rollBackCreate")
    @ApiOperation(value = "回滚对账单新增")
    public R rollBackReconciliationCreate(String keyId) {
        materialReconciliationService.rollBackReconciliationCreate(keyId);
        return R.success();
    }


    @GetMapping("/materialReconciliation/clearRelevanceId")
    @ApiOperation(value = "删除草稿清除对账单关联id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reconciliationId", value = "对账单id", required = true,
                    dataType = "String", paramType = "query")
    })
    public R clearRelevanceId(String reconciliationId) {
        materialReconciliationService.clearRelevanceId(reconciliationId);
        return R.success();
    }

    @GetMapping("/materialReconciliation/isCancellationByRelevanceId")
    @ApiOperation(value = "判断是否可作废")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reconciliationId", value = "对账单id", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Boolean> isCancellationByRelevanceId(String reconciliationId) {
        Boolean b = materialReconciliationService.isCancellationByRelevanceId(reconciliationId);
        return R.success(b);
    }

    @PostMapping("/dealOrderInfo/batchCreate")
    @ApiOperation(value = "批量新增结算单")
    public R dealOrderInfoSaveBatch(@RequestBody DealOrderInfoDTO dto) {
        String keyId = IdWorker.getIdStr();
        try {
            dealOrderInfoService.batchOutAdd(dto,keyId);
        } catch (Exception e) {
            LogUtil.writeErrorLog(null, "dealOrderInfoSaveBatch", dto, dto, null, e.getMessage(), ThirdOrderController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(keyId);
            iLog.setClassPackage(ThirdOrderController.class.getName());
            iLog.setMethodName("dealOrderInfoSaveBatch");
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(0);
            iLog.setLogType(3);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("/materialReconciliationDtl/addSettlementAmount")
    @ApiOperation(value = "对账单增加已结算金额")
    public R addSettlementAmount(@RequestBody @Valid AddSettlementAmountParentDTO dto) {
        String keyId = dto.getKeyId();
        Integer flag = dto.getFlag();
        try {
            materialReconciliationService.addSettlementAmount(dto);
        } catch (Exception e) {
            LogUtil.writeErrorLog(keyId, "addSettlementAmountFlag" + flag, null, dto, null, e.getMessage(), ThirdOrderController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(keyId);
            iLog.setClassPackage(ThirdOrderController.class.getName());
            iLog.setMethodName("addSettlementAmountFlag" + flag);
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(0);
            iLog.setLogType(3);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            e.printStackTrace();
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @GetMapping("/materialReconciliationDtl/rollBackAddSettlementAmount")
    @ApiOperation(value = "回滚对账单结算金额")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyId", value = "keyId", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "flag", value = "标识（1推送回滚2作废回滚）", required = true, dataType = "int", paramType = "query")
    })
    public R rollBackAddSettlementAmount(String keyId, Integer flag) {
        LogUtil.writeInfoLog(keyId, "rollBackAddSettlementAmount", null, flag, null, ThirdOrderController.class);
        materialReconciliationService.rollBackAddSettlementAmount(keyId, flag);
        return R.success();
    }

    @GetMapping("/materialReconciliation/checkReconciliationIsCancellation")
    @ApiOperation(value = "根据对账单id判断是否可结算（code200，data为null表示通过）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reconciliationId", value = "对账单id", required = true, dataType = "string", paramType = "query"),
    })
    public R<CheckReconciliationIsCancellationVO> checkReconciliationIsCancellation(String reconciliationId) {
        CheckReconciliationIsCancellationVO vo = materialReconciliationService.checkReconciliationIsCancellation(reconciliationId);
        return R.success(vo);
    }


    @PostMapping("/MaterialMonthSupplyPlan/getMaterialContractUseMonthPlanQty")
    @ApiOperation(value = "根据合同明细ids获取物资采购合同明细已生成月供计划数量")
    public R<List<GetMaterialContractUseMonthPlanQtyVO>> getMaterialContractUseMonthPlanQty(@RequestBody List<String> dtlIds) {
        List<GetMaterialContractUseMonthPlanQtyVO> vos = materialMonthSupplyPlanService.getMaterialContractUseMonthPlanQty(dtlIds);
        return R.success(vos);
    }


    @GetMapping("/synthesizeTemporary/updateStayPush")
    @ApiOperation(value = "pcwp删除草稿大宗临购计划时商城修改状态为可推送")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "商城大宗临购单id", required = true,
                    dataType = "String", paramType = "query")
    })
    public R updateStayPush(String id) {
        synthesizeTemporaryService.updateStayPush(id);
        return R.success();
    }


    @GetMapping("/MaterialMonthSupplyPlanDtl/getMonthPlanQtyCount")
    @ApiOperation(value = "获取大宗采购合同月供计划已使用数量")
    public R getMonthPlanQtyCount() {
        materialMonthSupplyPlanDtlService.getMonthPlanQtyCount();
        return R.success();
    }
}
