package scrbg.meplat.mall.enums.user;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.ArrayList;
import java.util.List;
//物资添加特殊权限类
public class AuthArrUtil {
    public static ArrayList<String> isBusinessOrg = new ArrayList<>();




    public static void addAuth(List<String> authArr){
        authArr.add("wisesoft");
        authArr.add("005718");
        authArr.add("srbg5718");
        authArr.add("005948");
        authArr.add("032232");
        authArr.add("laizq0119");
        authArr.add("008495");
        authArr.add("004899");

    }


    public static ArrayList<String> isBusinessOrgAuth(){

        isBusinessOrg.add("1838284925812342784");
        isBusinessOrg.add("aa5422473bfb-aeea-9146-8ce7-04ff3c5e");
        return isBusinessOrg;

    }

}
