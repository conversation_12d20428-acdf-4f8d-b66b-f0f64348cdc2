package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.BiddingType;

/**
 * @描述：招标类型表 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
public interface BiddingTypeService extends IService<BiddingType> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingType> queryWrapper);

    void create(BiddingType biddingType);

    void update(BiddingType biddingType);

    BiddingType getById(String id);

    void delete(String id);
}
