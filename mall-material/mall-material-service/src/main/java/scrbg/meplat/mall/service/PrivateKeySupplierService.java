package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.PrivateKeySupplier;
/**
 * @描述：供应商秘钥 服务类
 * @作者: ye
 * @日期: 2023-03-27
 */
public interface PrivateKeySupplierService extends IService<PrivateKeySupplier> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PrivateKeySupplier> queryWrapper);

        void create(PrivateKeySupplier privateKeySupplier);

        void update(PrivateKeySupplier privateKeySupplier);

        PrivateKeySupplier getById(String id);

        void delete(String id);
}