package scrbg.meplat.mall.service.impl;

import org.apache.commons.lang.StringUtils;
import scrbg.meplat.mall.entity.BiddingWinRecord;
import scrbg.meplat.mall.entity.BiddingWinRecord;
import scrbg.meplat.mall.mapper.BiddingWinRecordMapper;
import scrbg.meplat.mall.service.BiddingWinRecordService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @描述：竞价中标记录 服务类
 * @作者: ye
 * @日期: 2023-07-11
 */
@Service
public class BiddingWinRecordServiceImpl extends ServiceImpl<BiddingWinRecordMapper, BiddingWinRecord> implements BiddingWinRecordService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingWinRecord> queryWrapper) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        queryWrapper.eq(BiddingWinRecord::getSupplierId,user.getEnterpriseId());
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String supplierName = (String) innerMap.get("supplierName");
        String biddingId = (String) innerMap.get("biddingId");
        BigDecimal tallWinPrice = (BigDecimal) innerMap.get("tallWinPrice");
        BigDecimal lowWinPrice = (BigDecimal) innerMap.get("lowWinPrice");

        String keywords = (String) innerMap.get("keywords");
        String staWinTime = (String) innerMap.get("staWinTime");
        String endWinTime = (String) innerMap.get("endWinTime");
        queryWrapper.eq(supplierName!=null&&supplierName!="",BiddingWinRecord::getSupplierName,supplierName);
//        queryWrapper.gt(tallWinPrice!=null,BiddingWinRecord::getWinPrice,tallWinPrice);
//        queryWrapper.lt(lowWinPrice!=null,BiddingWinRecord::getWinPrice,lowWinPrice);
        queryWrapper.eq(biddingId!=null&&biddingId!="",BiddingWinRecord::getBiddingId,biddingId);
        if (staWinTime!=null&&staWinTime!=""){
            queryWrapper.gt(BiddingWinRecord::getWinTime,staWinTime);
        }
        if (endWinTime!=null&&endWinTime!=""){
            queryWrapper.lt(BiddingWinRecord::getWinTime,endWinTime);
        }
        if(StringUtils.isNotBlank(keywords)){
            queryWrapper.and((t) -> {
                t.like(BiddingWinRecord::getSupplierName,keywords);
            });
        }

        IPage<BiddingWinRecord> page = this.page(
        new Query<BiddingWinRecord>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(BiddingWinRecord biddingWinRecord) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingWinRecord);
    }

    @Override
    public void update(BiddingWinRecord biddingWinRecord) {
        super.updateById(biddingWinRecord);
    }


    @Override
    public BiddingWinRecord getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
