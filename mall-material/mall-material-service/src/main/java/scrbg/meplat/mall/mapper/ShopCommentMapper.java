package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.ShopComment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @描述：商铺评价 Mapper 接口
 * @作者: ye
 * @日期: 2025-05-21
 */
@Mapper
@Repository
public interface ShopCommentMapper extends BaseMapper<ShopComment> {
    int findShopCommentCount(@Param("dto") Map<String, Object> innerMap);
    List<ShopComment> findShopComment(Page<ShopComment> pages, @Param("dto") Map<String, Object> innerMap);
}