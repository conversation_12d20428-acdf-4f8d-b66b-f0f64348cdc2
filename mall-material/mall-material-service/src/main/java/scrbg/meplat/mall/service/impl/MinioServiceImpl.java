//package scrbg.meplat.mall.service.impl;
//
//import io.minio.*;
//import io.minio.http.Method;
//import lombok.extern.log4j.Log4j2;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.io.InputStreamResource;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.StringUtils;
//import org.springframework.web.multipart.MultipartFile;
//import scrbg.meplat.mall.config.minio.ImageContentType;
//import scrbg.meplat.mall.config.minio.VideoContentType;
//import scrbg.meplat.mall.entity.FileRecord;
//import scrbg.meplat.mall.exception.BusinessException;
//import scrbg.meplat.mall.service.FileRecordService;
//import scrbg.meplat.mall.service.MinioService;
//
//import java.io.InputStream;
//import java.math.BigDecimal;
//import java.time.LocalDate;
//import java.time.format.DateTimeFormatter;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.UUID;
//
///**
// * <AUTHOR>
// * @create 2023-03-27 17:06
// */
//@Log4j2
//@Service
//public class MinioServiceImpl implements MinioService {
//
//    @Autowired
//    private MinioClient minioClient;
//
//    @Autowired
//    private FileRecordService fileRecordService;
//
//    /**
//     * 文件上传
//     *
//     * @param map
//     * @return
//     */
//    @Override
//    public List<FileRecord> minioUpload(Map map) {
//        String bucketName = (String) map.get("bucketName");
//        String directory = (String) map.get("directory");
//        Integer fileType = (Integer) map.get("fileType");
//        List<MultipartFile> files = (List<MultipartFile>) map.get("files");
//        if (fileType == null) fileType = 1;
//        if (CollectionUtils.isEmpty(files) || files.get(0) == null || files.get(0).getSize() == 0) {
//            throw new BusinessException(400, "文件为空！");
//        }
//        if (StringUtils.isEmpty(bucketName)) {
//            throw new BusinessException(400, "桶名称不能为空！");
//        }
//        if (StringUtils.isEmpty(directory)) {
//            throw new BusinessException(400, "存储目录不能为空！");
//        }
//        ArrayList<FileRecord> vos = new ArrayList<>();
//        try {
//            if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build())) {
//                throw new BusinessException(400, "桶不存在！请联系管理员！");
//            }
//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
//            String thisDateStr = LocalDate.now().format(formatter);
//            for (MultipartFile file : files) {
//                String fileName = file.getOriginalFilename();
//                String contentType = null;
//                String newName = null;
//                String fileNewName = UUID.randomUUID().toString().replaceAll("-", "")
//                        + "-" + thisDateStr + fileName.substring(fileName.lastIndexOf("."));
//                if (fileType == 1) {
//                    contentType = ImageContentType.getContentType(fileName);
//                    if (contentType == null) {
//                        throw new BusinessException(400, "不支持该类型文件上传！");
//                    }
//                    newName = bucketName + "/" + directory + "/image/" + thisDateStr + "/" + fileNewName;
//                } else if (fileType == 2) {
//                    contentType = VideoContentType.getContentType(fileName);
//                    if (contentType == null) {
//                        throw new BusinessException(400, "不支持该类型文件上传！");
//                    }
//                    newName = bucketName + "/" + directory + "/video/" + thisDateStr + "/" + fileNewName;
//                } else {
//                    throw new BusinessException(400, "请输入正确的文件类型");
//                }
//                InputStream inputStream = file.getInputStream();
//                // 设置文件contentType，不设置默认url访问返回流会直接下载
//                PutObjectArgs build = PutObjectArgs.builder()
//                        .bucket(bucketName)
//                        .object(newName)
//                        .stream(inputStream, file.getSize(), -1)
//                        .contentType(contentType)
//                        .build();
//                minioClient.putObject(build);
//                inputStream.close();
//                String url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
//                        .method(Method.GET)
//                        .bucket(bucketName)
//                        .object(newName)
//                        .build());
//                // 需要建表
//                if (!StringUtils.isEmpty(url)) {
//                    String[] split = url.split("\\?");
//                    url = split[0];
//                }
//                FileRecord vo = new FileRecord();
//                vo.setObjectName(fileNewName);
//                vo.setObjectPath(url);
//                vo.setNonIpObjectPath(newName);
//                vo.setBucketName(bucketName);
//                BigDecimal bigDecimal = new BigDecimal(file.getSize());
//                BigDecimal divideMB = bigDecimal.divide(new BigDecimal(1024)).divide(new BigDecimal(1024));
//                BigDecimal divideKB = bigDecimal.divide(new BigDecimal(1024));
//                vo.setObjectSizeKb(divideKB.setScale(2, BigDecimal.ROUND_DOWN));
//                vo.setObjectSizeMb(divideMB.setScale(2, BigDecimal.ROUND_DOWN));
//                boolean save = fileRecordService.save(vo);
//                if (save) {
//                    vos.add(vo);
//                }
//            }
//            return vos;
//        } catch (Exception e) {
//            throw new BusinessException(400, e.getMessage());
//        }
//    }
//
//    /**
//     * 根据记录id下载
//     * @param recordId
//     * @return
//     */
//    @Override
//    public Object downloadById(String recordId) {
//        if (StringUtils.isEmpty(recordId)) {
//            throw new BusinessException(400, "未携带记录id！");
//        }
//        FileRecord fileRecord = fileRecordService.lambdaQuery().eq(FileRecord::getRecordId, recordId)
//                .select(FileRecord::getBucketName, FileRecord::getNonIpObjectPath).one();
//        if (fileRecord == null) {
//            throw new BusinessException(400, "文件记录不存在！");
//        }
//        GetObjectResponse response = null;
//        try {
//            response = minioClient.getObject(GetObjectArgs.builder().bucket(fileRecord.getBucketName()).object(fileRecord.getNonIpObjectPath()).build());
//        } catch (Exception e) {
//            log.error("异常信息：" + e.getMessage());
//            throw new BusinessException(400, "下载失败！");
//        }
//        InputStreamResource resource = new InputStreamResource(response);
//        return resource;
//    }
//}
