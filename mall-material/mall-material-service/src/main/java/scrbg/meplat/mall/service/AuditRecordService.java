package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.AuditRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.AuditRecord;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.entity.SupplierReconciliation;

/**
 * @描述：审核记录 服务类
 * @作者: ye
 * @日期: 2023-06-27
 */
public interface AuditRecordService extends IService<AuditRecord> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<AuditRecord> queryWrapper);

        void create(AuditRecord auditRecord);

        void update(AuditRecord auditRecord);

        AuditRecord getById(String id);

        void delete(String id);

        /**
         * 创建审核不通过记录
         */
        public void createDataNoPass(String relevanceId, int resultType, String auditResult);

        /**
         * 创建审核通过记录
         */
        public void createDataPass(String relevanceId, int resultType);
}
