package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.NegotiatedPrice;
import scrbg.meplat.mall.vo.user.userCenter.NegotiatedPriceVo;

/**
 * @描述：报价 服务类
 * @作者: y
 * @日期: 2022-11-22
 */
public interface NegotiatedPriceService extends IService<NegotiatedPrice> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<NegotiatedPrice> queryWrapper);

        void create(NegotiatedPrice negotiatedPrice);

        void update(NegotiatedPrice negotiatedPrice);

        NegotiatedPrice getById(String id);

        void delete(String id);

    PageUtils listByUser(JSONObject jsonObject, QueryWrapper<NegotiatedPriceVo> negotiatedPriceVoQueryWrapper);
}
