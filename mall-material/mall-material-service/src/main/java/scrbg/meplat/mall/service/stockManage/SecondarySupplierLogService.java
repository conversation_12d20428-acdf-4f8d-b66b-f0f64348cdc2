package scrbg.meplat.mall.service.stockManage;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SecondarySupplierRecord;

import java.util.List;

public interface SecondarySupplierLogService {
    void delete(String id);


    void saveLogs(List<SecondarySupplierRecord> log);

    PageUtils<SecondarySupplierRecord> queryPage(JSONObject jsonObject, LambdaQueryWrapper<SecondarySupplierRecord> queryWrapper);

}
