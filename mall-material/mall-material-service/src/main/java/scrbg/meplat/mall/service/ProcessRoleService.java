package scrbg.meplat.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;

import scrbg.meplat.mall.entity.ProcessRole;
/**
 * 审批流程人员表 服务类
 * <AUTHOR>
 * @date: 2025年6月20日 上午9:51:25
 */
public interface ProcessRoleService extends IService<ProcessRole> {

        void create(ProcessRole processRole);

        void update(ProcessRole processRole);

        ProcessRole getById(String id);

        void delete(String id);

}
