package scrbg.meplat.mall.service.stockManage;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SelfOperatedStoreRecord;

import java.util.List;

public interface SelfOperatedLogService {
    void delete(String id);


    void saveLog(SelfOperatedStoreRecord log);
    void saveLogs(List<SelfOperatedStoreRecord> logs);

    PageUtils<SelfOperatedStoreRecord> queryPage(JSONObject jsonObject, LambdaQueryWrapper<SelfOperatedStoreRecord> queryWrapper);

    PageUtils<SelfOperatedStoreRecord> getProductInfo(JSONObject jsonObject);

    SelfOperatedStoreRecord getSelfInfo(String id);
}
