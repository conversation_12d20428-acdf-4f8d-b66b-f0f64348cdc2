package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.entity.Role;

import java.util.List;

/**
 * @描述：企业类型表 服务类
 * @作者: y
 * @日期: 2022-11-07
 */
public interface RoleService extends IService<Role> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Role> queryWrapper);

    void create(Role role);

    R createList(List<Role> roleList);

    void update(Role role);

    Role getById(String id);

    void delete(String id);

    R findAllByMallType(int mallType);


    R insertRoleToShop(List<Role> shopRoles, String shopId);
}
