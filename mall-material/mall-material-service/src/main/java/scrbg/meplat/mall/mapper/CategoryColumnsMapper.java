package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.CategoryColumns;
import scrbg.meplat.mall.vo.floor.website.WColumnFloorVO;

import java.util.List;

/**
 * @描述： Mapper 接口
 * @作者: y
 * @日期: 2022-12-07
 */
@Mapper
@Repository
public interface CategoryColumnsMapper extends BaseMapper<CategoryColumns> {

    List<WColumnFloorVO> wColumnFloorVOsList(QueryWrapper<WColumnFloorVO> vosWrapper);
}
