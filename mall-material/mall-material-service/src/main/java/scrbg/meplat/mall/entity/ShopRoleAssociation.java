package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：店铺—角色类型关联表
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "店铺—角色类型关联表")
@Data
@TableName("shop_role_association")
public class ShopRoleAssociation extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "店铺角色关联id")

    private Integer id;

    @ApiModelProperty(value = "店铺id")

    private String shopId;

    @ApiModelProperty(value = "角色id")

    private String roleId;

    @ApiModelProperty(value = "状态")

    private Integer state;


}
