package scrbg.meplat.mall.service;

import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.dto.reconciliation.SupplierPlanReconciliationQueryDTO;

/**
 * @描述：计划对账 服务类
 * @作者: tanfei
 * @日期: 2025-06-19
 */
public interface PlanReconciliationService {

    /**
     * 供应商方-获取可对账的计划订单列表
     * @param dto 查询参数
     * @return 分页结果
     */
    PageUtils getReconciliablePlansBySupplierPageList(SupplierPlanReconciliationQueryDTO dto);

    /**
     * 供应商方-获取可对账的项目部
     * @param dto 查询参数
     * @return 分页结果
     */
    PageUtils getReconciliableEnterpriseBySupplierPageList(SupplierPlanReconciliationQueryDTO dto);

    /**
     * 采购方-获取可对账的供应商列表
     * @param dto 查询参数
     * @return 分页结果
     */
    PageUtils getReconciliableSupplierByEnterprisePageList(SupplierPlanReconciliationQueryDTO dto);

    /**
     * 采购方-获取可对账的计划订单列表
     * @param dto 查询参数
     * @return 分页结果
     */
    PageUtils getReconciliablePlansByEnterprisePageList(SupplierPlanReconciliationQueryDTO dto);
}
