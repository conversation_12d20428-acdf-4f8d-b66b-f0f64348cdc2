package scrbg.meplat.mall.enums.role;

/**
 * @package: scrbg.meplat.mall.enums.role
 * @author: 胡原武
 * @date: 2022.11.08
 */
public enum RoleCommonEnum {

    IS_SELECTIVE_YES(1,"可选"),
    IS_SELECTIVE_NO(0,"不可选"),

    MALL_TYPE_MATERIAL(1,"物资商城"),
    MALL_TYPE_DEVICE(0,"设备商城");


    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    RoleCommonEnum() {
    }

    RoleCommonEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
