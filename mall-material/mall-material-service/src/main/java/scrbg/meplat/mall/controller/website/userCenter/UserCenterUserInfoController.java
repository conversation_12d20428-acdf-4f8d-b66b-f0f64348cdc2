package scrbg.meplat.mall.controller.website.userCenter;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.OrganizationDTO;
import scrbg.meplat.mall.dto.plan.PlanFileDto;
import scrbg.meplat.mall.dto.user.userCenter.CreateShopExternalDTO;
import scrbg.meplat.mall.dto.user.userCenter.CreateShopInsideDTO;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.ShoppingCartService;
import scrbg.meplat.mall.service.UserService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.MaterialLockUtils;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.user.LoginVO;
import scrbg.meplat.mall.vo.user.userCenter.CreateShopEchoInfoVO;
import scrbg.meplat.mall.vo.user.userCenter.EnterpriseAuthUpdateVO;
import scrbg.meplat.mall.vo.user.userCenter.ShopStateVO;
import scrbg.meplat.mall.vo.user.userCenter.UserNameAndImgVO;

/**
 * @描述：用户控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/userCenter/user")
@ApiSort(value = 200)
@Api(tags = "用户(个人中心)")
public class UserCenterUserInfoController {

    @Autowired
    private UserService userService;

    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    @Autowired
    private ShoppingCartService shoppingCartService;

    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody User user) {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        user.setUserId(userId);
        userService.update(user);
        return R.success();
    }

    @GetMapping("/updateIsShowBid")
    @ApiOperation(value = "关闭是否显示招标")
    public R updateIsShowBid() {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        userService.lambdaUpdate().eq(User::getUserId, userId)
                .set(User::getIsShowBid, 0).update();
        return R.success();
    }

    @PostMapping("/updateImgByUserId")
    @ApiOperation(value = "修改个人头像根据用户id")
    public R updateImgByUserId(@RequestBody JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        String userImg = (String) innerMap.get("userImg");
        userService.updateUserImg(userId, userImg);
        return R.success();
    }

    @GetMapping("/createShopEchoInfo")
    @ApiOperation(value = "开店回显数据（外部）")
    public R<CreateShopEchoInfoVO> createShopEchoInfo(Integer mallType) {
        CreateShopEchoInfoVO vo = userService.createShopEchoInfo(mallType);
        return R.success(vo);
    }

    // 返回用户开店进度
    @GetMapping("/getShopProgress")
    public R getShopProgress() {
        JSONObject jsonObject = enterpriseInfoService.getShopProgress();
        return R.success(jsonObject);
    }
    // 未用、废弃
//    @GetMapping("/creatInsideShopEchoInfo")
//    @ApiOperation(value = "开店回显数据（内部）")
//    public R<CreateInsideShopEchoInfoVO> creatInsideShopEchoInfo() {
//        CreateInsideShopEchoInfoVO vo = userService.creatInsideShopEchoInfo();
//        return R.success(vo);
//    }

    @PostMapping("/createShopExternal")
    @ApiOperation(value = "开店（外部）")
    @NotResubmit
    public R createShopExternal(@RequestBody CreateShopExternalDTO dto) {
        userService.createShopExternal(dto);
        return R.success();
    }

    @PostMapping("/createShopInside")
    @ApiOperation(value = "开店（内部）")
    @NotResubmit
    public R createShopInside(@RequestBody CreateShopInsideDTO dto) {
        userService.createShopInside(dto);
        return R.success();
    }

    @GetMapping("/getShopStateByUserId")
    @ApiOperation(value = "根据当前的登陆的用户id查询店铺状态")
    public R getShopStateByUserId() {
        ShopStateVO vo = userService.getShopStateByUserId();
        return R.success(vo);
    }

    @GetMapping("/getUserNameInfo")
    @ApiOperation(value = "获取当前登陆用户名称和头像")
    public R getUserNameInfo() {
        UserNameAndImgVO vo = userService.getUserNameInfo();
        return R.success(vo);
    }

    @GetMapping("/getUserData")
    @ApiOperation(value = "根据当前的登陆的用户id查询用户信息")
    public R getUserData() {
        User user = userService.getUserData();
        return R.success(user);
    }

    @GetMapping("/getAuditState")
    @ApiOperation(value = "根据当前的登陆的判断供应商是否审核成功")
    public R getAuditState() {
        EnterpriseInfo auditStateMap = userService.getAuditState();
        return R.success(auditStateMap);
    }

    @GetMapping("/getEnterpriseAuthInfo")
    @ApiOperation(value = "根据当前的登陆的用户id获取企业信息")
    public R getEnterpriseAuthInfo(Integer mallType) {
        EnterpriseAuthUpdateVO vo = userService.getEnterpriseAuthInfo(mallType);
        return R.success(vo);
    }

    @PostMapping("/updateEnterprise")
    @ApiOperation(value = "修改企业")
    public R updateEnterprise(@RequestBody EnterpriseInfo enterpriseInfo) {
        enterpriseInfoService.updateEnterprise(enterpriseInfo);
        return R.success();
    }

    @PostMapping("/becomeEnterprise")
    @ApiOperation(value = "成为企业")
    public R becomeEnterprise(@RequestBody EnterpriseInfo enterpriseInfo) {
        enterpriseInfoService.becomeEnterprise(enterpriseInfo);
        return R.success();
    }

    @ApiOperation("用户退出登陆")
    @GetMapping("/loginOut")
    public R loginOut() {
        userService.loginOut();
        return R.success();
    }

    @ApiOperation("切换企业")
    @PostMapping("/cutOrg")
    public R cutOrg(@Valid @RequestBody OrganizationDTO dto, HttpServletRequest request) {
        LoginVO vo = userService.cutOrg(dto, request);
        userService.addUserMenu(vo);
        return R.success(vo);
    }

    @PostMapping("/updatePassSendCode")
    @ApiOperation(value = "修改密码发送验证码")
    public R updatePassSendCode(String phone, String privateKeyId, HttpServletRequest request) {
        userService.updatePassSendCode(phone, privateKeyId, request);
        return R.success();
    }

    @PostMapping("/checkUpdatePassCode")
    @ApiOperation(value = "检验修改密码的验证码")
    public R checkUpdatePassCode(String phone, String code) {
        userService.checkUpdatePassCode(phone, code);
        return R.success();
    }

    @PostMapping("/updatePassword")
    @ApiOperation(value = "修改密码")
    public R updatePassword(String newPassword) {
        userService.updatePassword(newPassword);
        return R.success();
    }

    @PostMapping("/updatePhoneSendCode")
    @ApiOperation(value = "修改手机号发送验证码")
    public R updatePhoneSendCode(String phone, String privateKeyId, HttpServletRequest request) {
        userService.updatePhoneSendCode(phone, privateKeyId, request);
        return R.success();
    }

    @PostMapping("/checkUpdatePhoneCode")
    @ApiOperation(value = "检验修改手机的验证码")
    public R checkUpdatePhoneCode(String phone, String code) {
        userService.checkUpdatePhoneCode(phone, code);
        return R.success();
    }

    @PostMapping("/updatePhone")
    @ApiOperation(value = "修改手机号")
    public R updatePhone(String newPhone, String code) {
        userService.updatePhone(newPhone, code);
        return R.success();
    }

    @PostMapping("/pushCardProductsArrive")
    @ApiOperation(value = "推送零星采购计划")
    @NotResubmit
    public R pushCardProductsArrive(@RequestBody List<String> cardId) {
        String idStr = IdWorker.getIdStr();
        StringBuilder farArg = new StringBuilder();
        try {
            UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
            String clientId = UUID.randomUUID().toString();
            Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(MaterialLockUtils.PUSH_LX_PIAN_LOCK + currentUser.getOrgId() + ":" + currentUser.getUserId() + ":" + cardId.size(), clientId, 1, TimeUnit.MINUTES);
            if (b) {
                shoppingCartService.pushCardProductsArrive(cardId, idStr, farArg);
                return R.success();
            } else {
                throw new BusinessException("请于1分钟后在推送计划，避免pcwp出现计划翻倍，请勿重复提交");
            }

        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "pushCardProductsArrive", cardId, null, null, e.getMessage(), UserCenterUserInfoController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(UserCenterUserInfoController.class.getName());
            iLog.setMethodName("pushCardProductsArrive");
            iLog.setLocalArguments(JSON.toJSONString(cardId));
            iLog.setFarArguments(farArg.toString());
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.createFailLog(iLog);
            shoppingCartService.pushCardProductsArriveRollBack(idStr);
            throw new BusinessException(e.getMessage());
        }
    }
    @PostMapping("/create_retail_plan")
    @ApiOperation(value = "推送零星采购计划")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R<Void> pushCardProductsArrive(@RequestBody PlanFileDto planDto) {
        shoppingCartService.pushPlan(planDto);
        return R.success();
    }
    @PostMapping("/create_synthesize_plan")
    @ApiOperation(value = "推送大宗计划")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R<Void> createSynthesizePlan(@RequestBody PlanFileDto planDto) {
        shoppingCartService.pushPlan(planDto);
        return R.success();
    }
    @PostMapping("/create_reval_plan")
    @ApiOperation(value = "推送大宗计划")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R<Void> createRevolPlan(@RequestBody PlanFileDto planDto) {
        shoppingCartService.pushPlan(planDto);
        return R.success();
    }

    @PostMapping("/checkSubmitPlanProductCondition")
    @ApiOperation(value = "检查推送计划情况")
    public R<Map> checkSubmitPlanProductCondition(@RequestBody Map map) {
        Map rMap = shoppingCartService.checkSubmitPlanProductCondition(map);
        return R.success(rMap);
    }

    /**
     * 废弃
     * @param cardId
     * @return
     */
//    @PostMapping("/PCWP1/pushCardProductsArrive")
//    @ApiOperation(value = "推送零星采购计划")
//    public R PCWP1pushCardProductsArrive(@RequestBody List<String> cardId) {
//        shoppingCartService.PCWP1pushCardProductsArrive(cardId);
//        return R.success();
//    }


}
