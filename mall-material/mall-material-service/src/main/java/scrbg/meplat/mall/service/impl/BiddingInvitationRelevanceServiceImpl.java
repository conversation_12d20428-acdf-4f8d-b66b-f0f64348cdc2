package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.BiddingInvitationRelevance;
import scrbg.meplat.mall.mapper.BiddingInvitationRelevanceMapper;
import scrbg.meplat.mall.service.BiddingInvitationRelevanceService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：邀请竞价关联表 服务类
 * @作者: ye
 * @日期: 2023-09-27
 */
@Service
public class BiddingInvitationRelevanceServiceImpl extends ServiceImpl<BiddingInvitationRelevanceMapper, BiddingInvitationRelevance> implements BiddingInvitationRelevanceService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingInvitationRelevance> queryWrapper) {
        IPage<BiddingInvitationRelevance> page = this.page(
        new Query<BiddingInvitationRelevance>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(BiddingInvitationRelevance biddingInvitationRelevance) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingInvitationRelevance);
    }

    @Override
    public void update(BiddingInvitationRelevance biddingInvitationRelevance) {
        super.updateById(biddingInvitationRelevance);
    }


    @Override
    public BiddingInvitationRelevance getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
