package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import io.swagger.models.auth.In;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.mail.ShopInfoUpdateDTO;
import scrbg.meplat.mall.dto.outer.GetOuterUseShopIdDTO;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.excelTemplate.ArrearageShopExcel;
import scrbg.meplat.mall.entity.excelTemplate.Material;
import scrbg.meplat.mall.entity.excelTemplate.StopShopExcel;
import scrbg.meplat.mall.enums.CodeEnum;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.file.FileEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.EnterpriseInfoMapper;
import scrbg.meplat.mall.mapper.ShopMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;
import scrbg.meplat.mall.vo.platform.MaterialVo;
import scrbg.meplat.mall.vo.platform.PlatformShopCountVo;
import scrbg.meplat.mall.vo.platform.ShopArrearageStateVo;
import scrbg.meplat.mall.vo.platform.ShopCountVo;
import scrbg.meplat.mall.vo.product.ImportExcelResultVO;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;
import scrbg.meplat.mall.vo.product.shop.website.WebsiteShopVo;
import scrbg.meplat.mall.vo.product.website.ImportShopExcelResultVO;
import scrbg.meplat.mall.vo.product.website.material.WMaterialVO;
import scrbg.meplat.mall.vo.shopManage.ShopInfoUpdateVO;
import scrbg.meplat.mall.vo.user.userCenter.OpenShopInfoVO;
import scrbg.meplat.mall.vo.w.IndexShopListVO;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述：店铺 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class ShopServiceImpl extends ServiceImpl<ShopMapper, Shop> implements ShopService {
    @Autowired
    RoleService roleService;
    @Autowired
    MallConfig mallConfig;
    @Autowired
    private RestTemplateUtils restTemplateUtils;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    RestTemplate restTemplate;
    @Autowired
    ShoppingCartService shoppingCartService;
    @Autowired
    ProductService productService;
    @Autowired
    ShopService shopService;

    @Autowired
    FileService fileService;

    @Autowired
    UserService userService;

    @Autowired
    EnterpriseInfoMapper enterpriseInfoMapper;

    @Autowired
    MallConfig mallconfig;

    @Autowired
    ShopCommentService shopCommentService;

    @Autowired
    ShopMapper shopMapper;

    @Resource
    private StationMessageService stationMessageService;


    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Shop> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String shopName = (String) innerMap.get("shopName");
        String city = (String) innerMap.get("city");
        Integer shopType = (Integer) innerMap.get("shopType");
        Integer isArrearage = (Integer) innerMap.get("isArrearage");
        Integer state = (Integer) innerMap.get("state");
        Integer auditStatus = (Integer) innerMap.get("auditStatus");
        Integer isBusiness = (Integer) innerMap.get("isBuusiness");
        Integer isSupplier = (Integer) innerMap.get("isSupplier");
        Integer isInternalShop = (Integer) innerMap.get("isInternalShop");
        String detailedAddress = (String) innerMap.get("detailedAddress");
        String enterpriseId = (String) innerMap.get("enterpriseId");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        //用户可以根据模糊匹配企业名称查询店铺
        if (!StringUtils.isEmpty(shopName)) {
            queryWrapper.like(Shop::getShopName, shopName);
        }
        if (!StringUtils.isEmpty(isArrearage)) {
            queryWrapper.like(Shop::getIsArrearage, isArrearage);
        }
        if (!StringUtils.isEmpty(city)) {
            queryWrapper.like(Shop::getCity, city);
        }
        if (shopType != null) {
            queryWrapper.eq(Shop::getShopType, shopType);
        }
        if (auditStatus != null) {
            queryWrapper.eq(Shop::getAuditStatus, auditStatus);
        }
        if (isInternalShop != null) {
            queryWrapper.eq(Shop::getIsInternalShop, isInternalShop);
        }
        if (isBusiness != null) {
            queryWrapper.eq(Shop::getIsBusiness, isBusiness);
        }
        if (isSupplier != null) {
            queryWrapper.eq(Shop::getIsSupplier, isSupplier);
        }
        if (!StringUtils.isEmpty(detailedAddress)) {
            queryWrapper.like(Shop::getDetailedAddress, detailedAddress);
        }
        if (state != null) {
            queryWrapper.eq(Shop::getState, state);
        }
        if (!StringUtils.isEmpty(enterpriseId)) {
            queryWrapper.eq(Shop::getEnterpriseId, enterpriseId);
        }
        queryWrapper.eq(Shop::getAuditStatus, 1);
        queryWrapper.eq(Shop::getMallType, mallconfig.mallType);
        if (!StringUtils.isEmpty(keywords)) {
            List<EnterpriseInfo> list = enterpriseInfoService.lambdaQuery().like(EnterpriseInfo::getEnterpriseName, keywords.trim())
                    .eq(EnterpriseInfo::getIsSupplier, 2)
                    .ne(EnterpriseInfo::getEnterpriseType, 2).select(EnterpriseInfo::getEnterpriseId).list();
            if (list != null && list.size() > 0) {
                List<String> enterPriseIds = list.stream().map(EnterpriseInfo::getEnterpriseId).collect(Collectors.toList());
                queryWrapper.and((t) -> {
                    t.like(Shop::getShopName, keywords)
                            .or().like(Shop::getRemarks, keywords)
                            .or().in(Shop::getEnterpriseId, enterPriseIds);
                });
            } else {
                queryWrapper.and((t) -> {
                    t.like(Shop::getShopName, keywords)
                            .or().like(Shop::getRemarks, keywords);
                });
            }
        }
        //排序方式(排序值升序)
        if (orderBy == null) {
        } else if (orderBy == 1) {
            queryWrapper.orderByDesc(Shop::getSort)
                    .orderByDesc(Shop::getGmtCreate);
            //排序方式(修改时间降序)
        } else if (orderBy == 2) {
            queryWrapper.orderByDesc(Shop::getGmtModified);
            //排序方式(创建时间降序)
        } else if (orderBy == 3) {
            queryWrapper.orderByDesc(Shop::getGmtCreate);
        }
        IPage<Shop> page = this.page(
                new Query<Shop>().getPage(jsonObject),
                queryWrapper
        );
        List<Shop> list = page.getRecords();
        List<String> collect = list.stream().map(Shop::getEnterpriseId).collect(Collectors.toList());
        if (collect.size()>0){
            List<EnterpriseInfo> enterpriseInfos = enterpriseInfoService.lambdaQuery()
                    .in(EnterpriseInfo::getEnterpriseId, collect)
                    .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getEnterpriseName).list();
            for (Shop shop : list) {
                for (EnterpriseInfo enterpriseInfo : enterpriseInfos) {
                    if (shop.getEnterpriseId().equals(enterpriseInfo.getEnterpriseId())) {
                        shop.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        break;
                    }
                }
            }
        }



        if (mallconfig.isPlatformFee == 1) {
            List<Shop> records = page.getRecords();
            if (!CollectionUtils.isEmpty(records)) {
                // 查询是否欠费
                for (Shop record : records) {
                    PlatformYearFee one = platformYearFeeService.lambdaQuery()
                            .eq(PlatformYearFee::getShopId, record.getShopId())
                            .eq(PlatformYearFee::getServeType, 1)
                            .one();
                    if (one == null) {
                        record.setIsArrearage(0);
                        continue;
                    }
                    LocalDate serveEndTime = one.getServeEndTime();
                    LocalDate currentDate = LocalDate.now();
                    // 只有当前时间在截止时间之后属于过期
                    if (serveEndTime == null || currentDate.isAfter(serveEndTime) || currentDate.equals(serveEndTime)) {
                        record.setIsArrearage(0);
                    } else {
                        record.setIsArrearage(1);
                    }
                }
            }
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils findTwoSupplierShopByPage(JSONObject jsonObject, LambdaQueryWrapper<Shop> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String shopName = (String) innerMap.get("shopName");
        //用户可以根据模糊匹配企业名称查询店铺
        if (!StringUtils.isEmpty(shopName)) {
            queryWrapper.like(Shop::getShopName, shopName);
        }
        queryWrapper.eq(Shop::getAuditStatus, 1);
        queryWrapper.eq(Shop::getMallType, mallconfig.mallType);
        queryWrapper.orderByDesc(Shop::getGmtModified);
        queryWrapper.exists("SELECT 1 FROM shop_supplier_rele WHERE shop_supplier_rele.supplier_id = shop.enterprise_id");
        IPage<Shop> page = this.page(
                new Query<Shop>().getPage(jsonObject),
                queryWrapper
        );
        List<Shop> list = page.getRecords();
        List<String> collect = list.stream().map(Shop::getEnterpriseId).collect(Collectors.toList());
        if (!collect.isEmpty()){
            List<EnterpriseInfo> enterpriseInfos = enterpriseInfoService.lambdaQuery()
                    .in(EnterpriseInfo::getEnterpriseId, collect)
                    .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getEnterpriseName).list();
            for (Shop shop : list) {
                for (EnterpriseInfo enterpriseInfo : enterpriseInfos) {
                    if (shop.getEnterpriseId().equals(enterpriseInfo.getEnterpriseId())) {
                        shop.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        break;
                    }
                }
            }
        }
        return new PageUtils(page);
    }

    @Override
    public List<String> findTwoSupplierShopId() {
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.exists("SELECT 1 FROM shop_supplier_rele WHERE shop_supplier_rele.supplier_id = shop.enterprise_id");
        List<Shop> shops = shopMapper.selectList(queryWrapper);
        return shops.stream().map(Shop::getShopId).distinct().collect(Collectors.toList());
    }

    @Override
    public PageUtils queryNotPage(JSONObject jsonObject, LambdaQueryWrapper<Shop> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String shopName = (String) innerMap.get("shopName");
        String city = (String) innerMap.get("city");
        Integer shopType = (Integer) innerMap.get("shopType");
        Integer state = (Integer) innerMap.get("state");
        Integer auditStatus = (Integer) innerMap.get("auditStatus");
        Integer isBusiness = (Integer) innerMap.get("isBusiness");
        Integer isInternalShop = (Integer) innerMap.get("isInternalShop");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String detailedAddress = (String) innerMap.get("detailedAddress");

        if (!StringUtils.isEmpty(shopName)) {
            queryWrapper.like(Shop::getShopName, shopName);
        }
        if (!StringUtils.isEmpty(city)) {
            queryWrapper.like(Shop::getCity, city);
        }
        if (!StringUtils.isEmpty(detailedAddress)) {
            queryWrapper.like(Shop::getDetailedAddress, detailedAddress);
        }
        if (shopType != null) {
            queryWrapper.eq(Shop::getShopType, shopType);
        }
        if (isInternalShop != null) {
            queryWrapper.eq(Shop::getIsInternalShop, isInternalShop);
        }
        if (isBusiness != null) {
            queryWrapper.eq(Shop::getIsBusiness, isBusiness);
        }
        if (state != null) {
            queryWrapper.eq(Shop::getState, state);
        }


        if (org.apache.commons.lang.StringUtils.isNotEmpty(keywords)) {
            List<EnterpriseInfo> list = enterpriseInfoService.lambdaQuery().like(EnterpriseInfo::getEnterpriseName, keywords)
                    .select(EnterpriseInfo::getEnterpriseId).list();
            if (list != null && list.size() > 0) {
                List<String> enterPriseIds = list.stream().map(item -> item.getEnterpriseId()).collect(Collectors.toList());
                queryWrapper.and((t) -> {
                    t.like(Shop::getShopName, keywords)
                            .or().like(Shop::getRemarks, keywords)
                            .or().in(Shop::getEnterpriseId, enterPriseIds);
                });
            } else {
                queryWrapper.and((t) -> {
                    t.like(Shop::getShopName, keywords)
                            .or().like(Shop::getRemarks, keywords);
                });
            }
        }
        queryWrapper.eq(Shop::getMallType, mallconfig.mallType);
        //排序方式(排序值升序)
        if (orderBy == 1) {
            queryWrapper.orderByDesc(Shop::getSort)
                    .orderByDesc(Shop::getGmtModified);
            //排序方式(修改时间降序)
        } else if (orderBy == 2) {
            queryWrapper.orderByDesc(Shop::getGmtModified);
            //排序方式(创建时间降序)
        } else if (orderBy == 3) {
            queryWrapper.orderByDesc(Shop::getGmtCreate);
        }
        //店铺审核状态不等于（1：通过）
        queryWrapper.ne(Shop::getAuditStatus, 1);
        //逻辑删除（0：未删除）
        queryWrapper.eq(Shop::getIsDelete, 0);
        IPage<Shop> page = this.page(
                new Query<Shop>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }


    /**
     * 添加店铺同事赋予角色
     *
     * @param shop
     * @param roles
     * @return R
     */
    @Override
    public R create(Shop shop, List<Role> roles) {
        //调用父类方法即可
        //也可以baseMapper.insert
        //雪花算法生成店铺id
        boolean save = super.save(shop);
        if (save) {
            R r = roleService.insertRoleToShop(roles, "1");//shopId使用雪花算法生成（待填）
            return r;
        } else {
            return R.failed("添加失败！");
        }

    }

    @Override
    public void update(Shop shop) {
        super.updateById(shop);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void freezeShop(Shop shop) {
        super.updateById(shop);
        //shop.state店铺状态 1:启用 0停用;product.showState是否不显示（0显示1不显示）默认0
        productService.lambdaUpdate().eq(Product::getShopId, shop.getShopId())
        .set(Product::getShowState, shop.getState()==0?1:0).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchIsOrderAuth(List<Shop> shops) {
        for (Shop shop : shops) {
            super.updateById(shop);
            //shop.state店铺状态 1:启用 0停用;product.showState是否不显示（0显示1不显示）默认0
            productService.lambdaUpdate().eq(Product::getShopId, shop.getShopId())
            .set(Product::getShowState, shop.getState()==0?1:0).update();
        }
    }


    @Override
    public Shop getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public Shop selectByUserId(String userId) {
        QueryWrapper<Shop> queryWrapper = new QueryWrapper();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("mall_type", mallconfig.mallType);
        Shop one = getOne(queryWrapper);

        return one;
    }

    @Override
    public WebsiteShopVo getWebsiteShop(String shopId, Integer mallType) {
        if (mallType == null) {
            mallType = mallconfig.mallType;
        }
        WebsiteShopVo vo = new WebsiteShopVo();
        Shop shop = getById(shopId);
        if (shop == null) {
            throw new BusinessException(400, "店铺不存在！");
        }
        if (shop.getState() != 1) {
            throw new BusinessException(400, "店铺被停用！");
        }
        shop.setCounty(null);
        shop.setDetailedAddress(shop.getProvince() + shop.getCity());
        ShopComment shopComment = shopCommentService.lambdaQuery().eq(ShopComment::getShopId,shop.getShopId()).one();
        if (shopComment != null) {
            shop.setCommentServiceScore(Optional.ofNullable(shopComment.getCommentServiceScore())
                    .orElse(BigDecimal.ZERO).toString());
            shop.setCommentLevel(Optional.ofNullable(shopComment.getCommentLevel())
                    .orElse(BigDecimal.ZERO).toString());
            shop.setCommentSupply(Optional.ofNullable(shopComment.getCommentSupply())
                    .orElse(BigDecimal.ZERO).toString());
            shop.setCommentIntegrity(Optional.ofNullable(shopComment.getCommentIntegrity())
                    .orElse(BigDecimal.ZERO).toString());
            shop.setCommentService(Optional.ofNullable(shopComment.getCommentService())
                    .orElse(BigDecimal.ZERO).toString());
        }
        EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, shop.getEnterpriseId())
                .one();
        vo.setEnterpriseInfo(one);
        shop.setTaxRate(one.getTaxRate());
        vo.setShop(shop);
        return vo;
    }

    @Override
    public String getShopPhoneByShopId(String shopId) {
        LambdaQueryWrapper<Shop> queryWrapper = getPulicShopLambdaQueryWrapper();
        queryWrapper.eq(Shop::getShopId, shopId);
        queryWrapper.select(Shop::getContactNumber);
        Shop shop = getOne(queryWrapper);
        if (shop != null) {
            return shop.getContactNumber();
        }
        return "查询失败";
    }

    String[] initials = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

    @Override
    public PageUtils findPublicShop(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer initial = (Integer) innerMap.get("initial");
        if (initial != null && initial > 0) {
            innerMap.put("initial",initials[initial - 1]);
        }
        String orderBy = (String) innerMap.get("orderBy");
        if (org.apache.commons.lang.StringUtils.isEmpty(orderBy)) {
            innerMap.put("orderBy", 0);
        } else {
            if (orderBy.equals("4-desc")) {
                innerMap.put("orderBy", 41);
            }
            if (orderBy.equals("4-asc")) {
                innerMap.put("orderBy", 40);
            }
            if (orderBy.startsWith("0")) {
                innerMap.put("orderBy", 0);
            }
        }
        if (innerMap.get("keywords") != null) {
            innerMap.put("keywords", (String) innerMap.get("keywords").toString().trim());
        }
        Page<Shop> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<Shop> vos = shopMapper.findPublicShop(pages, innerMap);
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    /**
     * 首页查询供应商
     *
     * @param jsonObject
     * @return
     */
    @Override
    public PageUtils getIndexSupplierList(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String area = (String) innerMap.get("area");
        if (area != null) {
            innerMap.put("area", area);
        }
        String keywords = (String) innerMap.get("keywords");
        if (keywords != null) {
            innerMap.put("keywords", keywords);
        }
        Integer initial = (Integer) innerMap.get("initial");
        if (initial != null && initial > 0) {
            innerMap.put("initial", initials[initial - 1]);
        }
        int count = shopMapper.getIndexSupplierListCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);
        Page<Shop> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<Shop> list = shopMapper.getIndexSupplierList(pages, innerMap);
        pages.setRecords(list);
        return new PageUtils(pages);
    }

    /**
     * 根据店铺id查询店铺名称
     *
     * @param shopId
     * @return
     */
    @Override
    public String getShopNameById(String shopId) {
        Shop one = lambdaQuery().eq(Shop::getShopId, shopId).select(Shop::getShopName).one();
        if (one != null) {
            return one.getShopName();
        }
        return null;
    }

    @Autowired
    PlatformYearFeeService platformYearFeeService;

    /**
     * 批量更新店铺信息
     *
     * @param shops
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchAuditStatus(List<Shop> shops) {
        for (Shop shop : shops) {
            Shop one = lambdaQuery().eq(Shop::getShopId, shop.getShopId())
                    .select(Shop::getEnterpriseId, Shop::getShopName).one();
            EnterpriseInfo byId = enterpriseInfoService.getById(one.getEnterpriseId());
            if (byId == null) {
                throw new BusinessException("企业信息不存在");
            } else {
                if (shop.getAuditStatus() == 1) {
                    if (byId.getSocialCreditCode() != null) {
                        if (byId.getIsPcwp() == 0) {

                            String url2 = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.IS_PCWP
                                    + byId.getSocialCreditCode();
                            HttpHeaders headers2 = new HttpHeaders();
                            headers2.add("token", mallConfig.thirdApiToken);
                            R isSupp = restTemplateUtils.get(url2, headers2, R.class).getBody();
                            if (isSupp.getCode() == null || isSupp.getCode() != 200) {
//                                throw new BusinessException(400, "该店铺不是PCWP供应商,请先去pcwp注册！");
                                byId.setIsPcwp(1);
                                enterpriseInfoService.updateById(byId);
                            }
                            if (isSupp.getData() == null) {
//                                throw new BusinessException(400, "该店铺不是PCWP供应商,请先去pcwp注册！");
                                byId.setIsPcwp(1);
                                enterpriseInfoService.updateById(byId);
                            } else {
                                byId.setIsPcwp(1);
                                enterpriseInfoService.updateById(byId);
                            }
                        }
                    } else {
                        if (byId.getIsSupplier() != 2) {
                            throw new BusinessException(400, "该店铺是内部用户,请在 账号查询-内部企业查询 设置为供应商！");
                        }
                    }
                }
                shop.setAuditPassTime(new Date());
                if (mallconfig.isPlatformFee == 1) {
                    if (shop.getAuditStatus() == 1) {
                        if (byId.getInteriorId() != null || byId.getShuDaoFlag() == 1) {
                        } else {
                            Integer count = platformYearFeeService.lambdaQuery().eq(PlatformYearFee::getEnterpriseId, byId.getEnterpriseId()).count();
                            if (count == 0) {
                                PlatformYearFee pyf = new PlatformYearFee();
                                pyf.setPlatformYearFeeNu(IdWorker.getIdStr());
                                pyf.setShopId(shop.getShopId());
                                pyf.setShopName(one.getShopName());
                                pyf.setEnterpriseId(byId.getEnterpriseId());
                                pyf.setEnterpriseName(byId.getEnterpriseName());
                                pyf.setServeType(1);
                                pyf.setOutTime(0);
                                platformYearFeeService.save(pyf);
                            }
                            // 新需求，审核后店铺自动停用
                            lambdaUpdate().eq(Shop::getShopId, shop.getShopId())
                                    .set(Shop::getState, 0).update();
                        }
                    }

                }
            }
            update(shop);
        }
    }

    /**
     * 根据店铺id查询店铺信息
     *
     * @param shopId
     * @return
     */
    @Override
    public OpenShopInfoVO getRestartOpenShopInfo(String shopId) {
        OpenShopInfoVO vo = new OpenShopInfoVO();
        Shop byId = getById(shopId);
        if (byId != null) {
            BeanUtils.copyProperties(byId, vo);
        }
        return vo;
    }

    /**
     * 查询店铺信息
     *
     * @return
     */
    @Override
    public ShopInfoUpdateVO getShopInfo() {
        ShopInfoUpdateVO vo = new ShopInfoUpdateVO();
        // 查询商品信息
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        Shop shop = getById(user.getShopId());
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId())
                .select(EnterpriseInfo::getBusinessLicense, EnterpriseInfo::getBusinessLicenseId, EnterpriseInfo::getTaxRate)
                .one();
        if (shop == null) return vo;
        BeanUtils.copyProperties(shop, vo);
        // 获取营业执照
        if (enterpriseInfo != null) {
            vo.setTaxRate(enterpriseInfo.getTaxRate());
            vo.setBusinessLicense(enterpriseInfo.getBusinessLicense());
            vo.setBusinessLicenseId(enterpriseInfo.getBusinessLicenseId());
        }
        // 获取主图
        List<File> files = fileService.listFileByParameters(shop.getShopId(),
                FileEnum.RELEVANCE_TYPE_SHOP.getCode(),
                FileEnum.IS_MIN_YES.getCode(),
                FileEnum.TYPE_IMG.getCode(),
                FileEnum.IMG_TYPE_GENERAL.getCode()
        );
        vo.setAdminFile(files);
        // 获取小图
        List<File> files2 = fileService.listFileByParameters(shop.getShopId(),
                FileEnum.RELEVANCE_TYPE_SHOP.getCode(),
                FileEnum.IS_MIN_NO.getCode(),
                FileEnum.TYPE_IMG.getCode(),
                FileEnum.IMG_TYPE_MIN.getCode()
        );
        vo.setMinFile(files2);
        return vo;
    }


    @Autowired
    PlatformYearFeeRecordService platformYearFeeRecordService;


    @Autowired
    PlatformDealFeeRecordService platformDealFeeRecordService;

    /**
     * 修改店铺信息
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShopInfo(ShopInfoUpdateDTO dto) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        Shop shop = new Shop();
        BeanUtils.copyProperties(dto, shop);
        String oneChar = ChineseCharacterUtil.convertHanzi2Pinyin(dto.getShopName().substring(0, 1), false);
        shop.setInitial(oneChar.toUpperCase());
        // 保存前删除所有图片
        fileService.deleteBatchFileByRelevanceIdAndType(shop.getShopId(), FileEnum.RELEVANCE_TYPE_SHOP.getCode());
        String businessLicense = dto.getBusinessLicense();
        if (org.apache.commons.lang.StringUtils.isNotBlank(businessLicense)) {
            boolean update = enterpriseInfoService.lambdaUpdate()
                    .eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId())
                    .set(EnterpriseInfo::getBusinessLicense, businessLicense)
                    .set(EnterpriseInfo::getBusinessLicenseId, dto.getBusinessLicenseId())
                    .update();
            if (!update) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "企业营业执修改失败！");
            }
        }

        // 修改冗余店铺名称的
        if (mallconfig.isPlatformFee == 1) {
            platformDealFeeRecordService.lambdaUpdate().eq(PlatformDealFeeRecord::getShopId, dto.getShopId())
                    .set(PlatformDealFeeRecord::getShopName, shop.getShopName()).update();
            platformYearFeeService.lambdaUpdate().eq(PlatformYearFee::getShopId, dto.getShopId())
                    .set(PlatformYearFee::getShopName, shop.getShopName()).update();
            platformYearFeeRecordService.lambdaUpdate().eq(PlatformYearFeeRecord::getShopId, dto.getShopId())
                    .set(PlatformYearFeeRecord::getShopName, shop.getShopName()).update();
        }


        //if (!CollectionUtils.isEmpty(dto.getBusinessLicense())){
        //    File licenseFile =  dto.getBusinessLicense().get(0);
        //    licenseFile.setFileId(null);
        //    licenseFile.setRelevanceId(user.getEnterpriseId());
        //    boolean save1 = fileService.save(licenseFile);
        //    if(!save1){
        //        throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "营业执保存失败！");
        //    }
        //    boolean update = enterpriseInfoService.lambdaUpdate()
        //            .eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId())
        //            .set(EnterpriseInfo::getBusinessLicense, licenseFile.getUrl()).update();
        //    if(!update){
        //        throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "企业营业执修改失败！");
        //
        //    }
        //}
        if (!CollectionUtils.isEmpty(dto.getAdminFile())) {
            // 保存主图
            File adminFile = dto.getAdminFile().get(0);
            adminFile.setFileId(null);
            adminFile.setRelevanceId(shop.getShopId());
            boolean save2 = fileService.save(adminFile);
            if (!save2) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "图片修改失败！");
            } else {
                shop.setAdImg(adminFile.getUrl());
            }
        }
        if (!CollectionUtils.isEmpty(dto.getMinFile())) {
            // 保存小图
            File minFile = dto.getMinFile().get(0);
            minFile.setFileId(null);
            minFile.setRelevanceId(shop.getShopId());
            boolean save4 = fileService.save(minFile);
            if (!save4) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "图片修改失败！");
            } else {
                shop.setShopImg(minFile.getUrl());
            }
        }
        updateById(shop);
    }

    /**
     * 首页查询店铺
     *
     * @param size
     * @return
     */
    @Override
    public List<IndexShopListVO> getIndexShopList(Integer size) {
        List<Shop> list = lambdaQuery().eq(Shop::getState, 1)
                .eq(Shop::getIsIndexShow, 1).select(Shop.class, f -> {
                    return !f.getProperty().equals("shopDescrible") && !f.getProperty().equals("remarks");
                })
                .orderByDesc(Shop::getGmtModified)
                .last("limit " + size).list();
        ArrayList<IndexShopListVO> vos = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return vos;
        } else {
            for (Shop shop : list) {
                IndexShopListVO vo = new IndexShopListVO();
                BeanUtils.copyProperties(shop, vo);
                vos.add(vo);
            }
            return vos;
        }
    }

    /**
     * 获取店铺id
     *
     * @param dto
     * @return
     */
    @Override
    public Map<String, String> getOuterUseShopId(GetOuterUseShopIdDTO dto) {
        HashMap<String, String> map = new HashMap<>();
        boolean phoneInvalid = RegexUtils.isPhoneInvalid(dto.getOrgIdOrPhone());
        if (phoneInvalid) {
            // 传入的是orgId
            EnterpriseInfo en = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getInteriorId, dto.getOrgIdOrPhone())
                    .select(EnterpriseInfo::getEnterpriseId).one();
            if (en != null) {
                Shop shop = lambdaQuery().eq(Shop::getEnterpriseId, en.getEnterpriseId())
                        .eq(Shop::getMallType, mallconfig.mallType)
                        .eq(Shop::getState, 1)
                        .select(Shop::getShopId).one();
                map.put("shopId", shop.getShopId());
                map.put("shopName", shop.getShopName());
                return map;
            }
        } else {
            // 传入的手机号
            User user = userService.lambdaQuery().eq(User::getUserMobile, dto.getOrgIdOrPhone())
                    .eq(User::getIsInternalUser, 0)
                    .select(User::getEnterpriseId).one();
            if (user != null) {
                Shop shop = lambdaQuery().eq(Shop::getEnterpriseId, user.getEnterpriseId())
                        .eq(Shop::getMallType, mallconfig.mallType)
                        .eq(Shop::getState, 1)
                        .select(Shop::getShopId).one();
                map.put("shopId", shop.getShopId());
                map.put("shopName", shop.getShopName());
                return map;
            }
        }
        return null;
    }

    /**
     * 检查店铺id是否存在和状态启用
     *
     * @param shopId
     * @return
     */
    @Override
    public boolean isCheckShopIdState(String shopId) {
        Integer count = lambdaQuery().eq(Shop::getShopId, shopId)
                .eq(Shop::getState, 1)
                .eq(Shop::getMallType, mallconfig.mallType).count();
        if (count == 0) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public String getEnterpriseInfoNameByShopId(String shopId) {
        Shop one = lambdaQuery().eq(Shop::getShopId, shopId)
                .select(Shop::getEnterpriseId).one();
        if (one != null) {
            EnterpriseInfo one1 = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, one.getEnterpriseId())
                    .select(EnterpriseInfo::getEnterpriseName).one();
            if (one1 != null) {
                return one1.getEnterpriseName();
            }
        }
        return null;
    }


    /**
     * 取得网站的基础wrapp条件
     *
     * @return
     */
    private LambdaQueryWrapper<Shop> getPulicShopLambdaQueryWrapper() {
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Shop::getMallType, mallconfig.mallType);
        queryWrapper.eq(Shop::getState, 1);
        queryWrapper.eq(Shop::getAuditStatus, 1);
        return queryWrapper;

    }


    /**
     * 批量发布取消发布
     *
     * @param ids
     * @param type
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByPublish(List<String> ids, String type) {
        List<Shop> resutls = listByIds(ids);
        for (Shop shop : resutls) {
            if ("1".equals(type)) {
                shop.setState(PublicEnum.STATE_OPEN.getCode());
            } else {
                shop.setState(PublicEnum.STATE_STOP.getCode());
                productService.updateProductStateStop(ids);
                shoppingCartService.removeRealByShopId(shop.getShopId());
                // 发送站内信
                StationMessageReceiveVO vo = new StationMessageReceiveVO();
                vo.setTitle("店铺停用通知");
                vo.setContent("您好，您的店铺已被系统管理员停用，商品已经下架，有问题请联系管理员。");
                ArrayList<String> id = new ArrayList<>(1);
                id.add(shop.getEnterpriseId());
                vo.setEnterpriseIdList(id);
                stationMessageService.createBatch(vo);
            }
            update(shop);
        }
    }

    /**
     * 店铺停用之后发送站内信
     *
     * @param enterpriseId
     */
    private void sendMsg(String enterpriseId) {
        // 发送站内信
        StationMessageReceiveVO vo = new StationMessageReceiveVO();
        vo.setTitle("店铺停用通知");
        vo.setContent("您好，您的店铺已被系统管理员停用，商品已经下架，有问题请联系管理员。");
        ArrayList<String> id = new ArrayList<>(1);
        id.add(enterpriseId);
        vo.setEnterpriseIdList(id);
        stationMessageService.createBatch(vo);
    }

    @Override
    public PlatformShopCountVo getPlatformShopCount(JSONObject
                                                            jsonObject, QueryWrapper<ShopCountVo> shopCountVoQueryWrapper) {
        QueryWrapper<PlatformShopCountVo> q = new QueryWrapper<>();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        String shopName = (String) innerMap.get("shopName");
        q.between("gmt_modified", startCreateDate, endCreateDate);
        q.like("s.shop_name", shopName);
        return null;
    }

    /**
     * 根据信用代码查询店铺id
     *
     * @param socialCreditCode
     * @return
     */
    @Override
    public String getShopIdBySocialCreditCode(String socialCreditCode) {
        if (mallconfig.mallType == 1) {
            EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                    .eq(EnterpriseInfo::getSocialCreditCode, socialCreditCode)
                    .select(EnterpriseInfo::getEnterpriseId).one();
            if (enterpriseInfo == null) {
                throw new BusinessException(400, "供应商未注册！");
            }
            Shop shop = lambdaQuery().eq(Shop::getEnterpriseId, enterpriseInfo.getEnterpriseId())
                    .select(Shop::getShopId).one();
            if (shop == null) {
                throw new BusinessException(400, "该供应商未开店");
            }
            return shop.getShopId();
        } else {
            EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                    .eq(EnterpriseInfo::getSocialCreditCode, socialCreditCode)
                    .select(EnterpriseInfo::getEnterpriseId)
                    .one();
            if (enterpriseInfo == null) {
                throw new BusinessException(400, "供应商未注册！");
            }
            Shop shop = lambdaQuery().eq(Shop::getEnterpriseId, enterpriseInfo.getEnterpriseId())
                    .select(Shop::getShopId).one();
            if (shop == null) {
                throw new BusinessException(400, "该供应商未开店");
            }
            return shop.getShopId();
        }
    }

    @Override
    public Shop getDataByEnterPriseId(String supplierId) {
        LambdaQueryWrapper<Shop> q = new LambdaQueryWrapper<>();
        q.eq(Shop::getEnterpriseId, supplierId);
        Shop one = getOne(q);
        return one;
    }

    @Override
    public EnterpriseInfo getEnterpriseInfoByShopId(String shopId) {
        Shop one = lambdaQuery().eq(Shop::getShopId, shopId)
                .select(Shop::getEnterpriseId).one();
        EnterpriseInfo one1 = enterpriseInfoService.getById(one.getEnterpriseId());
        return one1;

    }


    @Override
    public void updateBatchArrearage(ShopArrearageStateVo vo) {
        lambdaUpdate().in(Shop::getEnterpriseId,vo.getEnterpriseIds()).set(Shop::getIsArrearage,vo.getState()).update();
    }


    @Override
    public void auditArrearageByShopId(String shopId) {
        Shop byId = getById(shopId);
        if (byId.getIsArrearage()==1){
            throw  new BusinessException("当前供应商暂未缴费，请通知供应商联系物资公司缴费后才能上架商品和接收订单");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public List<ImportShopExcelResultVO> shopArrearageExcelFile(MultipartFile file) {
        try {
            ArrayList<ImportShopExcelResultVO> vos = new ArrayList<>();
            List<ArrearageShopExcel> objects = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), ArrearageShopExcel.class);
            if (objects.size() > 4000) {
                throw new BusinessException(500, "数量超过4000禁止导入！");
            }
            for (ArrearageShopExcel shop : objects) {
                ImportShopExcelResultVO importExcelResultVO = new ImportShopExcelResultVO();
                importExcelResultVO.setSupperName(shop.getSupplierName());
                List<EnterpriseInfo> list = enterpriseInfoService.lambdaQuery().
                        eq(EnterpriseInfo::getEnterpriseName, shop.getSupplierName()).list();
                importExcelResultVO.setSupperName(shop.getSupplierName());
                if (list!=null&&list.size()>0){
                    Shop one = lambdaQuery().eq(Shop::getEnterpriseId, list.get(0).getEnterpriseId()).one();
                    if (one!=null){
                        importExcelResultVO.setShopNane(one.getShopName());
                        one.setIsArrearage(shop.getIsArrearage());
                        boolean b = updateById(one);
                        if (b){
                            importExcelResultVO.setState(1);
                            if (shop.getIsArrearage()==1){
                                importExcelResultVO.setFail("店铺欠费设置成功");
                            }else if (shop.getIsArrearage()==0){
                                importExcelResultVO.setFail("店铺续费费设置成功");
                            }
                        }else {
                            importExcelResultVO.setState(0);
                            importExcelResultVO.setFail("店铺保存失败，修改店铺欠费续费功能失败");
                        }
                    }else {
                        importExcelResultVO.setState(0);
                        importExcelResultVO.setFail("店铺不存在，修改店铺欠费续费功能失败");
                    }
                }else {
                    importExcelResultVO.setState(0);
                    importExcelResultVO.setFail("供应商不存在，修改店铺欠费续费功能失败");
                }
                vos.add(importExcelResultVO);
            }
            return vos;
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
       return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public List<ImportShopExcelResultVO> stopShopStateExcelFile(MultipartFile file) {
        try {
            ArrayList<ImportShopExcelResultVO> vos = new ArrayList<>();
            List<StopShopExcel> objects = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), StopShopExcel.class);
            if (objects.size() > 4000) {
                throw new BusinessException(500, "数量超过4000禁止导入！");
            }
            for (StopShopExcel shop : objects) {
                ImportShopExcelResultVO importExcelResultVO = new ImportShopExcelResultVO();
                List<EnterpriseInfo> list = enterpriseInfoService.lambdaQuery().
                        eq(EnterpriseInfo::getEnterpriseName, shop.getSupplierName()).list();
                importExcelResultVO.setSupperName(shop.getSupplierName());
                if (list!=null&&list.size()>0){
                    Shop one = lambdaQuery().eq(Shop::getEnterpriseId, list.get(0).getEnterpriseId()).one();
                    if (one!=null){
                        importExcelResultVO.setShopNane(one.getShopName());
                        one.setState(shop.getState());
                        boolean b = updateById(one);
                        if (b){
                            importExcelResultVO.setState(1);
                            if (shop.getState()==1){
                                importExcelResultVO.setFail("店铺启用成功");
                            }else if (shop.getState()==0){
                                importExcelResultVO.setFail("店铺关闭成功");
                            }
                        }else {
                            importExcelResultVO.setState(0);
                            importExcelResultVO.setFail("店铺修改启用停用状态失败");
                        }
                    }else {
                        importExcelResultVO.setState(0);
                        importExcelResultVO.setFail("店铺不存在，修改店铺启用停用状态失败");
                    }
                }else {
                    importExcelResultVO.setState(0);
                    importExcelResultVO.setFail("供应商不存在，修改店铺状态失败");
                }
                vos.add(importExcelResultVO);
            }
            return vos;
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
        return null;
    }
}
