package scrbg.meplat.mall.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import scrbg.meplat.mall.entity.ProcessRole;
import scrbg.meplat.mall.mapper.ProcessRoleMapper;
import scrbg.meplat.mall.service.ProcessRoleService;

/**
 * 流程配置表 服务类
 * <AUTHOR>
 * @date: 2025年6月20日 上午10:06:41
 */
@Service
public class ProcessRoleServiceImpl extends ServiceImpl<ProcessRoleMapper, ProcessRole> implements ProcessRoleService {

    @Autowired
    ProcessRoleService processRoleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProcessRole processRole) {
        super.save(processRole);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProcessRole processRole) {
        super.updateById(processRole);
    }

    @Override
    public ProcessRole getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        super.removeById(id);
    }

}
