package scrbg.meplat.mall.util;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * JWT配置类
 * <AUTHOR>
 */
@Getter
//@ConfigurationProperties(prefix = "jwt")
public class JWTConfig {
    /**
     * 密钥KEY
     */
    public static String secret = "sdfa2-shk2";
    /**
     * TokenKey
     */
    public static String tokenHeader = "token";
    /**
     * Token前缀字符
     */
    public static String tokenPrefix = "mall";
    /**
     * 过期时间（单位的分钟）
     */
    public static Integer expiration = 60;

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public void setTokenHeader(String tokenHeader) {
        this.tokenHeader = tokenHeader;
    }

    public void setTokenPrefix(String tokenPrefix) {
        this.tokenPrefix = tokenPrefix;
    }

    public void setExpiration(Integer expiration) {
        this.expiration = expiration;
    }


}