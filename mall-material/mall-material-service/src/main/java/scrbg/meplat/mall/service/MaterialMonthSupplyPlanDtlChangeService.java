package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtlChange;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtlChange;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
/**
 * @描述：计划明细 服务类
 * @作者: ye
 * @日期: 2023-06-27
 */
public interface MaterialMonthSupplyPlanDtlChangeService extends IService<MaterialMonthSupplyPlanDtlChange> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMonthSupplyPlanDtlChange> queryWrapper);

        void create(MaterialMonthSupplyPlanDtlChange materialMonthSupplyPlanDtlChange);

        void update(MaterialMonthSupplyPlanDtlChange materialMonthSupplyPlanDtlChange);

        MaterialMonthSupplyPlanDtlChange getById(String id);

        void delete(String id);
}