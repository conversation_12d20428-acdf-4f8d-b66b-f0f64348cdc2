package scrbg.meplat.mall.service.impl;

import cn.hutool.db.sql.Order;
import com.alibaba.fastjson.JSON;
import com.scrbg.common.utils.R;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.dto.plan.*;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.dto.user.OrgAndSon;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.MaterialMonthSupplyPlanDtlMapper;
import scrbg.meplat.mall.mapper.MaterialMonthSupplyPlanMapper;
import scrbg.meplat.mall.service.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.order.GetMaterialContractUseMonthPlanQtyVO;
import scrbg.meplat.mall.vo.user.userCenter.GetPlanChangeDtlInfoByPlanNoDtlVO;
import scrbg.meplat.mall.vo.user.userCenter.GetPlanChangeDtlInfoByPlanNoVO;
import scrbg.meplat.mall.vo.user.userCenter.GetPlanDtlInfoByPlanNoDtlVO;
import scrbg.meplat.mall.vo.user.userCenter.GetPlanDtlInfoByPlanNoVO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述：计划表 服务类
 * @作者: ye
 * @日期: 2023-06-27
 */
@Service
public class MaterialMonthSupplyPlanServiceImpl extends ServiceImpl<MaterialMonthSupplyPlanMapper, MaterialMonthSupplyPlan> implements MaterialMonthSupplyPlanService {

    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    @Autowired
    MallConfig mallConfig;

    @Autowired
    OrdersService ordersService;
    @Autowired
    OrderItemService orderItemService;

    @Autowired
    private MaterialMonthSupplyPlanDtlService materialMonthSupplyPlanDtlService;

    @Autowired
    private OrderSelectPlanService orderSelectPlanService;

    @Autowired
    private MaterialMonthSupplyPlanChangeService materialMonthSupplyPlanChangeService;

    @Autowired
    private MaterialMonthSupplyPlanDtlChangeService materialMonthSupplyPlanDtlChangeService;
    @Autowired
    private AuditRecordService auditRecordService;

    @Autowired
    private MaterialMonthSupplyPlanDtlMapper materialMonthSupplyPlanDtlMapper;

    @Autowired
    private RestTemplateUtils restTemplateUtils;

    // 大宗月供新增变更校验库存
    private static String URL1 = "/thirdapi/material/verifyInventory";
    // 审核大宗月供计划反写合同消耗数量
    private static String URL2 = "/thirdapi/material/saveConsumeRecord";
    // 获取物资合同清单
    private static String URL3 = "/thirdapi/material/contractInfoList";

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMonthSupplyPlan> q) {
        String profilesActive = mallConfig.profilesActive;
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        // 需要控制数据权限（本级和下级机构）
        if ("dev".equals(profilesActive)) {
            // 要查询的下级机构数据
            // 高级查询选择 1本机及子级2只看本级 3 指定
            Integer dataSelect = (Integer)jsonObject.get("dataSelect");
            List dataScopes = (List) jsonObject.get("dataScope");
            String roleName = RoleEnum.ROLE_5.getName();
            List<MallRole> mallRoles = currentUser.getMallRoles();
            if (CollectionUtils.isEmpty(mallRoles) || mallRoles.stream().noneMatch(t -> t.getName().equals(roleName))){
                throw new BusinessException(500, "当前用户无物资采购平台履约系统权限！");
            }
            MallRole role = mallRoles.stream().filter(t -> t.getName().equals(roleName)).findFirst().get();
            // 是否有下级单据查看权限
            // 机构数据查看权限（1本机及子级2只看本级3指定）
            // 获取当前人可查看的数据权限列表(ID使用的PCWP的ID)
            if (dataScopes != null && dataSelect == 1 && role.getOrgSearch() == 1){
                // 本机及子级
                    List<OrgAndSon> orgAndSons = currentUser.getOrgAndSon();
                    List<String> collect = orgAndSons.stream().map(t -> t.getOrgId()).collect(Collectors.toList());
                    q.in(!CollectionUtils.isEmpty(collect), MaterialMonthSupplyPlan::getOrgId, collect);
            }else if (dataScopes != null && dataSelect == 2){
                // 只看自己
                q.eq(MaterialMonthSupplyPlan::getOrgId, currentUser.getOrgId());
            } else if (dataScopes != null &&dataSelect == 3 && role.getOrgSearch() == 1 ){
                if (!CollectionUtils.isEmpty(dataScopes)){
                    q.in(!CollectionUtils.isEmpty(dataScopes), MaterialMonthSupplyPlan::getOrgId, dataScopes);
                }else {
                    // 查询除自己之外的下级机构
                    List<OrgAndSon> orgAndSons = currentUser.getOrgAndSon();
                    List<OrgAndSon> sons = orgAndSons.stream().filter(t -> !t.getOrgId().equals(currentUser.getOrgId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(sons)){
                        // 查本单位
                        q.eq(MaterialMonthSupplyPlan::getOrgId, currentUser.getOrgId());
                    }else {
                        List<String> collect = sons.stream().map(OrgAndSon::getOrgId).collect(Collectors.toList());
                        q.in(!CollectionUtils.isEmpty(collect), MaterialMonthSupplyPlan::getOrgId, collect);
                    }
                }
            }else{
                q.eq(MaterialMonthSupplyPlan::getOrgId, currentUser.getOrgId());
            }

        }else {
            // 默认只查询本机构的数据
            q.eq(MaterialMonthSupplyPlan::getLocalOrgId, ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        }
        String keywords = (String) jsonObject.get("keywords");
        String planNo = (String) jsonObject.get("planNo");
        String contractNo = (String) jsonObject.get("contractNo");
        String supplierName = (String) jsonObject.get("supplierName");
        String startPlanDate = (String) jsonObject.get("startPlanDate");
        String endPlanDate = (String) jsonObject.get("endPlanDate");
        List states = (ArrayList) jsonObject.get("states");

        q.between(io.seata.common.util.StringUtils.isNotEmpty(startPlanDate) && io.seata.common.util.StringUtils.isNotEmpty(endPlanDate), MaterialMonthSupplyPlan::getPlanDate, startPlanDate, endPlanDate);

        q.in(!CollectionUtils.isEmpty(states), MaterialMonthSupplyPlan::getState, states);
        q.like(StringUtils.isNotBlank(planNo), MaterialMonthSupplyPlan::getPlanNo, planNo);
        q.like(StringUtils.isNotBlank(contractNo), MaterialMonthSupplyPlan::getContractNo, contractNo);
        q.like(StringUtils.isNotBlank(supplierName), MaterialMonthSupplyPlan::getSupplierName, supplierName);

        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(MaterialMonthSupplyPlan::getPlanNo, keywords)
                        .or()
                        .like(MaterialMonthSupplyPlan::getContractNo, keywords)
                        .or()
                        .like(MaterialMonthSupplyPlan::getSupplierName, keywords);
            });
        }
        q.orderByDesc(MaterialMonthSupplyPlan::getGmtCreate);
        IPage<MaterialMonthSupplyPlan> page = this.page(
                new Query<MaterialMonthSupplyPlan>().getPage(jsonObject),
                q
        );
        List<MaterialMonthSupplyPlan> records = page.getRecords();
        // 如果计划不存在，查询变更状态、变更计划
        if (!CollectionUtils.isEmpty(records)) {
            for (MaterialMonthSupplyPlan record : records) {
                String planId = record.getPlanId();
                MaterialMonthSupplyPlanChange change = materialMonthSupplyPlanChangeService.lambdaQuery()
                        .eq(MaterialMonthSupplyPlanChange::getPlanId, planId)
                        .notIn(MaterialMonthSupplyPlanChange::getState, 2, 4)
                        .select(MaterialMonthSupplyPlanChange::getState, MaterialMonthSupplyPlanChange::getPlanChangeNo)
                        .one();
                if (change != null) {
                    record.setChangeState(change.getState());
                    record.setPlanChangeNo(change.getPlanChangeNo());
                }
            }
        }
        return new PageUtils(page);
    }

    @Resource
    private SysRoleService sysRoleService;

    @Override
    public void create(MaterialMonthSupplyPlan materialMonthSupplyPlan) {

        materialMonthSupplyPlan.setPlanCreateDate(new Date());
        materialMonthSupplyPlan.setLocalOrgId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        super.save(materialMonthSupplyPlan);
    }

    @Override
    public void update(MaterialMonthSupplyPlan materialMonthSupplyPlan) {
        super.updateById(materialMonthSupplyPlan);
    }


    @Override
    public MaterialMonthSupplyPlan getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }


    @Autowired
    InterfaceLogsService interfaceLogsService;

    /**
     * 新增计划并且新增计划明细
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPlanAndPlanDtl(MaterialMonthSupplyPlanDTO dto) {
        // 创建一个 Calendar 对象，并将 Date 设置进去
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dto.getPlanDate());

        // 获取年份和月份
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        // 一月只能计划一次
        Integer count = lambdaQuery().eq(MaterialMonthSupplyPlan::getContractId, dto.getContractId())
                .apply(String.format("YEAR(plan_date) = '%s'", year))
                .apply(String.format("MONTH(plan_date) = '%s'", month))
                .ne(MaterialMonthSupplyPlan::getState, 4)
                .eq(MaterialMonthSupplyPlan::getOrgId, dto.getOrgId()).count();
        if (count > 0) {
            throw new BusinessException(500, "该合同该月已生成计划！");
        }


        // 查询供应商，如果供应商不存在直接异常
        String supplierName = dto.getSupplierName();
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseName, supplierName)
                .select(EnterpriseInfo::getEnterpriseId)
                .one();
        if (enterpriseInfo == null) {
            throw new BusinessException(500, "供应商未在商城入驻！");
        }
        Integer isSubmit = dto.getIsSubmit();
        List<MaterialMonthSupplyPlanDtl> dtls = dto.getDtls();
        if (CollectionUtils.isEmpty(dtls)) {
            throw new BusinessException(500, "未选择计划明细！");
        }

        dto.setPlanCreateDate(new Date());
        dto.setLocalOrgId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        dto.setLocalSupplierId(enterpriseInfo.getEnterpriseId());
        if (isSubmit != null && isSubmit == 1) {
            dto.setState(1);
        } else {
            dto.setState(0);
        }
        MaterialMonthSupplyPlan materialMonthSupplyPlan = new MaterialMonthSupplyPlan();
        BeanUtils.copyProperties(dto, materialMonthSupplyPlan);
        save(materialMonthSupplyPlan);


        //  计算合同明细数量已消耗数量是否超过总数量
        for (MaterialMonthSupplyPlanDtl dtl : dtls) {
            if (mallConfig.isContractConsumeNum == 0) {
                QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
                dtlQ.eq("contract_dtl_id", dtl.getContractDtlId());
                dtlQ.ne("state", 4);
                dtlQ.select("sum(this_plan_qty) as count");
                List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
                if (maps.get(0) != null) {
                    Map<String, Object> stringObjectMap = maps.get(0);
                    // 获得总数量
                    BigDecimal totalNum = (BigDecimal) stringObjectMap.get("count");
                    // 选择数量+已消耗数量
                    totalNum = totalNum.add(dtl.getThisPlanQty());
                    if (totalNum.compareTo(dtl.getSourceQty()) == 1) {
                        throw new BusinessException(500, "【" + dtl.getMaterialName() + "】商品数量超过限制");
                    }
                }
            }


            dtl.setState(dto.getState());
            dtl.setPlanId(materialMonthSupplyPlan.getPlanId());

            materialMonthSupplyPlanDtlService.save(dtl);
        }
    }


    /**
     * 检查总数返回已消耗数量
     *
     * @param dtos
     * @return
     */
    @Override
    public List<Map> checkTotalNum(List<Map> dtos) {
        // 第一个map存储合同id和合同的version
        for (Map dto : dtos) {
            String dtlId = (String) dto.get("DtlId");
            QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
            dtlQ.eq("contract_dtl_id", dtlId);
            dtlQ.ne("state", 4);
            dtlQ.select("sum(this_plan_qty) as count");
            List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
            if (maps.get(0) == null) {
                dto.put("useQty", 0);
            } else {
                dto.put("useQty", maps.get(0).get("count"));
            }
            // 获取总数量
            BigDecimal qty = new BigDecimal(dto.get("Qty").toString());
            dto.put("maxQty", qty.subtract(new BigDecimal(dto.get("useQty").toString())));
            if (mallConfig.isContractConsumeNum == 1) {
                // 统计商城消耗数量（审核通过）
                QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ2 = new QueryWrapper<>();
                dtlQ2.eq("contract_dtl_id", dtlId);
                dtlQ2.eq("state", 2);
                dtlQ2.select("sum(this_plan_qty) as count");
                List<Map<String, Object>> maps2 = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ2);
                if (maps2.get(0) == null) {
                    dto.put("useQty", 0);
                } else {
                    dto.put("useQty", maps2.get(0).get("count"));
                }
                // 使用pcwp最大数量
                dto.put("maxQty", dto.get("ResidueQty"));
            }
            dto.put("thisPlanQty", 0);
        }
        return dtos;
    }

    /**
     * 批量提交审核
     *
     * @param planIds
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSubmitPlan(List<String> planIds) {
        if (CollectionUtils.isEmpty(planIds)) {
            throw new BusinessException(500, "计划id不能为空！");
        }
        for (String planId : planIds) {
            MaterialMonthSupplyPlan byId = lambdaQuery().eq(MaterialMonthSupplyPlan::getPlanId, planId)
                    .select(MaterialMonthSupplyPlan::getState, MaterialMonthSupplyPlan::getPlanId).one();
            if (byId == null) {
                continue;
            } else {
                Integer state = byId.getState();
                // 草稿，未通过才能提交
                if (state == 0 || state == 3) {
                    byId.setState(1);
                    materialMonthSupplyPlanDtlService.lambdaUpdate()
                            .eq(MaterialMonthSupplyPlanDtl::getPlanId, planId)
                            .set(MaterialMonthSupplyPlanDtl::getState, 1).update();
                    update(byId);
                }
            }
        }
    }

    /**
     * 批量删除
     *
     * @param planIds
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeletePlan(List<String> planIds) {
        if (CollectionUtils.isEmpty(planIds)) {
            throw new BusinessException(500, "计划id不能为空！");
        }
        for (String planId : planIds) {
            MaterialMonthSupplyPlan byId = lambdaQuery().eq(MaterialMonthSupplyPlan::getPlanId, planId).one();
            if (byId == null) {
                throw new BusinessException("未找到计划信息");
            } else {
                Integer state = byId.getState();
                // 草稿才能删除
                if (state == 0 || state == 3) {
                    delete(planId);
                    materialMonthSupplyPlanDtlService.lambdaUpdate().eq(MaterialMonthSupplyPlanDtl::getPlanId, planId).remove();
                }
            }
        }
    }

    /**
     * 根据计划编号获取计划明细
     *
     * @param planNo
     * @return
     */
    @Override
    public GetPlanDtlInfoByPlanNoVO getPlanDtlInfoByPlanNo(String planNo) {
        MaterialMonthSupplyPlan materialMonthSupplyPlan = lambdaQuery().eq(MaterialMonthSupplyPlan::getPlanNo, planNo).one();
        if (materialMonthSupplyPlan == null) {
            throw new BusinessException(500, "计划不存在！");
        }
        GetPlanDtlInfoByPlanNoVO vo = new GetPlanDtlInfoByPlanNoVO();
        BeanUtils.copyProperties(materialMonthSupplyPlan, vo);
        String planId = materialMonthSupplyPlan.getPlanId();
        List<MaterialMonthSupplyPlanDtl> dtlList = materialMonthSupplyPlanDtlService.lambdaQuery().eq(MaterialMonthSupplyPlanDtl::getPlanId, planId).list();

        List<GetPlanDtlInfoByPlanNoDtlVO> dtlVOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dtlList)) {
            for (MaterialMonthSupplyPlanDtl dtl : dtlList) {
                GetPlanDtlInfoByPlanNoDtlVO dtlVO = new GetPlanDtlInfoByPlanNoDtlVO();
                BeanUtils.copyProperties(dtl, dtlVO);

                dtlVO.setPlanNo(planNo);
                // 统计已消耗数量，和最多可选数量
                QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
                dtlQ.eq("contract_dtl_id", dtl.getContractDtlId());
                dtlQ.ne("state", 4);
                dtlQ.select("sum(this_plan_qty) as count");
                List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
                if (maps.get(0) == null) {
                    dtlVO.setUseQty(new BigDecimal(0));
                } else {
                    dtlVO.setUseQty(new BigDecimal(maps.get(0).get("count").toString()));
                }
                BigDecimal maxQty = dtlVO.getSourceQty().subtract(dtlVO.getUseQty());
                dtlVO.setMaxQty(maxQty);
                if (mallConfig.isContractConsumeNum == 1) {

                    // 统计已消耗数量，和最多可选数量
                    QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ2 = new QueryWrapper<>();
                    dtlQ2.eq("contract_dtl_id", dtl.getContractDtlId());
                    dtlQ2.eq("state", 2);
                    dtlQ2.select("sum(this_plan_qty) as count");
                    List<Map<String, Object>> maps2 = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ2);
                    if (maps2.get(0) == null) {
                        dtlVO.setUseQty(new BigDecimal(0));
                    } else {
                        dtlVO.setUseQty(new BigDecimal(maps2.get(0).get("count").toString()));
                    }


                    // 拿取最大值
                    dtlVO.setMaxQty(null);
                    // 拿到剩余数量
                    List<Map> rMaps = getContactResidueQtyByContactIdAndVersion(vo.getContractId(), vo.getPcwpVersion());
                    if (CollectionUtils.isEmpty(rMaps)) {
                        throw new BusinessException("合同明细数据为空！");
                    } else {
                        for (Map rMap : rMaps) {
                            if (dtlVO.getContractDtlId().equals(rMap.get("DtlId"))) {
                                dtlVO.setMaxQty(new BigDecimal(rMap.get("ResidueQty").toString()));
                            }
                        }
                        if (dtlVO.getMaxQty() == null) {
                            throw new BusinessException("未找到合同明细数据！");
                        }
                    }
                }

                dtlVO.setOldThisPlanQty(dtlVO.getThisPlanQty());
                dtlVO.setChangeQty(dtlVO.getThisPlanQty());
                // 用于变更
                dtlVO.setDtlUpdateState(2);

                if (mallConfig.isCountPlanOrderNum == 1) {
                    // TODO 计划最新统计
                    BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtl.getPlanDtlId());
                    dtlVO.setOrderQty(qty);

//                    Map map1 =  ordersService.getOrderUseCountMapBySelectPlanDtlId(dtl.getPlanDtlId());
//                    BigDecimal orderQty = new BigDecimal(map1.get("orderQty").toString());
//                    BigDecimal confirmCounts = new BigDecimal(map1.get("confirmCounts").toString());
//                    dtlVO.setOrderQty(orderQty);
//                    dtlVO.setConfirmCounts(confirmCounts);
                }


                dtlVOS.add(dtlVO);
            }
        }
        vo.setDtls(dtlVOS);

        // 获取审核历史
        List<AuditRecord> auditRecords = auditRecordService.lambdaQuery()
                .eq(AuditRecord::getRelevanceType, 1)
                .orderByDesc(AuditRecord::getGmtCreate)
                .eq(AuditRecord::getRelevanceId, planId).list();
        // 获取变更列表
        List<MaterialMonthSupplyPlanChange> list1 = materialMonthSupplyPlanChangeService.lambdaQuery()
                .eq(MaterialMonthSupplyPlanChange::getPlanId, materialMonthSupplyPlan.getPlanId())
                .orderByDesc(MaterialMonthSupplyPlanChange::getGmtCreate)
                .list();
        vo.setPlanChanges(list1);

        // 获取更变的审核
        if (!CollectionUtils.isEmpty(list1)) {
            List<String> collect = list1.stream().map(t -> t.getPlanChangeId()).collect(Collectors.toList());
            List<AuditRecord> list = auditRecordService.lambdaQuery()
                    .eq(AuditRecord::getRelevanceType, 2)
                    .orderByDesc(AuditRecord::getGmtCreate)
                    .in(AuditRecord::getRelevanceId, collect).list();
            auditRecords.addAll(list);
        }
        vo.setAuditList(auditRecords);

        // 变更次数
        Integer count = materialMonthSupplyPlanChangeService.lambdaQuery()
                .eq(MaterialMonthSupplyPlanChange::getPlanId, materialMonthSupplyPlan.getPlanId())
                .eq(MaterialMonthSupplyPlanChange::getState, 2)
                .count();
        vo.setAlterationCount(count);
        return vo;
    }

    /**
     * 修改月供计划
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlanDtlByPlanId(UpdatePlanDtlByPlanIdDTO dto) {
        String planId = dto.getPlanId();
        MaterialMonthSupplyPlan materialMonthSupplyPlan = getById(planId);
        if (materialMonthSupplyPlan == null) {
            throw new BusinessException(500, "计划不存在！");
        }
        Integer state = materialMonthSupplyPlan.getState();
        // 是草稿、未通过才能修改
        if (state == 0 || state == 3) {
            // 创建一个 Calendar 对象，并将 Date 设置进去
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(dto.getPlanDate());

            // 获取年份和月份
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            // 一月只能计划一次
            Integer count = lambdaQuery().eq(MaterialMonthSupplyPlan::getContractId, materialMonthSupplyPlan.getContractId())
                    .apply(String.format("YEAR(plan_date) = '%s'", year))
                    .apply(String.format("MONTH(plan_date) = '%s'", month))
                    .ne(MaterialMonthSupplyPlan::getState, 4)
                    .eq(MaterialMonthSupplyPlan::getOrgId, materialMonthSupplyPlan.getOrgId())
                    .ne(MaterialMonthSupplyPlan::getPlanId, planId)
                    .count();
            if (count > 0) {
                throw new BusinessException(500, "该合同该月已生成计划！");
            }
            materialMonthSupplyPlan.setPlanDate(dto.getPlanDate());
            Integer isSubmit = dto.getIsSubmit();
            if (isSubmit != null && isSubmit == 1) {
                materialMonthSupplyPlan.setState(1);
            } else {
                materialMonthSupplyPlan.setState(0);
            }
            materialMonthSupplyPlan.setRemarks(dto.getRemarks());
            update(materialMonthSupplyPlan);

            // 修改明细
            List<MaterialMonthSupplyPlanDtl> dtls = dto.getDtls();
            if (CollectionUtils.isEmpty(dtls)) {
                return;
            }
            ArrayList<String> dtlIds = new ArrayList<>();
            List<Map> rMaps = new ArrayList<>();
            // 拿到最新的数量
            for (MaterialMonthSupplyPlanDtl dtl : dtls) {
                String planDtlId = dtl.getPlanDtlId();
                // 是新增
                if (StringUtils.isEmpty(planDtlId)) {
                    if (mallConfig.isContractConsumeNum == 0) {
                        QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
                        dtlQ.eq("contract_dtl_id", dtl.getContractDtlId());
                        dtlQ.ne("state", 4);
                        dtlQ.select("sum(this_plan_qty) as count");
                        List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
                        BigDecimal useQty = null;
                        if (maps.get(0) == null) {
                            useQty = new BigDecimal(0);
                        } else {
                            useQty = new BigDecimal(maps.get(0).get("count").toString());
                        }
                        useQty = useQty.add(dtl.getThisPlanQty());
                        if (useQty.compareTo(dtl.getSourceQty()) == 1) {
                            throw new BusinessException("物资：【" + dtl.getMaterialName() + "】所选数量超过剩余数量！");
                        }
                    }
                    materialMonthSupplyPlanDtlService.save(dtl);
                    dtlIds.add(dtl.getPlanDtlId());
                } else {
                    // 修改
                    dtlIds.add(planDtlId);
                    MaterialMonthSupplyPlanDtl materialMonthSupplyPlanDtl = materialMonthSupplyPlanDtlService.getById(planDtlId);
                    if (materialMonthSupplyPlanDtl == null) {
                        throw new BusinessException("计划明细不存在");
                    }
                    if (mallConfig.isContractConsumeNum == 0) {
                        QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
                        dtlQ.eq("contract_dtl_id", materialMonthSupplyPlanDtl.getContractDtlId());
                        dtlQ.ne("state", 4);
                        dtlQ.ne("plan_dtl_id", planDtlId);
                        dtlQ.select("sum(this_plan_qty) as count");
                        List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
                        BigDecimal useQty = null;
                        if (maps.get(0) == null) {
                            useQty = new BigDecimal(0);
                        } else {
                            useQty = new BigDecimal(maps.get(0).get("count").toString());
                        }
                        useQty = useQty.add(dtl.getThisPlanQty());
                        if (useQty.compareTo(materialMonthSupplyPlanDtl.getSourceQty()) == 1) {
                            throw new BusinessException("物资：【" + dtl.getMaterialName() + "】所选数量超过剩余数量！");
                        }
                    }
                    materialMonthSupplyPlanDtl.setThisPlanQty(dtl.getThisPlanQty());
                    if (isSubmit != null && isSubmit == 1) {
                        materialMonthSupplyPlanDtl.setState(1);
                    } else {
                        materialMonthSupplyPlanDtl.setState(0);
                    }
                    materialMonthSupplyPlanDtlService.update(materialMonthSupplyPlanDtl);
                }
            }
            // 如果id不为空删除除此之外的数据
            if (!CollectionUtils.isEmpty(dtlIds)) {
                materialMonthSupplyPlanDtlService.lambdaUpdate().eq(MaterialMonthSupplyPlanDtl::getPlanId, planId)
                        .notIn(MaterialMonthSupplyPlanDtl::getPlanDtlId, dtlIds).remove();

            }

        }

    }

    /**
     * 批量作废
     *
     * @param planIds
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancellationPlan(List<String> planIds) {
        Integer isMothPlanAudit = ThreadLocalUtil.getCurrentUser().getIsMonthPlanAudit();
        if (isMothPlanAudit == null || isMothPlanAudit != 1) {
            throw new BusinessException(500, "没有审核权限请联系管理员！");
        }
        if (CollectionUtils.isEmpty(planIds)) {
            throw new BusinessException(500, "计划id不能为空！");
        }
        for (String planId : planIds) {
            MaterialMonthSupplyPlan byId = lambdaQuery().eq(MaterialMonthSupplyPlan::getPlanId, planId)
                    .select(MaterialMonthSupplyPlan::getState, MaterialMonthSupplyPlan::getPlanId).one();
            if (byId == null) {
                continue;
            } else {
                Integer state = byId.getState();
                if (state == 1) {
                    byId.setState(4);
                    materialMonthSupplyPlanDtlService.lambdaUpdate()
                            .eq(MaterialMonthSupplyPlanDtl::getPlanId, planId)
                            .set(MaterialMonthSupplyPlanDtl::getState, 4).update();
                    update(byId);
                }
            }
        }
    }

    /**
     * 审核计划
     *
     * @param dto
     * @param idStr
     * @param farArg
     * @param rD
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditPlan(AuditPlanDTO dto, String idStr, StringBuilder farArg, MaterialMonthSupplyPlan rD) {
        String planId = dto.getPlanId();
        Integer isMothPlanAudit = ThreadLocalUtil.getCurrentUser().getIsMonthPlanAudit();
        if (isMothPlanAudit == null || isMothPlanAudit != 1) {
            throw new BusinessException(500, "没有审核权限请联系管理员！");
        }
        MaterialMonthSupplyPlan byId = lambdaQuery().eq(MaterialMonthSupplyPlan::getPlanId, planId).one();
        rD.setPcwpVersion(byId.getPcwpVersion());
        if (byId == null) {
            throw new BusinessException(500, "计划不存在！");
        } else {
            Integer state1 = byId.getState();
            Integer isOpen = dto.getIsOpen();

            // 是待审核才进行审核
            if (state1 == 1 && isOpen != null) {
                // 通过
                if (isOpen == 1) {
                    AuditRecord auditRecord = new AuditRecord();
                    auditRecord.setRelevanceType(1);
                    auditRecord.setRelevanceId(planId);
                    auditRecord.setResultType(1);
                    auditRecord.setAuditType(1);
                    auditRecord.setAuditResult("【同意】");
                    auditRecordService.create(auditRecord);
                    byId.setState(2);

                    // 调用反写问题
                    if (mallConfig.isContractConsumeNum == 1) {
                        List<MaterialMonthSupplyPlanDtl> dtls = materialMonthSupplyPlanDtlService.lambdaQuery()
                                .eq(MaterialMonthSupplyPlanDtl::getPlanId, planId).list();
                        List<Map> maps = new ArrayList<>();
                        SaveConsumeRecordDTO redto = new SaveConsumeRecordDTO();
                        redto.setVersion(byId.getPcwpVersion());
                        redto.setContractId(byId.getContractId());
                        redto.setCredential(idStr);
                        redto.setType("add");
                        for (MaterialMonthSupplyPlanDtl dtl : dtls) {
                            HashMap<String, Object> map = new HashMap<>();
                            map.put("listId", dtl.getContractDtlId());
                            map.put("quantity", dtl.getThisPlanQty());
                            maps.add(map);
                        }
                        redto.setListInfo(maps);
                        // 发起反写计划请求
                        String content = JSON.toJSONString(redto);
                        farArg.append(content);
                        log.warn("反写合同商城数量：" + content);
                        // 发送请求
                        String url = mallConfig.prodPcwp2Url02 + URL2;
                        LogUtil.writeInfoLog(idStr, "auditPlan", dto, redto, null, MaterialMonthSupplyPlanServiceImpl.class);
                        R rMap = null;
                        if (mallConfig.selectMaterialMonthSupplyPlan==1){
                            try {
                                rMap = restTemplateUtils.postPCWP2(url, redto);
                            } catch (Exception e) {
                                LogUtil.writeErrorLog(idStr, "auditPlan", dto, redto, rMap, e.getMessage(), MaterialMonthSupplyPlanServiceImpl.class);
                                log.error(e.getMessage());
                                throw new BusinessException("【远程异常】反写合同商城数量错误！" + e.getMessage());
                            }
                            if (rMap.getCode() == null || rMap.getCode() != 200) {
                                LogUtil.writeErrorLog(idStr, "auditPlan", dto, redto, rMap, rMap.getMessage(), MaterialMonthSupplyPlanServiceImpl.class);
                                log.error("反写销售合同商城数量！返回：" + rMap);
                                throw new BusinessException("【远程异常】反写合同商城数量错误！" + rMap);
                            }


                            InterfaceLogs iLog = new InterfaceLogs();
                            iLog.setSecretKey(idStr);
                            iLog.setClassPackage(MaterialMonthSupplyPlanServiceImpl.class.getName());
                            iLog.setMethodName("auditPlan");
                            iLog.setLocalArguments(JSON.toJSONString(dto));
                            iLog.setFarArguments(content);
                            iLog.setIsSuccess(1);
                            iLog.setLogType(1);
                            iLog.setErrorInfo(null);
                            interfaceLogsService.create(iLog);
                        }

                    }

                }
                // 未通过
                if (isOpen == 0) {
                    String auditResult = dto.getAuditResult();
                    AuditRecord auditRecord = new AuditRecord();
                    auditRecord.setRelevanceType(1);
                    auditRecord.setRelevanceId(planId);
                    auditRecord.setResultType(2);
                    auditRecord.setAuditType(1);
                    auditRecord.setAuditResult("【拒绝】" + auditResult);
                    auditRecordService.create(auditRecord);
                    byId.setState(3);
                }
                update(byId);
                materialMonthSupplyPlanDtlService.lambdaUpdate()
                        .eq(MaterialMonthSupplyPlanDtl::getPlanId, planId)
                        .set(MaterialMonthSupplyPlanDtl::getState, byId.getState()).update();
            }

        }

    }

    /**
     * 新增变更计划
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createChangePlanAndPlanDtl(CreateChangePlanAndPlanDtlDTO dto) {
        // 创建一个 Calendar 对象，并将 Date 设置进去
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dto.getPlanDate());

        // 获取年份和月份
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        // 一月只能计划一次
        Integer count = lambdaQuery().eq(MaterialMonthSupplyPlan::getContractId, dto.getContractId())
                .apply(String.format("YEAR(plan_date) = '%s'", year))
                .apply(String.format("MONTH(plan_date) = '%s'", month))
                .ne(MaterialMonthSupplyPlan::getState, 4)
                .ne(MaterialMonthSupplyPlan::getPlanNo, dto.getPlanNo())
                .eq(MaterialMonthSupplyPlan::getOrgId, dto.getOrgId()).count();
        if (count > 0) {
            throw new BusinessException(500, "该合同该月已生成计划，无法变更！");
        }

        Integer isSubmit = dto.getIsSubmit();
        List<MaterialMonthSupplyPlanDtlChange> dtls = dto.getDtls();
        if (CollectionUtils.isEmpty(dtls)) {
            throw new BusinessException(500, "未选择计划明细！");
        }
        MaterialMonthSupplyPlanChange data = new MaterialMonthSupplyPlanChange();
        BeanUtils.copyProperties(dto, data);
        if (isSubmit != null && isSubmit == 1) {
            data.setState(1);
        } else {
            data.setState(0);
        }
        materialMonthSupplyPlanChangeService.save(data);


        List<Map> dtoRMaps = new ArrayList<>();
        for (MaterialMonthSupplyPlanDtlChange dtl : dtls) {
            // 新增
            if (dtl.getDtlUpdateState() == 1) {
                if (mallConfig.isContractConsumeNum == 0) {
                    QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
                    dtlQ.eq("contract_dtl_id", dtl.getContractDtlId());
                    dtlQ.ne("state", 4);
                    dtlQ.select("sum(this_plan_qty) as count");
                    List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
                    BigDecimal useQty = null;
                    if (maps.get(0) == null) {
                        useQty = new BigDecimal(0);
                    } else {
                        useQty = new BigDecimal(maps.get(0).get("count").toString());
                    }
                    useQty = useQty.add(dtl.getThisPlanQty());
                    if (useQty.compareTo(dtl.getSourceQty()) == 1) {
                        throw new BusinessException("【" + dtl.getMaterialName() + "】物资所选变更数量超过合同明细剩余数量！");
                    }
                }

                if (mallConfig.isContractConsumeNum == 1) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("listId", dtl.getContractDtlId());
                    map.put("quantity", dtl.getThisPlanQty());
                    dtoRMaps.add(map);
                }
            }
            MaterialMonthSupplyPlanDtl dtlServiceById = materialMonthSupplyPlanDtlService.getById(dtl.getPlanDtlId());

            // 修改
            if (dtl.getDtlUpdateState() == 2) {
                BigDecimal orderQty = dtlServiceById.getOrderQty();
                if (mallConfig.isCountPlanOrderNum == 1) {
                    // TODO 计划最新统计
                    BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtl.getPlanDtlId());
                    orderQty = qty;
                }
                BigDecimal thisPlanQty = dtl.getThisPlanQty();
                if (orderQty.compareTo(thisPlanQty) == 1) {
                    throw new BusinessException("【" + dtl.getMaterialName() + "】物资变更数量低于已下单数量！");
                }
                if (mallConfig.isContractConsumeNum == 0) {
                    QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
                    dtlQ.eq("contract_dtl_id", dtl.getContractDtlId());
                    dtlQ.ne("state", 4);
                    dtlQ.ne("plan_dtl_id", dtl.getPlanDtlId());
                    dtlQ.select("sum(this_plan_qty) as count");
                    List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
                    if (maps.get(0) != null) {
                        Map<String, Object> stringObjectMap = maps.get(0);
                        BigDecimal totalNum = (BigDecimal) stringObjectMap.get("count");
                        totalNum = totalNum.add(dtl.getThisPlanQty());
                        if (totalNum.compareTo(dtl.getSourceQty()) == 1) {
                            throw new BusinessException("【" + dtl.getMaterialName() + "】物资数量超过合同明细数量");
                        }
                    }
                }

                if (mallConfig.isContractConsumeNum == 1) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("listId", dtl.getContractDtlId());
                    BigDecimal oldQty = dtlServiceById.getThisPlanQty();
                    BigDecimal newQty = dtl.getThisPlanQty();
                    // 使用新数量进去旧数量
                    map.put("quantity", newQty.subtract(oldQty));
                    dtoRMaps.add(map);
                }
            }
            // 删除
            if (dtl.getDtlUpdateState() == 3) {
                BigDecimal orderQty = dtlServiceById.getOrderQty();
                if (mallConfig.isCountPlanOrderNum == 1) {
                    // TODO 计划最新统计
                    BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtl.getPlanDtlId());
                    orderQty = qty;
                }
                if (orderQty.compareTo(BigDecimal.ZERO) == 1) {
                    throw new BusinessException("物资：【" + dtlServiceById.getMaterialName() + "】已下单不可删除变更！");
                }
                if (mallConfig.isContractConsumeNum == 1) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("listId", dtl.getContractDtlId());
                    BigDecimal oldQty = dtlServiceById.getThisPlanQty();
                    // 删除直接给负数
                    map.put("quantity", oldQty.negate());
                    dtoRMaps.add(map);
                }
            }

            dtl.setPlanId(data.getPlanId());
            dtl.setPlanChangeId(data.getPlanChangeId());
            materialMonthSupplyPlanDtlChangeService.save(dtl);
        }

        if (mallConfig.isContractConsumeNum == 1) {
            SaveConsumeRecordDTO redto = new SaveConsumeRecordDTO();
            MaterialMonthSupplyPlan byId = getById(data.getPlanId());
            redto.setVersion(byId.getPcwpVersion());
            redto.setContractId(dto.getContractId());
            redto.setListInfo(dtoRMaps);
            // 验证库存
            String content = JSON.toJSONString(redto);
            log.warn("大宗月供新增变更校验库存请求参数：" + content);
            // 发送请求
            String url = mallConfig.prodPcwp2Url02 + URL1;
            R rMap = null;
            try {
                rMap = restTemplateUtils.postPCWP2(url, redto);
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new BusinessException("【远程异常】大宗月供新增变更校验消耗数异常！");
            }
            if (rMap == null) {
                throw new BusinessException("【远程异常】大宗月供新增变更校验消耗数异常！");
            }
            if (rMap.getCode() == null || rMap.getCode() != 200) {
                log.error("大宗月供新增变更校验消耗数异常！返回：" + rMap);
                throw new BusinessException(rMap.getCode(), rMap.getMessage());
            }
        }


    }

    /**
     * 根据变更计划编号获取变更数据
     *
     * @param planChangeNo
     * @return
     */
    @Override
    public GetPlanChangeDtlInfoByPlanNoVO getPlanChangeDtlInfoByPlanNo(String planChangeNo) {
        MaterialMonthSupplyPlanChange materialMonthSupplyPlanChange = materialMonthSupplyPlanChangeService.lambdaQuery()
                .eq(MaterialMonthSupplyPlanChange::getPlanChangeNo, planChangeNo).one();
        if (materialMonthSupplyPlanChange == null) {
            throw new BusinessException(500, "变更计划不存在！");
        }

        MaterialMonthSupplyPlan materialMonthSupplyPlan = lambdaQuery().eq(MaterialMonthSupplyPlan::getPlanId, materialMonthSupplyPlanChange.getPlanId())
                .select(MaterialMonthSupplyPlan::getPlanDate, MaterialMonthSupplyPlan::getPcwpVersion).one();
        if (materialMonthSupplyPlan == null) {
            throw new BusinessException(500, "计划不存在！");
        }

        GetPlanChangeDtlInfoByPlanNoVO vo = new GetPlanChangeDtlInfoByPlanNoVO();
        BeanUtils.copyProperties(materialMonthSupplyPlanChange, vo);

        vo.setThisTruePlanDate(materialMonthSupplyPlan.getPlanDate());
        vo.setPcwpVersion(materialMonthSupplyPlan.getPcwpVersion());

        String planChangeId = materialMonthSupplyPlanChange.getPlanChangeId();
        List<MaterialMonthSupplyPlanDtlChange> dtlList = materialMonthSupplyPlanDtlChangeService.lambdaQuery()
                .eq(MaterialMonthSupplyPlanDtlChange::getPlanChangeId, planChangeId).list();
        if (CollectionUtils.isEmpty(dtlList)) {
            throw new BusinessException(500, "计划明细不存在！");
        }
        List<GetPlanChangeDtlInfoByPlanNoDtlVO> dtlVOS = new ArrayList<>();
        for (MaterialMonthSupplyPlanDtlChange dtl : dtlList) {
            GetPlanChangeDtlInfoByPlanNoDtlVO dtlVO = new GetPlanChangeDtlInfoByPlanNoDtlVO();
            BeanUtils.copyProperties(dtl, dtlVO);

            // 可能存在新增的变更，或已删除变更成功的数据是没有源明细数据
            if (StringUtils.isNotEmpty(dtl.getPlanDtlId())) {
                MaterialMonthSupplyPlanDtl materialMonthSupplyPlanDtl = materialMonthSupplyPlanDtlService.lambdaQuery()
                        .eq(MaterialMonthSupplyPlanDtl::getPlanDtlId, dtl.getPlanDtlId())
                        .select(MaterialMonthSupplyPlanDtl::getThisPlanQty).one();
                if (materialMonthSupplyPlanDtl != null) {
                    dtlVO.setThisTrueQty(materialMonthSupplyPlanDtl.getThisPlanQty());
                }
            }

            // 统计已消耗数量，和最多可选数量
            QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
            dtlQ.eq("contract_dtl_id", dtl.getContractDtlId());
            dtlQ.ne("state", 4);
            dtlQ.select("sum(this_plan_qty) as count");
            List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
            BigDecimal userQty = null;
            if (maps.get(0) == null) {
                userQty = new BigDecimal(0);
            } else {
                userQty = new BigDecimal(maps.get(0).get("count").toString());
            }
            dtlVO.setMaxQty(dtlVO.getSourceQty().subtract(userQty));

            if (mallConfig.isContractConsumeNum == 1) {
                dtlVO.setMaxQty(null);
                // 拿到剩余数量
                List<Map> rMaps = getContactResidueQtyByContactIdAndVersion(vo.getContractId(), vo.getPcwpVersion());
                if (CollectionUtils.isEmpty(rMaps)) {
                    throw new BusinessException("合同明细数据为空！");
                } else {
                    for (Map rMap : rMaps) {
                        if (dtlVO.getContractDtlId().equals(rMap.get("DtlId"))) {
                            dtlVO.setMaxQty(new BigDecimal(rMap.get("ResidueQty").toString()));
                        }
                    }
                    if (dtlVO.getMaxQty() == null) {
                        throw new BusinessException("未找到合同明细数据！");
                    }
                }
            }


            dtlVO.setOldThisPlanQty(dtlVO.getThisPlanQty());


            if (mallConfig.isCountPlanOrderNum == 1) {
                // TODO 计划最新统计
                BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtl.getPlanDtlId());
                dtlVO.setOrderQty(qty);
            }
            dtlVOS.add(dtlVO);
        }
        vo.setDtls(dtlVOS);

        // 获取审核历史
        List<AuditRecord> list = auditRecordService.lambdaQuery()
                .eq(AuditRecord::getRelevanceType, 2)
                .orderByDesc(AuditRecord::getGmtCreate)
                .eq(AuditRecord::getRelevanceId, planChangeId).list();
        vo.setAuditList(list);
        return vo;
    }

    /**
     * 修改变更月供计划
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlanChangeDtlByPlanId(UpdatePlanChangeDtlByPlanIdDTO dto) {
        String planChangeId = dto.getPlanChangeId();
        MaterialMonthSupplyPlanChange materialMonthSupplyPlanChange = materialMonthSupplyPlanChangeService.getById(planChangeId);
        if (materialMonthSupplyPlanChange == null) {
            throw new BusinessException(500, "计划不存在！");
        }
        // 创建一个 Calendar 对象，并将 Date 设置进去
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dto.getPlanDate());

        // 获取年份和月份
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        // 一月只能计划一次
        Integer count = lambdaQuery().eq(MaterialMonthSupplyPlan::getContractId, materialMonthSupplyPlanChange.getContractId())
                .apply(String.format("YEAR(plan_date) = '%s'", year))
                .apply(String.format("MONTH(plan_date) = '%s'", month))
                .ne(MaterialMonthSupplyPlan::getState, 4)
                .eq(MaterialMonthSupplyPlan::getOrgId, materialMonthSupplyPlanChange.getOrgId())
                .ne(MaterialMonthSupplyPlan::getPlanId, materialMonthSupplyPlanChange.getPlanId())
                .count();
        if (count > 0) {
            throw new BusinessException(500, "该合同该月已生成计划！");
        }
        Integer state = materialMonthSupplyPlanChange.getState();
        if (state == 1 || state == 2 || state == 4) {
            throw new BusinessException(500, "单据不可修改！");
        }
        materialMonthSupplyPlanChange.setPlanDate(dto.getPlanDate());
        Integer isSubmit = dto.getIsSubmit();
        if (isSubmit != null && isSubmit == 1) {
            materialMonthSupplyPlanChange.setState(1);
        } else {
            // 如果状态是已提交重新变为操作
            materialMonthSupplyPlanChange.setState(0);
        }
        materialMonthSupplyPlanChange.setRemarks(dto.getRemarks());
        materialMonthSupplyPlanChangeService.update(materialMonthSupplyPlanChange);

        // 修改明细
        List<UpdatePlanChangeDtlByPlanIdDtlDTO> dtls = dto.getDtls();
        if (CollectionUtils.isEmpty(dtls)) {
            return;
        }
        // 处理新增、修改删除


        ArrayList<String> ids = new ArrayList<>();
        List<Map> dtoRMaps = new ArrayList<>();
        // 拿到最新的数量
        for (UpdatePlanChangeDtlByPlanIdDtlDTO dtl : dtls) {
            String planDtlChangeId = dtl.getPlanDtlChangeId();


            // 新增
            if (dtl.getDtlUpdateState() == 1) {
                if (mallConfig.isContractConsumeNum == 0) {
                    QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
                    dtlQ.eq("contract_dtl_id", dtl.getContractDtlId());
                    dtlQ.ne("state", 4);
                    dtlQ.select("sum(this_plan_qty) as count");
                    List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
                    BigDecimal useQty = null;
                    if (maps.get(0) == null) {
                        useQty = new BigDecimal(0);
                    } else {
                        useQty = new BigDecimal(maps.get(0).get("count").toString());
                    }
                    useQty = useQty.add(dtl.getThisPlanQty());
                    // 如果全部已消耗数量大于合同明细数量报错
                    if (useQty.compareTo(dtl.getSourceQty()) == 1) {
                        throw new BusinessException("【" + dtl.getMaterialName() + "】物资所选变更数量超过合同明细剩余数量！");
                    }
                }
                if (mallConfig.isContractConsumeNum == 1) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("listId", dtl.getContractDtlId());
                    map.put("quantity", dtl.getThisPlanQty());
                    dtoRMaps.add(map);
                }
            }
            MaterialMonthSupplyPlanDtl dtlServiceById = materialMonthSupplyPlanDtlService.getById(dtl.getPlanDtlId());
            if (dtl.getDtlUpdateState() == 2) {
                // 取真实的下单数量
                BigDecimal orderQty = dtlServiceById.getOrderQty();
                if (mallConfig.isCountPlanOrderNum == 1) {
                    // TODO 计划最新统计
                    BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtl.getPlanDtlId());
                    orderQty = qty;
                }
                BigDecimal thisPlanQty = dtl.getThisPlanQty();
                if (orderQty.compareTo(thisPlanQty) == 1) {
                    throw new BusinessException("【" + dtl.getMaterialName() + "】物资变更数量低于已下单数量！");
                }
                if (mallConfig.isContractConsumeNum == 0) {
                    QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
                    dtlQ.eq("contract_dtl_id", dtl.getContractDtlId());
                    dtlQ.ne("state", 4);
                    dtlQ.ne("plan_dtl_id", dtl.getPlanDtlId());
                    dtlQ.select("sum(this_plan_qty) as count");
                    List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
                    if (maps.get(0) != null) {
                        Map<String, Object> stringObjectMap = maps.get(0);
                        // 获得总数量
                        BigDecimal totalNum = (BigDecimal) stringObjectMap.get("count");
                        // 选择数量+已消耗数量
                        totalNum = totalNum.add(dtl.getThisPlanQty());
                        if (totalNum.compareTo(dtl.getSourceQty()) == 1) {
                            throw new BusinessException("【" + dtl.getMaterialName() + "】物资数量超过合同明细数量");
                        }
                    }
                }

                if (mallConfig.isContractConsumeNum == 1) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("listId", dtl.getContractDtlId());
                    BigDecimal oldQty = dtlServiceById.getThisPlanQty();
                    BigDecimal newQty = dtl.getThisPlanQty();
                    // 使用新数量进去旧数量
                    map.put("quantity", newQty.subtract(oldQty));
                    dtoRMaps.add(map);
                }
            }
            // 删除
            if (dtl.getDtlUpdateState() == 3) {
                BigDecimal orderQty = dtlServiceById.getOrderQty();
                if (mallConfig.isCountPlanOrderNum == 1) {
                    // TODO 计划最新统计
                    BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtl.getPlanDtlId());
                    orderQty = qty;
                }
                // 拿到原单判断是否存在下单
                if (orderQty.compareTo(BigDecimal.ZERO) == 1) {
                    throw new BusinessException("物资：【" + dtlServiceById.getMaterialName() + "】已下单不可删除变更！");
                }
                if (mallConfig.isContractConsumeNum == 1) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("listId", dtl.getContractDtlId());
                    BigDecimal oldQty = dtlServiceById.getThisPlanQty();
                    // 删除直接给负数
                    map.put("quantity", oldQty.negate());
                    dtoRMaps.add(map);
                }
            }


            if (StringUtils.isEmpty(planDtlChangeId)) {
                MaterialMonthSupplyPlanDtlChange d = new MaterialMonthSupplyPlanDtlChange();
                BeanUtils.copyProperties(dtl, d);
                // 如果数据为空则直接新增
                materialMonthSupplyPlanDtlChangeService.save(d);
                ids.add(d.getPlanDtlChangeId());
            } else {
                // 注意乐观锁
                MaterialMonthSupplyPlanDtlChange materialMonthSupplyPlanDtlChange = materialMonthSupplyPlanDtlChangeService.getById(planDtlChangeId);
                if (materialMonthSupplyPlanDtlChange == null) {
                    throw new BusinessException(500, "计划明细不存在");
                }
                ids.add(planDtlChangeId);
                materialMonthSupplyPlanDtlChange.setThisPlanQty(dtl.getThisPlanQty());
                materialMonthSupplyPlanDtlChange.setDtlUpdateState(dtl.getDtlUpdateState());
                materialMonthSupplyPlanDtlChangeService.update(materialMonthSupplyPlanDtlChange);
            }
        }


        // 删除没有携带过来的数据
        materialMonthSupplyPlanDtlChangeService.lambdaUpdate()
                .eq(MaterialMonthSupplyPlanDtlChange::getPlanChangeId, planChangeId)
                .notIn(MaterialMonthSupplyPlanDtlChange::getPlanDtlChangeId, ids).remove();

        if (mallConfig.isContractConsumeNum == 1) {
            SaveConsumeRecordDTO redto = new SaveConsumeRecordDTO();
            MaterialMonthSupplyPlan byId = getById(materialMonthSupplyPlanChange.getPlanId());
            redto.setVersion(byId.getPcwpVersion());
            redto.setContractId(byId.getContractId());
            redto.setListInfo(dtoRMaps);
            // 验证库存
            String content = JSON.toJSONString(redto);
            log.warn("大宗月供新增变更校验库存请求参数：" + content);
            // 发送请求
            String url = mallConfig.prodPcwp2Url02 + URL1;
            R rMap = null;
            try {
                rMap = restTemplateUtils.postPCWP2(url, redto);
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new BusinessException("【远程异常】大宗月供新增变更校验消耗数异常！");
            }
            if (rMap == null) {
                throw new BusinessException("【远程异常】大宗月供新增变更校验消耗数异常！");
            }
            if (rMap.getCode() == null || rMap.getCode() != 200) {
                log.error("大宗月供新增变更校验消耗数异常！返回：" + rMap);
                throw new BusinessException(rMap.getCode(), rMap.getMessage());
            }

        }

    }

    /**
     * 审核变更计划
     *
     * @param dto
     * @param idStr
     * @param farArg
     * @param rD
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditChangePlan(AuditChangePlanDTO dto, String idStr, StringBuilder farArg, MaterialMonthSupplyPlan rD) {
        String planChangeId = dto.getPlanChangeId();

        Integer isMothPlanAudit = ThreadLocalUtil.getCurrentUser().getIsMonthPlanAudit();
        if (isMothPlanAudit == null || isMothPlanAudit != 1) {
            throw new BusinessException(500, "没有审核权限请联系管理员！");
        }
        MaterialMonthSupplyPlanChange m = materialMonthSupplyPlanChangeService.lambdaQuery()
                .eq(MaterialMonthSupplyPlanChange::getPlanChangeId, planChangeId).one();
        MaterialMonthSupplyPlan one = lambdaQuery().eq(MaterialMonthSupplyPlan::getPlanId, m.getPlanId())
                .select(MaterialMonthSupplyPlan::getPcwpVersion, MaterialMonthSupplyPlan::getPlanId).one();
        rD.setPcwpVersion(one.getPcwpVersion());

        if (m == null) {
            throw new BusinessException(500, "变更计划不存在！");
        } else {
            Integer state = m.getState();
            Integer isOpen = dto.getIsOpen();

            // 是待审核才进行审核
            if (state == 1 && isOpen != null) {
                // 通过
                if (isOpen == 1) {
                    // 创建一个 Calendar 对象，并将 Date 设置进去
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(m.getPlanDate());

                    // 获取年份和月份
                    int year = calendar.get(Calendar.YEAR);
                    int month = calendar.get(Calendar.MONTH) + 1;

                    // 一月只能计划一次
                    Integer count = lambdaQuery().eq(MaterialMonthSupplyPlan::getContractId, m.getContractId())
                            .apply(String.format("YEAR(plan_date) = '%s'", year))
                            .apply(String.format("MONTH(plan_date) = '%s'", month))
                            .ne(MaterialMonthSupplyPlan::getState, 4)
                            .eq(MaterialMonthSupplyPlan::getOrgId, m.getOrgId())
                            .ne(MaterialMonthSupplyPlan::getPlanId, m.getPlanId())
                            .count();
                    if (count > 0) {
                        throw new BusinessException(500, "该合同该月已生成计划！");
                    }


                    AuditRecord auditRecord = new AuditRecord();
                    auditRecord.setRelevanceType(2);
                    auditRecord.setRelevanceId(planChangeId);
                    auditRecord.setResultType(1);
                    auditRecord.setAuditType(2);
                    auditRecord.setAuditResult("【同意】");
                    auditRecordService.create(auditRecord);
                    m.setState(2);
                    // 修改源单
                    MaterialMonthSupplyPlan materialMonthSupplyPlan = new MaterialMonthSupplyPlan();
                    materialMonthSupplyPlan.setPlanId(m.getPlanId());
                    materialMonthSupplyPlan.setPlanDate(m.getPlanDate());
                    if (StringUtils.isNotBlank(m.getRemarks())) {
                        materialMonthSupplyPlan.setRemarks(m.getRemarks());
                    }
                    update(materialMonthSupplyPlan);

                    List<Map> rMaps = new ArrayList<>();
                    // 修改明细处理新，新增、修改、删除的变更
                    // 修改源单明细
                    List<MaterialMonthSupplyPlanDtlChange> list = materialMonthSupplyPlanDtlChangeService.lambdaQuery()
                            .eq(MaterialMonthSupplyPlanDtlChange::getPlanChangeId, planChangeId).list();
                    if (!CollectionUtils.isEmpty(list)) {
                        for (MaterialMonthSupplyPlanDtlChange dtlChange : list) {
                            Integer dtlUpdateState = dtlChange.getDtlUpdateState();
                            // 新增
                            if (dtlUpdateState == 1) {
                                // 如果是新增源明细id是没有的，需要先新增原明细
                                MaterialMonthSupplyPlanDtl uD = new MaterialMonthSupplyPlanDtl();
                                BeanUtils.copyProperties(dtlChange, uD);
                                uD.setState(2);
                                materialMonthSupplyPlanDtlService.save(uD);
                                dtlChange.setPlanDtlId(uD.getPlanDtlId());
                                materialMonthSupplyPlanDtlChangeService.update(dtlChange);

                                if (mallConfig.isContractConsumeNum == 1) {
                                    HashMap<String, Object> map = new HashMap<>();
                                    map.put("listId", dtlChange.getContractDtlId());
                                    map.put("quantity", dtlChange.getThisPlanQty());
                                    rMaps.add(map);
                                }
                                continue;
                            }
                            String planDtlId = dtlChange.getPlanDtlId();
                            MaterialMonthSupplyPlanDtl dtlServiceById = materialMonthSupplyPlanDtlService.getById(planDtlId);


                            // 修改
                            if (dtlUpdateState == 2) {
                                // 检验数量是否使用超过数量
                                // 拿到最新的下单数量
                                BigDecimal orderQty = dtlServiceById.getOrderQty();
                                if (mallConfig.isCountPlanOrderNum == 1) {
                                    // TODO 计划最新统计
                                    BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtlServiceById.getPlanDtlId());
                                    orderQty = qty;
                                }
                                BigDecimal thisPlanQty = dtlChange.getThisPlanQty();
                                if (orderQty.compareTo(thisPlanQty) == 1) {
                                    throw new BusinessException("物资：【" + dtlServiceById.getMaterialName() + "】变更数量不能低于原单已下单数量！");
                                }

                                if (mallConfig.isContractConsumeNum == 0) {
                                    //  计算合同明细数量已消耗数量是否超过总数量
                                    QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
                                    dtlQ.eq("contract_dtl_id", dtlServiceById.getContractDtlId());
                                    dtlQ.ne("state", 4);
                                    dtlQ.ne("plan_dtl_id", dtlChange.getPlanDtlId()); // 排除自己
                                    dtlQ.select("sum(this_plan_qty) as count");
                                    List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
                                    if (maps.get(0) != null) {
                                        Map<String, Object> stringObjectMap = maps.get(0);
                                        // 获得总数量
                                        BigDecimal totalNum = (BigDecimal) stringObjectMap.get("count");
                                        // 选择数量+已消耗数量
                                        totalNum = totalNum.add(dtlChange.getThisPlanQty());
                                        if (totalNum.compareTo(dtlChange.getSourceQty()) == 1) {
                                            throw new BusinessException("【" + dtlChange.getMaterialName() + "】物资变更数量超过合同明细总数量");
                                        }
                                    }
                                }


                                if (mallConfig.isContractConsumeNum == 1) {
                                    HashMap<String, Object> map = new HashMap<>();
                                    map.put("listId", dtlChange.getContractDtlId());
                                    BigDecimal oldQty = dtlServiceById.getThisPlanQty();
                                    BigDecimal newQty = dtlChange.getThisPlanQty();
                                    map.put("quantity", newQty.subtract(oldQty));
                                    rMaps.add(map);
                                }
                                dtlServiceById.setThisPlanQty(dtlChange.getThisPlanQty());
                                materialMonthSupplyPlanDtlService.update(dtlServiceById);
                            }
                            // 删除
                            if (dtlUpdateState == 3) {
                                BigDecimal orderQty = dtlServiceById.getOrderQty();
                                if (mallConfig.isCountPlanOrderNum == 1) {
                                    // TODO 计划最新统计
                                    BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtlServiceById.getPlanDtlId());
                                    orderQty = qty;
                                }
                                // 拿到原单判断是否存在下单
                                if (orderQty.compareTo(BigDecimal.ZERO) == 1) {
                                    throw new BusinessException("物资：【" + dtlServiceById.getMaterialName() + "】已下单不可删除变更！");
                                }

                                if (mallConfig.isContractConsumeNum == 1) {
                                    HashMap<String, Object> map = new HashMap<>();
                                    map.put("listId", dtlServiceById.getContractDtlId());
                                    BigDecimal oldQty = dtlServiceById.getThisPlanQty();
                                    // 删除直接给负数
                                    map.put("quantity", oldQty.negate());
                                    rMaps.add(map);
                                }
                                materialMonthSupplyPlanDtlService.delete(planDtlId);
                            }
                        }


                        // 调用反写问题
                        if (mallConfig.isContractConsumeNum == 1) {
                            if (mallConfig.selectMaterialMonthSupplyPlan==1){
                                SaveConsumeRecordDTO redto = new SaveConsumeRecordDTO();
                                redto.setVersion(one.getPcwpVersion());
                                redto.setContractId(one.getContractId());
                                redto.setCredential(idStr);
                                redto.setType("change");
                                redto.setListInfo(rMaps);
                                // 发起反写计划请求
                                String content = JSON.toJSONString(redto);
                                farArg.append(content);
                                log.warn("反写合同商城数量：" + content);
                                // 发送请求
                                String url = mallConfig.prodPcwp2Url02 + URL2;
                                LogUtil.writeInfoLog(idStr, "auditChangePlan", dto, redto, null, MaterialMonthSupplyPlanServiceImpl.class);
                                R rMap = null;
                                try {
                                    rMap = restTemplateUtils.postPCWP2(url, redto);
                                } catch (Exception e) {
                                    LogUtil.writeErrorLog(idStr, "auditChangePlan", dto, redto, rMap, e.getMessage(), MaterialMonthSupplyPlanServiceImpl.class);
                                    log.error(e.getMessage());
                                    throw new BusinessException("【远程异常】反写合同商城数量错误！");
                                }
                                if (rMap.getCode() == null || rMap.getCode() != 200) {
                                    LogUtil.writeErrorLog(idStr, "auditChangePlan", dto, redto, rMap, rMap.getMessage(), MaterialMonthSupplyPlanServiceImpl.class);
                                    log.error("反写销售合同商城数量！返回：" + rMap);
                                    throw new BusinessException("【远程异常】反写合同商城数量错误！");
                                }


                                InterfaceLogs iLog = new InterfaceLogs();
                                iLog.setSecretKey(idStr);
                                iLog.setClassPackage(MaterialMonthSupplyPlanServiceImpl.class.getName());
                                iLog.setMethodName("auditChangePlan");
                                iLog.setLocalArguments(JSON.toJSONString(dto));
                                iLog.setFarArguments(content);
                                iLog.setIsSuccess(1);
                                iLog.setLogType(1);
                                iLog.setErrorInfo(null);
                                interfaceLogsService.create(iLog);
                            }
//                            SaveConsumeRecordDTO redto = new SaveConsumeRecordDTO();
//                            redto.setVersion(one.getPcwpVersion());
//                            redto.setContractId(one.getContractId());
//                            redto.setCredential(idStr);
//                            redto.setType("change");
//                            redto.setListInfo(rMaps);
//                            // 发起反写计划请求
//                            String content = JSON.toJSONString(redto);
//                            farArg.append(content);
//                            log.warn("反写合同商城数量：" + content);
//                            // 发送请求
//                            String url = mallConfig.prodPcwp2Url02 + URL2;
//                            LogUtil.writeInfoLog(idStr, "auditChangePlan", dto, redto, null, MaterialMonthSupplyPlanServiceImpl.class);
//                            R rMap = null;
//                            try {
//                                rMap = restTemplateUtils.postPCWP2(url, redto);
//                            } catch (Exception e) {
//                                LogUtil.writeErrorLog(idStr, "auditChangePlan", dto, redto, rMap, e.getMessage(), MaterialMonthSupplyPlanServiceImpl.class);
//                                log.error(e.getMessage());
//                                throw new BusinessException("【远程异常】反写合同商城数量错误！");
//                            }
//                            if (rMap.getCode() == null || rMap.getCode() != 200) {
//                                LogUtil.writeErrorLog(idStr, "auditChangePlan", dto, redto, rMap, rMap.getMessage(), MaterialMonthSupplyPlanServiceImpl.class);
//                                log.error("反写销售合同商城数量！返回：" + rMap);
//                                throw new BusinessException("【远程异常】反写合同商城数量错误！");
//                            }
//
//
//                            InterfaceLogs iLog = new InterfaceLogs();
//                            iLog.setSecretKey(idStr);
//                            iLog.setClassPackage(MaterialMonthSupplyPlanServiceImpl.class.getName());
//                            iLog.setMethodName("auditChangePlan");
//                            iLog.setLocalArguments(JSON.toJSONString(dto));
//                            iLog.setFarArguments(content);
//                            iLog.setIsSuccess(1);
//                            iLog.setLogType(1);
//                            iLog.setErrorInfo(null);
//                            interfaceLogsService.create(iLog);
                        }

                    }


                }
                // 未通过
                if (isOpen == 0) {
                    String auditResult = dto.getAuditResult();
                    AuditRecord auditRecord = new AuditRecord();
                    auditRecord.setRelevanceType(2);
                    auditRecord.setRelevanceId(planChangeId);
                    auditRecord.setResultType(2);
                    auditRecord.setAuditType(2);
                    auditRecord.setAuditResult("【拒绝】" + auditResult);
                    auditRecordService.create(auditRecord);
                    m.setState(3);
                }
                materialMonthSupplyPlanChangeService.update(m);


            }


        }
    }

    /**
     * 批量作废变更计划
     *
     * @param planChangeIds
     */
    @Override
    public void cancellationChangePlan(List<String> planChangeIds) {
        Integer isMothPlanAudit = ThreadLocalUtil.getCurrentUser().getIsMonthPlanAudit();
        if (isMothPlanAudit == null || isMothPlanAudit != 1) {
            throw new BusinessException(500, "没有作废权限请联系管理员！");
        }
        if (CollectionUtils.isEmpty(planChangeIds)) {
            throw new BusinessException(500, "计划id不能为空！");
        }
        for (String planChangeId : planChangeIds) {
            MaterialMonthSupplyPlanChange byId = materialMonthSupplyPlanChangeService.lambdaQuery()
                    .eq(MaterialMonthSupplyPlanChange::getPlanChangeId, planChangeId)
                    .select(MaterialMonthSupplyPlanChange::getState, MaterialMonthSupplyPlanChange::getPlanChangeId).one();
            if (byId != null) {
                if (byId.getState() == 1) {
                    byId.setState(4);
                    materialMonthSupplyPlanChangeService.update(byId);
                }
            }
        }
    }

    /**
     * 根据合同明细ids获取物资采购合同明细已生成月供计划数量
     *
     * @param dtlIds
     * @return
     */
    @Override
    public List<GetMaterialContractUseMonthPlanQtyVO> getMaterialContractUseMonthPlanQty(List<String> dtlIds) {
        ArrayList<GetMaterialContractUseMonthPlanQtyVO> vos = new ArrayList<>();
        for (String dtlId : dtlIds) {
            GetMaterialContractUseMonthPlanQtyVO vo = new GetMaterialContractUseMonthPlanQtyVO();
            QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
            dtlQ.eq("contract_dtl_id", dtlId);
            dtlQ.ne("state", 4);
            dtlQ.select("sum(this_plan_qty) as count");
            List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
            vo.setDtlId(dtlId);
            if (maps.get(0) == null) {
                vo.setUseQty(new BigDecimal("0"));
            } else {
                vo.setUseQty(new BigDecimal(maps.get(0).get("count").toString()));
            }
            vos.add(vo);
        }
        return vos;
    }


    @Override
    public PageUtils shopMangeListByEntity(JSONObject jsonObject, LambdaQueryWrapper<MaterialMonthSupplyPlan> q) {
        q.eq(MaterialMonthSupplyPlan::getLocalSupplierId, ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        String keywords = (String) jsonObject.get("keywords");
        String planNo = (String) jsonObject.get("planNo");
        String contractNo = (String) jsonObject.get("contractNo");
        String supplierName = (String) jsonObject.get("supplierName");
        String orgName = (String) jsonObject.get("orgName");
        String startPlanDate = (String) jsonObject.get("startPlanDate");
        String endPlanDate = (String) jsonObject.get("endPlanDate");
        Integer states = (Integer) jsonObject.get("state");

        q.between(io.seata.common.util.StringUtils.isNotEmpty(startPlanDate) && io.seata.common.util.StringUtils.isNotEmpty(endPlanDate), MaterialMonthSupplyPlan::getPlanDate, startPlanDate, endPlanDate);

        q.eq(states != null, MaterialMonthSupplyPlan::getState, states);
        q.like(StringUtils.isNotBlank(planNo), MaterialMonthSupplyPlan::getPlanNo, planNo);
        q.like(StringUtils.isNotBlank(contractNo), MaterialMonthSupplyPlan::getContractNo, contractNo);
        q.like(StringUtils.isNotBlank(supplierName), MaterialMonthSupplyPlan::getSupplierName, supplierName);
        q.like(StringUtils.isNotBlank(orgName), MaterialMonthSupplyPlan::getOrgName, orgName);

        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(MaterialMonthSupplyPlan::getPlanNo, keywords)
                        .or()
                        .like(MaterialMonthSupplyPlan::getContractNo, keywords)
                        .or()
                        .like(MaterialMonthSupplyPlan::getOrgName, keywords)
                        .or()
                        .like(MaterialMonthSupplyPlan::getSupplierName, keywords);
            });
        }
        q.orderByDesc(MaterialMonthSupplyPlan::getGmtCreate);
        IPage<MaterialMonthSupplyPlan> page = this.page(
                new Query<MaterialMonthSupplyPlan>().getPage(jsonObject),
                q
        );
        List<MaterialMonthSupplyPlan> records = page.getRecords();
        // 如果计划不存在，查询变更状态、变更计划
        if (!CollectionUtils.isEmpty(records)) {
            for (MaterialMonthSupplyPlan record : records) {
                String planId = record.getPlanId();
                MaterialMonthSupplyPlanChange change = materialMonthSupplyPlanChangeService.lambdaQuery()
                        .eq(MaterialMonthSupplyPlanChange::getPlanId, planId)
                        .notIn(MaterialMonthSupplyPlanChange::getState, 2, 4)
                        .select(MaterialMonthSupplyPlanChange::getState, MaterialMonthSupplyPlanChange::getPlanChangeNo)
                        .one();
                if (change != null) {
                    record.setChangeState(change.getState());
                    record.setPlanChangeNo(change.getPlanChangeNo());
                }
            }
        }
        return new PageUtils(page);
    }


    @Override
    public PageUtils secondLevelPlanMonthList(JSONObject jsonObject, QueryWrapper<MaterialMonthSupplyPlan> q) {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        q.eq("dtl.two_supplier_id", enterpriseId);
        String keywords = (String) jsonObject.get("keywords");
        String planNo = (String) jsonObject.get("planNo");
        String contractNo = (String) jsonObject.get("contractNo");
        String supplierName = (String) jsonObject.get("supplierName");
        String startPlanDate = (String) jsonObject.get("startPlanDate");
        String endPlanDate = (String) jsonObject.get("endPlanDate");
        Integer states = (Integer) jsonObject.get("state");
        q.between(io.seata.common.util.StringUtils.isNotEmpty(startPlanDate)
                        && io.seata.common.util.StringUtils.isNotEmpty(endPlanDate),
                "p_plan_date", startPlanDate, endPlanDate);

        q.eq(states != null, "p.state", states);
        q.like(StringUtils.isNotBlank(planNo), "p.plan_no", planNo);
        q.like(StringUtils.isNotBlank(contractNo), "p.contract_no", contractNo);
        q.like(StringUtils.isNotBlank(supplierName), "p.supplier_name", supplierName);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("p.plan_no", keywords)
                        .or()
                        .like("p.contract_no", keywords)
                        .or()
                        .like("p.supplier_name", keywords);
            });
        }
        q.orderByDesc("p.gmt_create");
        IPage<MaterialMonthSupplyPlan> page = new Query<MaterialMonthSupplyPlan>().getPage(jsonObject);
        ArrayList<MaterialMonthSupplyPlan> list = baseMapper.secondLevelPlanMonthList(page, q);
        page.setRecords(list);

        return new PageUtils(page);


    }

    @Override
    public void planExport(String id, HttpServletResponse response) {
        String src = mallConfig.templateFormUrl;
        MaterialMonthSupplyPlan plan = getById(id);
        List<MaterialMonthSupplyPlanDtl> dtls = materialMonthSupplyPlanDtlService.getListByPlanId(id);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("planDateStr", DateUtil.getYYYYmm(plan.getPlanDate()));
        dataMap.put("supplierName", plan.getSupplierName());
        dataMap.put("orgName", plan.getOrgName());
        dataMap.put("dataList", dtls);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "物资月度供应计划表模板.xlsx", src, "物资月度供应计划表.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }


    }

    @Override
    public void planSecondLevelExport(String id, HttpServletResponse response) {
        String src = mallConfig.templateFormUrl;
        MaterialMonthSupplyPlan plan = getById(id);
        List<MaterialMonthSupplyPlanDtl> dtls = materialMonthSupplyPlanDtlService.getSecondLevelListByPlanId(id);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("planDateStr", DateUtil.getYYYYmm(plan.getPlanDate()));
        dataMap.put("supplierName", ThreadLocalUtil.getCurrentUser().getEnterpriseName());
        dataMap.put("orgName", plan.getSupplierName());
        dataMap.put("dataList", dtls);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "物资月度供应计划表模板.xlsx", src, "物资月度供应计划表.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }

    }

    @Override
    public GetPlanDtlInfoByPlanNoVO getSecondLevelPlanDtlInfoByPlanNo(String planNo) {
        MaterialMonthSupplyPlan materialMonthSupplyPlan = lambdaQuery().eq(MaterialMonthSupplyPlan::getPlanNo, planNo).one();

        if (materialMonthSupplyPlan == null) {
            throw new BusinessException(500, "计划不存在！");
        }
        GetPlanDtlInfoByPlanNoVO vo = new GetPlanDtlInfoByPlanNoVO();
        BeanUtils.copyProperties(materialMonthSupplyPlan, vo);
        String planId = materialMonthSupplyPlan.getPlanId();
        List<MaterialMonthSupplyPlanDtl> dtlList = materialMonthSupplyPlanDtlService.getSecondLevelListByPlanId(planId);

        List<GetPlanDtlInfoByPlanNoDtlVO> dtlVOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dtlList)) {
            for (MaterialMonthSupplyPlanDtl dtl : dtlList) {
                GetPlanDtlInfoByPlanNoDtlVO dtlVO = new GetPlanDtlInfoByPlanNoDtlVO();
                BeanUtils.copyProperties(dtl, dtlVO);

                dtlVO.setPlanNo(planNo);
                // 统计已消耗数量，和最多可选数量
                QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
                dtlQ.eq("contract_dtl_id", dtl.getContractDtlId());
                dtlQ.ne("state", 4);
                dtlQ.select("sum(this_plan_qty) as count");
                List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
                if (maps.get(0) == null) {
                    dtlVO.setUseQty(new BigDecimal(0));
                } else {
                    dtlVO.setUseQty(new BigDecimal(maps.get(0).get("count").toString()));
                }
                BigDecimal maxQty = dtlVO.getSourceQty().subtract(dtlVO.getUseQty());
                dtlVO.setMaxQty(maxQty);
                if (mallConfig.isContractConsumeNum == 1) {

                    // 统计已消耗数量，和最多可选数量
                    QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ2 = new QueryWrapper<>();
                    dtlQ2.eq("contract_dtl_id", dtl.getContractDtlId());
                    dtlQ2.eq("state", 2);
                    dtlQ2.select("sum(this_plan_qty) as count");
                    List<Map<String, Object>> maps2 = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ2);
                    if (maps.get(0) == null) {
                        dtlVO.setUseQty(new BigDecimal(0));
                    } else {
                        dtlVO.setUseQty(new BigDecimal(maps2.get(0).get("count").toString()));
                    }


                    // 拿取最大值
                    dtlVO.setMaxQty(null);
                    // 拿到剩余数量
                    List<Map> rMaps = getContactResidueQtyByContactIdAndVersion(vo.getContractId(), vo.getPcwpVersion());
                    if (CollectionUtils.isEmpty(rMaps)) {
                        throw new BusinessException("合同明细数据为空！");
                    } else {
                        for (Map rMap : rMaps) {
                            if (dtlVO.getContractDtlId().equals(rMap.get("DtlId"))) {
                                dtlVO.setMaxQty(BigDecimal.valueOf((Double) rMap.get("ResidueQty")));
                            }
                        }
                        if (dtlVO.getMaxQty() == null) {
                            throw new BusinessException("未找到合同明细数据！");
                        }
                    }
                }
                dtlVO.setOldThisPlanQty(dtlVO.getThisPlanQty());
                dtlVO.setChangeQty(dtlVO.getThisPlanQty());
                // 用于变更
                dtlVO.setDtlUpdateState(2);

                if (mallConfig.isCountPlanOrderNum == 1) {
                    // TODO 计划最新统计
                    BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtl.getPlanDtlId());
                    dtlVO.setOrderQty(qty);
                }
                dtlVOS.add(dtlVO);
            }
        }
        vo.setDtls(dtlVOS);

        // 获取审核历史
        List<AuditRecord> auditRecords = auditRecordService.lambdaQuery()
                .eq(AuditRecord::getRelevanceType, 1)
                .orderByDesc(AuditRecord::getGmtCreate)
                .eq(AuditRecord::getRelevanceId, planId).list();
        // 获取变更列表
        List<MaterialMonthSupplyPlanChange> list1 = materialMonthSupplyPlanChangeService.lambdaQuery()
                .eq(MaterialMonthSupplyPlanChange::getPlanId, materialMonthSupplyPlan.getPlanId())
                .orderByDesc(MaterialMonthSupplyPlanChange::getGmtCreate)
                .list();
        vo.setPlanChanges(list1);

        // 获取更变的审核
        if (!CollectionUtils.isEmpty(list1)) {
            List<String> collect = list1.stream().map(t -> t.getPlanChangeId()).collect(Collectors.toList());
            List<AuditRecord> list = auditRecordService.lambdaQuery()
                    .eq(AuditRecord::getRelevanceType, 2)
                    .orderByDesc(AuditRecord::getGmtCreate)
                    .in(AuditRecord::getRelevanceId, collect).list();
            auditRecords.addAll(list);
        }
        vo.setAuditList(auditRecords);

        // 变更次数
        Integer count = materialMonthSupplyPlanChangeService.lambdaQuery()
                .eq(MaterialMonthSupplyPlanChange::getPlanId, materialMonthSupplyPlan.getPlanId())
                .eq(MaterialMonthSupplyPlanChange::getState, 2)
                .count();
        vo.setAlterationCount(count);
        vo.setOrgName(vo.getSupplierName());
        vo.setSupplierName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
        return vo;
    }

    /**
     * 根据合同id和版本获取合同明细数据
     *
     * @param contactId
     * @param version
     * @return BillId DtlId ResidueQty
     */
    @Override
    public List<Map> getContactResidueQtyByContactIdAndVersion(String contactId, Integer version) {
        HashMap<String, Object> dto = new HashMap<>();
        HashMap<String, Object> params = new HashMap<>();
        params.put("billid", contactId);
        params.put("version", version);
        dto.put("params", params);
        String url = mallConfig.prodPcwp2Url02 + URL3;
        log.warn("请求参数：\n" + JSON.toJSONString(dto));
        R<List<Map>> rMap = null;
        try {
            rMap = restTemplateUtils.postPCWP2(url, dto);
        } catch (Exception e) {
            throw new BusinessException("【远程异常】获取物资合同清单异常！");
        }
        if (rMap == null) {
            throw new BusinessException("【远程异常】获取物资合同清单异常！");
        }
        if (rMap.getCode() == null || rMap.getCode() != 200) {
            throw new BusinessException("【远程异常】获取物资合同清单异常!" + rMap.getMessage());
        }
        return rMap.getData();
    }

    /**
     * 删除计划
     *
     * @param planId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePlanInfo(String planId) {
        MaterialMonthSupplyPlan dto = lambdaQuery().eq(MaterialMonthSupplyPlan::getPlanId, planId).one();
        if (dto.getState() == 0 || dto.getState() == 3) {
        } else {
            throw new BusinessException("草稿、未通过才能删除！");
        }
        delete(planId);
        materialMonthSupplyPlanDtlService.lambdaUpdate().eq(MaterialMonthSupplyPlanDtl::getPlanId, planId).remove();
    }


    @Override
    public void closeMaterialMonthSupplyPlanByPlanNo(String planNo, String idStr, StringBuilder farArg) {
        MaterialMonthSupplyPlan one = lambdaQuery().eq(MaterialMonthSupplyPlan::getPlanNo, planNo).one();
        List<OrderSelectPlan> list = orderSelectPlanService.lambdaQuery().eq(OrderSelectPlan::getBillId, one.getPlanId()).list();
        if (list.size() > 0) {
            List<String> orders = list.stream().map(item -> item.getOrderId()).distinct().collect(Collectors.toList());
            Integer count = ordersService.lambdaQuery().in(Orders::getOrderId, orders).ne(Orders::getState, 10).count();
            if (count > 0) {
                throw new BusinessException(500, "计划编号为" + planNo + "的计划还有订单未完成，请先完成订单");
            }
        }
        if (mallConfig.isCountPlanOrderNum == 1) {
            SaveConsumeRecordDTO redto = new SaveConsumeRecordDTO();
            List<Map> rMaps = new ArrayList<>();
            // TODO 计划最新统计
            List<MaterialMonthSupplyPlanDtl> dtls = materialMonthSupplyPlanDtlService.lambdaQuery()
                    .eq(MaterialMonthSupplyPlanDtl::getPlanId, one.getPlanId()).list();
            for (MaterialMonthSupplyPlanDtl dtl : dtls) {
                HashMap<String, Object> map = new HashMap<>();
                BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtl.getPlanDtlId());
                BigDecimal subtract = dtl.getThisPlanQty().subtract(qty);
                dtl.setCloseQty(subtract);
                map.put("listId", dtl.getContractDtlId());
                map.put("quantity", subtract.negate());
                rMaps.add(map);
            }
            materialMonthSupplyPlanDtlService.updateBatchById(dtls);
            redto.setVersion(one.getPcwpVersion());
            redto.setCredential(idStr);
            redto.setType("change");
            redto.setListInfo(rMaps);
            String content = JSON.toJSONString(redto);
            farArg.append(content);
            log.warn("完结数量错误：" + content);
            // 发送请求
            String url = mallConfig.prodPcwp2Url02 + URL2;
            LogUtil.writeInfoLog(idStr, "closeMaterialMonthSupplyPlanByPlanNo", planNo, redto, null, MaterialMonthSupplyPlanServiceImpl.class);
            R rMap = null;
            try {
                rMap = restTemplateUtils.postPCWP2(url, redto);
            } catch (Exception e) {
                LogUtil.writeErrorLog(idStr, "closeMaterialMonthSupplyPlanByPlanNo", planNo, redto, rMap, e.getMessage(), MaterialMonthSupplyPlanServiceImpl.class);
                log.error(e.getMessage());
                throw new BusinessException("【远程异常】完结数量错误！");
            }
            if (rMap.getCode() == null || rMap.getCode() != 200) {
                LogUtil.writeErrorLog(idStr, "closeMaterialMonthSupplyPlanByPlanNo", planNo, redto, rMap, rMap.getMessage(), MaterialMonthSupplyPlanServiceImpl.class);
                log.error("完结计划！返回：" + rMap);
                throw new BusinessException("【远程异常】完结数量错误！");
            }
            one.setIsClose(1);
            update(one);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(MaterialMonthSupplyPlanServiceImpl.class.getName());
            iLog.setMethodName("closeMaterialMonthSupplyPlanByPlanNo");
            iLog.setLocalArguments(JSON.toJSONString(planNo));
            iLog.setFarArguments(content);
            iLog.setIsSuccess(1);
            iLog.setLogType(1);
            iLog.setErrorInfo(null);
            interfaceLogsService.create(iLog);


        }


    }
}
