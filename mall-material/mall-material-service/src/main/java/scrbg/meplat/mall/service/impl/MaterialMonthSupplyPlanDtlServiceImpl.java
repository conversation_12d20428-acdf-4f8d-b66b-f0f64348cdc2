package scrbg.meplat.mall.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtl;
import scrbg.meplat.mall.mapper.MaterialMonthSupplyPlanDtlMapper;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.MaterialMonthSupplyPlanDtlService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.vo.supplier.PushPlanToSupplierVo;

import java.util.ArrayList;
import java.util.List;

/**
 * @描述：计划明细 服务类
 * @作者: ye
 * @日期: 2023-06-27
 */
@Service
public class MaterialMonthSupplyPlanDtlServiceImpl extends ServiceImpl<MaterialMonthSupplyPlanDtlMapper, MaterialMonthSupplyPlanDtl> implements MaterialMonthSupplyPlanDtlService{
    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMonthSupplyPlanDtl> queryWrapper) {
        IPage<MaterialMonthSupplyPlanDtl> page = this.page(
        new Query<MaterialMonthSupplyPlanDtl>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(MaterialMonthSupplyPlanDtl materialMonthSupplyPlanDtl) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(materialMonthSupplyPlanDtl);
    }

    @Override
    public void update(MaterialMonthSupplyPlanDtl materialMonthSupplyPlanDtl) {
        super.updateById(materialMonthSupplyPlanDtl);
    }


    @Override
    public MaterialMonthSupplyPlanDtl getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }


    @Override
    public void pushPlanToSupplierVo(PushPlanToSupplierVo pushPlanToSupplierVo) {
        List<String> planDtlIds = pushPlanToSupplierVo.getPlanDtlIds();
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getById(pushPlanToSupplierVo.getTwoSupplierId());
        if (planDtlIds!=null&&planDtlIds.size()>0){
            List<MaterialMonthSupplyPlanDtl> dtls = baseMapper.selectBatchIds(planDtlIds);
            ArrayList<MaterialMonthSupplyPlanDtl> updatePlanDtls = new ArrayList<>();
            for (MaterialMonthSupplyPlanDtl dtl : dtls) {
                if (dtl.getTwoSupplierId()==null){
                    dtl.setTwoSupplierId(enterpriseInfo.getEnterpriseId());
                    dtl.setTwoSupplierName(enterpriseInfo.getEnterpriseName());
                    updatePlanDtls.add(dtl);
                }
            }
            updateBatchById(updatePlanDtls);
        }
    }


    @Override
    public void closePushDtl(String id) {
       baseMapper.closePushDtl(id);
    }

    @Override
    public String getTwoSupplierIdByPlanDtl(String planDtl) {
        MaterialMonthSupplyPlanDtl byId = getById(planDtl);
        return byId.getTwoSupplierId();
    }


    @Override
    public List<MaterialMonthSupplyPlanDtl> getListByPlanId(String planId) {
        List<MaterialMonthSupplyPlanDtl> list = lambdaQuery().eq(MaterialMonthSupplyPlanDtl::getPlanId, planId).list();
        return list;
    }


    @Override
    public List<MaterialMonthSupplyPlanDtl> getSecondLevelListByPlanId(String planId) {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        List<MaterialMonthSupplyPlanDtl> list = lambdaQuery().eq(MaterialMonthSupplyPlanDtl::getPlanId, planId).eq(MaterialMonthSupplyPlanDtl::getTwoSupplierId,enterpriseId).list();
        return list;
    }

    /**
     * 获取大宗采购合同月供计划已使用数量
     */
    @Override
    public void getMonthPlanQtyCount() {
    }
}
