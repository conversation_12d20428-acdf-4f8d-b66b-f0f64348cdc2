package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @描述：设备需求
 * @作者: y
 * @日期: 2022-11-21
 */
@ApiModel(value = "设备需求")
@Data
@TableName("device_demand")
public class DeviceDemand extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "需求id")

    private String demandId;

    @ApiModelProperty(value = "名称")

    private String name;

    @ApiModelProperty(value = "规格型号")

    private String spec;

    @ApiModelProperty(value = "关键字")

    private String keyword;

    @ApiModelProperty(value = "关联id（根据发布类型存储不同id）")

    private String relevanceId;

    @ApiModelProperty(value = "分类id")

    private String classId;

    @ApiModelProperty(value = "分类名称")

    private String className;

    @ApiModelProperty(value = "品牌id")

    private String brandId;

    @ApiModelProperty(value = "品牌名称")

    private String brandName;

    @ApiModelProperty(value = "省份")

    private String province;

    @ApiModelProperty(value = "城市")

    private String city;

    @ApiModelProperty(value = "县区")

    private String county;

    @ApiModelProperty(value = "地址")

    private String address;

    @ApiModelProperty(value = "经度")

    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度")

    private BigDecimal latitude;

    @ApiModelProperty(value = "数量")

    private Integer num;

    @ApiModelProperty(value = "需求类型（1租赁设备2二手设备）")

    private Integer demandType;

    @ApiModelProperty(value = "计量单位")

    private String numUnit;

    @ApiModelProperty(value = "租赁时长")

    private String duration;

    @ApiModelProperty(value = "进场时间")
    private Date enterDate;

    @ApiModelProperty(value = "联系人")

    private String linkman;

    @ApiModelProperty(value = "联系电话")

    private String linkmanPhone;

    @ApiModelProperty(value = "发布时间")

    private Date releaseDate;

    @ApiModelProperty(value = "截止时间")

    private Date stopDate;


    @ApiModelProperty(value = "预算金额")

    private BigDecimal budgetAmount;

    @ApiModelProperty(value = "需求状态（0初始1已审核2报价中）")

    private Integer state;

    @ApiModelProperty(value = "审核状态（0待审核1通过2不通过）")

    private Integer checkState;

    @ApiModelProperty(value = "发布类型（0店铺1用户2平台）")

    private Integer releaseType;

    @ApiModelProperty(value = "分类id路径/分割")

    private String classPath;

    @ApiModelProperty(value = "审核失败原因")
    private String failReason;

    @ApiModelProperty(value = "发起方(企业)")
    private String launch;

    @ApiModelProperty(value = "出发地")
    private String startAddress;

    @ApiModelProperty(value = "目的地")
    private String endAddress;
}
