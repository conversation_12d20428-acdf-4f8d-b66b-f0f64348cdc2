package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.BiddingInfo;

/**
 * @描述：招标详细信息 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
public interface BiddingInfoService extends IService<BiddingInfo> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingInfo> queryWrapper);

    void create(BiddingInfo biddingInfo);

    void update(BiddingInfo biddingInfo);

    BiddingInfo getById(String id);

    void delete(String id);
}
