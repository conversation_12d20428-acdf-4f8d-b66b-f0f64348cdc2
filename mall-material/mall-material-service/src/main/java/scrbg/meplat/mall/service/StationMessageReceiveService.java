package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.StationMessageReceive;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;
import scrbg.meplat.mall.vo.product.material.StationMessageReceiveVo;

import java.util.List;
import java.util.Map;

/**
 * @描述：站点接收消息 服务类
 * @作者: y
 * @日期: 2022-11-24
 */
public interface StationMessageReceiveService extends IService<StationMessageReceive> {
        PageUtils queryPage(JSONObject jsonObject, QueryWrapper<StationMessageReceiveVo> queryWrapper);

        void create(StationMessageReceive stationMessageReceive);

        void update(StationMessageReceive stationMessageReceive);

        StationMessageReceive getById(String id);

        void delete(String id);


        List<StationMessageReceive> getMessageNum(Integer receiveType);

    void addBath(StationMessageReceiveVO vo);

    void changStateById(String id);

    PageUtils receivePageList(JSONObject jsonObject, LambdaQueryWrapper<StationMessageReceive> stationMessageReceiveVoLambdaQueryWrapper);

    void updateBathState(List<String> receiceId);
    /**
     * 获取需要提醒得消息
     * @return
     */

    Map<String,Object> getRemindMessage();
}
