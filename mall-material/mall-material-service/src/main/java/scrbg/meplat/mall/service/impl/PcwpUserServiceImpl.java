package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.alibaba.fastjson.JSONObject;
import com.scrbg.common.utils.PageUtils;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.entity.PcwpUser;
import scrbg.meplat.mall.mapper.PcwpUserMapper;
import scrbg.meplat.mall.service.PcwpUserService;


import java.util.List;
import java.util.Map;

@Service
public class PcwpUserServiceImpl extends ServiceImpl<PcwpUserMapper, PcwpUser> implements PcwpUserService {
    @Override
    public PageUtils getInUserLedger(JSONObject jsonObject) {

        QueryWrapper<PcwpUser> wrapper = new QueryWrapper<>();
        Map<String, Object> innerMap = jsonObject.getInnerMap();

        String nickName = (String) innerMap.get("nickName");
        String name = (String) innerMap.get("name");
        String phone = (String) innerMap.get("phone");
        String keywords = (String) innerMap.get("keywords");
        List<String> shortCodes = (List<String>) innerMap.get("shortCodes");
        wrapper.eq("ppp.role_name", "物资下单权限");
        // 机构简码过滤
        if (null != shortCodes && !shortCodes.isEmpty()){
            wrapper.in("ppp.short_code", shortCodes);
        }
        // 高级搜索
        if (!StringUtils.isEmpty(nickName)) {
            wrapper.like("ppp.user_name", nickName);
        }
        if (!StringUtils.isEmpty(name)) {
            wrapper.like("ppp.user_name", name);
        }
        if (!StringUtils.isEmpty(phone)) {
            wrapper.like("pp.mobile", phone);
        }
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.and(w -> w.like("pp.mobile", keywords)
                    .or()
                    .like("ppp.user_name", keywords)
                    .or()
                    .like("ppp.org_name", keywords));
        }
        //wrapper.ne("pp.mdmstate", -1);
        Page<PcwpUser> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<PcwpUser> list = baseMapper.queryUserOrg(pages, wrapper);
        PageUtils<PcwpUser> pageUtils = new PageUtils(pages);
        pageUtils.setList(list);
        return pageUtils;
    }
}
