package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.ShopRoleAssociation;
import scrbg.meplat.mall.mapper.ShopRoleAssociationMapper;
import scrbg.meplat.mall.service.ShopRoleAssociationService;

/**
 * @描述：店铺-角色关联表 服务类
 * @作者: y
 * @日期: 2022-11-07
 */
@Service
public class ShopRoleAssociationServiceImpl extends ServiceImpl<ShopRoleAssociationMapper, ShopRoleAssociation> implements ShopRoleAssociationService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShopRoleAssociation> queryWrapper) {
        IPage<ShopRoleAssociation> page = this.page(
                new Query<ShopRoleAssociation>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ShopRoleAssociation shopRoleAssociation) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(shopRoleAssociation);
    }

    @Override
    public void update(ShopRoleAssociation shopRoleAssociation) {
        super.updateById(shopRoleAssociation);
    }


    @Override
    public ShopRoleAssociation getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
