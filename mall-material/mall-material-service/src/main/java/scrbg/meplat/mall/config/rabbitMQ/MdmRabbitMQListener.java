package scrbg.meplat.mall.config.rabbitMQ;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.rabbitmq.client.Channel;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.dto.pcwpmq.MqMessageDTO;
import scrbg.meplat.mall.mapper.pcwpmq.MqMessageMapper;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Component
@Log4j2
@ConditionalOnProperty(name = "spring.rabbitmq.mdm.isSync", havingValue = "true", matchIfMissing = false)
public class MdmRabbitMQListener {

    @Autowired
    private MqMessageMapper mqMessageMapper;

    // 使用Map存储队列处理方法，避免长switch-case
    private final Map<String, Consumer<Message>> queueHandlers = new HashMap<>();

    public MdmRabbitMQListener() {
        // 完全采用原始Lambda写法，避免任何方法引用
        queueHandlers.put("Personinfo_Full", msg -> {
            try {
                processPersonInfo(msg);
            } catch (Exception e) {
                throw new RuntimeException(e.getMessage());
            }
        });

        queueHandlers.put("Zcinfo_Full", msg -> {
            try {
                processZcinfo(msg);
            } catch (Exception e) {
                throw new RuntimeException("处理Zcinfo消息失败", e);
            }
        });

        queueHandlers.put("Zyzginfo_Full", msg -> {
            try {
                processZyzginfo(msg);
            } catch (Exception e) {
                throw new RuntimeException("处理Zyzginfo消息失败", e);
            }
        });

        queueHandlers.put("Grhjinfo_Full", msg -> {
            try {
                processGrhjinfo(msg);
            } catch (Exception e) {
                throw new RuntimeException("处理Grhjinfo消息失败", e);
            }
        });

        queueHandlers.put("Rzjlinfo_Full", msg -> {
            try {
                processRzjlinfo(msg);
            } catch (Exception e) {
                throw new RuntimeException("处理Rzjlinfo消息失败", e);
            }
        });

        queueHandlers.put("Orginfo_Full", msg -> {
            try {
                processOrginfo(msg);
            } catch (Exception e) {
                throw new RuntimeException("处理Orginfo消息失败", e);
            }
        });

        queueHandlers.put("Sys_Org_Full", msg -> {
            try {
                processSysOrginfo(msg);
            } catch (Exception e) {
                throw new RuntimeException("处理Sys_Org消息失败", e);
            }
        });

        queueHandlers.put("Sys_ConOrg_Full", msg -> {
            try {
                processSysConOrginfo(msg);
            } catch (Exception e) {
                throw new RuntimeException("处理Sys_ConOrg消息失败", e);
            }
        });

        queueHandlers.put("Project_Project_Full", msg -> {
            try {
                processProjectinfo(msg);
            } catch (Exception e) {
                throw new RuntimeException("处理Project消息失败", e);
            }
        });

        queueHandlers.put("TTAccount_Full", msg -> {
            try {
                processTTAccountinfo(msg);
            } catch (Exception e) {
                throw new RuntimeException("处理TTAccount消息失败", e);
            }
        });
    }

    //    @RabbitListener(queues = "Personinfo_Full_dev",containerFactory = "rabbitListenerContainerFactory") // 明确指定工厂
//    @RabbitListener(queues = "Zcinfo_Full_dev",containerFactory = "rabbitListenerContainerFactory") // 明确指定工厂
    @RabbitListener(queues = "#{@mdmQueueNames}", containerFactory = "rabbitListenerContainerFactory") // 明确指定工厂
    @Transactional
    public void handleAllMessages(Message message,
                                  Channel channel,
                                  @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                  @Header("amqp_consumerQueue") String queueName) {
//            log.info(message.getMessageProperties());
        log.info("开始处理队列[{}]消息，消息ID: {}", queueName, message.getMessageProperties().getMessageId());
        /**
         * @queueName
         * 正式环境队列名：Personinfo_Full_formal
         * 测试环境队列名：Personinfo_Full_test
         * 开发环境队列名：Personinfo_Full_dev
         */
        String splitS = "_";
        String[] methodNames = queueName.split(splitS);
        String methodName = methodNames[0].concat(splitS).concat(methodNames[1]);
        Consumer<Message> handler = queueHandlers.get(methodName);
        if (handler == null) {
            log.error("未找到队列[{}]对应的处理器", queueName);
            try {
                channel.basicNack(deliveryTag, false, false); // 不重新入队
            } catch (IOException e) {
                log.error("未找到队列[{}]对应的处理器"+e.getMessage(),queueName);
            }
        } else {// 处理消息
            try {
                handler.accept(message);// 确认消息处理完成
                if (channel.isOpen()) {
                    channel.basicAck(deliveryTag, false);
                    log.info("队列[{}]消息处理完成", queueName);
                } else {
                    log.error("队列[{}]消息通道关闭没有处理完成", queueName);
                }
            } catch (Exception e) {
                log.error("处理队列[{}]消息异常，消息将重新入队: {}", queueName, e.getMessage());
                try {
                    channel.basicNack(deliveryTag, false, true); //
                } catch (IOException ioException) {
                    log.error("拒绝消息失败: {}", ioException.getMessage());
                }
            }
        }
    }

    private void processPersonInfo(Message msg) throws Exception {
        try {
            String message = new String(msg.getBody());
            JSONArray jsonArray = JSON.parseArray(message);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("人员基本信息队列收到空消息");
                return;
            }
            log.info("人员基本信息队列收到空消息数量为：" + jsonArray.size());
//            log.info(jsonArray.get(0).toString());
            insertMQdata(msg);
        } catch (Exception e) {
            throw new Exception("处理人员信息异常: " + e.getMessage());
        }
    }

    private void processZcinfo(Message msg) throws Exception {
        try {
            String message = new String(msg.getBody());
            JSONArray jsonArray = JSON.parseArray(message);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("证书信息队列收到空消息");
                return;
            }
            log.info("证书信息队列收到空消息数量为：" + jsonArray.size());
//            log.info(jsonArray.get(0).toString());
            insertMQdata(msg);
        } catch (Exception e) {
            throw new Exception("处理证书信息异常: " + e.getMessage());
        }
    }

    private void processZyzginfo(Message msg) throws Exception {
        try {
            String message = new String(msg.getBody());
            JSONArray jsonArray = JSON.parseArray(message);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("执业资格信息队列收到空消息");
                return;
            }
            log.info("执业资格信息队列收到空消息数量为：" + jsonArray.size());
//            log.info(jsonArray.get(0).toString());
            insertMQdata(msg);
        } catch (Exception e) {
            throw new Exception("处理执业资格信息异常: " + e.getMessage());
        }
    }

    // 其他处理方法保持不变，但建议都添加类似的异常处理逻辑
    private void processGrhjinfo(Message msg) throws Exception {
        try {
            String message = new String(msg.getBody());
            JSONArray jsonArray = JSON.parseArray(message);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("个人获奖信息队列收到空消息");
                return;
            }
            log.info("个人获奖信息队列收到空消息数量为：" + jsonArray.size());
//            log.info(jsonArray.get(0).toString());
            insertMQdata(msg);
        } catch (Exception e) {
            throw new Exception("处理个人获奖信息异常: " + e.getMessage());
        }
    }

    private void processRzjlinfo(Message msg) throws Exception {
        try {
            String message = new String(msg.getBody());
            JSONArray jsonArray = JSON.parseArray(message);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("任职项目经历信息队列收到空消息");
                return;
            }
            log.info("任职项目经历信息队列收到空消息数量为：" + jsonArray.size());
//            log.info(jsonArray.get(0).toString());
            insertMQdata(msg);
        } catch (Exception e) {
            throw new Exception("处理任职项目经历信息异常: " + e.getMessage());
        }
    }

    private void processOrginfo(Message msg) throws Exception {
        try {
            String message = new String(msg.getBody());
            JSONArray jsonArray = JSON.parseArray(message);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("HR机构表信息队列收到空消息");
                return;
            }
            log.info("HR机构表信息队列收到空消息数量为：" + jsonArray.size());
//            log.info(jsonArray.get(0).toString());
            insertMQdata(msg);
        } catch (Exception e) {
            throw new Exception("处理HR机构表信息异常: " + e.getMessage());
        }
    }

    private void processSysOrginfo(Message msg) throws Exception {
        try {
            String message = new String(msg.getBody());
            JSONArray jsonArray = JSON.parseArray(message);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("PCWP机构表信息队列收到空消息");
                return;
            }
            log.info("PCWP机构表信息队列收到空消息数量为：" + jsonArray.size());
//            log.info(jsonArray.get(0).toString());
            insertMQdata(msg);
        } catch (Exception e) {
            throw new Exception("处理PCWP机构表信息异常: " + e.getMessage());
        }
    }

    private void processSysConOrginfo(Message msg) throws Exception {
        try {
            String message = new String(msg.getBody());
            JSONArray jsonArray = JSON.parseArray(message);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("PCWP大经理部信息队列收到空消息");
                return;
            }
            log.info("PCWP大经理部信息队列收到空消息数量为：" + jsonArray.size());
//            log.info(jsonArray.get(0).toString());
            insertMQdata(msg);
        } catch (Exception e) {
            throw new Exception("处理PCWP大经理部信息异常: " + e.getMessage());
        }
    }

    private void processProjectinfo(Message msg) throws Exception {
        try {
            String message = new String(msg.getBody());
            JSONArray jsonArray = JSON.parseArray(message);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("PCWP项目表信息队列收到空消息");
                return;
            }
            log.info("PCWP项目表信息队列收到空消息数量为：" + jsonArray.size());
//            log.info(jsonArray.get(0).toString());
            insertMQdata(msg);
        } catch (Exception e) {
            throw new Exception("处理PCWP项目表信息异常: " + e.getMessage());
        }
    }

    private void processTTAccountinfo(Message msg) throws Exception {
        try {
            String message = new String(msg.getBody());
            JSONArray jsonArray = JSON.parseArray(message);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("TT用户信息信息队列收到空消息");
                return;
            }
            log.info("TT用户信息队列收到空消息数量为：" + jsonArray.size());
//            log.info(jsonArray.get(0).toString());
            insertMQdata(msg);
        } catch (Exception e) {
            throw new Exception("处理TT用户信息信息异常: " + e.getMessage());
        }
    }

    private void insertMQdata(Message msg) throws Exception {
        MqMessageDTO mqMessageDTO = new MqMessageDTO();
        mqMessageDTO.setTime(LocalDateTime.now().toString());
        mqMessageDTO.setType(msg.getMessageProperties().getReceivedRoutingKey());
        mqMessageDTO.setIsGet("0");
        mqMessageDTO.setDeliveryTag(msg.getMessageProperties().getDeliveryTag());
        List<MqMessageDTO> list = mqMessageMapper.selectUnconsumedByTypeAndTag(msg.getMessageProperties().getReceivedRoutingKey(), msg.getMessageProperties().getDeliveryTag());
        if (!list.isEmpty()) {
            log.info("存在重复数据，无须插入");
            return;
        }
        // 解析原始JSON数组
        JSONArray jsonArray = JSON.parseArray(new String(msg.getBody()));
        int batchSize = 10000; // 每批处理的数据量
        int total = jsonArray.size();
        int batchCount = (total + batchSize - 1) / batchSize; // 计算需要分多少批
        // 分批处理
        for (int i = 0; i < batchCount; i++) {
            int from = i * batchSize;
            int to = Math.min(from + batchSize, total);

            // 创建当前批次的数据对象
            MqMessageDTO batchDto = new MqMessageDTO();
            batchDto.setTime(mqMessageDTO.getTime());
            batchDto.setType(mqMessageDTO.getType());
            batchDto.setIsGet(mqMessageDTO.getIsGet());
            batchDto.setDeliveryTag(mqMessageDTO.getDeliveryTag());
            batchDto.setPatch(i + 1); // 设置批次号

            // 提取当前批次的JSON数据
            JSONArray batchArray = new JSONArray();
            for (int j = from; j < to; j++) {
                batchArray.add(jsonArray.get(j));
            }
            batchDto.setJsonText(batchArray.toJSONString());

            // 插入当前批次数据
            int count = mqMessageMapper.insertWithId(batchDto);
            if (count <= 0) {
                throw new Exception(msg.getMessageProperties().getReceivedRoutingKey() + "数据插入失败，批次：" + (i + 1));
            }
        }
        log.info("成功插入{}条数据，分{}批次", total, batchCount);
    }

}