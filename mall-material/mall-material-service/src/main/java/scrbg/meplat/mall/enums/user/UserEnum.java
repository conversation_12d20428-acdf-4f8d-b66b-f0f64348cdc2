package scrbg.meplat.mall.enums.user;

/**
 * @package: scrbg.meplat.mall.enums.enums.user
 * @author: 胡原武
 * @date: 2022.11.08
 */
public enum UserEnum {

    STATE_START(0,"禁用"),
    STATE_OPEN(1,"启用"),
//    STATE_FORBIDDEN(2,"禁用"),

    IS_ADMIN_NO(0,"非管理员"),
    IS_ADMIN_YES(1,"管理员"),

    IS_MATERIAL_YSE(1,"是物资商城注册"),
    IS_MATERIAL_NO(0,"不是物资商城注册"),

    IS_DEVICE_YES(1,"是设备商城注册"),
    IS_DEVICE_NO(0,"不是物资商城注册"),

    IS_INTERNAL_USER_YES(1,"是内部用户"),
    IS_INTERNAL_USER_NO(0,"非内部用户");



    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    UserEnum() {
    }

    UserEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
