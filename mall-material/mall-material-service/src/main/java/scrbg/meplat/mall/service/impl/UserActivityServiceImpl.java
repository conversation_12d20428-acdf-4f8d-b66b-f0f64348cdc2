package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.UserActivity;
import scrbg.meplat.mall.mapper.UserActivityMapper;
import scrbg.meplat.mall.service.UserActivityService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：用户行为表 服务类
 * @作者: ye
 * @日期: 2023-09-04
 */
@Service
public class UserActivityServiceImpl extends ServiceImpl<UserActivityMapper, UserActivity> implements UserActivityService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<UserActivity> queryWrapper) {
        IPage<UserActivity> page = this.page(
        new Query<UserActivity>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(UserActivity userActivity) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(userActivity);
    }

    @Override
    public void update(UserActivity userActivity) {
        super.updateById(userActivity);
    }


    @Override
    public UserActivity getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
