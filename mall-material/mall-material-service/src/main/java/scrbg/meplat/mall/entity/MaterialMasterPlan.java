package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @描述：物资总计划
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "物资总计划")
@Data
@TableName("material_master_plan")
public class MaterialMasterPlan extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "物资总计划id")

    private String billId;


    @ApiModelProperty(value = "流程id")

    private String workId;

    @ApiModelProperty(value = "计划编号")

    private String billNo;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划日期")

    private Date planDate;

    @ApiModelProperty(value = "工程计划Id")

    private String projectPlanId;

    @ApiModelProperty(value = "计划金额(元)")

    private BigDecimal amount;

    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;

    @ApiModelProperty(value = "税额")

    private BigDecimal taxAmount;

    @ApiModelProperty(value = "税价合计")

    private BigDecimal totalAmount;

    @ApiModelProperty(value = "填报人Id")

    private String preparerId;

    @ApiModelProperty(value = "填报人名称")

    private String preparer;

    @JsonIgnore
    @ApiModelProperty(value = "币种Id", hidden = true)

    private String currencyId;

    @JsonIgnore
    @ApiModelProperty(value = "币种名称", hidden = true)

    private String currency;

    @ApiModelProperty(value = "采购方式")

    private Integer purchaseType;

    @ApiModelProperty(value = "单据机构Id")

    private String orgId;

    @ApiModelProperty(value = "单据机构名称")

    private String orgName;


    @ApiModelProperty(value = "本位币id")

    private String baseCurId;

    @ApiModelProperty(value = "本位币名称")

    private String baseCurName;

    @ApiModelProperty(value = "本位币汇率")

    private BigDecimal baseCurRate;

    @ApiModelProperty(value = "计划金额(本位币)")

    private BigDecimal baseCurAmount;

    @ApiModelProperty(value = "税额(本位币)")

    private BigDecimal baseCurTaxAmount;

    @ApiModelProperty(value = "税价合计(本位币)c")

    private BigDecimal baseCurTotalAmount;

    @JsonIgnore
    @ApiModelProperty(value = "人民币id", hidden = true)

    private String rmbId;

    @JsonIgnore
    @ApiModelProperty(value = "人民币名称", hidden = true)

    private String rmbName;

    @JsonIgnore
    @ApiModelProperty(value = "人民币汇率", hidden = true)

    private BigDecimal rmbRate;

    @JsonIgnore
    @ApiModelProperty(value = "计划金额(人民币)", hidden = true)

    private BigDecimal rmbAmount;

    @JsonIgnore
    @ApiModelProperty(value = "税额(人民币)", hidden = true)

    private BigDecimal rmbTaxAmount;

    @JsonIgnore
    @ApiModelProperty(value = "税价合计(人民币)", hidden = true)

    private BigDecimal rmbTotalAmount;

    @ApiModelProperty(value = "订单id")

    private String orderId;

    @ApiModelProperty(value = "状态")

    private Integer state;
    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;

}
