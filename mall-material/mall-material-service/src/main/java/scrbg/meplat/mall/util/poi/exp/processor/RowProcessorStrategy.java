package scrbg.meplat.mall.util.poi.exp.processor;

import com.google.common.collect.Lists;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.util.List;

public class RowProcessorStrategy {
	
	public static final List<RowProcessor> processors = Lists.newArrayList();
	static{
		processors.add(new ForeachRowProcessor());
	}
	
	public static RowProcessor getRowProcessor(XSSFRow row){
		for(RowProcessor processor : processors){
			int flag = processor.support(row);
			if(flag >= 0){
				return processor;
			}
		}
		
		return DefaultRowProcessor.SINGLE.INSTANCE;
	}
	
}
