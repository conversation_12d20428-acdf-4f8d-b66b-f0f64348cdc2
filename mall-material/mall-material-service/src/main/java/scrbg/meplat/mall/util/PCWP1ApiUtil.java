//package scrbg.meplat.mall.util;
//
//import com.alibaba.fastjson.JSON;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.client.RestTemplate;
//import scrbg.meplat.mall.config.MallConfig;
//
//import java.util.HashMap;
//import java.util.Map;
//
//public   class PCWP1ApiUtil  {
//    @Autowired
//    private    RestTemplate restTemplate;
//
//    public    String PCWP1VerifyDZPlan(String prodPcwp2Url02,String keyId){
//        Map<String, Object> params = new HashMap<>();
//        params.put("KeyId", keyId); // 参数赋值，这里假设 KeyId 是一个 String 类型的参数
//
//        // 创建 tags Map
//        Map<String, String> tags = new HashMap<>();
//        tags.put("userid", "a94220f02a0b-a622-e84f-a0d9-a24be950");
//        tags.put("username", "王跃");
//        tags.put("orgid", "9b840098ab5a-b199-fa4b-7cb5-5c62a30d");
//        tags.put("orgname", "四川公路桥梁建设集团有限公司");
//        tags.put("companycode", "1000");
//        tags.put("auth_client_id", "test");
//        tags.put("auth_token", "test");
//        tags.put("platformid", "1");
//
//        // 创建 JSON-RPC 请求的完整 Map 结构
//        Map<String, Object> jsonRpcRequest = new HashMap<>();
//        jsonRpcRequest.put("jsonrpc", "2.0");
//        jsonRpcRequest.put("method", "Material.BulkTemporaryPlan.QueryBykeyId");
//        jsonRpcRequest.put("params", params);
//        jsonRpcRequest.put("id", 1);
//        jsonRpcRequest.put("tags", tags);
//        String content = JSON.toJSONString(params);
//        com.scrbg.common.utils.R<Map> r = null;
//
//        try {
//            Map map = restTemplate.postForObject(prodPcwp2Url02, content, Map.class);
//            HashMap result = (HashMap) map.get("result");
//            if (result != null) {
//                return result.get("BillNo").toString();
//            } else {
//                return null;
//            }
//        } catch (Exception e) {
//            return  null;
//        }
//
//
//
//    }
//
//    public  static  String PCWP1VerifylXPlan(String prodPcwp2Url02,String keyId){
//        Map<String, Object> params = new HashMap<>();
//        params.put("KeyId", keyId); // 参数赋值，这里假设 KeyId 是一个 String 类型的参数
//
//        // 创建 tags Map
//        Map<String, String> tags = new HashMap<>();
//        tags.put("userid", "a94220f02a0b-a622-e84f-a0d9-a24be950");
//        tags.put("username", "王跃");
//        tags.put("orgid", "9b840098ab5a-b199-fa4b-7cb5-5c62a30d");
//        tags.put("orgname", "四川公路桥梁建设集团有限公司");
//        tags.put("companycode", "1000");
//        tags.put("auth_client_id", "test");
//        tags.put("auth_token", "test");
//        tags.put("platformid", "1");
//
//        // 创建 JSON-RPC 请求的完整 Map 结构
//        Map<String, Object> jsonRpcRequest = new HashMap<>();
//        jsonRpcRequest.put("jsonrpc", "2.0");
//        jsonRpcRequest.put("method", "Material.SporadicPlan.QueryBykeyId");
//        jsonRpcRequest.put("params", params);
//        jsonRpcRequest.put("id", 1);
//        jsonRpcRequest.put("tags", tags);
//        String content = JSON.toJSONString(params);
//        com.scrbg.common.utils.R<Map> r = null;
//
//        try {
//            Map map = restTemplate.postForObject(prodPcwp2Url02, content, Map.class);
//            HashMap result = (HashMap) map.get("result");
//            if (result != null) {
//                return result.get("BillNo").toString();
//            } else {
//                return null;
//            }
//        } catch (Exception e) {
//           return  null;
//        }
//
//
//
//    }
//
//
//    public static String PCWP1VerifyOrderShip(String prodPcwp2Url02,String keyId){
//
//        Map<String, Object> params = new HashMap<>();
//        params.put("KeyId", keyId); // 参数赋值，这里假设 KeyId 是一个 String 类型的参数
//
//        // 创建 tags Map
//        Map<String, String> tags = new HashMap<>();
//        tags.put("userid", "a94220f02a0b-a622-e84f-a0d9-a24be950");
//        tags.put("username", "王跃");
//        tags.put("orgid", "9b840098ab5a-b199-fa4b-7cb5-5c62a30d");
//        tags.put("orgname", "四川公路桥梁建设集团有限公司");
//        tags.put("companycode", "1000");
//        tags.put("auth_client_id", "test");
//        tags.put("auth_token", "test");
//        tags.put("platformid", "1");
//
//        // 创建 JSON-RPC 请求的完整 Map 结构
//        Map<String, Object> jsonRpcRequest = new HashMap<>();
//        jsonRpcRequest.put("jsonrpc", "2.0");
//        jsonRpcRequest.put("method", "Material.SiteReceive.QueryBykeyId");
//        jsonRpcRequest.put("params", params);
//        jsonRpcRequest.put("id", 1);
//        jsonRpcRequest.put("tags", tags);
//        String content = JSON.toJSONString(params);
//        com.scrbg.common.utils.R<Map> r = null;
//
//        try {
//            Map map = restTemplate.postForObject(prodPcwp2Url02, content, Map.class);
//            HashMap result = (HashMap) map.get("result");
//            if (result != null) {
//                return result.get("BillNo").toString();
//            } else {
//                return null;
//            }
//        } catch (Exception e) {
//            return  null;
//        }
//
//    }
//
//}
