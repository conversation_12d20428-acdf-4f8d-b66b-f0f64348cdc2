package scrbg.meplat.mall.service;

import scrbg.meplat.mall.vo.bidding.GetBidingRecordItemInfoVO;
import scrbg.meplat.mall.entity.BiddingBidRecordItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
/**
 * @描述：竞价记录 服务类
 * @作者: ye
 * @日期: 2023-07-19
 */
public interface BiddingBidRecordItemService extends IService<BiddingBidRecordItem> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingBidRecordItem> queryWrapper);

        void create(BiddingBidRecordItem biddingBidRecordItem);

        void update(BiddingBidRecordItem biddingBidRecordItem);

        BiddingBidRecordItem getById(String id);

        void delete(String id);

        /**
         * 根据记录id获取记录明细
         * @param jsonObject
         * @param biddingBidRecordItemLambdaQueryWrapper
         * @return
         */
        GetBidingRecordItemInfoVO getBidingRecordItemInfo(JSONObject jsonObject, LambdaQueryWrapper<BiddingBidRecordItem> biddingBidRecordItemLambdaQueryWrapper);

        /**
         * 根据记录id获取记录明细-平台
         * @param jsonObject
         * @param biddingBidRecordItemLambdaQueryWrapper
         * @return
         */
        GetBidingRecordItemInfoVO getPlatformBidingRecordItemInfo(JSONObject jsonObject, LambdaQueryWrapper<BiddingBidRecordItem> biddingBidRecordItemLambdaQueryWrapper);
}