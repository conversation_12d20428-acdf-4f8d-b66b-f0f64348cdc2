package scrbg.meplat.mall.util;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

public class AESUtil2 {

    private static final String KEY_ALGORITHM = "AES";
    private static final String CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";
    private static final int KEY_SIZE = 128;

    /**
     * 生成密钥
     */
    public static byte[] generateKey() throws NoSuchAlgorithmException {
        KeyGenerator kgen = KeyGenerator.getInstance(KEY_ALGORITHM);
        kgen.init(KEY_SIZE);
        SecretKey secretKey = kgen.generateKey();
        return secretKey.getEncoded();
    }

    /**
     * 加密
     * @param data 明文数据
     * @param key 密钥
     * @return 返回加密后的密文
     */
    public static byte[] encrypt(byte[] data, byte[] key) throws NoSuchPaddingException, NoSuchAlgorithmException,
            InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        SecretKeySpec keySpec = new SecretKeySpec(key, KEY_ALGORITHM);
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        return cipher.doFinal(data);
    }

    /**
     * 解密
     * @param data 密文数据
     * @param key 密钥
     * @return 返回解密后的明文
     */
    public static byte[] decrypt(byte[] data, byte[] key) throws NoSuchPaddingException, NoSuchAlgorithmException,
            InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        SecretKeySpec keySpec = new SecretKeySpec(key, KEY_ALGORITHM);
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        return cipher.doFinal(data);
    }


    /**
     *     // 生成 AES 密钥
     *     byte[] key = AESUtil.generateKey();
     *
     *     // 明文数据
     *     String originalText = "Hello, world!";
     *
     *     // 加密
     *     byte[] encryptedData = AESUtil.encrypt(originalText.getBytes("UTF-8"), key);
     *
     *     // 解密
     *     byte[] decryptedData = AESUtil.decrypt(encryptedData, key);
     *
     *     // 输出结果
     *     System.out.println("原始数据：" + originalText);
     *     System.out.println("加密后的数据：" + new String(encryptedData, "UTF-8"));
     *     System.out.println("解密后的数据：" + new String(decryptedData, "UTF-8"));
     */
}
