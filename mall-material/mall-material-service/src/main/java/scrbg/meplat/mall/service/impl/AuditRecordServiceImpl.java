package scrbg.meplat.mall.service.impl;

import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.entity.AuditRecord;
import scrbg.meplat.mall.entity.SupplierReconciliation;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.AuditRecordMapper;
import scrbg.meplat.mall.service.AuditRecordService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

/**
 * @描述：审核记录 服务类
 * @作者: ye
 * @日期: 2023-06-27
 */
@Service
public class AuditRecordServiceImpl extends ServiceImpl<AuditRecordMapper, AuditRecord> implements AuditRecordService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<AuditRecord> queryWrapper) {
        IPage<AuditRecord> page = this.page(
        new Query<AuditRecord>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(AuditRecord auditRecord) {
        // 验证必填字段
        if (auditRecord.getRelevanceType() == null) {
            throw new BusinessException("relevanceType字段不能为空");
        }
        if (auditRecord.getRelevanceId() == null || auditRecord.getRelevanceId().trim().isEmpty()) {
            throw new BusinessException("relevanceId字段不能为空");
        }
        if (auditRecord.getResultType() == null) {
            throw new BusinessException("resultType字段不能为空");
        }
        if (auditRecord.getAuditType() == null) {
            throw new BusinessException("auditType字段不能为空");
        }

        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(auditRecord);
    }

    @Override
    public void update(AuditRecord auditRecord) {
        super.updateById(auditRecord);
    }


    @Override
    public AuditRecord getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }


    /**
     * 创建审核通过记录
     *
     * @param relevanceId 关联业务单据的ID（如对账单ID、计划ID等）
     * @param resultType 关联类型，标识审核的业务类型：
     *                   1-月供计划, 2-月供变更计划, 3-竞价采购提交, 4-竞价采购中标,
     *                   5-对账单, 6-二级对账单, 7-大宗临购清单, 8-发票,
     *                   9-大宗临购供应商拒绝审核, 10-年度服务费缴费审核,
     *                   11-交易服务费缴费审核, 12-交易服务费对账单确认审核
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDataPass(String relevanceId, int resultType) {
        // 创建审核记录对象
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setRelevanceType(resultType);    // 设置关联业务类型
        auditRecord.setRelevanceId(relevanceId);     // 设置关联业务单据ID
        auditRecord.setResultType(1);                // 设置审核结果类型：1-通过
        auditRecord.setAuditType(1);                 // 设置审核类型：1-提交审核
        auditRecord.setAuditResult("【同意】");       // 设置审核结果描述

        // 保存审核记录到数据库
        create(auditRecord);
    }
    /**
     * 创建审核不通过记
     * @param relevanceId 关联业务单据的ID（如对账单ID、计划ID等）
     * @param resultType 关联类型，标识审核的业务类型：
     *                   1-月供计划, 2-月供变更计划, 3-竞价采购提交, 4-竞价采购中标,
     *                   5-对账单, 6-二级对账单, 7-大宗临购清单, 8-发票,
     *                   9-大宗临购供应商拒绝审核, 10-年度服务费缴费审核,
     *                   11-交易服务费缴费审核, 12-交易服务费对账单确认审核
     * @param auditResult 审核不通过的具体原因描述，用于告知业务人员需要修改的内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDataNoPass(String relevanceId, int resultType, String auditResult) {
        // 创建审核记录对象
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setRelevanceType(resultType);    // 设置关联业务类型
        auditRecord.setRelevanceId(relevanceId);     // 设置关联业务单据ID
        auditRecord.setResultType(2);                // 设置审核结果类型：2-不通过
        auditRecord.setAuditType(1);                 // 设置审核类型：1-提交审核
        auditRecord.setAuditResult(auditResult);     // 设置审核不通过的具体原因

        // 保存审核记录到数据库
        create(auditRecord);
    }
}
