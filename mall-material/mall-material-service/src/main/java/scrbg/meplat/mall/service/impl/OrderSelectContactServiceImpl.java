package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.OrderSelectContact;
import scrbg.meplat.mall.mapper.OrderSelectContactMapper;
import scrbg.meplat.mall.service.OrderSelectContactService;

/**
 * @描述：订单关联合同 服务类
 * @作者: ye
 * @日期: 2023-04-23
 */
@Service
public class OrderSelectContactServiceImpl extends ServiceImpl<OrderSelectContactMapper, OrderSelectContact> implements OrderSelectContactService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectContact> queryWrapper) {
        IPage<OrderSelectContact> page = this.page(
        new Query<OrderSelectContact>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(OrderSelectContact orderSelectContact) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(orderSelectContact);
    }

    @Override
    public void update(OrderSelectContact orderSelectContact) {
        super.updateById(orderSelectContact);
    }


    @Override
    public OrderSelectContact getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}