package scrbg.meplat.mall.adapter.configAutoBean;

import com.alibaba.fastjson.JSON;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 记录日志
 */
@Aspect
public class SystemRequestLogAspect {
    private Logger logger = LoggerFactory.getLogger(SystemRequestLogAspect.class);

    /**
     * 对各个Api类进行切面
     */
    @Pointcut("execution(* scrbg.meplat.mall.*..*Controller.*(..))")
    public void aspectApiLog() {
    }


    /**
     * 输出进入切面之前的日志信息AccountApi
     *
     * @param joinPoint 连接点
     */
    @Before("aspectApiLog()")
    public void beforeInfoLog(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        Object[] args = joinPoint.getArgs();
        try {
            String argJSON = JSON.toJSONString(args);
            String msg = "\n" +
                    "┌─────────────────────────────────────────────────────────────────────────────────────────────────────────" + "\n" +
                    "│\t into:    " + signature.getDeclaringTypeName() + "." + signature.getName() + "\n" +
                    "│\t args:    " + argJSON + "\n" +
                    "└─────────────────────────────────────────────────────────────────────────────────────────────────────────";
            logger.info(msg);
        }catch (Exception e) {

        }

    }

    /**
     * 输出进入切面之后的日志信息
     *
     * @param joinPoint 连接点
     * @param returnObj obj
     */
    @AfterReturning(pointcut = "aspectApiLog()", returning = "returnObj")
    public void returnInfoLog(JoinPoint joinPoint, Object returnObj) {
        logger.info("──────────────────────────────────────────────结束──────────────────────────────────────────────");
    }

    @AfterThrowing(pointcut = "aspectApiLog()", throwing = "e")
    public void throwingLog(JoinPoint joinPoint, Exception e) {
        logger.error("aspect api log error: " + e.getMessage(), e);
    }
}
