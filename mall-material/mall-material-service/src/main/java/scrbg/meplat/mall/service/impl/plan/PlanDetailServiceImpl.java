package scrbg.meplat.mall.service.impl.plan;

import scrbg.meplat.mall.entity.plan.PlanDetail;
import scrbg.meplat.mall.dto.plan.PlanDetailDTO;
import scrbg.meplat.mall.mapper.plan.PlanDetailMapper;
import scrbg.meplat.mall.service.plan.PlanDetailService;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import com.scrbg.common.utils.PageUtils;
import java.util.List;

/**
 * @描述：采购计划明细表 服务类
 * @作者: ye
 * @日期: 2025-05-27
 */
@Service
public class PlanDetailServiceImpl extends ServiceImpl<PlanDetailMapper, PlanDetail> implements PlanDetailService{

        @Override
        public PageUtils queryPage(PlanDetailDTO dto) {
            QueryWrapper<PlanDetail> queryWrapper = new QueryWrapper<>();
            //dto传值给queryWrapper作为查询条件
            IPage<PlanDetail> page = this.page(
                    new Page<>(dto.getPage(), dto.getLimit()),
                    queryWrapper
            );
            return new PageUtils(page);
        }

        @Override
        @Transactional(rollbackFor = Exception.class)
        public void create(PlanDetail planDetail) {
            super.save(planDetail);
        }

        @Override
        @Transactional(rollbackFor = Exception.class)
        public void update(PlanDetail planDetail) {
            super.updateById(planDetail);
        }

        @Override
        public PlanDetail getById(String id) {
            return super.getById(id);
        }

        @Override
        @Transactional(rollbackFor = Exception.class)
        public void delete(String id) {
            super.removeById(id);
        }

        @Override
        @Transactional(rollbackFor = Exception.class)
        public void deleteBatch(List<String> ids) {
            super.removeByIds(ids);
        }
}
