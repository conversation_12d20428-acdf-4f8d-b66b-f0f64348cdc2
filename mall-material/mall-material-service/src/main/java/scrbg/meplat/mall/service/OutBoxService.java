package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.vo.user.userCenter.OutBoxVo;
/**
 * @描述：供应商和对应店铺 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface OutBoxService extends IService<EnterpriseInfo> {

    PageUtils queryPageSupplierShops(JSONObject jsonObject, QueryWrapper<OutBoxVo> q);



}
