package scrbg.meplat.mall.service;

import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.entity.ShudaoEnterprise;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ShudaoEnterprise;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2024-01-29
 */
public interface ShudaoEnterpriseService extends IService<ShudaoEnterprise> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShudaoEnterprise> queryWrapper);

        void create(ShudaoEnterprise shudaoEnterprise);

        void update(ShudaoEnterprise shudaoEnterprise);

        ShudaoEnterprise getById(String id);

        void delete(String id);
        void deleteBatch( List<String> ids);

        /**
         * 蜀道企业导入
         * @param file
         * @param response
         */
        void uploadSdExcelFile(MultipartFile file, HttpServletResponse response);

        /**
         * 导出蜀道企业数据
         * @param jsonObject
         * @param response
         */
        void exportShopPayExcelFile(JSONObject jsonObject, HttpServletResponse response);
}
