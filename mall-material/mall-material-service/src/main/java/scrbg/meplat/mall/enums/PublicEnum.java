package scrbg.meplat.mall.enums;

/**
 * <AUTHOR>
 * @create 2022-11-08 12:00
 */
public enum PublicEnum {

    /**
     * 系统参数
     */
    // 大宗临购生成限制最高金额
    //   SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode, PublicEnum.SYNTHESIZE_TEMPORARY_AMOUNT_MAX.getRemark()).one();
    SYNTHESIZE_TEMPORARY_AMOUNT_MAX(0,"synthesizeTemporaryAmountMax"),
    PLATFORMFREEYHADDRESS(0,"platformFreeyhAddress"), // 平台收费银行开户行
    PLATFORMFREEYHACCOUNT(0,"platformFreeyhAccount"), // 平台收费银行账号
    PLATFORMFREEYHORGNAME(0,"platformFreeyhOrgName"), // 平台收费公司名称
    PlatformYearFeeExpirationReminderDays(0,"platformYearFeeExpirationReminderDays"),//年费到期提醒天数
    PlatformPaymentDeadlineDays(0,"paymentDeadlineDays"),//交易费缴费截止提醒天数
    PlatformShopYearFeeAmount(0,"platformShopYearFeeAmount"),//店铺年费金额

    PLATFORM_DEAL_MAX_QUOTA_DP(0,"platformFeeDealMaxQuotaludp"), // 平台交易最大免费额度 店铺
    PLATFORM_DEAL_MAX_QUOTA_LY(0,"platformFeeDealMaxQuotaly"), // 平台交易最大免费额度 履约
    PLATFORM_SHOP_FEE_DEAL_RATIO(0,"platformShopFeeDealRatio"), // 平台店铺交易费收取比例（单位千分之）
    PLATFORM_CONTRACT_FEE_DEAL_RATIO(0,"platformContractFeeDealRatio"), // 平台合同交易费收取比例（单位千分之）

    UpPercentage(0,"upPercentage"),//均价上限（单位百分之）
    LowerPercentage(0,"lowerPercentage"),//均价下限（单位百分之）

    ACCOUNT_PERIOD_ANNUAL_RATE(0,"accountPeriodAnnualRate"),//账期年化利率

    MAX_SEND_CODE(30,"发送验证码最大次数！"),
    RESULT_CODE_400(400,""),
    VALIDATE_FAILED(405, "参数检验失败!"),

    /**
     * 短信校验错误码
     */
    RESULT_CODE_500200(500200,""),
    RESULT_SAVE_ERROR(-1, "保存失败！"),

    MATERIALS(0,"物资商城"),
    DEVICE(1,"设备商城"),

    TYPE_MATERIAL(0,"物资"),
    TYPE_EQUIPMENT(1,"设备"),
    TYPE_MATERIAL_ZC(2,"周材（物资）"),
    TYPE_EQUIPMENT_ZC(3,"周材（设备）"),
    TYPE_EQUIPMENT_USED(4,"二手设备"),
    TYPE_EQUIPMENT_LEASE(5,"租赁设备"),
    STATE_OPEN(1,"启用"),
    STATE_STOP(0,"停用"),

    STATE_UNPUBLISH(0,"未发布"),
    STATE_PUBLISH(1,"发布"),

    IS_YES(1,"是"),
    IS_NO(0,"否"),

    IS_DELETE_YES(-1,"删除"),
    IS_DELETE_NO(0,"未删除"),

    CATEGORY_TYPE_1(1,"后台管理平台"),
    CATEGORY_TYPE_2(2,"供应商管理平台"),
    CATEGORY_TYPE_3(3,"履约管理平台"),

    ORDER_BY_SORT(1,"根据排序值排序"),
    ORDER_BY_GMT_CREATE(2,"根据创建时间排序"),
    ORDER_BY_GMT_MODIFIED(3,"根据修改时间排序"),
    ORDER_BY_GMT_RELEASE(4,"根据发布时间排序"),

    //流程状态
    ProcessStatus_RUNNING(0, "运行中"),
    ProcessStatus_COMPLETED(1, "已完成"),
    ProcessStatus_TERMINATED(2, "已中止"),
    //节点操作类型
    OperationType_SUBMIT(0, "提交"),
    OperationType_APPROVE(1, "通过"),
    OperationType_REJECT(2, "不通过"),

    //交易服务费状态
    TRADE_FEE_STATUS_WAIT_CONFIRM(0, "待确认"),
    TRADE_FEE_STATUS_CONFIRMING(1, "确认中"),
    TRADE_FEE_STATUS_CONFIRM_SUCCESS(2, "确认成功"),
    TRADE_FEE_STATUS_CONFIRM_FAIL(3, "确认失败"),
    TRADE_FEE_STATUS_AUDITING(4, "审核中"),
    TRADE_FEE_STATUS_AUDIT_PASS(5, "审核通过"),
    TRADE_FEE_STATUS_AUDIT_REJECT(6, "审核未通过");
    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    PublicEnum() {
    }

    PublicEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


}

