package scrbg.meplat.mall.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.log4j.Log4j2;
import springfox.documentation.spring.web.json.Json;

import java.text.SimpleDateFormat;
import java.util.Date;


@Log4j2
public class LogUtil {


    /**
     * 记录info日志
     * @param key 秘钥key
     * @param m 方法名
     * @param localArg 本地接口请求参数
     * @param farArg 远程接口请求参数
     * @param res 接口返回
     * @param cas 当前类
     */
    public static void writeInfoLog(String key, String m, Object localArg, Object farArg, Object res, Class<?> cas) {
        // 保留null值属性
        // String localArgJson = JSON.toJSONString(localArg, SerializerFeature.WriteMapNullValue);
        // 不保留null值属性
        String localArgJson = JSON.toJSONString(localArg);
        String farArgJson = JSON.toJSONString(farArg);
        String resJson = JSON.toJSONString(res);
        log.info("分布式事务info日志-秘钥key:{} \n方法名:{} \n本地接口请求参数:{} \n远程接口请求参数:{} \n接口返回:{} \n类包名:{} " +
                "\n---------------------------------------结束---------------------------------------"
                ,key,m,localArgJson,farArgJson,resJson,cas.getName());
    }

    /**
     * 记录错误日志
     * @param key 秘钥key
     * @param m 方法名
     * @param localArg 本地接口请求参数
     * @param farArg 远程接口请求参数
     * @param res 接口返回
     * @param errorInfo 错误信息
     * @param cas 当前类
     */
    public static void writeErrorLog(String key, String m, Object localArg, Object farArg, Object res, String errorInfo, Class<?> cas) {
        String localArgJson = JSON.toJSONString(localArg);
        String farArgJson = JSON.toJSONString(farArg);
        String resJson = JSON.toJSONString(res);
        log.error("分布式事务error日志-秘钥key:{} \n方法名:{} \n本地接口请求参数:{} \n远程接口请求参数:{} \n接口返回:{} \n错误信息:{} \n类包名:{} " +
                        "\n---------------------------------------结束---------------------------------------"
                ,key,m,localArgJson,farArgJson,resJson,errorInfo,cas.getName());
    }

    public static void writeErrorLog2(String key, String m, Object localArg, String farArg, Object res, String errorInfo, Class<?> cas) {
        String localArgJson = JSON.toJSONString(localArg);
        String farArgJson = farArg;
        String resJson = JSON.toJSONString(res);
        log.error("分布式事务error日志-秘钥key:{} \n方法名:{} \n本地接口请求参数:{} \n远程接口请求参数:{} \n接口返回:{} \n错误信息:{} \n类包名:{} " +
                        "\n---------------------------------------结束---------------------------------------"
                ,key,m,localArgJson,farArgJson,resJson,errorInfo,cas.getName());
    }
}
