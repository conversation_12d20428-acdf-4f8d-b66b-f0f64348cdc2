package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.FloorGoods;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.mapper.FloorGoodsMapper;
import scrbg.meplat.mall.service.FloorGoodsService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.vo.product.website.IndexMaterialVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @描述：楼层显示的商品 服务类
 * @作者: 胡原武
 * @日期: 2022-11-10
 */
@Service
public class FloorGoodsServiceImpl extends ServiceImpl<FloorGoodsMapper, FloorGoods> implements FloorGoodsService {

    @Autowired
    private ProductService productService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<FloorGoods> queryWrapper) {
        IPage<FloorGoods> page = this.page(
                new Query<FloorGoods>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(FloorGoods floorGoods) {
        floorGoods.setState(PublicEnum.IS_NO.getCode());
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(floorGoods);
    }

    @Override
    public void update(FloorGoods floorGoods) {
        super.updateById(floorGoods);
    }


    @Override
    public FloorGoods getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public PageUtils queryFlooerGoodsPage(JSONObject jsonObject, LambdaQueryWrapper<FloorGoods> floorGoodsLambdaQueryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String floorId = (String) innerMap.get("floorId");
        String founderName = (String) innerMap.get("founderName");
        Integer state = (Integer) innerMap.get("state");
        if (!StringUtils.isEmpty(floorId)) {
            floorGoodsLambdaQueryWrapper.eq(FloorGoods::getFloorId, floorId);
        }
        if (!StringUtils.isEmpty(founderName)) {
            floorGoodsLambdaQueryWrapper.like(FloorGoods::getFounderName, founderName);
        }
        if (state != null) {
            floorGoodsLambdaQueryWrapper.eq(FloorGoods::getState, state);
        }
        floorGoodsLambdaQueryWrapper.orderByDesc(FloorGoods::getGmtModified);
        IPage<FloorGoods> page = this.page(
                new Query<FloorGoods>().getPage(jsonObject),
                floorGoodsLambdaQueryWrapper
        );
        List<FloorGoods> records = page.getRecords();
        for (FloorGoods record : records) {
            IndexMaterialVO indexMaterialVO = productService.getIndexMaterialVO(record.getGoodsId());
            record.setVo(indexMaterialVO);
        }
        return new PageUtils(page);
    }

    @Override
    public void batchCreate(List<FloorGoods> floorGoods) {
        ArrayList<FloorGoods> list = new ArrayList<>();
        for (FloorGoods floorGood : floorGoods) {
            Integer count = lambdaQuery().eq(FloorGoods::getFloorId, floorGood.getFloorId())
                    .eq(FloorGoods::getGoodsId, floorGood.getGoodsId()).count();
            if(count > 0){
                continue;
            }else {
                floorGood.setState(PublicEnum.IS_NO.getCode());
                list.add(floorGood);
            }
        }
        saveBatch(list);
    }

    @Override
    public void updateByPublish(List<String> ids, String s) {
            if("1".equals(s)){
                lambdaUpdate().in(FloorGoods::getFloorGoodsId,ids)
                        .set(FloorGoods::getState,1)
                        .set(FloorGoods::getGmtModified,new Date()).update();
            }else {
                lambdaUpdate().in(FloorGoods::getFloorGoodsId,ids)
                        .set(FloorGoods::getState,0)
                        .set(FloorGoods::getGmtModified,new Date()).update();
            }
    }

}
