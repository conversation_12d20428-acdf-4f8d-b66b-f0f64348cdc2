package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.OrdersMapper;
import scrbg.meplat.mall.mapper.SystemParamMapper;
import scrbg.meplat.mall.service.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述：系统参数表 服务类
 * @作者: sund
 * @日期: 2022-12-05
 */
@Service
public class SystemParamServiceImpl extends ServiceImpl<SystemParamMapper, SystemParam> implements SystemParamService {

    @Autowired
    MallConfig mallConfig;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    DeviceDemandService deviceDemandService;

    @Autowired
    ShopBusinessService shopBusinessService;

    @Autowired
    OrdersService ordersService;

    @Autowired
    ShopService shopService;

    @Autowired
    OrdersMapper ordersMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private EnterpriseInfoService enterpriseInfoService;


    @Override
    public PageUtils SysQueryPage(JSONObject jsonObject, LambdaQueryWrapper<SystemParam> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        Integer maintain = 1;
        Integer type = 0;
        Integer mallType = Integer.valueOf(mallConfig.mallType);
        String name = (String) innerMap.get("name");
        if (org.apache.commons.lang.StringUtils.isNotEmpty(name)) {
            queryWrapper.like(SystemParam::getName, name);
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(keywords)) {
            queryWrapper.and(wrapper -> wrapper.like(SystemParam::getName, keywords)
                    .or().like(SystemParam::getKeyValue, keywords));
        }
        if (mallType != null) {
            queryWrapper.eq(SystemParam::getMallType, mallType);
        }
        if (maintain != null) {
            queryWrapper.eq(SystemParam::getMaintain, maintain);
        }
        if (type != null) {
            queryWrapper.eq(SystemParam::getType, type);
        }

        //排序方式(排序值升序)
        if (orderBy == null) {
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_CREATE.getCode()) {
            queryWrapper.orderByDesc(SystemParam::getGmtModified);

            //排序方式(修改时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(SystemParam::getGmtCreate);
        }

        //queryWrapper.select()

        IPage<SystemParam> page = this.page(
                new Query<SystemParam>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SystemParam> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        Integer type = (Integer) innerMap.get("type");
        Integer maintain = 1;
        Integer mallType = mallConfig.mallType;
        String name = (String) innerMap.get("name");
        if (org.apache.commons.lang.StringUtils.isNotEmpty(name)) {
            queryWrapper.like(SystemParam::getName, name);
        }
        queryWrapper.eq(SystemParam::getIsShow, 1);
        if (org.apache.commons.lang.StringUtils.isNotEmpty(keywords)) {
            // 转义特殊字符
            String escapedKeywords = keywords.replace("\\", "\\\\").replace("%", "\\%").replace("_", "\\_");
            queryWrapper.and(wrapper -> wrapper.like(SystemParam::getName, escapedKeywords)
                    .or().like(SystemParam::getKeyValue, escapedKeywords));
        }
        if (mallType != null) {
            queryWrapper.eq(SystemParam::getMallType, mallType);
        }
        if (maintain != null) {
            queryWrapper.eq(SystemParam::getMaintain, maintain);
        }
        if (type != null) {
            queryWrapper.eq(SystemParam::getType, type);
        }

        //排序方式(排序值升序)
        if (orderBy == null) {
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_CREATE.getCode()) {
            queryWrapper.orderByDesc(SystemParam::getGmtModified);

            //排序方式(修改时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(SystemParam::getGmtCreate);
        }

        //queryWrapper.select()

        IPage<SystemParam> page = this.page(
                new Query<SystemParam>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public void create(SystemParam systemParam) {
        //也可以baseMapper.insert
        Integer count = lambdaQuery()
                .eq(SystemParam::getMallType, mallConfig.mallType)
                .eq(SystemParam::getKeyValue2, systemParam.getKeyValue())
                .eq(SystemParam::getKeyValue, systemParam.getKeyValue()).count();
        if (count > 0) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "名称重复");
        }
        systemParam.setType(1);
        systemParam.setMaintain(1);
        save(systemParam);
    }
    @Override
    public void createSysParam(SystemParam systemParam) {
        //也可以baseMapper.insert
        Integer count = lambdaQuery()
                .eq(SystemParam::getMallType, mallConfig.mallType)
                .eq(SystemParam::getName, systemParam.getName())
                .eq(SystemParam::getRemarks, systemParam.getRemarks())
                .eq(SystemParam::getKeyValue, systemParam.getKeyValue()).count();
        if (count > 0) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "名称重复");
        }
        systemParam.setType(0);
        systemParam.setMaintain(1);
        save(systemParam);
    }
    @Override
    public void update(SystemParam systemParam) {
//        SystemParam one = lambdaQuery().eq(SystemParam::getName, systemParam.getName()).one();
//        if(one != null){
//            if(one.getSystemId().equals(systemParam.getSystemId())){
//                super.updateById(systemParam);
//            }
//            else {
//                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(),"名称重复！");
//            }
//        }else {
//            super.updateById(systemParam);
//        }
        super.updateById(systemParam);

    }

    @Override
    public void updateByBatch(List<String> ids) {
        List<SystemParam> systemParams = listByIds(ids);
        super.saveOrUpdateBatch(systemParams);
    }


    @Override
    public SystemParam getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {

        super.removeById(id);
    }

    @Override
    public void batchDelete(List<String> ids) {
        super.removeByIds(ids);
    }

    /**
     * 根据编码获取参数列表
     *
     * @param code
     * @param size
     * @return
     */
    @Override
    public List<SystemParam> listByCode(String code, Integer size) {
        List<SystemParam> list = lambdaQuery().eq(SystemParam::getCode, code)
                .eq(SystemParam::getMallType, mallConfig.mallType)
                .orderByDesc(SystemParam::getSort, SystemParam::getGmtModified)
                .last(size != null, "limit " + size)
                .list();
        return list;
    }

    /**
     * 获取所有系统参数
     *
     * @return 系统参数列表
     */
    @Override
    public List<SystemParam> listAllParams() {
        return lambdaQuery()
                .eq(SystemParam::getMallType, mallConfig.mallType)
                .eq(SystemParam::getIsDelete, 0)
                .orderByDesc(SystemParam::getSort, SystemParam::getGmtModified)
                .list();
    }

    @Override
    public double listByCode(String code) {
        SystemParam one = lambdaQuery().eq(SystemParam::getCode, code).select(SystemParam::getKeyValue).one();
        return Double.parseDouble(one.getKeyValue());
    }

    @Override
    public Boolean getIsBusinessOrg(String orgId) {
        boolean flag = false;
        String orgIds = stringRedisTemplate.opsForValue().get("isBusinessOrg");
        if (orgIds != null) {
            if (orgIds.contains(orgId)) {
                flag = true;
            }

        } else {
            List<ShopBusiness> list = shopBusinessService.lambdaQuery().eq(ShopBusiness::getState, 1).list();
            List<String> shopIds = list.stream().map(ShopBusiness::getShopId).collect(Collectors.toList());
            List<String> interiorIds = list.stream().map(ShopBusiness::getInteriorId).collect(Collectors.toList());
            stringRedisTemplate.opsForValue().set("isBusinessShopId", String.join(",", shopIds));
            stringRedisTemplate.opsForValue().set("isBusinessOrg", String.join(",", interiorIds));
            if (interiorIds.toString().contains(orgId)) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 修改首页统计
     */
    @Override
    public void updateIndexParameterCount() {
//        if(mallConfig.mallType == 1){
////            if("dev".equals(mallConfig.profilesActive)){
////                return;
////            }
//            if(1 == 1){
//                return;
//            }
//            Integer demandCount = deviceDemandService.lambdaQuery().eq(DeviceDemand::getCheckState, 1)
//                    .count();
//            lambdaUpdate().eq(SystemParam::getCode,"publishRequireTotal")
//                    .set(SystemParam::getKeyValue,demandCount).update();
//            Integer orderCount = ordersService.lambdaQuery().eq(Orders::getState, 6).count();
//            lambdaUpdate().eq(SystemParam::getCode,"transactionProjectTotal")
//                    .set(SystemParam::getKeyValue,orderCount).update();
//            // 交易金额 发送请求获取
////            if("dev".equals(mallConfig.profilesActive)){
////                HashMap<Object, Object> dto = new HashMap<>();
////                dto.put("jsonrpc","2.0");
////                dto.put("method","CBM.ToEquipMaterialService.GetSettleAmount");
////                dto.put("id",1);
////                HashMap<String, String> tags = new HashMap<>();
////                tags.put("userid", "a94220f01db0-aa87-8848-cfc6-4d321eea");
////                tags.put("username", "林清香");
////                tags.put("orgid", "a94220dc34ae-a5b7-bb41-a2a1-b590b187");
////                tags.put("orgname", "四川公路桥梁建设集团有限公司");
////                tags.put("companycode", "1000");
////                tags.put("auth_client_id", "test");
////                tags.put("auth_token", "test");
////                tags.put("platformid", "1");
////                dto.put("tags",tags);
////                List<Object> params = new ArrayList<>();
////                dto.put("params",params);
////                String content = JSON.toJSONString(dto);
////                // 发送请求
////                HttpHeaders headers = new HttpHeaders();
////                HttpEntity<String> request = new HttpEntity<>(content, headers);
////                String url = mallConfig.pcwp1ContactUrl + "/json.rpc";
////                Map rMap  = restTemplate.postForObject(url, request, Map.class);
////                System.out.println("交易金额返回：" + rMap);
////                Double resultRes = (Double) rMap.get("result");
////                if(resultRes != null) {
////                    BigDecimal result = new BigDecimal( resultRes);
////                    BigDecimal divide = result.divide(new BigDecimal(10000));
////                    int count = divide.setScale(0, BigDecimal.ROUND_UP).intValue();
////                    lambdaUpdate().eq(SystemParam::getCode,"transactionAmount")
////                            .set(SystemParam::getKeyValue,count).update();
////                }
////            }
//
////            Integer orderPriceCount =  ordersMapper.getOrderPriceSum();
////            lambdaUpdate().eq(SystemParam::getCode,"transactionAmount")
////                    .set(SystemParam::getKeyValue,orderPriceCount).update();
//            Integer shopCount = shopService.lambdaQuery().eq(Shop::getAuditStatus, 1).count();
//            lambdaUpdate().eq(SystemParam::getCode,"merchantsSettledTotal")
//                    .set(SystemParam::getKeyValue,shopCount).update();
//        }
    }
}
