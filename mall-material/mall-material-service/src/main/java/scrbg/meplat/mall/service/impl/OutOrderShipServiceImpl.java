package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.order.OrderShipmentsQtyIsOkDTO;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.OrderShipMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.app.ShipFiles;
import scrbg.meplat.mall.vo.ship.*;
import scrbg.meplat.mall.vo.shopManage.ReturnGoodItemVo;
import scrbg.meplat.mall.vo.shopManage.ReturnGoodsVo;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-05-22
 */
@Service
public class OutOrderShipServiceImpl extends ServiceImpl<OrderShipMapper, OrderShip> implements OutOrderShipService {
    @Autowired
    OrdersService ordersService;
    @Autowired
    ProductSkuService productSkuService;
    @Autowired
    RestTemplateUtils restTemplateUtils;

    @Autowired
    OrderItemService orderItemService;
    @Autowired
    ProductCategoryService productCategoryService;

    @Autowired
    MaterialMonthSupplyPlanDtlService materialMonthSupplyPlanDtlService;

    @Autowired
    InterfaceLogsService interfaceLogsService;

    @Autowired
    public MallConfig mallConfig;

    @Autowired
    FileService fileService;
    @Autowired
    OrderShipDtlService orderShipDtlService;
    @Autowired
    OrderReturnService orderReturnService;
    @Autowired
    OrderReturnItemService orderReturnItemService;


    @Autowired
    private EnterpriseInfoService enterpriseInfoService;
    @Autowired
    OrderSelectPlanService orderSelectPlanService;
    @Autowired
    OutSupplierRecevieService outSupplierRecevieService;
    @Autowired
    ShopService shopService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        queryWrapper.eq(OrderShip::getShipEnterpriseId, user.getEnterpriseId());
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        String orderId = (String) innerMap.get("orderId");
        Integer type = (Integer) innerMap.get("type");
        String orderSn = (String) innerMap.get("orderSn");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        Integer sourceType = (Integer) innerMap.get("sourceType");
        String otherOrderSn = (String) innerMap.get("otherOrderSn");
        String staConfirmTime = (String) innerMap.get("staConfirmTime");
        String endConfirmTime = (String) innerMap.get("endConfirmTime");
        String startGmtCreate = (String) innerMap.get("startGmtCreate");
        String endGmtCreate = (String) innerMap.get("endGmtCreate");
        String staShipTime = (String) innerMap.get("staShipTime");
        String endShipTime = (String) innerMap.get("endShipTime");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String receiveOrgName = (String) innerMap.get("receiveOrgName");
        Integer productType = (Integer) innerMap.get("productType");
        queryWrapper.eq(orderClass != null, OrderShip::getOrderClass, orderClass);
        if (productType != null) {
            queryWrapper.eq(OrderShip::getProductType, productType);
        }
        if (orderId != null && orderId != "") {
            queryWrapper.eq(OrderShip::getOrderId, orderId);
        }
        if (belowPrice != null && belowPrice != "") {
            queryWrapper.lt(OrderShip::getRateAmount, belowPrice);
        }

        if (abovePrice != null && abovePrice != "") {
            queryWrapper.gt(OrderShip::getRateAmount, abovePrice);
        }
        if (type != null) {
            queryWrapper.eq(OrderShip::getType, type);
        }
        queryWrapper.eq(sourceType != null, OrderShip::getSourceType, sourceType);
        if (StringUtils.isNotBlank(orderSn)) {
            queryWrapper.eq(OrderShip::getOrderSn, orderSn);
        }
        if (StringUtils.isNotBlank(otherOrderSn)) {
            queryWrapper.eq(OrderShip::getOtherOrderSn, otherOrderSn);
        }
        if (staConfirmTime != null && staConfirmTime != "") {
            queryWrapper.gt(OrderShip::getConfirmTime, staConfirmTime);
        }
        if (endConfirmTime != null && endConfirmTime != "") {
            queryWrapper.lt(OrderShip::getConfirmTime, endConfirmTime);
        }
        if (startGmtCreate != null && startGmtCreate != "") {
            queryWrapper.gt(OrderShip::getGmtCreate, startGmtCreate);
        }
        if (endGmtCreate != null && endGmtCreate != "") {
            queryWrapper.lt(OrderShip::getGmtCreate, endGmtCreate);
        }
        if (staShipTime != null && staShipTime != "") {
            queryWrapper.gt(OrderShip::getShipData, staShipTime);
        }
        if (endShipTime != null && endShipTime != "") {
            queryWrapper.lt(OrderShip::getShipData, endShipTime);
        }
        if (orderBy != null) {
            if (orderBy == 1) {
                queryWrapper.orderByDesc(OrderShip::getGmtCreate);
            }
            if (orderBy == 2) {
                queryWrapper.orderByDesc(OrderShip::getFlishTime);
            }
            if (orderBy == 3) {
                queryWrapper.orderByDesc(OrderShip::getShipData);
            }
        } else {
            queryWrapper.orderByDesc(OrderShip::getGmtCreate);
        }
        if (receiveOrgName != null && receiveOrgName != "") {
            queryWrapper.like(OrderShip::getReceiveOrgName, receiveOrgName);
        }
        if (keywords != null && keywords != "") {
            queryWrapper.and(i -> i.like(OrderShip::getReceiveName, keywords)
                    .or().like(OrderShip::getReceiveOrgName, keywords)
                    .or().like(OrderShip::getBillSn, keywords)
                    .or().like(OrderShip::getOrderSn, keywords));
        }
        IPage<OrderShip> page = page(new Query<OrderShip>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }


    @Override
    public PageUtils queryPageTwo(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        queryWrapper.eq(OrderShip::getSupplierId, user.getEnterpriseId());
        getWrapper(queryWrapper, innerMap);

        IPage<OrderShip> page = page(new Query<OrderShip>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changShipNum(List<SubmitOrderShipDtl> vos) {
        for (SubmitOrderShipDtl vo : vos) {
            if (vo.getShipNum().compareTo(BigDecimal.valueOf(0)) < 0) {
                throw new BusinessException(500, "发货单项" + vo.getDtlId() + "确认收货数量不能为负数");
            }
            OrderShipDtl dtl = orderShipDtlService.getById(vo.getDtlId());
            if (dtl != null) {
                if (vo.getShipNum().compareTo(dtl.getShipCounts()) > 0) {
                    throw new BusinessException(500, "发货单" + dtl.getDtlId() + "确认收货数量不能大于发货数量");
                } else if (vo.getShipNum().compareTo(dtl.getShipCounts()) == 0) {
                    dtl.setShipNum(vo.getShipNum());
                    dtl.setIsEqual(0);
                } else {
                    dtl.setShipNum(vo.getShipNum());
                    dtl.setIsEqual(1);
                }
                orderShipDtlService.updateById(dtl);
            }


        }


    }

    private void getWrapper(LambdaQueryWrapper<OrderShip> queryWrapper, Map<String, Object> innerMap) {
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        String orderId = (String) innerMap.get("orderId");
        Integer type = (Integer) innerMap.get("type");
        String orderSn = (String) innerMap.get("orderSn");
        String staConfirmTime = (String) innerMap.get("staConfirmTime");
        String endConfirmTime = (String) innerMap.get("endConfirmTime");
        String startGmtCreate = (String) innerMap.get("startGmtCreate");
        String endGmtCreate = (String) innerMap.get("endGmtCreate");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        String staShipTime = (String) innerMap.get("staShipTime");
        String endShipTime = (String) innerMap.get("endShipTime");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String receiveOrgName = (String) innerMap.get("receiveOrgName");
        Integer productType = (Integer) innerMap.get("productType");

        if (productType != null) {
            queryWrapper.eq(OrderShip::getProductType, productType);
        }
        if (orderId != null && orderId != "") {
            queryWrapper.eq(OrderShip::getOrderId, orderId);
        }
        if (belowPrice != null && belowPrice != "") {
            queryWrapper.lt(OrderShip::getRateAmount, belowPrice);
        }

        if (abovePrice != null && abovePrice != "") {
            queryWrapper.gt(OrderShip::getRateAmount, abovePrice);
        }
        if (type != null) {
            queryWrapper.eq(OrderShip::getType, type);
        }
        if (orderSn != null && orderSn != "") {
            queryWrapper.eq(OrderShip::getOrderSn, orderSn);
        }
        if (staConfirmTime != null && staConfirmTime != "") {
            queryWrapper.gt(OrderShip::getConfirmTime, staConfirmTime);
        }
        if (endConfirmTime != null && endConfirmTime != "") {
            queryWrapper.lt(OrderShip::getConfirmTime, endConfirmTime);
        }
        if (startGmtCreate != null && startGmtCreate != "") {
            queryWrapper.gt(OrderShip::getGmtCreate, startGmtCreate);
        }
        if (endGmtCreate != null && endGmtCreate != "") {
            queryWrapper.lt(OrderShip::getGmtCreate, endGmtCreate);
        }
        if (staShipTime != null && staShipTime != "") {
            queryWrapper.gt(OrderShip::getShipData, staShipTime);
        }
        if (endShipTime != null && endShipTime != "") {
            queryWrapper.lt(OrderShip::getShipData, endShipTime);
        }
        if (orderBy != null) {
            if (orderBy == 1) {
                queryWrapper.orderByDesc(OrderShip::getGmtCreate);
            }
            if (orderBy == 2) {
                queryWrapper.orderByDesc(OrderShip::getFlishTime);
            }
            if (orderBy == 3) {
                queryWrapper.orderByDesc(OrderShip::getShipData);
            }
        } else {
            queryWrapper.orderByDesc(OrderShip::getShipData);
        }
        if (receiveOrgName != null && receiveOrgName != "") {
            queryWrapper.like(OrderShip::getReceiveOrgName, receiveOrgName);
        }
        if (keywords != null && keywords != "") {
            queryWrapper.and(i -> i.like(OrderShip::getReceiveName, keywords).or()
                    .like(OrderShip::getReceiveOrgName, keywords)
                    .or().like(OrderShip::getOrderSn, keywords));
        }


        if (orderClass != null) {
            queryWrapper.eq(OrderShip::getOrderClass, orderClass);
        }
//        else {
//            queryWrapper.in(OrderShip::getOrderClass, 1, 2);
//        }
    }

    @Override
    public void create(OrderShip orderShip) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(orderShip);
    }

    @Override
    public void update(OrderShip orderShip) {
        super.updateById(orderShip);
    }


    @Override
    public OrderShip getById(String id) {
        OrderShip byId = baseMapper.selectById(id);
        List<File> files = fileService.listReIdAndTypeAndPKey(id, 10, null);
        if (files != null && files.size() > 0) {
            byId.setFiles(files);
        } else {
            byId.setFiles(new ArrayList<>());
        }
        return byId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (id == null) {
            throw new BusinessException(601, "发货单号为空");
        }
        OrderShip byId = getById(id);

        if (byId != null) {
            if (byId.getType() == 0) {
                List<OrderShipDtl> dtls = orderShipDtlService.getDataByOrderShipId(id);
                for (OrderShipDtl dtl : dtls) {
                    OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
                    orderItem.setShipCounts(orderItem.getShipCounts().subtract(dtl.getShipCounts()));
//                    if (orderItem.getParentOrderItemId()!=null){
//                        OrderItem parent = orderItemService.getById(orderItem.getParentOrderItemId());
//                        parent.setShipCounts( orderItem.getShipCounts());
//                        orderItemService.updateShipCounts(parent);
//                    }

                    orderShipDtlService.removeById(dtl.getDtlId());
                    orderItemService.updateShipCounts(orderItem);
                }
                super.removeById(id);

            } else {
                throw new BusinessException(603, "发货单已确认，不能删除");
            }

        } else {
            throw new BusinessException(602, "发货单不存在");
        }

    }

    /**
     * 根据订单项生成发货单
     *
     * @param orderShipVo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderShip(OrderShipVo orderShipVo) {
        if (orderShipVo == null || orderShipVo.getDtls() == null || orderShipVo.getDtls().size() == 0) {
            throw new BusinessException(500, "请输入发货单或者填写发货单项");
        }
        OrderShip orderShip = new OrderShip();
        Orders orders = ordersService.getById(orderShipVo.getOrderId());


        //根据订单生成发货单，保存数据库
        if (orders.getParentOrderId() != null) {
            Orders one = ordersService.getById(orders.getParentOrderId());
            orderShipVo.setOrderClass(3);
            orderShip = saveShipInfo(orderShipVo, one);
            orderShip.setOtherOrderId(orders.getOrderId());
            orderShip.setOtherOrderSn(orders.getOrderSn());

        } else{
            orderShip = saveShipInfo(orderShipVo, orders);
        }
        //判断是否有二级订单

        BigDecimal rateAmous = BigDecimal.valueOf(0);
        BigDecimal noRateAmous = BigDecimal.valueOf(0);
        BigDecimal otherRateAmous = BigDecimal.valueOf(0);
        BigDecimal otherNoRateAmous = BigDecimal.valueOf(0);
        List<OrderShipDtl> dtls = orderShip.getDtls();
        //判断传递的发货单项的数量是否全部为0
        int dtlcounts = 0;
        //根据订单项和来源创建发货单明细
        ArrayList<OrderShipmentsQtyIsOkDTO> list = new ArrayList<>();
        for (OrderShipDtl dtl : dtls) {
//            dtl.setShipNum(dtl.getShipCounts());
            if (dtl.getShipCounts().compareTo(BigDecimal.valueOf(0)) == 0) {
                dtlcounts++;
                continue;
            } else {
                dtl.setBillId(orderShip.getBillId());
                dtl.setBillSn(orderShip.getBillSn());
                dtl.setOrderId(orderShip.getOrderId());
                dtl.setOrderSn(orderShip.getOrderSn());
                //
                createOrderShipDtl(dtl, orderShip.getSourceType(), list);
                rateAmous = rateAmous.add(dtl.getTotalAmount());
                noRateAmous = noRateAmous.add(dtl.getNoRateAmount());
                if (orders.getOrderClass() != 1) {
                    otherRateAmous = otherRateAmous.add(dtl.getOtherTotalAmount());
                    otherNoRateAmous = otherNoRateAmous.add(dtl.getOtherNoRateAmount());
                }
                if (orderShip.getSourceType() == 1) {
                    orderItemService.orderShipmentsQtyIsOkYG(list);
                } else if (orderShip.getSourceType() == 6) {
                    orderItemService.orderShipmentsQtyIsOkLG(list);
                }
            }
        }
        if (dtls.size() == dtlcounts) {
            throw new BusinessException(500, "请填写发货数量");
        }
        orderShip.setRateAmount(rateAmous);
        orderShip.setNoRateAmount(noRateAmous);
        orderShip.setOtherRateAmount(otherRateAmous);
        orderShip.setOtherNoRateAmount(otherNoRateAmous);
        update(orderShip);
        orderShipDtlService.saveBatch(dtls);
    }

    private void createOrderShipDtl(OrderShipDtl dtl, Integer sourType, ArrayList<OrderShipmentsQtyIsOkDTO> list) {
        OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
        if (orderItem.getParentOrderItemId() != null) {
            orderItem = orderItemService.getById(orderItem.getParentOrderItemId());
        }
        /**
         * 设置发货单项
         */
        dtl.setProductId(orderItem.getProductId());
        dtl.setTotalSum(orderItem.getBuyCounts());
        dtl.setProductName(orderItem.getProductName());
        dtl.setProductSn(orderItem.getProductSn());
        dtl.setSkuName(orderItem.getSkuName());
        dtl.setTexture(orderItem.getTexture());
        dtl.setUnit(orderItem.getUnit());
        /**
         * 查询商城退货数量
         */
        BigDecimal count = orderItem.getReturnCounts();
        BigDecimal pcwpReturns = orderItem.getPcwpReturn();
        //2 计划
        if (sourType == 2) {
            //可发货数量= 购买数量-（订单发货数量+商城退货数量）
            if (dtl.getShipCounts().compareTo(orderItem.getBuyCounts().subtract(orderItem.getShipCounts().add(count).subtract(pcwpReturns))) <= 0) {
                setOrderShipCounts(dtl, orderItem);
            } else {
                throw new BusinessException(500, "发货+商城退货数量大于购买数量，不能生成发货单");
            }
        } else {
            OrderShipmentsQtyIsOkDTO orderShipmentsQtyIsOkDTO = new OrderShipmentsQtyIsOkDTO();
            BigDecimal aboveQty = orderItem.getBuyCounts().add(orderItem.getBuyCounts().multiply(BigDecimal.valueOf(0.1)));
            if (dtl.getShipCounts().compareTo(aboveQty.subtract(orderItem.getShipCounts().add(count).subtract(pcwpReturns))) <= 0) {
                if (sourType == 1) {
                    setMonthOrderShipCounts(dtl, orderItem);
                } else if (sourType == 6) {
                    setOrderShipCounts(dtl, orderItem);
                }
                orderShipmentsQtyIsOkDTO.setOrderItemId(dtl.getOrderItemId());
                orderShipmentsQtyIsOkDTO.setQty(dtl.getShipCounts());
                list.add(orderShipmentsQtyIsOkDTO);
            } else {
                throw new BusinessException(500, "发货+商城退货数量大于超量（购买数量+10%），不能生成发货单");
            }
        }
//        /**
//         *
//         * 2，3  大宗临购和零星采购有金额
//         * 零星采购发货单项根据发货数量后确认发货数量生成 金额 和单价
//         * @param orderItem   订单项
//         * @param dtl    发货单
//         * @param count  发货数量后确认发货数量
//         */
//        orderShipDtlService.updateAmount(orderItem, dtl, dtl.getShipCounts());

    }


    //1  合同  没有金额
    //月供，不计算价格,默认价格为o
    private void setMonthOrderShipCounts(OrderShipDtl dtl, OrderItem main0rderItem) {
        BigDecimal count = main0rderItem.getShipCounts().add(dtl.getShipCounts());
        dtl.setOrderSn(main0rderItem.getOrderSn());
        dtl.setOrderItemId(main0rderItem.getOrderItemId());
        dtl.setOrderId(main0rderItem.getOrderId());
        dtl.setProductPrice(BigDecimal.ZERO);
        dtl.setTotalAmount(BigDecimal.ZERO);
        dtl.setNoRatePrice(BigDecimal.ZERO);
        dtl.setNoRateAmount(BigDecimal.ZERO);
        dtl.setNoRateAmount(BigDecimal.ZERO);
        //二级修改数量
        OrderItem son = orderItemService.getOrderItemByParentId(main0rderItem.getOrderItemId());
        if (son != null) {
            dtl.setOtherTotalAmount(BigDecimal.ZERO);
            dtl.setOtherNoRateAmount(BigDecimal.ZERO);
            dtl.setOtherNoRatePrice(BigDecimal.ZERO);
            dtl.setOtherProductPrice(BigDecimal.ZERO);
            son.setShipCounts(count);
            orderItemService.update(son);
        }


    }


    /**
     * orderItem 为主订单
     *
     * @param dtl
     * @param main0rderItem
     */
    private void setOrderShipCounts(OrderShipDtl dtl, OrderItem main0rderItem) {
        BigDecimal count = main0rderItem.getShipCounts().add(dtl.getShipCounts());
        dtl.setOrderSn(main0rderItem.getOrderSn());
        dtl.setOrderItemId(main0rderItem.getOrderItemId());
        dtl.setOrderId(main0rderItem.getOrderId());
        //计算发货单项的含税和不含税价格
        dtl.setProductPrice(main0rderItem.getProductPrice());
        dtl.setTotalAmount(main0rderItem.getProductPrice().multiply(count));
        dtl.setTaxRate(main0rderItem.getTaxRate());
        BigDecimal notRatePrice = TaxCalculator.calculateNotTarRateAmount(main0rderItem.getProductPrice(), main0rderItem.getTaxRate());
        BigDecimal notRateAmount = TaxCalculator.noTarRateItemAmount(dtl.getTotalAmount(),notRatePrice,count, main0rderItem.getTaxRate());
        dtl.setNoRatePrice(notRatePrice);
        dtl.setNoRateAmount(notRateAmount);

        //二级修改数量
        OrderItem son = orderItemService.getOrderItemByParentId(main0rderItem.getOrderItemId());
        if (son != null) {
            son.setShipCounts(count);
            dtl.setOtherTotalAmount(son.getProductPrice().multiply(count));
            dtl.setOtherProductPrice(son.getProductPrice());
            dtl.setOtherTaxRate(son.getTaxRate());
            BigDecimal otherNoRatePrice = TaxCalculator.calculateNotTarRateAmount(son.getProductPrice(), son.getTaxRate());
            BigDecimal otherNoRateAmount = TaxCalculator.noTarRateItemAmount(dtl.getOtherTotalAmount(),otherNoRatePrice,count, son.getTaxRate());
            dtl.setOtherNoRateAmount(otherNoRateAmount);
            dtl.setOtherNoRatePrice(otherNoRatePrice);
            orderItemService.update(son);
        }
    }

    //根据订单保存发货单信息
    private OrderShip saveShipInfo(OrderShipVo orderShip, Orders orders) {


        OrderSelectPlan plan = null;
        orderShip.setType(0);
        //设置发货单编号
        orderShip.setBillSn(OrderUtils.getOrder());
        //发货单需要使用主订单的订单id和编号 主订单关联的计划或合同
        if (orders.getOrderClass() == 1 || orders.getOrderClass() == 2) {
            plan = orderSelectPlanService.getDataByOrderSn(orders.getOrderSn());
            saveorderPlan(orderShip, orders, plan);
        } else {

            Orders parent = ordersService.getById(orders.getParentOrderId());
            plan = orderSelectPlanService.getDataByOrderSn(parent.getOrderSn());
            orderShip.setOtherOrderId(orders.getOrderId());
            orderShip.setOtherOrderSn(orders.getOrderSn());
            orders.setOrderClass(3);
            saveorderPlan(orderShip, parent, plan);
        }
        /**
         * 设置供应商信息
         */
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        orderShip.setShipUserName(user.getUserName());
        orderShip.setShipUserId(user.getUserId());
        orderShip.setShipEnterpriseId(user.getEnterpriseId());
        orderShip.setShipEnterpriseName(user.getEnterpriseName());
        orderShip.setSupplierOrgId(user.getOrgId());
        orderShip.setSupplierCode(user.getSocialCreditCode());


        /**
         * 设置采购员信息
         */
        orderShip.setEnterpriseId(orders.getEnterpriseId());
        orderShip.setEnterpriseName(orders.getEnterpriseName());
        orderShip.setReceivePhone(orders.getReceiverMobile());
        orderShip.setShipAddress(orders.getReceiverAddress());
        orderShip.setOrgId(orders.getOrgId());
        orderShip.setReceiveOrgName(orders.getEnterpriseName());


        orderShip.setFlishTime(orders.getFlishTime());
        orderShip.setOrderClass(orders.getOrderClass());
        OrderShip orderShip1 = new OrderShip();
        BeanUtils.copyProperties(orderShip, orderShip1);
        Integer count = ordersService.lambdaQuery().eq(Orders::getParentOrderId, orders.getOrderId()).count();
        if (count>0){
            orderShip1.setOrderClass(3);
        }
        save(orderShip1);
        return orderShip1;
    }

    private OrderShip outSaveShipInfo(OrderShipVo orderShip, Orders orders) {


        OrderSelectPlan plan = null;
        orderShip.setType(0);
        //设置发货单编号
        orderShip.setBillSn(OrderUtils.getOrder());
        //发货单需要使用主订单的订单id和编号 主订单关联的计划或合同
        if (orders.getOrderClass() == 1 || orders.getOrderClass() == 2) {
            plan = orderSelectPlanService.getDataByOrderSn(orders.getOrderSn());
            saveorderPlan(orderShip, orders, plan);
        } else {

            Orders parent = ordersService.getById(orders.getParentOrderId());
            plan = orderSelectPlanService.getDataByOrderSn(parent.getOrderSn());
            orderShip.setOtherOrderId(orders.getOrderId());
            orderShip.setOtherOrderSn(orders.getOrderSn());
            orders.setOrderClass(3);
            saveorderPlan(orderShip, parent, plan);
        }
        /**
         * 设置采购员信息
         */
        orderShip.setEnterpriseId(orders.getEnterpriseId());
        orderShip.setEnterpriseName(orders.getEnterpriseName());
        orderShip.setReceivePhone(orders.getReceiverMobile());
        orderShip.setShipAddress(orders.getReceiverAddress());
        orderShip.setOrgId(orders.getOrgId());
        orderShip.setReceiveOrgName(orders.getEnterpriseName());


        orderShip.setFlishTime(orders.getFlishTime());
        orderShip.setOrderClass(orders.getOrderClass());
        OrderShip orderShip1 = new OrderShip();
        BeanUtils.copyProperties(orderShip, orderShip1);
        Integer count = ordersService.lambdaQuery().eq(Orders::getParentOrderId, orders.getOrderId()).count();
        if (count>0){
            orderShip1.setOrderClass(3);
        }
        save(orderShip1);
        return orderShip1;
    }

    private void saveorderPlan(OrderShipVo orderShip, Orders orders, OrderSelectPlan plan) {
        orderShip.setOrderSn(orders.getOrderSn());
        orderShip.setOrderId(orders.getOrderId());
        orderShip.setBillType(orders.getBillType());
        orderShip.setSupplierName(orders.getSupplierName());
        orderShip.setSupplierId(orders.getSupplierId());
        orderShip.setShopName(orders.getShopName());
        orderShip.setShopId(orders.getShopId());
        /**
         * 零星采购 计划编号 类型 2
         * 大宗材料 合同编号 类型2
         */
        if (plan != null) {
            switch (orders.getProductType()) {
                case 10:
                    orderShip.setSourceId(plan.getBillId());
                    orderShip.setSourceNo(plan.getBillNo());
                    orderShip.setProductType(10);
                    orderShip.setSourceType(2);
                    break;
                case 12:
                    orderShip.setSourceId(plan.getContractId());
                    orderShip.setSourceNo(plan.getContractNo());
                    orderShip.setProductType(12);
                    orderShip.setSourceType(1);
                    break;
                case 13:
                    orderShip.setSourceId(plan.getBillId());
                    orderShip.setSourceNo(plan.getBillNo());
                    orderShip.setProductType(13);
                    orderShip.setSourceType(6);
                    break;
                default:
                    break;
            }
        } else {
            throw new BusinessException(500, "订单没有关联计划");
        }
    }

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmShip(SubmitOrderShipVo vo, String idStr, StringBuilder stringBuilder) {
        String clientId =UUID.randomUUID().toString();
        Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(MaterialLockUtils.SHIPBillNO_LOCK+vo.getBillSn(), clientId, 3, TimeUnit.MINUTES);
        if(b){
            OrderShip ship = getById(vo.getBillId());

            if (ship.getType() == 2) {
                throw new BusinessException(1007, "该发货单已经收货，不能重复收货");
            }
            //2024-6-18 pcwp反馈发货单有重复提交数据问题，使用redis加锁缓存解决，时间设置为20秒过期
            String s = stringRedisTemplate.opsForValue().get(ship.getBillSn());
            if (s != null) {
                throw new BusinessException(1007, "该发货单已经收货，不能重复收货");
            }else {
                //2024-6-18 pcwp反馈发货单有重复提交数据问题，使用redis加锁缓存解决，时间设置为2
                stringRedisTemplate.opsForValue().set(ship.getBillSn(), ship.getBillSn(),20, TimeUnit.SECONDS);
            }
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            ship.setReceiveId(user.getFarUserId());
            ship.setReceiveName(user.getUserName());
            ship.setReceiveOrgName(user.getEnterpriseName());
            ship.setReceiveOrgId(user.getOrgId());
            ship.setConfirmTime(new Date());
            ship.setType(2);
            update(ship);
            BigDecimal totalAmounts = BigDecimal.valueOf(0);
            BigDecimal noRateAmounts = BigDecimal.valueOf(0);
            BigDecimal otherRateAmounts = BigDecimal.valueOf(0);
            BigDecimal otherNoRateAmounts = BigDecimal.valueOf(0);
            //查询发货单的所有发货项
            List<SubmitOrderShipDtl> dtls = vo.getDtls();
            if (dtls == null || dtls.size() <= 0) {
                throw new BusinessException(500, "发货单项不能为空");
            }
            if (dtls != null && dtls.size() > 0) {
                ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
                int dtlsCount = 0;
                for (SubmitOrderShipDtl dtl : dtls) {

                    if (dtl.getShipNum().compareTo(BigDecimal.valueOf(0)) <= 0) {
                        throw new BusinessException(500, "发货单项" + dtl.getDtlId() + "确认收货数量不能为0");
                    }
                    if (dtl != null) {
                        OrderShipDtl byId = orderShipDtlService.getById(dtl.getDtlId());
                        byId.setShipNum(dtl.getShipNum());
                        if (dtl.getShipNum().compareTo(byId.getShipCounts()) > 0) {
                            throw new BusinessException(500, "发货单" + byId.getDtlId() + "确认收货数量不能大于发货数量");
                        }
                        OrderItem orderItem = orderItemService.getById(byId.getOrderItemId());
                        if (orderItem == null) {
                            throw new BusinessException(500, "订单项不存在，请联系管理员");
                        } else {
                            orderItem.setConfirmCounts(orderItem.getConfirmCounts().add(dtl.getShipNum()));
                            if (StringUtils.isNotBlank(orderItem.getParentOrderItemId())) {
                                OrderItem parentOrderItem = orderItemService.getById(orderItem.getParentOrderItemId());
                                parentOrderItem.setConfirmCounts(orderItem.getConfirmCounts());
                                orderItemService.update(parentOrderItem);
                            } else {
                                OrderItem son = orderItemService.getOrderItemByParentId(orderItem.getOrderItemId());
                                if (son != null) {
                                    son.setConfirmCounts(orderItem.getConfirmCounts());
                                    orderItemService.update(son);
                                }
                            }
                            orderItemService.update(orderItem);
                        }
                        if (dtl.getShipNum().compareTo(byId.getShipCounts()) == 0) {
                            dtlsCount++;
                            byId.setIsEqual(0);
                        } else {
                            dtlsCount++;
                            byId.setIsEqual(1);
                        }
                        byId.setShipNum(dtl.getShipNum());
                        if (ship.getSourceType() != 1) {
                            //发货单项价格
                            byId.setTotalAmount(dtl.getShipNum().multiply(byId.getProductPrice()));
                            byId.setOtherTotalAmount(dtl.getShipNum().multiply(byId.getOtherProductPrice()));
                            BigDecimal noProductPrice = TaxCalculator.calculateNotTarRateAmount(byId.getProductPrice(), byId.getTaxRate());

                            BigDecimal noOtherProductPrice = TaxCalculator.calculateNotTarRateAmount(byId.getOtherProductPrice(), byId.getTaxRate());


                            BigDecimal notTarRateAmount = TaxCalculator.noTarRateItemAmount(byId.getTotalAmount(),noProductPrice,dtl.getShipNum(), byId.getTaxRate());
                            BigDecimal notOtherTarRateAmount = TaxCalculator.noTarRateItemAmount(byId.getOtherTotalAmount(),noOtherProductPrice,dtl.getShipNum(), byId.getTaxRate());

                            byId.setNoRateAmount(notTarRateAmount);
                            byId.setOtherNoRateAmount(notOtherTarRateAmount);
                            //发货单价格
                            totalAmounts = totalAmounts.add(byId.getTotalAmount());
                            noRateAmounts = noRateAmounts.add(notTarRateAmount);
                            otherRateAmounts = otherRateAmounts.add(byId.getOtherTotalAmount());
                            otherNoRateAmounts = otherNoRateAmounts.add(notOtherTarRateAmount);
                        }
                        shipDtls.add(byId);
                    } else {
                        throw new BusinessException(500, "发货单项" + dtl.getDtlId() + "不存在");
                    }
                }
                if (dtlsCount == 0) {
                    throw new BusinessException(500, "收料单确认收货不能为空，请输入确认收货数量");
                }
                ;

                orderShipDtlService.updateBatchById(shipDtls);
                ship.setDtls(shipDtls);
            }
            ship.setRateAmount(totalAmounts);
            ship.setOtherRateAmount(otherRateAmounts);
            ship.setOtherNoRateAmount(otherNoRateAmounts);
            ship.setNoRateAmount(noRateAmounts);
            //修改价格
            update(ship);
            Boolean r11Bool = null;
            //  判断是否能推送验收单
            R r11 = null;
            try {
                String urlx = mallConfig.prodPcwp2Url02
                        + PCWP2ApiUtil.IS_CON_OPERA_BILL_URL + "?orgId=" + ship.getOrgId() + "&date=" + DateUtil.getymd(LocalDate.now());
                log.warn("判断是否可推送验收请求参数：" + urlx);
                r11 = restTemplateUtils.getPCWP2NotParams(urlx);

            } catch (Exception e) {
                throw new BusinessException("【远程异常】判断是否可推送收料单：" + e.getMessage());
            }
            if (r11.getCode() == null || r11.getCode() != 200) {
                // 返回不是200异常
                throw new BusinessException("【远程异常】判断是否可推送收料单：" + r11.getMessage());
            } else {
                r11Bool = (Boolean) r11.getData();
            }
            log.warn("判断是否可推送验收返回：" + r11);
            if (r11Bool == null || r11Bool == false) {
                ship.setIsNotPush(1);
                ship.setIdStr(idStr);
                update(ship);
                throw new BusinessException("pcwp月结中，请月结结束后在重新收货，参数orgid：" + ship.getOrgId() + "日期：" + new Date().toString());
            } else {
                update(ship);
            }
            outSupplierRecevieService.confirmShipUrl2(ship, idStr, stringBuilder);
            ordersService.closeOrder2(ship.getOrderId());
        }else {
            throw new BusinessException(1007, "请退回上一步，避免重复推送收料单");
        }



    }


    @Override
    public PageUtils selectShipList(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();
        q.eq(OrderShip::getEnterpriseId, enterpriseId);
        q.orderByDesc(OrderShip::getShipData);
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String type = (String) innerMap.get("type");
        String orderSn = (String) innerMap.get("orderSn");
        String billSn = (String) innerMap.get("billSn");
        String supplierName = (String) innerMap.get("supplierName");
//        if (supplierName != null && supplierName != "") {
//            q.eq(OrderShip::getSupplierName, supplierName);
//        }
        if (StringUtils.isNotBlank(supplierName)) {
            q.eq(OrderShip::getSupplierName, supplierName);
        }

        if (StringUtils.isNotBlank(orderSn)) {
            q.eq(OrderShip::getOrderSn, orderSn);
        }
        if (StringUtils.isNotBlank(billSn)) {
            q.eq(OrderShip::getBillSn, billSn);
        }


        if (type != null) {
            q.eq(OrderShip::getType, type);
        }
        q.orderByAsc(OrderShip::getShipData);
        IPage<OrderShip> page = this.page(
                new Query<OrderShip>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShipType(OrderShip orderShip) {
        String deliveryFlowId = orderShip.getDeliveryFlowId();
        if (deliveryFlowId == null || deliveryFlowId == "") {
            throw new BusinessException(500, "该用户没有填写发货单号");
        }
        String company = orderShip.getLogisticsCompany();
        if (company == null || company == "") {
            throw new BusinessException(1002, "该用户没有填写物流公司");
        }
        OrderShip info = getById(orderShip.getBillId());
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (!info.getShipEnterpriseId().equals(user.getEnterpriseId())) {
            throw new BusinessException(500, "该用户没有发货权限");
        }
        if (info != null) {
            if (info.getType() == 0) {
                info.setDeliveryFlowId(orderShip.getDeliveryFlowId());
                info.setLogisticsCompany(orderShip.getLogisticsCompany());
                info.setSupplierCode(user.getSocialCreditCode());
                info.setType(1);
                info.setShipData(new Date());
                info.setShipUserId(user.getUserId());
                update(info);
                /***
                 *  查看订单项是否发货完,修改订单状态
                 */
                orderItemService.updateOrderStateAndOrderItemByShip(info);

            } else {
                throw new BusinessException(302, "该发货单已经发货");
            }

        } else {
            throw new BusinessException(302, "该发货单不存在");
        }
    }


    /**
     * 供应商查询发货单
     *
     * @param outBillId
     * @return
     */
    @Override
    public OrderShip getDataByOutBillId(String outBillId) {
        LambdaQueryWrapper<OrderShip> q = new LambdaQueryWrapper<>();
        q.eq(OrderShip::getOutBillId, outBillId);
        OrderShip one = getOne(q);
        return one;
    }

//    /**
//     * 供应商查询发货单
//     *
//     * @param orderId
//     * @return
//     */

//    public void isFilshOrder(String orderId) {
//        Orders orders = orderItemService.isFilshAllOrderItem(orderId);
//        if (orders.getState() == 10) {
//            List<OrderShip> list = getListByOrderId(orderId);
//            for (OrderShip orderShip : list) {
//                if (!orderShip.getType().equals(2)) {
//                    orders.setState(9);
//                }
//            }
//        }
//        ordersService.update(orders);
//    }

    @Override
    public List<OrderShip> getListByOrderId(String ordersId) {
        LambdaQueryWrapper<OrderShip> q = new LambdaQueryWrapper<OrderShip>();
        q.eq(OrderShip::getOrderId, ordersId);
        List<OrderShip> list = list(q);
        return list;
    }

    @Override
    public List<OrderShip> getListByOrderIdAndType(String orderId) {
        return null;
    }


//    private Orders updateOrder(OrderShipVo orderShipVo, Orders orders) {
//        //设置订单状态
//
//        if (orderShipVo.getDeliveryFlowId() != null && orderShipVo.getDeliveryFlowId() != "") {
//            if (orders.getState() < 8) {
//                orderShipVo.setShipData(new Date());
//                orders.setState(8);
//                orders.setDeliveryTime(new Date());
//                ordersService.update(orders);
//            }
//            if (orders.getOrderClass() == 2 && orders.getParentOrderId() != null) {
//                Orders mainOrder = ordersService.getById(orders.getParentOrderId());
//                if (mainOrder.getState() < 8) {
//                    mainOrder.setState(8);
//                    mainOrder.setDeliveryTime(new Date());
//                    ordersService.update(mainOrder);
//                }
//
//            }
//
//        }
//
//        //如果为二级订单，必须更改主订单的信息
//
//        return orders;
//    }


    @Override
    public void returnGoods(ReturnGoodsVo returnGoodsVo) {
//        String billId = returnGoodsVo.getBillId();
////        OrderShipDtl dtl = orderShipDtlService.getDtlListByShipIdAndProductId(one.getBillId();
//        OrderShipDtl  dtl= orderShipDtlService.getById(returnGoodsVo.getDtlId());
//        if (dtl.getReturnCounts() == new BigDecimal(0)) {
//            dtl.setReturnCounts(returnGoodsVo.getReturnCounts());
//        } else {
//            dtl.setReturnCounts(returnGoodsVo.getReturnCounts().add(dtl.getReturnCounts()));
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFiles(ShipFiles shipFiles) {

        if (shipFiles.getRelevanceId() != null) {

            OrderShip byId = super.getById(shipFiles.getRelevanceId());
            fileService.deleteBatchFileByRelevanceIdAndType(byId.getBillId(), shipFiles.getRelevanceType());
            List<File> files = shipFiles.getFiles();
            if (files != null && files.size() > 0) {
                for (File file : files) {
                    if (file.getRelevanceId() != null) {
                        if (byId != null) {
                            file.setRelevanceType(10);
                            fileService.save(file);
                        } else {
                            throw new BusinessException(500, "发货单不存在");
                        }
                    } else {
                        throw new BusinessException(500, "发货单id和发货单类型不存在");
                    }
                }
            }
        } else {
            throw new BusinessException(500, "发货单不能为空");
        }
    }

    @Override
    public void exportDataByBillId(String billId, HttpServletResponse response) {

        OrderShipInfoVo orderShipInfoVo = baseMapper.exportDataByBillId(billId);
        List<OrderShipDtl> listByIds = orderShipDtlService.getDataByOrderShipId(billId);
        orderShipInfoVo.setDtls(listByIds);
        try {
            String src = mallConfig.templateFormUrl;
            String text = mallConfig.miniProgram;
            Map<String, Object> dataMap = new HashMap<>();
            List<OrderShipDtl> dtls = orderShipInfoVo.getDtls();
            dataMap.put("shipData", DateUtil.getyyymmddHHmmss(orderShipInfoVo.getShipData()));
            dataMap.put("supplierName", orderShipInfoVo.getSupplierName());
            dataMap.put("receiveOrgName", orderShipInfoVo.getReceiveOrgName());
            dataMap.put("billSn", orderShipInfoVo.getBillSn());
            dataMap.put("remarks", orderShipInfoVo.getRemarks());
            dataMap.put("totalPrice", orderShipInfoVo.getRateAmount());
            if (!orderShipInfoVo.getSupplierId().equals(orderShipInfoVo.getShipEnterpriseId())) {
                dataMap.put("remarks", "送货单位： " + orderShipInfoVo.getShipEnterpriseName());
            } else {
                dataMap.put("remarks", "");
            }
            dataMap.put("dataList", dtls);
            /// 增加合计数量
            BigDecimal totalCounts = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

            if (dtls != null) {
                totalCounts = dtls.stream()
                        .map(item -> Optional.ofNullable(item.getShipCounts()).orElse(BigDecimal.ZERO))
                        .reduce(totalCounts, BigDecimal::add);
            }
            dataMap.put("totalCounts", totalCounts);
            ExcelForWebUtil.exportExcelQrCode(response, dataMap, "发货单模板.xlsx", src, "发货单.xlsx",text+billId);
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }


    }


    @Override
    public void exportDataTwoById(String id, HttpServletResponse response) {
        OrderShip orderShip = getById(id);
        String src = mallConfig.templateFormUrl;
        String url = mallConfig.miniProgram;
        Map<String, Object> dataMap = new HashMap<>();

        UserLogin user = ThreadLocalUtil.getCurrentUser();

        try {
            dataMap.put("shipData", DateUtil.getyyymmddHHmmss(orderShip.getShipData()));
            dataMap.put("billSn", orderShip.getBillSn());
            List<OrderShipDtl> dtls = new ArrayList<>();
            if (orderShip.getSupplierId().equals(user.getEnterpriseId())) {
                dtls = orderShipDtlService.getDataByOrderShipId(id);
                dataMap.put("supplierName", orderShip.getSupplierName());
                dataMap.put("receiveOrgName", orderShip.getReceiveOrgName());
                dataMap.put("totalPrice", orderShip.getRateAmount());
                dataMap.put("remarks", "送货单位： " + orderShip.getShipEnterpriseName());
            } else {
                dataMap.put("supplierName", orderShip.getShipEnterpriseName());
                dataMap.put("receiveOrgName", orderShip.getSupplierName());
                dataMap.put("totalPrice", orderShip.getOtherRateAmount());
                dataMap.put("remarks", "");
                dtls = orderShipDtlService.getTwoOrderShipByBillid(id);
            }
            dataMap.put("dataList", dtls);
            ExcelForWebUtil.exportExcelQrCode(response, dataMap, "发货单模板.xlsx", src, "发货单.xlsx",url+id);
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }

    }


    @Override
    public void exportDataPurchase(String id, HttpServletResponse response) {
        OrderShipInfoVo orderShipInfoVo = baseMapper.exportDataByBillId(id);
        if (orderShipInfoVo.getConfirmTime() == null) {
            throw new BusinessException(500, "数据错误，收货时间不能为空！");
        }
        try {
            String src = mallConfig.templateFormUrl;
            Map<String, Object> dataMap = new HashMap<>();
            List<OrderShipDtl> dtls = orderShipDtlService.lambdaQuery().eq(OrderShipDtl::getBillSn, orderShipInfoVo.getBillSn()).list();
            dataMap.put("confirmTime", DateUtil.getyyymmddHHmmss(orderShipInfoVo.getConfirmTime()));
            dataMap.put("supplierName", orderShipInfoVo.getSupplierName());
            dataMap.put("receiveOrgName", orderShipInfoVo.getReceiveOrgName());
            dataMap.put("orderSn", orderShipInfoVo.getOrderSn());
            dataMap.put("billSn", orderShipInfoVo.getBillSn());
            dataMap.put("remarks", orderShipInfoVo.getRemarks());
            dataMap.put("totalPrice", orderShipInfoVo.getRateAmount());
            dataMap.put("dataList", dtls);
            if (dtls == null) {

                throw new BusinessException(500, "物资入库单有误，明细不能为空");
            }
            ExcelForWebUtil.exportExcel(response, dataMap, "物资入库单模板.xlsx", src, "物资入库单.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPCWPOrderRetuen(ReturnGoodsVo dto) {
        //获取密钥key
        String idStr = dto.getKeyId();
        //本地修改 参数虎汇总
        HashMap<String, Object> vo = new HashMap<>();
        //退货数据集合
        ArrayList<String> orderReturnIdList = new ArrayList<>();
        //    本地修改订单项参数集合保存
        ArrayList<HashMap<String, Object>> orderItems = new ArrayList<>();

        // 大宗退货明细参数集合
        ArrayList<HashMap<String, Object>> planDtlList = new ArrayList<>();
        //本地修改发货单项参数集合
        ArrayList<OrderReturnShipDtlVo> dtlVos = new ArrayList<>();


        //获取订单信息
        Orders orders = ordersService.getById(dto.getOutOrderId());



        if (orders != null) {
            //物资Id收料单集合ids
            List<ReturnGoodItemVo> dtls = dto.getDtls();
            //创建退货单项集合
            ArrayList<OrderReturnItem> itemLists = new ArrayList<>();

            if (dtls != null && dtls.size() > 0) {
                //查询所有退货物资数  （不同物资的退货）
                for (ReturnGoodItemVo dtl : dtls) {
                    //针对单个物资经行查询
                    String outDtlIds = dtl.getOutDtlIds();
                    //针对订单进行合并，同一个订单的同多个发货单项，查询同一个订单项的所有发货单项数据
                    String[] split = outDtlIds.split(",");
                    BigDecimal returnCounts = dtl.getReturnCounts();

                    if (returnCounts.compareTo(BigDecimal.valueOf(0)) == 0) {
                        throw new BusinessException(500, "退货数量不能为空");
                    }
                    //查询退货的所有收料单集合
                    List<OrderShipDtl> list = orderShipDtlService.getListByIds(split);

                    if (list != null && list.size() > 0) {

                        //获取单个收料项单数据，获取订单项-获取收料单项创建退货单项数据
                        OrderShipDtl orderShipDtl = list.get(0);

                        //收集修改后订单项数据
                        HashMap<String, Object> itemMap = new HashMap<>();
                        itemMap.put("orderItemId", orderShipDtl.getOrderItemId());
                        itemMap.put("returnCounts", dtl.getReturnCounts());
                        OrderShip orderShip = getById(orderShipDtl.getBillId());
                        // 根据发货单（收料）得到的订单项
                        OrderItem item = orderItemService.getById(orderShipDtl.getOrderItemId());
                        //根据订单项数据生成退货单项数据
                        OrderReturnItem orderReturnItem = new OrderReturnItem();
                        orderReturnItem.setSkuName(item.getSkuName());
                        orderReturnItem.setSkuId(item.getSkuId());
                        orderReturnItem.setProductSn(item.getProductSn());
                        orderReturnItem.setProductImg(item.getProductImg());
                        orderReturnItem.setTexture(item.getTexture());
                        orderReturnItem.setUnit(item.getUnit());
                        orderReturnItem.setOrderSn(item.getOrderSn());
                        orderReturnItem.setBuyCounts(item.getBuyCounts());
                        orderReturnItem.setOrderId(item.getOrderId());
                        orderReturnItem.setOrderItemId(item.getOrderItemId());
                        orderReturnItem.setProductName(item.getProductName());
                        orderReturnItem.setProductId(item.getProductId());
                        orderReturnItem.setBrandId(item.getBrandId());
                        orderReturnItem.setBrandName(item.getBrandName());
                        orderReturnItem.setCount(dtl.getReturnCounts());
                        BigDecimal add = item.getPcwpReturn().add(dtl.getReturnCounts());
                        item.setPcwpReturn(add);
                        // 大宗无价格
                        if (item.getProductType() != 12) {
                            //修改订单项信息
                            orderItemService.createAmouns(item, orderReturnItem, dtl.getReturnCounts());
                        }
                        if (item.getParentOrderItemId() != null) {
                            OrderItem main0rderItem = orderItemService.getById(item.getParentOrderItemId());
                            main0rderItem.setPcwpReturn(item.getPcwpReturn());
                            // 如果是手动完结，记录最大发货数量
                            if (orders.getFinishType() == 1) {
                                main0rderItem.setCloseMaxQty(dtl.getReturnCounts());
                            }
                            orderItemService.update(main0rderItem);
                        } else {
                            OrderItem son = orderItemService.getOrderItemByParentId(item.getOrderItemId());
                            if (son != null) {
                                son.setPcwpReturn(item.getPcwpReturn());
                                // 如果是手动完结，记录最大发货数量
                                if (orders.getFinishType() == 1) {
                                    son.setCloseMaxQty(dtl.getReturnCounts());
                                }
                                orderItemService.update(son);
                            }
                        }

                        orderItemService.update(item);
                        if (orderShip.getSourceType() == 2) {
                            ProductSku sku = productSkuService.getById(item.getSkuId());
                            sku.setStock(sku.getStock().add(dtl.getReturnCounts()));
                            productSkuService.update(sku);
//                            if (sku != null) {
//                                HashMap<String, Object> skuMap = new HashMap<>();
//                                skuMap.put("skuId", sku.getSkuId());
//                                // 现场收料时候的退货数量（后面减库存用）
//                                skuMap.put("returnCounts", dtl.getReturnCounts());
//                                stockList.add(skuMap);
//                            }


                            //如果是二级订单，获取二级订单信息，更改二级订单
                            if (orders.getOrderClass() != 1) {
                                OrderItem son = orderItemService.getOrderItemByParentId(item.getOrderItemId());
                                if (son != null) {
                                    HashMap<String, Object> sonItem = new HashMap<>();
                                    sonItem.put("orderItemId", son.getOrderItemId());
                                    sonItem.put("returnCounts", dtl.getReturnCounts());
                                    orderReturnItem.setOtherOrderItemId(son.getOrderItemId());
                                    orderReturnItem.setOrderSn(son.getOrderSn());
                                    orderReturnItem.setOrderId(son.getOrderId());
                                    orderItems.add(sonItem);
                                }
                            }

                        } else if (orderShip.getSourceType() == 6) {
                            ProductSku sku = productSkuService.getById(item.getSkuId());
                            sku.setStock(sku.getStock().add(dtl.getReturnCounts()));
                            productSkuService.update(sku);
//                            if (sku != null) {
//                                HashMap<String, Object> skuMap = new HashMap<>();
//                                skuMap.put("skuId", sku.getSkuId());
//                                skuMap.put("returnCounts", dtl.getReturnCounts());
//                                stockList.add(skuMap);
//                            }


                            //如果是二级订单，获取二级订单信息，更改二级订单
                            if (orders.getOrderClass() != 1) {
                                OrderItem son = orderItemService.getOrderItemByParentId(item.getOrderItemId());
                                if (son != null) {
                                    HashMap<String, Object> sonItem = new HashMap<>();
                                    sonItem.put("orderItemId", son.getOrderItemId());
                                    sonItem.put("returnCounts", dtl.getReturnCounts());
                                    orderReturnItem.setOtherOrderItemId(son.getOrderItemId());
                                    orderReturnItem.setOrderSn(son.getOrderSn());
                                    orderReturnItem.setOrderId(son.getOrderId());
                                    orderItems.add(sonItem);
                                }
                            }
                        }
                        // 处理大宗月供退货逻辑  // 供应商区分二级 拆分二级订单
                        else {
                            // 大宗月供计划存在二级供应商
                            if (orders.getOrderClass() != 1 && orderShip.getSourceType() == 1) {
                                // 大宗的二级订单 item是被拆分的订单项
                                OrderSelectPlan orderPlan = orderSelectPlanService.getDataByorderItemId(item.getParentOrderItemId());
                                MaterialMonthSupplyPlanDtl planDtl = materialMonthSupplyPlanDtlService.getById(orderPlan.getDtlId());
                                // 根据发货单获取发货数量
                                // 查询当前订单的计划明细
                                BigDecimal orderQty = planDtl.getOrderQty();
                                if (orderQty.compareTo(dtl.getReturnCounts()) < 0) {
                                    throw new BusinessException(500, "退货数量不能大于已生成订单明细数量" + orderQty + "");
                                } else {
                                    planDtl.setOrderQty(orderQty.subtract(dtl.getReturnCounts()));
                                }
                                HashMap<String, Object> hashMap = new HashMap<>();
                                hashMap.put("id", planDtl.getPlanDtlId());
                                hashMap.put("returnCounts", dtl.getReturnCounts());
                                planDtlList.add(hashMap);
                                // 修改计划中的下单数量
                                materialMonthSupplyPlanDtlService.update(planDtl);
                                // 构建子订单退货项
                                OrderItem secondOrderItem = orderItemService.getOrderItemByParentId(item.getParentOrderItemId());
                                if (secondOrderItem != null) {
                                    HashMap<String, Object> sonItem = new HashMap<>();
                                    sonItem.put("orderItemId", secondOrderItem.getOrderItemId());
                                    sonItem.put("returnCounts", dtl.getReturnCounts());
                                    orderReturnItem.setOtherOrderItemId(secondOrderItem.getOrderItemId());
                                    orderReturnItem.setOrderSn(secondOrderItem.getOrderSn());
                                    orderReturnItem.setOrderId(secondOrderItem.getOrderId());
                                    orderItems.add(sonItem);
                                }

                            } else {
                                // 当前选择的大宗月供计划
                                OrderSelectPlan orderPlan = orderSelectPlanService.getDataByorderItemId(item.getOrderItemId());
                                //大宗月供计划明细
                                MaterialMonthSupplyPlanDtl planDtl = materialMonthSupplyPlanDtlService.getById(orderPlan.getDtlId());
                                // （月供计划中）已经下单的数量
                                BigDecimal orderQty = planDtl.getOrderQty();
                                if (mallConfig.isCountPlanOrderNum == 1) {
                                    // TODO 计划最新统计
                                    BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(planDtl.getPlanDtlId());
                                    orderQty = qty;
                                }
                                if (orderQty.compareTo(dtl.getReturnCounts()) < 0) {
                                    throw new BusinessException(500, "退货数量不能大于已生成订单明细数量" + orderQty + "");
                                } else {
                                    planDtl.setOrderQty(orderQty.subtract(dtl.getReturnCounts()));
                                }
                                HashMap<String, Object> hashMap = new HashMap<>();
                                hashMap.put("id", planDtl.getPlanDtlId());
                                hashMap.put("returnCounts", dtl.getReturnCounts());
                                planDtlList.add(hashMap);
                                // 修改计划中的下单数量
                                materialMonthSupplyPlanDtlService.update(planDtl);
                            }
                        }
                        orderItems.add(itemMap);
                        itemLists.add(orderReturnItem);
                    } else {
                        throw new BusinessException(500, "收料单不存在，收料单发送错误");
                    }
                    // 处理发货单中构建退货单
                    for (OrderShipDtl shipDtl : list) {
                        OrderReturnShipDtlVo dtlVo = new OrderReturnShipDtlVo();
                        //如果发货数量==退货数量  跳过循环
                        if (shipDtl.getShipNum().compareTo(shipDtl.getReturnCounts()) == 0) {
                            continue;
                        } else {
                            //  退货总数量>(确认发货数量-已退货数量）
                            BigDecimal subtract = shipDtl.getShipNum().subtract(shipDtl.getReturnCounts()); // (还可以退货的数量)
                            // PCWP退货数量
                            // PCWP退货总数量 - （已确认收获数量 - PCWP已经退货数量） returnCounts > subtract
                            int i = returnCounts.subtract(subtract).compareTo(BigDecimal.valueOf(0));
                            // 是否还可以退货 i>0 全退
                            if (i > 0) {
                                //得到新的总退货数量
                                returnCounts = returnCounts.subtract(subtract);

                                //本次退货数量
                                BigDecimal nowReturnCount = shipDtl.getShipNum().subtract(shipDtl.getReturnCounts());
                                //这个退货数量是 ==》记录发货单中
                                shipDtl.setReturnCounts(shipDtl.getShipNum());


                                //保存发货单项编号和本次退货数量，回调时修改发货单项数据
                                dtlVo.setDtlId(shipDtl.getDtlId());
                                dtlVo.setReturnCounts(nowReturnCount);
                                dtlVos.add(dtlVo);
                            } else {

                                //本次退货数量
                                BigDecimal nowReturnCount = returnCounts.subtract(shipDtl.getReturnCounts());
                                shipDtl.setReturnCounts(returnCounts.add(shipDtl.getReturnCounts()));

                                dtlVo.setReturnCounts(shipDtl.getReturnCounts());

                                //保存发货单项编号和本次退货数量，回调时修改发货单项数据
                                dtlVo.setDtlId(shipDtl.getDtlId());
                                dtlVo.setReturnCounts(nowReturnCount);

                                dtlVos.add(dtlVo);
                                break;
                            }
                        }
                    }
                }

            } else {
                throw new BusinessException(500, "退货项不存在");
            }
            // itemLists 是构建的退货的明细项  orders 是该退货对应的订单
            Map<String, List<OrderReturnItem>> orderReturnList = itemLists.stream().collect(Collectors.groupingBy(OrderReturnItem::getOrderId));
            orderReturnList.forEach((orderId, orderReturnItems) -> {
                Orders son = ordersService.getById(orderId);
                if (son != null) {
                    if (son.getParentOrderId() != null) {
                        Orders parentOrder = ordersService.getById(son.getParentOrderId());
                        parentOrder.setState(8);
                        ordersService.update(parentOrder);
                    }
                    son.setState(8);
                    ordersService.update(son);
                }
                OrderReturn orderReturn = new OrderReturn();
                String orderReturnNo = UUID.randomUUID().toString().replace("-", "");
                orderReturn.setOrderReturnNo(orderReturnNo);
                orderReturn.setBillType(orders.getBillType());
                orderReturn.setIsOut(1);

                orderReturn.setEnterpriseId(orders.getEnterpriseId());
                orderReturn.setSupplierId(orders.getSupplierId());
                orderReturn.setSupplierName(orders.getSupplierName());
                orderReturn.setEnterpriseId(orders.getEnterpriseId());
                orderReturn.setEnterpriseName(orders.getEnterpriseName());
                orderReturn.setShipEnterpriseName(son.getSupplierName());
                orderReturn.setShipEnterpriseId(son.getSupplierId());
                orderReturn.setOrderId(orders.getOrderId());
                orderReturn.setOrderSn(orders.getOrderSn());
                if (orders.getOrderClass() == 2) {
                    orderReturn.setOtherOrderSn(son.getOrderSn());
                    orderReturn.setOtherOrderId(son.getOrderId());
                }
                if (orders.getProductType() == 10 || orders.getProductType() == 11) {
                    orderReturn.setSourceType(2);
                    orderReturn.setShopId(orders.getShopId());
                    orderReturn.setShopName(orders.getShopName());
                } else if (orders.getProductType() == 12) {
                    orderReturn.setSourceType(1);
                    if (orders.getOrderClass() == 2 || orders.getOrderClass() == 3) {
                        orderReturn.setOtherOrderSn(son.getOrderSn());
                        orderReturn.setOrderClass(2);
                    }
                } else if (orders.getProductType() == 13) {
                    orderReturn.setSourceType(6);
                    orderReturn.setOrderClass(2);
                }
                orderReturn.setFlishTime(new Date());
//               orderReturn.setFounderId(dto.getFounderId());
                orderReturn.setFounderName(dto.getFounderName());
                orderReturn.setRemarks(dto.getRemark());
                orderReturn.setGmtCreate(new Date());
                orderReturn.setState(3);
                orderReturnService.save(orderReturn);

                BigDecimal otherNoRateAmount = BigDecimal.valueOf(0);
                BigDecimal otherRateAmount = BigDecimal.valueOf(0);
                BigDecimal noRateAmount = BigDecimal.valueOf(0);
                BigDecimal rateAmount = BigDecimal.valueOf(0);
                StringBuilder untitled = new StringBuilder();
                for (OrderReturnItem orderReturnItem : orderReturnItems) {
                    // 退货子明细关联退货主数据
                    orderReturnItem.setIsOut(1);
                    orderReturnItem.setOrderReturnId(orderReturn.getOrderReturnId());
                    orderReturnItem.setOrderReturnNo(orderReturn.getOrderReturnNo());
                    untitled.append(orderReturnItem.getProductName()).append(",");
                    // 大宗合同无价格和金额，其他类型都需要获取金额并更新退货总金额等
                    if (orders.getProductType() != 12) {
                        if (orders.getOrderClass() != 1) {
                            otherNoRateAmount = otherNoRateAmount.add(orderReturnItem.getOtherNoRateAmount());
                            otherRateAmount = otherRateAmount.add(orderReturnItem.getOtherRateAmount());
                        }
                        rateAmount = rateAmount.add(orderReturnItem.getTotalAmount());
                        noRateAmount = noRateAmount.add(orderReturnItem.getNoRateAmount());
                    }
                }
                orderReturn.setUntitled(untitled.toString());
                orderReturn.setRateAmount(rateAmount);
                orderReturn.setTotalAmount(rateAmount);
                orderReturn.setNoRateAmount(noRateAmount);
                if (orders.getProductType() != 12) {
                    orderReturn.setShopName(orders.getShopName());
                    orderReturn.setOtherNoRateAmount(otherNoRateAmount);
                    orderReturn.setOtherRateAmount(otherRateAmount);
                }
                orderReturnService.updateById(orderReturn);
                //保存退货编号，回调时删除数据
                orderReturnIdList.add(orderReturn.getOrderReturnId());

            });

            orderReturnItemService.saveBatch(itemLists);
            vo.put("shipDtl", dtlVos);
            vo.put("orderReturnId", orderReturnIdList);
            vo.put("orderItemList", orderItems);
            vo.put("planDtl", planDtlList);

            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(OutOrderShipServiceImpl.class.getName());
            iLog.setMethodName("createPCWPOrderRetuen");
            iLog.setLocalArguments(JSON.toJSONString(vo));
            iLog.setFarArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(1);
            iLog.setLogType(3);
            interfaceLogsService.create(iLog);
            LogUtil.writeInfoLog(idStr, "createPCWPOrderRetuen", vo, dto, null, OutOrderShipServiceImpl.class);

            ordersService.closeOrder2(orders.getOrderId());
        } else {
            throw new BusinessException(500, "订单不存在");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollBackReconciliationCreate(String keyId) {
        StringBuilder stringBuilder = new StringBuilder();
        InterfaceLogs interfaceLogs = interfaceLogsService.lambdaQuery().eq(InterfaceLogs::getSecretKey, keyId)
                .eq(InterfaceLogs::getIsSuccess, 1)
                .eq(InterfaceLogs::getLogType, 3)
                .select()
                .one();
        if (interfaceLogs != null) {
            String localArguments = interfaceLogs.getLocalArguments();
            HashMap vo = JSON.parseObject(localArguments, HashMap.class);


            //修该发货单数据
            if (vo.get("shipDtl") != null) {
                ArrayList<OrderReturnShipDtlVo> shipDtl = (ArrayList<OrderReturnShipDtlVo>) vo.get("shipDtl");
                if (shipDtl != null && shipDtl.size() > 0) {
                    ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
                    for (OrderReturnShipDtlVo orderReturnShipDtlVo : shipDtl) {
                        OrderShipDtl byId = orderShipDtlService.getById(orderReturnShipDtlVo.getDtlId());
                        byId.setReturnCounts(orderReturnShipDtlVo.getReturnCounts());
                        shipDtls.add(byId);
                    }
//                    orderShipDtlService.updateBatchById(shipDtls);
                }
            }

            //删除退货数据
            if (vo.get("orderReturnId") != null) {
                ArrayList<String> orderReturnId = (ArrayList<String>) vo.get("orderReturnId");
                if (orderReturnId != null && orderReturnId.size() > 0) {
                    for (String s : orderReturnId) {
                        orderReturnService.delete(s);
                        orderReturnItemService.deleteByOrderReturnId(s);
                    }

                }
            }
            if (vo.get("orderItemList") != null) {
                ArrayList<HashMap<String, Object>> itemLists = (ArrayList) vo.get("orderItemList");
                if (itemLists != null && itemLists.size() > 0) {
                    ArrayList<OrderItem> items = new ArrayList<>();
                    for (HashMap<String, Object> orderItem : itemLists) {
                        OrderItem byId = orderItemService.getById(orderItem.get("orderItemId").toString());
                        byId.setReturnCounts(byId.getReturnCounts().subtract((BigDecimal) orderItem.get("returnCounts")));
                        items.add(byId);
                    }
//                    orderItemService.updateBatchById(items);
                }
            }


            //修改库存数据
            if (vo.get("skuList") != null) {
                ArrayList<HashMap<String, Object>> list = (ArrayList) vo.get("orderItemList");
                if (list != null && list.size() > 0) {
                    ArrayList<ProductSku> skuList = new ArrayList<>();
                    for (HashMap<String, Object> sku : list) {
                        ProductSku byId = productSkuService.getById(sku.get("skuId").toString());
                        byId.setStock(byId.getStock().add((BigDecimal) sku.get("returnCounts")));
                        skuList.add(byId);
                    }
                    productSkuService.updateBatchById(skuList);
                }
            }
            //修改大宗集合数据
            if (vo.get("planDtl") != null) {
                ArrayList<HashMap<String, Object>> list = (ArrayList) vo.get("orderItemList");
                if (list != null && list.size() > 0) {
                    ArrayList<MaterialMonthSupplyPlanDtl> planDtls = new ArrayList<>();
                    for (HashMap<String, Object> plan : list) {
                        MaterialMonthSupplyPlanDtl byId = materialMonthSupplyPlanDtlService.getById(plan.get("id").toString());
                        BigDecimal orderQty = byId.getOrderQty();
                        if (mallConfig.isCountPlanOrderNum == 1) {
                            // TODO 计划最新统计
                            BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(byId.getPlanDtlId());
                            orderQty = qty;
                        }
                        byId.setOrderQty(orderQty.add((BigDecimal) plan.get("returnCounts")));
                        planDtls.add(byId);
                    }
                    materialMonthSupplyPlanDtlService.updateBatchById(planDtls);
                }
            }

        }
    }

    @Override
    public void pushOrderShipData() {
//        StringBuilder stringBuilder = new StringBuilder();
//        List<OrderShip> list = lambdaQuery().eq(OrderShip::getType, 2)
//                .eq(OrderShip::getIsNotPush, 1).list();
//        for (OrderShip orderShip : list) {
//            Boolean r11Bool = null;
//            R r11 = null;
//            try {
//                r11 = restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02
//                        + PCWP2ApiUtil.IS_CON_OPERA_BILL_URL + "?orgId=" + orderShip.getOrderId() + "&date=" + DateUtil.getymd(LocalDate.now()));
//            } catch (Exception e) {
//                throw new BusinessException("【远程异常】判断是否可推送验收单：" + e.getMessage());
//            }
//            if (r11.getCode() == null || r11.getCode() != 200) {
//                // 返回不是200异常
//                throw new BusinessException("【远程异常】判断是否可推送验收单：" + r11.getMessage());
//            } else {
//                r11Bool = (Boolean) r11.getData();
//                if (r11Bool != null && r11Bool == true) {
//                    outSupplierRecevieService.confirmShipUrl2(orderShip, orderShip.getIdStr(), stringBuilder);
//                    orderShip.setIsNotPush(0);
//                    update(orderShip);
//                }
//            }
//        }

    }


    @Override
    public void wxExportDataPurchase(String id, HttpServletResponse response) {
        OrderShipInfoVo orderShipInfoVo = baseMapper.exportDataByBillId(id);

        try {
            String src = mallConfig.templateFormUrl;
            Map<String, Object> dataMap = new HashMap<>();
            List<OrderShipDtl> dtls = orderShipInfoVo.getDtls();
            dataMap.put("shipData", DateUtil.getyyymmddHHmmss(orderShipInfoVo.getShipData()));
            dataMap.put("supplierName", orderShipInfoVo.getSupplierName());
            dataMap.put("receiveOrgName", orderShipInfoVo.getReceiveOrgName());
            dataMap.put("billSn", orderShipInfoVo.getBillSn());
            dataMap.put("orderSn", orderShipInfoVo.getOrderSn());
            dataMap.put("remarks", orderShipInfoVo.getRemarks());
            dataMap.put("totalPrice", orderShipInfoVo.getRateAmount());
            if (!orderShipInfoVo.getSupplierId().equals(orderShipInfoVo.getShipEnterpriseId())) {
                dataMap.put("remarks", "送货单位： " + orderShipInfoVo.getShipEnterpriseName());
            }
            for (OrderShipDtl dtl : dtls) {
                OrderItem byId = orderItemService.getById(dtl.getOrderItemId());
                if (byId != null) {
                    ProductCategory className = productCategoryService.getById(byId.getClassId());
                    if (className != null) {
                        dtl.setProductCategoryName(className.getClassName());
                    }
                }
            }
            dataMap.put("dataList", dtls);

            ExcelForWebUtil.exportExcel(response, dataMap, "微信物资入库单模板.xlsx", src, "入库单模板.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }

    }


    @Transactional(rollbackFor = Exception.class)
    public void createMaterialReconciliation2(ReturnGoodsVo dto) {
        //退货数据集合
        ArrayList<String> orderReturnIdList = new ArrayList<>();
        //本地修改发货单项参数集合
        ArrayList<OrderReturnShipDtlVo> dtlVos = new ArrayList<>();

        List<ReturnGoodItemVo> dtls = dto.getDtls();
        Orders orders = ordersService.getById(dto.getOutOrderId());
        OrderReturn orderReturn = new OrderReturn();
        orderReturn.setIsOut(1);
        orderReturn.setState(3);
        orderReturnService.createDataByOrders(orderReturn, orders);
        if (orders.getOrderClass() == 1) {
            for (ReturnGoodItemVo dtl : dtls) {
                String[] split = dtl.getOutDtlIds().split(",");
                BigDecimal returnCounts = dtl.getReturnCounts();
                //查询退货的所有收料单集合
                List<OrderShipDtl> list = orderShipDtlService.getListByIds(split);
                OrderShipDtl orderShipDtl = list.get(0);
                String orderItemId = orderShipDtl.getOrderItemId();

                /**
                 * 订单项确认收货数量
                 */
                BigDecimal shipNum = orderShipDtlService.getCountByOrderItemIdAndType(orderItemId, 2);
                /**
                 * pcwp退货数量
                 */
                BigDecimal pcwpCount = orderReturnItemService.getDataByOrderItmId(orderItemId, 1);
                /**
                 * 可退货数量=已收货数量-pcwp退货数量-现在退货数量
                 */
                if (shipNum.compareTo(pcwpCount.add(returnCounts)) < 0) {
                    throw new BusinessException(500, "退货数量不能大于收货数量");
                }
                OrderItem byId = orderItemService.getById(orderItemId);

            }
        } else {
            for (ReturnGoodItemVo dtl : dtls) {
                String[] split = dtl.getOutDtlIds().split(",");

                //查询退货的所有收料单集合
                List<OrderShipDtl> list = orderShipDtlService.getListByIds(split);
                OrderShipDtl orderShipDtl = list.get(0);
                String orderItemId = orderShipDtl.getOrderItemId();
                OrderItem byId = orderItemService.getById(orderItemId);
                /**
                 * 订单项确认收货数量
                 */
                BigDecimal shipNum = orderShipDtlService.getCountByOrderItemIdAndType(orderItemId, 2);
                /**
                 * pcwp退货数量
                 */
                BigDecimal pcwpCount = orderReturnItemService.getDataByOrderItmId(orderItemId, 1);

                BigDecimal returnCounts = dtl.getReturnCounts();
                /**
                 * 可退货数量=已收货数量-pcwp退货数量-现在退货数量
                 */
                if (shipNum.compareTo(pcwpCount.add(returnCounts)) < 0) {
                    throw new BusinessException(500, "退货数量不能大于收货数量");
                }
                OrderReturnItem orderReturnItem = new OrderReturnItem();
                orderReturnItem.setIsOut(1);
                orderReturnItem.setCount(returnCounts);
                orderReturnItemService.setOrderReturn(byId, orderReturnItem, orders.getTaxRate());
                OrderItem sonOrder = orderItemService.getOrderItemByParentId(byId.getOrderItemId());
                if (sonOrder != null) {
                    orderReturnItemService.setOrderReturn(sonOrder, orderReturnItem, orders.getTaxRate());
                    sonOrder.setReturnCounts(byId.getReturnCounts().add(orderReturnItem.getCount()));
                    orderItemService.update(sonOrder);
                }
                for (OrderShipDtl shipDtl : list) {
                    OrderReturnShipDtlVo dtlVo = new OrderReturnShipDtlVo();
                    if (shipDtl.getShipNum().compareTo(shipDtl.getReturnCounts()) == 0) {
                        continue;
                    } else {
                        //  退货总数量>(确认发货数量-已退货数量）
                        BigDecimal subtract = shipDtl.getShipNum().subtract(shipDtl.getReturnCounts());
                        int i = returnCounts.subtract(subtract).compareTo(BigDecimal.valueOf(0));
                        if (i > 0) {
                            //得到新的总退货数量
                            returnCounts = returnCounts.subtract(subtract);

                            //本次退货数量
                            BigDecimal nowReturnCount = shipDtl.getShipNum().subtract(shipDtl.getReturnCounts());
                            shipDtl.setReturnCounts(shipDtl.getShipNum());


                            //保存发货单项编号和本次退货数量，回调时修改发货单项数据
                            dtlVo.setDtlId(shipDtl.getDtlId());
                            dtlVo.setReturnCounts(nowReturnCount);
                            dtlVos.add(dtlVo);
                        } else {

                            //本次退货数量
                            BigDecimal nowReturnCount = returnCounts.subtract(shipDtl.getReturnCounts());
                            shipDtl.setReturnCounts(returnCounts.add(shipDtl.getReturnCounts()));

                            dtlVo.setReturnCounts(shipDtl.getReturnCounts());
                            //保存发货单项编号和本次退货数量，回调时修改发货单项数据
                            dtlVo.setDtlId(shipDtl.getDtlId());
                            dtlVo.setReturnCounts(nowReturnCount);

                            dtlVos.add(dtlVo);
                            break;
                        }
                    }


                }


            }
        }


    }


    public List<OrderShipDtl> getDataByOrderIdAndState(String orderId, Integer type) {
        ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
        List<OrderShip> list = lambdaQuery().eq(OrderShip::getOrderId, orderId).eq(OrderShip::getType, type).list();
        for (OrderShip orderShip : list) {
            LambdaQueryWrapper<OrderShipDtl> q = new LambdaQueryWrapper<>();
            q.eq(OrderShipDtl::getBillId, orderShip.getBillId());
            List<OrderShipDtl> dtls = orderShipDtlService.list(q);
            shipDtls.addAll(dtls);
        }
        return shipDtls;
    }


    @Override
    public PageUtils WXqueryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        queryWrapper.eq(OrderShip::getSupplierId, user.getEnterpriseId()).or().eq(OrderShip::getShipEnterpriseId, user.getEnterpriseId());

        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        String orderId = (String) innerMap.get("orderId");
        Integer type = (Integer) innerMap.get("type");
        String orderSn = (String) innerMap.get("orderSn");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        Integer sourceType = (Integer) innerMap.get("sourceType");
        String otherOrderSn = (String) innerMap.get("otherOrderSn");
        String staConfirmTime = (String) innerMap.get("staConfirmTime");
        String endConfirmTime = (String) innerMap.get("endConfirmTime");
        String startGmtCreate = (String) innerMap.get("startGmtCreate");
        String endGmtCreate = (String) innerMap.get("endGmtCreate");
        String staShipTime = (String) innerMap.get("staShipTime");
        String endShipTime = (String) innerMap.get("endShipTime");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String receiveOrgName = (String) innerMap.get("receiveOrgName");
        if (orderId != null && orderId != "") {
            queryWrapper.eq(OrderShip::getOrderId, orderId);
        }
        if (belowPrice != null && belowPrice != "") {
            queryWrapper.lt(OrderShip::getRateAmount, belowPrice);
        }

        if (abovePrice != null && abovePrice != "") {
            queryWrapper.gt(OrderShip::getRateAmount, abovePrice);
        }
        if (type != null) {
            queryWrapper.eq(OrderShip::getType, type);
        }
        queryWrapper.eq(sourceType != null, OrderShip::getSourceType, sourceType);
        if (StringUtils.isNotBlank(orderSn)) {
            queryWrapper.eq(OrderShip::getOrderSn, orderSn);
        }
        if (StringUtils.isNotBlank(otherOrderSn)) {
            queryWrapper.eq(OrderShip::getOtherOrderSn, otherOrderSn);
        }
        if (staConfirmTime != null && staConfirmTime != "") {
            queryWrapper.gt(OrderShip::getConfirmTime, staConfirmTime);
        }
        if (endConfirmTime != null && endConfirmTime != "") {
            queryWrapper.lt(OrderShip::getConfirmTime, endConfirmTime);
        }
        if (startGmtCreate != null && startGmtCreate != "") {
            queryWrapper.gt(OrderShip::getGmtCreate, startGmtCreate);
        }
        if (endGmtCreate != null && endGmtCreate != "") {
            queryWrapper.lt(OrderShip::getGmtCreate, endGmtCreate);
        }
        if (staShipTime != null && staShipTime != "") {
            queryWrapper.gt(OrderShip::getShipData, staShipTime);
        }
        if (endShipTime != null && endShipTime != "") {
            queryWrapper.lt(OrderShip::getShipData, endShipTime);
        }
        if (orderBy != null) {
            if (orderBy == 1) {
                queryWrapper.orderByDesc(OrderShip::getGmtCreate);
            }
            if (orderBy == 2) {
                queryWrapper.orderByDesc(OrderShip::getFlishTime);
            }
            if (orderBy == 3) {
                queryWrapper.orderByDesc(OrderShip::getShipData);
            }
        } else {
            queryWrapper.orderByDesc(OrderShip::getGmtCreate);
        }
        if (receiveOrgName != null && receiveOrgName != "") {
            queryWrapper.like(OrderShip::getReceiveOrgName, receiveOrgName);
        }
        if (keywords != null && keywords != "") {
            queryWrapper.and(i -> i.like(OrderShip::getReceiveName, keywords)
                    .or().like(OrderShip::getReceiveOrgName, keywords)
                    .or().like(OrderShip::getBillSn, keywords)
                    .or().like(OrderShip::getOrderSn, keywords));
        }
        IPage<OrderShip> page = page(new Query<OrderShip>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public void materialShipExport(String id, HttpServletResponse response) {
        OrderShip byId = getById(id);
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();

        dataMap.put("supplierName", byId.getSupplierName());
        dataMap.put("shipEnterpriseId", byId.getShipEnterpriseName());
        dataMap.put("shipAddress", byId.getShipAddress());
        String yyyymmdd = DateUtil.getYYYYmmdd(byId.getConfirmTime(), "yyyy年 MM月 dd日");
        dataMap.put("confirmTime", yyyymmdd);
        List<MaterialShipDtlVo> dtls = orderShipDtlService.getMaterialShipDtlsByOrderShipId(id);
        dataMap.put("dataList", dtls);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "实物收料单模板.xlsx", src, "实物收料单.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Override
    public void materialShipDzExport(String id, HttpServletResponse response) {
        OrderShip byId = getById(id);
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();

        dataMap.put("supplierName", byId.getSupplierName());
        dataMap.put("shipEnterpriseId", byId.getShipEnterpriseName());
        dataMap.put("shipAddress", byId.getShipAddress());
        String yyyymmdd = DateUtil.getYYYYmmdd(byId.getConfirmTime(), "yyyy年 MM月 dd日");
        dataMap.put("confirmTime", yyyymmdd);
        List<MaterialShipDtlVo> dtls = orderShipDtlService.getMaterialShipDzDtlsByOrderShipId(id);
        dataMap.put("dataList", dtls);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "实物收料单模板.xlsx", src, "实物收料单.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Override
    public void saveTwoOrderShip(OrderShipVo orderShipVo) {
        if (orderShipVo == null || orderShipVo.getDtls() == null || orderShipVo.getDtls().size() == 0) {
            throw new BusinessException(500, "请输入发货单或者填写发货单项");
        }
    }


    @Override
    public void updateDtls(OrderShip orderShip) {
        //发货单现在金额
        BigDecimal nowTotalAmoney = BigDecimal.valueOf(0.0);
        BigDecimal nowNoRateAmoney = BigDecimal.valueOf(0.0);
        OrderShip byId = getById(orderShip.getBillId());
        if (byId.getType() == 2) {
            throw new BusinessException(500, "供应商已经收货，不能修改发货单");
        }
        ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
        List<OrderShipDtl> dtls = orderShip.getDtls();
        if (dtls == null || dtls.size() == 0) {
            throw new BusinessException(500, "请输入发货单明细");
        }
        for (OrderShipDtl dtl : dtls) {
            OrderShipDtl oldDtl = orderShipDtlService.getById(dtl.getDtlId());
            oldDtl.setShipNum(dtl.getShipCounts());
            OrderItem orderItem = orderItemService.getById(oldDtl.getOrderItemId());
            //查询商城退货数量
            BigDecimal returnCount = orderReturnItemService.getDataByOrderItmId(orderItem.getOrderItemId(), 0);
            BigDecimal buyCounts = orderItem.getBuyCounts();
            BigDecimal shipCount = buyCounts.subtract(returnCount).add(oldDtl.getShipCounts()).subtract(orderItem.getShipCounts());
            if (dtl.getShipCounts().compareTo(shipCount) <= 0) {
                BigDecimal nowShipCount = orderItem.getShipCounts().subtract(oldDtl.getShipCounts()).add(dtl.getShipCounts());
                orderItem.setShipCounts(nowShipCount);
                //现在发货单项发货数量
                oldDtl.setShipCounts(dtl.getShipCounts());
                oldDtl.setShipNum(dtl.getShipCounts());
                //现在发货单项金额
                orderShipDtlService.updateAmount(orderItem, oldDtl, oldDtl.getShipCounts());
                nowTotalAmoney = nowTotalAmoney.add(oldDtl.getTotalAmount());
                nowNoRateAmoney = nowNoRateAmoney.add(oldDtl.getNoRateAmount());
                orderItemService.updateById(orderItem);
                shipDtls.add(oldDtl);
            } else {
                throw new BusinessException(500, "修改数量大于发货数量，不能修改");
            }


        }
        orderShipDtlService.updateBatchById(shipDtls);
        byId.setNoRateAmount(nowNoRateAmoney);
        byId.setRateAmount(nowTotalAmoney);
        update(orderShip);
//        lambdaQuery().get
//        if (dtls == null||dtls.size()==0){
//            throw new BusinessException(500, "请输入发货单信息");
//        }
//        List<OrderShipDtl> err = dtls.stream().filter(item -> item.getDtlId() == null || item.getShipCounts().compareTo(BigDecimal.ZERO) <= 0).collect(Collectors.toList());
//        if (err.size()>0){
//            throw new BusinessException(500, "请输入发货单信息");
//        }
    }


    @Override
    public void updateMonOrderShip(OrderShip orderShip) {
        //发货单现在金额
        BigDecimal nowTotalAmoney = BigDecimal.valueOf(0.0);
        BigDecimal nowNoRateAmoney = BigDecimal.valueOf(0.0);
        OrderShip byId = getById(orderShip.getBillId());
        if (byId.getType() == 2) {
            throw new BusinessException(500, "供应商已经收货，不能修改发货单");
        }
        ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
        List<OrderShipDtl> dtls = orderShip.getDtls();
        if (dtls == null || dtls.size() == 0) {
            throw new BusinessException(500, "请输入发货单明细");
        }
        ArrayList<OrderShipmentsQtyIsOkDTO> list = new ArrayList<>();
        for (OrderShipDtl dtl : dtls) {
            //订单项
            OrderShipmentsQtyIsOkDTO orderShipmentsQtyIsOkDTO = new OrderShipmentsQtyIsOkDTO();

            OrderShipDtl oldDtl = orderShipDtlService.getById(dtl.getDtlId());
            //超量
            orderShipmentsQtyIsOkDTO.setOrderItemId(oldDtl.getOrderItemId());
            orderShipmentsQtyIsOkDTO.setDtlId(oldDtl.getDtlId());
            orderShipmentsQtyIsOkDTO.setUpdate(true);
            orderShipmentsQtyIsOkDTO.setQty(dtl.getShipCounts());
            list.add(orderShipmentsQtyIsOkDTO);


            oldDtl.setShipNum(dtl.getShipCounts());
            OrderItem orderItem = orderItemService.getById(oldDtl.getOrderItemId());
            //查询商城退货数量
            BigDecimal returnCount = orderReturnItemService.getDataByOrderItmId(orderItem.getOrderItemId(), 0);
            BigDecimal buyCounts = orderItem.getBuyCounts();
            buyCounts = buyCounts.add(buyCounts.multiply(BigDecimal.valueOf(0.1)));
            BigDecimal shipCount = buyCounts.subtract(returnCount).add(oldDtl.getShipCounts()).subtract(orderItem.getShipCounts());
            if (dtl.getShipCounts().compareTo(shipCount) <= 0) {
                BigDecimal nowShipCount = orderItem.getShipCounts().subtract(oldDtl.getShipCounts()).add(dtl.getShipCounts());
                orderItem.setShipCounts(nowShipCount);
                //现在发货单项发货数量
                oldDtl.setShipCounts(dtl.getShipCounts());
                oldDtl.setShipNum(dtl.getShipCounts());
                orderItemService.updateById(orderItem);
                shipDtls.add(oldDtl);
            } else {
                throw new BusinessException(500, "修改数量大于发货数量，不能修改");
            }


        }
        orderItemService.orderShipmentsQtyIsOkLG(list);
        orderShipDtlService.updateBatchById(shipDtls);
        byId.setNoRateAmount(nowNoRateAmoney);
        byId.setRateAmount(nowTotalAmoney);
        update(byId);
    }

    @Override
    public void UpdateDzOrderShip(OrderShip orderShip) {
        //发货单现在金额
        BigDecimal nowTotalAmoney = BigDecimal.valueOf(0.0);
        BigDecimal nowNoRateAmoney = BigDecimal.valueOf(0.0);
        OrderShip byId = getById(orderShip.getBillId());
        if (byId.getType() == 2) {
            throw new BusinessException(500, "供应商已经收货，不能修改发货单");
        }
        ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
        List<OrderShipDtl> dtls = orderShip.getDtls();
        if (dtls == null || dtls.size() == 0) {
            throw new BusinessException(500, "请输入发货单明细");
        }
        ArrayList<OrderShipmentsQtyIsOkDTO> list = new ArrayList<>();
        for (OrderShipDtl dtl : dtls) {
            //订单项
            OrderShipmentsQtyIsOkDTO orderShipmentsQtyIsOkDTO = new OrderShipmentsQtyIsOkDTO();

            OrderShipDtl oldDtl = orderShipDtlService.getById(dtl.getDtlId());
            //超量
            orderShipmentsQtyIsOkDTO.setOrderItemId(oldDtl.getOrderItemId());
            orderShipmentsQtyIsOkDTO.setDtlId(oldDtl.getDtlId());
            orderShipmentsQtyIsOkDTO.setUpdate(true);
            orderShipmentsQtyIsOkDTO.setQty(dtl.getShipCounts());
            list.add(orderShipmentsQtyIsOkDTO);


            oldDtl.setShipNum(dtl.getShipCounts());
            OrderItem orderItem = orderItemService.getById(oldDtl.getOrderItemId());
            //查询商城退货数量
            BigDecimal returnCount = orderReturnItemService.getDataByOrderItmId(orderItem.getOrderItemId(), 0);
            BigDecimal buyCounts = orderItem.getBuyCounts();
            buyCounts = buyCounts.add(buyCounts.multiply(BigDecimal.valueOf(0.1)));
            BigDecimal shipCount = buyCounts.subtract(returnCount).add(oldDtl.getShipCounts()).subtract(orderItem.getShipCounts());
            if (dtl.getShipCounts().compareTo(shipCount) <= 0) {
                BigDecimal nowShipCount = orderItem.getShipCounts().subtract(oldDtl.getShipCounts()).add(dtl.getShipCounts());
                orderItem.setShipCounts(nowShipCount);
                //现在发货单项发货数量
                //现在发货单项发货数量
                oldDtl.setShipCounts(dtl.getShipCounts());
                oldDtl.setShipNum(dtl.getShipCounts());
                BigDecimal amount = oldDtl.getTotalAmount();
                BigDecimal noRateAmount = oldDtl.getNoRateAmount();
                orderShipDtlService.updateAmount(orderItem, oldDtl, oldDtl.getShipCounts());
                nowTotalAmoney = nowTotalAmoney.add(oldDtl.getTotalAmount().subtract(amount));
                nowNoRateAmoney = nowNoRateAmoney.add(oldDtl.getNoRateAmount().subtract(noRateAmount));
                orderItemService.updateById(orderItem);
                shipDtls.add(oldDtl);
            } else {
                throw new BusinessException(500, "修改数量大于发货数量，不能修改");
            }


        }
        orderItemService.orderShipmentsQtyIsOkLG(list);
        orderShipDtlService.updateBatchById(shipDtls);
        byId.setNoRateAmount(nowNoRateAmoney);
        byId.setRateAmount(nowTotalAmoney);
        update(byId);

    }

    @Override
    public void shippingOrderShip(OrderShip one) {
        String deliveryFlowId = one.getDeliveryFlowId();
        if (deliveryFlowId == null || deliveryFlowId == "") {
            throw new BusinessException(500, "该用户没有填写发货单号");
        }
        String company = one.getLogisticsCompany();
        if (company == null || company == "") {
            throw new BusinessException(1002, "该用户没有填写物流公司");
        }
        OrderShip info = getById(one.getBillId());
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (!info.getShipEnterpriseId().equals(user.getEnterpriseId())) {
            throw new BusinessException(500, "该用户没有发货权限");
        }
        if (info != null) {
            if (info.getType() == 0) {
                info.setDeliveryFlowId(one.getDeliveryFlowId());
                info.setLogisticsCompany(one.getLogisticsCompany());
                info.setSupplierCode(user.getSocialCreditCode());
                info.setType(1);
                info.setShipData(new Date());
                info.setShipUserId(user.getUserId());
                update(info);
                /***
                 *  查看订单项是否发货完,修改订单状态
                 */
                orderItemService.updateOrderStateAndOrderItemByShip(info);

            } else {
                throw new BusinessException(302, "该发货单已经发货");
            }

        } else {
            throw new BusinessException(302, "该发货单不存在");
        }

    }

    @Override
    public void delMonthOrderShipByBillSn(OrderShip byId) {
        if (byId != null) {
            if (byId.getType() == 0) {
                List<OrderShipDtl> dtls = orderShipDtlService.getDataByOrderShipId(byId.getBillId());
                for (OrderShipDtl dtl : dtls) {
                    OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
                    orderItem.setShipCounts(orderItem.getShipCounts().subtract(dtl.getShipCounts()));
//                    if (orderItem.getParentOrderItemId()!=null){
//                        OrderItem parent = orderItemService.getById(orderItem.getParentOrderItemId());
//                        parent.setShipCounts( orderItem.getShipCounts());
//                        orderItemService.updateShipCounts(parent);
//                    }

                    orderShipDtlService.removeById(dtl.getDtlId());
                    orderItemService.updateShipCounts(orderItem);
                }
                super.removeById(byId.getBillId());

            } else {
                throw new BusinessException(603, "发货单已确认，不能删除");
            }
        } else {
            throw new BusinessException(602, "发货单不存在");
        }
    }

    @Override
    public void delDzOrderShipByBillSn(OrderShip byId) {
        if (byId.getType() == 0) {
            List<OrderShipDtl> dtls = orderShipDtlService.getDataByOrderShipId(byId.getBillId());
            for (OrderShipDtl dtl : dtls) {
                OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
                orderItem.setShipCounts(orderItem.getShipCounts().subtract(dtl.getShipCounts()));
//                    if (orderItem.getParentOrderItemId()!=null){
//                        OrderItem parent = orderItemService.getById(orderItem.getParentOrderItemId());
//                        parent.setShipCounts( orderItem.getShipCounts());
//                        orderItemService.updateShipCounts(parent);
//                    }

                orderShipDtlService.removeById(dtl.getDtlId());
                orderItemService.updateShipCounts(orderItem);
            }
            super.removeById(byId.getBillId());

        } else {
            throw new BusinessException(603, "发货单已确认，不能删除");
        }
    }

    @Override
    public void delLiXinBillId(OrderShip byId) {

        if (byId.getType() == 0) {
            List<OrderShipDtl> dtls = orderShipDtlService.getDataByOrderShipId(byId.getBillId());
            for (OrderShipDtl dtl : dtls) {
                OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
                orderItem.setShipCounts(orderItem.getShipCounts().subtract(dtl.getShipCounts()));
//                    if (orderItem.getParentOrderItemId()!=null){
//                        OrderItem parent = orderItemService.getById(orderItem.getParentOrderItemId());
//                        parent.setShipCounts( orderItem.getShipCounts());
//                        orderItemService.updateShipCounts(parent);
//                    }

                orderShipDtlService.removeById(dtl.getDtlId());
                orderItemService.updateShipCounts(orderItem);
            }
            super.removeById(byId.getBillId());

        } else {
            throw new BusinessException(603, "发货单已确认，不能删除");
        }
    }


    @Override
    public void OutSaveOrderShip(OrderShipVo orderShipVo, User user) {
        if (orderShipVo == null || orderShipVo.getDtls() == null || orderShipVo.getDtls().size() == 0) {
            throw new BusinessException(500, "请输入发货单或者填写发货单项");
        }
        OrderShip orderShip = new OrderShip();
        Orders orders = ordersService.getById(orderShipVo.getOrderId());
        if (orders.getParentOrderId() != null) {
            Orders one = ordersService.getById(orders.getParentOrderId());
            orderShipVo.setOrderClass(3);
            orderShip = outSaveShipInfo(orderShipVo, one);
            orderShip.setShipEnterpriseName(one.getSupplierName());
            orderShip.setOtherOrderId(orders.getOrderId());
            orderShip.setOtherOrderSn(orders.getOrderSn());

        } else{

            orderShip = outSaveShipInfo(orderShipVo, orders);
        }
        /**
         * 设置供应商信息
         */
        orderShip.setShipUserName(user.getNickName());
        orderShip.setShipUserId(user.getUserId());
        orderShip.setShipEnterpriseId(user.getEnterpriseId());
//        orderShip.setSupplierOrgId(user.getOrgId());
//        orderShip.setSupplierCode(user.getSocialCreditCode());
    }
}


