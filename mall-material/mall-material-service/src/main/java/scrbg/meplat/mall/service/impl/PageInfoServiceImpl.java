package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.PageInfo;
import scrbg.meplat.mall.mapper.PageInfoMapper;
import scrbg.meplat.mall.service.PageInfoService;

/**
 * @描述： 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
@Service
public class PageInfoServiceImpl extends ServiceImpl<PageInfoMapper, PageInfo> implements PageInfoService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PageInfo> queryWrapper) {
        IPage<PageInfo> page = this.page(
        new Query<PageInfo>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(PageInfo pageInfo) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(pageInfo);
    }

    @Override
    public void update(PageInfo pageInfo) {
        super.updateById(pageInfo);
    }


    @Override
    public PageInfo getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
