package scrbg.meplat.mall.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import scrbg.meplat.mall.entity.ProcessNode;
import scrbg.meplat.mall.mapper.ProcessNodeMapper;
import scrbg.meplat.mall.service.ProcessNodeService;
import scrbg.meplat.mall.service.ProcessRoleService;

/**
 * 流程配置表 服务类
 * <AUTHOR>
 * @date: 2025年6月20日 上午10:06:41
 */
@Service
public class ProcessNodeServiceImpl extends ServiceImpl<ProcessNodeMapper, ProcessNode> implements ProcessNodeService {

    @Autowired
    ProcessRoleService processRoleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProcessNode processNode) {
        super.save(processNode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProcessNode processNode) {
        super.updateById(processNode);
    }

    @Override
    public ProcessNode getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        super.removeById(id);
    }

}
