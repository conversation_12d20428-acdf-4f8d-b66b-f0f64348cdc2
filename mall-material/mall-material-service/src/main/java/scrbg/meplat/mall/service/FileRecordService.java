package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.FileRecord;
/**
 * @描述：文件上传记录信息 服务类
 * @作者: ye
 * @日期: 2023-03-27
 */
public interface FileRecordService extends IService<FileRecord> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<FileRecord> queryWrapper);

        void create(FileRecord fileRecord);

        void update(FileRecord fileRecord);

        FileRecord getById(String id);

        void delete(String id);
}