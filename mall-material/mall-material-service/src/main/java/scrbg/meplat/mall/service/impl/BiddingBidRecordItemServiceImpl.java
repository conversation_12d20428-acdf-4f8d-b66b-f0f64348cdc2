package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.pageUtils;
import scrbg.meplat.mall.vo.bidding.GetBidingRecordItemInfoVO;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.BiddingBidRecordItemMapper;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.vo.bidding.GetBidingRecordItemVO;
import scrbg.meplat.mall.vo.product.device.PlatformDeviceListVO;
import scrbg.meplat.mall.vo.product.website.demand.DeviceDemandHomeVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @描述：竞价记录 服务类
 * @作者: ye
 * @日期: 2023-07-19
 */
@Service
public class BiddingBidRecordItemServiceImpl extends ServiceImpl<BiddingBidRecordItemMapper, BiddingBidRecordItem> implements BiddingBidRecordItemService {
    @Autowired
    BiddingBidRecordService biddingBidRecordService;

    @Autowired
    BiddingPurchaseService biddingPurchaseService;

    @Autowired
    AuditRecordService auditRecordService;

    @Autowired
    FileService fileService;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingBidRecordItem> queryWrapper) {
        String bidRecordId = (String) jsonObject.get("bidRecordId");
        String keywords = (String) jsonObject.get("keywords");
        if(StringUtils.isBlank(bidRecordId)) {
            throw new BusinessException("经济记录不能为空！");
        }
        IPage<BiddingBidRecordItem> page = this.page(
                new Query<BiddingBidRecordItem>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(BiddingBidRecordItem biddingBidRecordItem) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingBidRecordItem);
    }

    @Override
    public void update(BiddingBidRecordItem biddingBidRecordItem) {
        super.updateById(biddingBidRecordItem);
    }


    @Override
    public BiddingBidRecordItem getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 根据记录id获取记录明细
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public GetBidingRecordItemInfoVO getBidingRecordItemInfo(JSONObject jsonObject, LambdaQueryWrapper<BiddingBidRecordItem> q) {
        GetBidingRecordItemInfoVO vo = new GetBidingRecordItemInfoVO();
        String bidRecordId = (String) jsonObject.get("bidRecordId");
        if(StringUtils.isBlank(bidRecordId)) {
            throw new BusinessException("经济记录不能为空！");
        }
        String keywords = (String) jsonObject.get("keywords");
        BiddingBidRecord biddingBidRecord = biddingBidRecordService.getById(bidRecordId);
        if(biddingBidRecord != null) {
            BeanUtils.copyProperties(biddingBidRecord,vo);
            List<AuditRecord> r = auditRecordService.lambdaQuery()
                    .eq(AuditRecord::getRelevanceId, biddingBidRecord.getBidRecordId())
                    .eq(AuditRecord::getRelevanceType, 4).list();
            vo.setAuditRecordList(r);

            File file = fileService.lambdaQuery()
                    .eq(File::getRelevanceId, biddingBidRecord.getBidRecordId())
                    .eq(File::getRelevanceType, 15).one();
            if (file != null) {
                vo.setFileFarId(file.getFileFarId());
                vo.setFileName(file.getName());
            }
        }
        int count = baseMapper.listSupplierBiddingRecordItemsCount(jsonObject);
        pageUtils.pageDispose(jsonObject,count);
        Page<GetBidingRecordItemVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<GetBidingRecordItemVO> vos = baseMapper.listSupplierBiddingRecordItems(pages,jsonObject);
        pages.setRecords(vos);
        PageUtils pageUtils = new PageUtils(pages);
        vo.setPageList(pageUtils);
        return vo;
    }

    /**
     * 根据记录id获取记录明细-平台
     * @param jsonObject
     * @param biddingBidRecordItemLambdaQueryWrapper
     * @return
     */
    @Override
    public GetBidingRecordItemInfoVO getPlatformBidingRecordItemInfo(JSONObject jsonObject, LambdaQueryWrapper<BiddingBidRecordItem> biddingBidRecordItemLambdaQueryWrapper) {
        GetBidingRecordItemInfoVO vo = new GetBidingRecordItemInfoVO();
        String bidRecordId = (String) jsonObject.get("bidRecordId");
        if(StringUtils.isBlank(bidRecordId)) {
            throw new BusinessException("经济记录不能为空！");
        }
        String keywords = (String) jsonObject.get("keywords");
        BiddingBidRecord biddingBidRecord = biddingBidRecordService.getById(bidRecordId);
        if(biddingBidRecord != null) {
            BeanUtils.copyProperties(biddingBidRecord,vo);
        }
        //查询竞价采购，如果状态没有结束，不查看金额
        String biddingId = biddingBidRecord.getBiddingId();
        BiddingPurchase biddingPurchase = biddingPurchaseService.lambdaQuery()
                .eq(BiddingPurchase::getBiddingId, biddingId)
                .select(BiddingPurchase::getBiddingState).one();
        Integer biddingState = biddingPurchase.getBiddingState();
        if(biddingState != 3) {
            vo.setBidAmount(null);
            vo.setBidRateAmount(null);
            vo.setRemarks(null);
        }

        int count = baseMapper.listPlatformBiddingRecordItemsCount(jsonObject);
        pageUtils.pageDispose(jsonObject,count);
        Page<GetBidingRecordItemVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<GetBidingRecordItemVO> vos = baseMapper.listPlatformBiddingRecordItems(pages,jsonObject);
        // 清空备注信息
        if(biddingState != 3) {
            for (GetBidingRecordItemVO getBidingRecordItemVO : vos) {
                getBidingRecordItemVO.setBidPrice(null);
                getBidingRecordItemVO.setTaxRate(null);
                getBidingRecordItemVO.setBidRatePrice(null);
                getBidingRecordItemVO.setBidRateAmount(null);
                getBidingRecordItemVO.setBidAmount(null);
                getBidingRecordItemVO.setRemarks(null);
            }
        }
        pages.setRecords(vos);
        PageUtils pageUtils = new PageUtils(pages);
        vo.setPageList(pageUtils);
        return vo;
    }

}