package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.ProductCompare;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ProductCompare;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.vo.productCompare.ProductCompareDetail;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-12-12
 */
public interface ProductCompareService extends IService<ProductCompare> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductCompare> queryWrapper);

        void create(ProductCompare productCompare);
        void update(ProductCompare productCompare);
        ProductCompare getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

        /**
         * 根据当前登录用户新增比价主表数据
         * @return
         */
        ProductCompare createUseCurrentUser();

        /**
         * 根据ID删除比价记录（包括明细）
         * @param id
         */
        void removeByID(String id);
        /**
         * 获取比价明细
         * @param id 比价主表ID
         * @return ProductCompareDetail 视图对象
         */
        ProductCompareDetail getCompareDetail(String id);

        /**
         * 导出比价明细为PDF(废弃)
         * @param id
         * @param response
         */
        void exportCompareDetailToPDF(String id, HttpServletResponse response);
        // 合并比价明细并转换PDF

        /**
         *  合并比价明细并转换PDF
         * @param ids 比价主表ID列表
         * @param response 响应对象
         */
        void mergeCompareDetailToPDF1(List<String> ids, OutputStream response);


        /**
         * 根据compareIds合并比价明细并转换PDF
         * @param ids
         * @param response
         */
        void mergeCompareDetail(List<String> ids, HttpServletResponse response);




}
