package scrbg.meplat.mall.controller.website.thirdpartyApi;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.Brand;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.BrandService;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrdersService;
import scrbg.meplat.mall.service.UserService;
import scrbg.meplat.mall.vo.product.website.WBrandVO;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/w/api/thirdParty/order")
@ApiSort(value = 9999)
@Api(tags = "第三方订单api")
public class WebsiteOrderController {

    @Autowired
    private OrdersService ordersService;

    @Autowired
    private UserService userService;

    @Autowired
    private OrderItemService orderItemService;

    @PostMapping("/list/supplierOrderPageList")
    @ApiOperation(value = "查询供应商订单列表（普通供应商订单）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数（默认20）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称，订单号、收货人姓名）", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "订单状态：6:待发货 7:已关闭 8:发货中 9:待收货 10:已完成", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "订单类型：10:零星采购订单, 12:大宗月供订单", dataTypeClass = Integer.class),
    })
    public PageR<Orders> supplierOrderPageList(HttpServletRequest request, @RequestBody JSONObject jsonObject) {
        String privateKey = request.getHeader("privateKey");
        if (StringUtils.isBlank(privateKey) || !privateKey.equals(HeaderKey.key)) {
            throw new BusinessException("请求无效！");
        }
        LambdaQueryWrapper<Orders> q = Wrappers.lambdaQuery(Orders.class);
        // 初始查询条件
        q.eq(Orders::getOrderClass, 1);
        q.isNull(Orders::getParentOrderId);
        String userId = request.getHeader("userId");
        User user = userService.lambdaQuery().eq(User::getUserId, userId)
                .select(User::getUserId, User::getEnterpriseId).one();
        q.eq(Orders::getSupplierId, user.getEnterpriseId());

        Integer page = (Integer) jsonObject.get("page");
        Integer limit = (Integer) jsonObject.get("limit");
        if (page == null) page = 1;
        if (limit == null) limit = 20;
        if (limit > 2000) limit = 20;

        String orderSn = (String) jsonObject.get("orderSn");
        q.eq(StringUtils.isNotBlank(orderSn), Orders::getOrderSn, orderSn);
        q.orderByDesc(Orders::getGmtCreate);
        String keywords = (String) jsonObject.get("keywords");
        if (org.apache.commons.lang.StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(Orders::getOrderSn, keywords)
                        .or()
                        .like(Orders::getUntitled, keywords)
                        .or()
                        .like(Orders::getReceiverMobile, keywords);
            });
        }
        Integer state = (Integer) jsonObject.get("state");
        if (state != null) {
            if (state >= 6 && state <= 10) {
                q.eq(Orders::getState, state);
            }
        }
        Integer productType = (Integer) jsonObject.get("productType");
        if (productType != null) {
            if (productType == 10 || productType == 12) {
                q.eq(Orders::getProductType, productType);
            }
        }
        IPage<Orders> iPage = ordersService.page(
                new Query<Orders>().getPage(jsonObject),
                q
        );
        return PageR.success(new PageUtils(iPage));
    }



    @PostMapping("/list/twoSupplierOrderPageList")
    @ApiOperation(value = "查询供应商订单列表（二级供应商订单）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数（默认20）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "affirmState", value = "供方确认状态（0待确认1已确认2已拒绝）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称，订单号、收货人姓名）", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "订单状态：6:待发货 7:已关闭 8:发货中 9:待收货 10:已完成", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "订单类型：10:零星采购订单, 12:大宗月供订单 13:大宗临购订单", dataTypeClass = Integer.class),
    })
    public PageR<Orders> twoSupplierOrderPageList(HttpServletRequest request, @RequestBody JSONObject jsonObject) {
        String privateKey = request.getHeader("privateKey");
        if (StringUtils.isBlank(privateKey) || !privateKey.equals(HeaderKey.key)) {
            throw new BusinessException("请求无效！");
        }
        LambdaQueryWrapper<Orders> q = Wrappers.lambdaQuery(Orders.class);
        q.in(Orders::getAffirmState, 0,1,2);
        // 初始查询条件
        q.eq(Orders::getMasterAffirmState, 1);
        q.eq(Orders::getOrderClass, 3);


        String userId = request.getHeader("userId");
        User user = userService.lambdaQuery().eq(User::getUserId, userId)
                .select(User::getUserId, User::getEnterpriseId).one();
        q.eq(Orders::getSupplierId, user.getEnterpriseId());

        Integer page = (Integer) jsonObject.get("page");
        Integer limit = (Integer) jsonObject.get("limit");
        if (page == null) page = 1;
        if (limit == null) limit = 20;
        if (limit > 2000) limit = 20;

        String orderSn = (String) jsonObject.get("orderSn");
        Integer affirmState = (Integer) jsonObject.get("affirmState");
        q.eq(StringUtils.isNotBlank(orderSn), Orders::getOrderSn, orderSn);
        q.orderByDesc(Orders::getGmtCreate);
        q.eq(affirmState!= null,Orders::getAffirmState,affirmState);
        String keywords = (String) jsonObject.get("keywords");
        if (org.apache.commons.lang.StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(Orders::getOrderSn, keywords)
                        .or()
                        .like(Orders::getUntitled, keywords)
                        .or()
                        .like(Orders::getReceiverMobile, keywords);
            });
        }
        Integer state = (Integer) jsonObject.get("state");
        if (state != null) {
            if (state >= 6 && state <= 10) {
                q.eq(Orders::getState, state);
            }
        }
        Integer productType = (Integer) jsonObject.get("productType");
        if (productType != null) {
            if (productType == 10 || productType == 12) {
                q.eq(Orders::getProductType, productType);
            }
        }
        IPage<Orders> iPage = ordersService.page(
                new Query<Orders>().getPage(jsonObject),
                q
        );
        return PageR.success(new PageUtils(iPage));
    }

    @GetMapping("/update/affirmTwoOrder")
    @ApiOperation(value = "二级供方确认订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "订单id", value = "orderId", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "是否确认", value = "isAffirm", required = true, dataType = "Boolean", paramType = "query")
    })
    public R affirmTwoOrder(String orderId, Boolean isAffirm) {
        if (org.apache.commons.lang.StringUtils.isEmpty(orderId) || isAffirm == null) {
            throw new BusinessException("参数错误！");
        }
        Orders byId = ordersService.getById(orderId);
        if (byId != null) {
            if (isAffirm) {
                byId.setAffirmState(1);
                byId.setFlishTime(new Date());
            } else {
                byId.setAffirmState(2);
            }
            ordersService.updateById(byId);
        } else {
            throw new BusinessException("修改失败");
        }
        return R.success();
    }


    @GetMapping("/get/orderInfo")
    @ApiOperation(value = "查询订单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "订单号", value = "orderSn", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Orders> orderInfo(HttpServletRequest request,String orderSn) {
        String privateKey = request.getHeader("privateKey");
        if (StringUtils.isBlank(privateKey) || !privateKey.equals(HeaderKey.key)) {
            throw new BusinessException("请求无效！");
        }
        String userId = request.getHeader("userId");
        User user = userService.lambdaQuery().eq(User::getUserId, userId)
                .select(User::getUserId, User::getEnterpriseId).one();
        Orders one = ordersService.lambdaQuery().eq(Orders::getSupplierId, user.getEnterpriseId())
                .eq(Orders::getOrderSn, orderSn).one();
        if(one != null) {
            List<OrderItem> list = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, one.getOrderId()).list();
            one.setOrderItems(list);
        }
        return R.success(one);
    }


    @GetMapping("/get/twoOrderInfo")
    @ApiOperation(value = "查询二级订单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "订单号", value = "orderSn", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Orders> twoOrderInfo(HttpServletRequest request,String orderSn) {
        String privateKey = request.getHeader("privateKey");
        if (StringUtils.isBlank(privateKey) || !privateKey.equals(HeaderKey.key)) {
            throw new BusinessException("请求无效！");
        }
        String userId = request.getHeader("userId");
        User user = userService.lambdaQuery().eq(User::getUserId, userId)
                .select(User::getUserId, User::getEnterpriseId).one();
        Orders one = ordersService.lambdaQuery().eq(Orders::getSupplierId, user.getEnterpriseId())
                .eq(Orders::getOrderSn, orderSn).one();
        if(one != null) {
            List<OrderItem> list = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, one.getOrderId()).list();
            one.setOrderItems(list);
        }
        return R.success(one);
    }


    @GetMapping("/get/orderShipList")
    @ApiOperation(value = "查询发货单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "订单号", value = "orderSn", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Orders> orderShipList(HttpServletRequest request,String orderSn) {
        String privateKey = request.getHeader("privateKey");
        if (StringUtils.isBlank(privateKey) || !privateKey.equals(HeaderKey.key)) {
            throw new BusinessException("请求无效！");
        }
        String userId = request.getHeader("userId");
        User user = userService.lambdaQuery().eq(User::getUserId, userId)
                .select(User::getUserId, User::getEnterpriseId).one();
        Orders one = ordersService.lambdaQuery().eq(Orders::getSupplierId, user.getEnterpriseId())
                .eq(Orders::getOrderSn, orderSn).one();
        if(one != null) {
            List<OrderItem> list = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, one.getOrderId()).list();
            one.setOrderItems(list);
        }
        return R.success(one);
    }

}

