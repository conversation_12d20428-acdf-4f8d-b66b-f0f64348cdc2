package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.OrderReturn;
import scrbg.meplat.mall.entity.OrderReturnItem;
import scrbg.meplat.mall.mapper.OrderReturnItemMapper;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrderReturnItemService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.TaxCalculator;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * @描述：退货项表 服务类
 * @作者: ye
 * @日期: 2023-08-07
 */
@Service
public class OrderReturnItemServiceImpl extends ServiceImpl<OrderReturnItemMapper, OrderReturnItem> implements OrderReturnItemService{


    @Autowired
    OrderItemService orderItemService;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderReturnItem> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String orderReturnNo = (String) innerMap.get("orderReturnNo");
        String productName = (String) innerMap.get("productName");
        String orderSn = (String) innerMap.get("orderSn");
        String orderItemId = (String) innerMap.get("orderItemId");
        String orderReturnId = (String) innerMap.get("orderReturnId");
        String startDate = (String) innerMap.get("startDate");
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        String endDate = (String) innerMap.get("endDate");

        if (!StringUtils.isEmpty(orderReturnNo)) {
            queryWrapper.eq(OrderReturnItem::getOrderReturnNo, orderReturnNo);
        }
        if (!StringUtils.isEmpty(productName)) {
            queryWrapper.like(OrderReturnItem::getProductName, productName);
        }
        if (!StringUtils.isEmpty(orderItemId)) {
            queryWrapper.eq(OrderReturnItem::getOrderItemId, orderItemId);
        }
        if (!StringUtils.isEmpty(orderSn)) {
            queryWrapper.like(OrderReturnItem::getOrderSn, orderSn);
        }
        if (!StringUtils.isEmpty(orderReturnId)) {
            queryWrapper.eq(OrderReturnItem::getOrderReturnId, orderReturnId);
        }
        if (!StringUtils.isEmpty(keywords)) {
            queryWrapper.like(OrderReturnItem::getOrderSn, keywords).or()
                    .like(OrderReturnItem::getOrderReturnNo, keywords).or()
                    .like(OrderReturnItem::getProductName, keywords);

        }
        if (!StringUtils.isEmpty(startDate)) {
            queryWrapper.gt(OrderReturnItem::getGmtCreate, startDate);
        }
        if (!StringUtils.isEmpty(endDate)) {
            queryWrapper.lt(OrderReturnItem::getGmtCreate, endDate);
        }
        if (!StringUtils.isEmpty(abovePrice)) {
            queryWrapper.gt(OrderReturnItem::getTotalAmount, abovePrice);
        }
        if (!StringUtils.isEmpty(belowPrice)) {
            queryWrapper.lt(OrderReturnItem::getTotalAmount, belowPrice);
        }

        queryWrapper.orderByDesc(OrderReturnItem::getGmtCreate);
        IPage<OrderReturnItem> page = this.page(
        new Query<OrderReturnItem>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(OrderReturnItem orderReturnItem) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(orderReturnItem);
    }

    @Override
    public void update(OrderReturnItem orderReturnItem) {
        super.updateById(orderReturnItem);
    }


    @Override
    public OrderReturnItem getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {

        super.removeByIds(ids);
        }


    @Override
    public List<OrderReturnItem> getReturnNo(String orderReturnNo) {
        LambdaQueryWrapper<OrderReturnItem> q = new LambdaQueryWrapper<>();
        q.eq(OrderReturnItem::getOrderReturnNo,orderReturnNo);
        List<OrderReturnItem> list = list(q);


        return list;
    }


    @Override
    public void deleteReturnNo(String orderReturnNo) {
        LambdaQueryWrapper<OrderReturnItem> q = new LambdaQueryWrapper<>();
        q.eq(OrderReturnItem::getOrderReturnNo,orderReturnNo);
        remove(q);
    }


    @Override
    public List<OrderReturnItem> selectAllByOrderReturmId(String orderReturnId) {
        LambdaQueryWrapper<OrderReturnItem> q = new LambdaQueryWrapper<>();
        q.eq(OrderReturnItem::getOrderReturnId,orderReturnId);
        List<OrderReturnItem> list = list(q);
        return list;
    }

    @Override
    public BigDecimal getDataByOrderItmId(String orderItemId, int i) {
//        LambdaQueryWrapper<OrderReturnItem> q = new LambdaQueryWrapper<>();
//        q.eq(OrderReturnItem::getOrderItemId,orderItemId)
//                .eq(OrderReturnItem::getIsOut,i);
//        List<OrderReturnItem> list = list(q);
        //商城退货数量
        QueryWrapper<OrderReturnItem> q = new QueryWrapper<>();
        q.eq("oi.order_item_id",orderItemId)
                .eq("oi.is_out",i)
                .eq("o.is_delete",0)
                .ne("o.state",4);
        List<OrderReturnItem> list= baseMapper.selectRetuenList(q);
        BigDecimal count= BigDecimal.valueOf(0);
        if (list!=null&&list.size()>0){
            for (OrderReturnItem orderReturnItem : list) {
                count=count.add(orderReturnItem.getCount());
            }
        }
        return  count;
    }


    @Override
    public void deleteByOrderReturnId(String id) {
        LambdaQueryWrapper<OrderReturnItem> eq = new LambdaQueryWrapper<OrderReturnItem>().eq(OrderReturnItem::getOrderReturnId, id);
        remove(eq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderReturnItem getDataByIdAndIsReconciliation(String sourceDtlId, int i) {
        LambdaQueryWrapper<OrderReturnItem> eq = new LambdaQueryWrapper<OrderReturnItem>()
                .eq(OrderReturnItem::getOrderReturnItemId, sourceDtlId)
                .eq(OrderReturnItem::getIsReconciliation,i);
      return getOne(eq);
    }


    @Override
    public void setOrderReturn(OrderItem byId, OrderReturnItem orderReturnItem,BigDecimal taxRate) {
        if (byId.getParentOrderItemId()==null){
            orderReturnItem.setOrderItemId(byId.getOrderItemId());
            orderReturnItem.setOrderSn(byId.getOrderSn());
            orderReturnItem.setOrderId(byId.getOrderId());
            orderReturnItem.setProductSn(byId.getProductSn());
            orderReturnItem.setProductName(byId.getProductName());
            orderReturnItem.setProductImg(byId.getProductImg());
            orderReturnItem.setProductId(byId.getProductId());
            orderReturnItem.setSkuId(byId.getSkuId());
            orderReturnItem.setSkuName(byId.getSkuName());
            orderReturnItem.setTexture(byId.getTexture());
            orderReturnItem.setUnit(byId.getUnit());
            orderReturnItem.setBrandName(byId.getBrandName());
            orderReturnItem.setBuyCounts(byId.getBuyCounts());
            if (byId.getProductType()!=12){
                orderReturnItem.setProductPrice(byId.getProductPrice());
                orderReturnItem.setTotalAmount(byId.getProductPrice().multiply(orderReturnItem.getCount()).setScale(2, RoundingMode.HALF_UP));

                BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(orderReturnItem.getProductPrice(), taxRate).setScale(2, RoundingMode.HALF_UP);
                BigDecimal noRateAmount =  TaxCalculator.noTarRateItemAmount(orderReturnItem.getTotalAmount(),noRatePrice,orderReturnItem.getCount(),taxRate);
                orderReturnItem.setNoRatePrice(noRatePrice);
                orderReturnItem.setNoRateAmount(noRateAmount);

            }

        }else {
            orderReturnItem.setOrderItemId(byId.getParentOrderItemId());
            orderReturnItem.setOtherOrderItemId(byId.getOrderItemId());
            orderReturnItem.setOtherOrderId(byId.getOrderId());
            orderReturnItem.setOtherOrderSn(byId.getOrderSn());
            if (byId.getProductType()!=12){
                orderReturnItem.setOtherProductPrice(byId.getProductPrice());
                orderReturnItem.setOtherRateAmount(byId.getProductPrice().multiply(orderReturnItem.getCount()));
                BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(orderReturnItem.getOtherProductPrice(), taxRate);
                BigDecimal noRateAmount=TaxCalculator.noTarRateItemAmount(orderReturnItem.getOtherRateAmount(), noRatePrice,orderReturnItem.getCount(),taxRate);

                orderReturnItem.setOtherNoProductPrice(noRatePrice);
                orderReturnItem.setOtherNoRateAmount(noRateAmount);
            }
        }

    }

    /**
     *  订单总量：订单总量减商城退货总量
     * @param orderItems
     * @return
     */
    @Override
    public BigDecimal getDataByIdAndIsReconciliation(List<String> orderItems) {
        QueryWrapper<OrderReturnItem> q = new QueryWrapper<>();
        q.in("oi.order_item_id",orderItems)
                .eq("oi.is_out",0)
                .eq("o.is_delete",0)
                .ne("o.state",4);
        BigDecimal count = baseMapper.selectCounts(q);
        if(count == null) count = new BigDecimal(0);
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderItem::getOrderItemId,orderItems);
        List<OrderItem> list = orderItemService.list(wrapper);
        BigDecimal sum= BigDecimal.valueOf(0);
        if (list!=null&&list.size()>0){
            for (OrderItem orderItem : list) {
                sum = sum.add(orderItem.getBuyCounts());
            }
        }
        return sum.subtract(count);

    }


    @Override
    public BigDecimal getDataByOrderItmIds(List<String> orderItemIds, int i) {

        QueryWrapper<OrderReturnItem> q = new QueryWrapper<>();
        q.in("oi.order_item_id",orderItemIds)
                .eq("oi.is_out",1)
                .eq("o.is_delete",0)
                .eq("o.state",3);
        BigDecimal count= baseMapper.selectCounts(q);
        if (count==null){
            return new BigDecimal(0);
        }
        return count;
    }

    @Override
    public BigDecimal getDataByOrderItmIdAndState(String orderItemId, int isOut, int state) {
        QueryWrapper<OrderReturnItem> q = new QueryWrapper<>();
        q.eq("oi.order_item_id",orderItemId)
                .eq("oi.is_out",isOut)
                .eq("o.is_delete",0)
                .eq("o.state",state);
        BigDecimal count= baseMapper.selectCounts(q);
        if (count==null){
            return new BigDecimal(0);
        }
        return count;
    }
}
