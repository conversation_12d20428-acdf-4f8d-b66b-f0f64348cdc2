package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.OrderSelectPlan;
import scrbg.meplat.mall.entity.Orders;

import java.util.List;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-02-28
 */
public interface OrderSelectPlanService extends IService<OrderSelectPlan> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> queryWrapper);

    void create(OrderSelectPlan orderSelectPlan);

    void update(OrderSelectPlan orderSelectPlan);

    OrderSelectPlan getById(String id);

    void delete(String id);

    /**
     * 恢复计划数量
     * @param orderItemId
     */
    void recoverPlanNum(List<String> orderItemId);

    /**
     * 根据主订单Id查询计划信息，一个计划有多个订单，返回list(0)个
     * @param orderSn
     * @return
     */
    OrderSelectPlan getDataByOrderSn(String orderSn);

    /**
     * 获取可对账的合同或计划列表
     * @param jsonObject
     * @param q
     * @return
     */
    PageUtils getContactPlanPageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> q);

    /**
     * 获取可对账的合同或计划列表（获取供应商的）
     * @param jsonObject
     * @param orderSelectPlanLambdaQueryWrapper
     * @return
     */
    PageUtils supplierGetContactPlanPageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> orderSelectPlanLambdaQueryWrapper);

    OrderSelectPlan getDataByorderItemId(String orderItemId);

    /**
     * 获取可对账的项目部
     *  createType: 1 //1采购方  2供应商
     * @param jsonObject
     * @param orderSelectPlanLambdaQueryWrapper
     * @return
     */
    PageUtils supplierGetEnterprisePageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> orderSelectPlanLambdaQueryWrapper);

    PageUtils getContactSupplierPageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> orderSelectPlanLambdaQueryWrapper);

    /**
     * 跟根据合同编号查询计划数量
     * @param jsonObject
     * @return
     */
    List<Orders> selectOrderListByPlanNo(JSONObject jsonObject);
}
