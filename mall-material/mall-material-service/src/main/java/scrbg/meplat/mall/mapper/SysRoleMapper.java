package scrbg.meplat.mall.mapper;

import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.entity.SysMenu;
import scrbg.meplat.mall.entity.SysRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @描述：角色表 Mapper 接口
 * @作者: ye
 * @日期: 2023-12-21
 */
@Mapper
@Repository
public interface SysRoleMapper extends BaseMapper<SysRole> {


    /**
     * 根据pcwp角色列表查询角色以及对应(禁止直接调用mapper)
     * @param roleNames
     * @return
     */
    List<MallRole> getRoleMenuListByRoleNames(@Param("roleNames") List<String> roleNames);

    /**
     * 根据角色名称和所属平台获取菜单列表
     * @param roleNames
     * @param categoryType
     * @return
     */
    List<SysMenu> getMenuListByRoleNames(@Param("roleNames") List<String> roleNames,@Param("categoryType") Integer categoryType, @Param("showDev")  Integer showDev);

    /**
     * 根据pcwp角色列表查询角色以及对应(禁止直接调用mapper，不包含菜单)
     * @param roleNames
     * @return
     */
    List<MallRole> getRoleMenuListByRoleNameLists(@Param("roleNames") List<String> roleNames);
}