package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.Brand;
import scrbg.meplat.mall.service.BrandService;

import javax.swing.*;
import java.util.List;

/**
 * @描述：品牌控制类
 * @作者: y
 * @日期: 0000-11-10
 */
@RestController
@RequestMapping("/platform/brand")
@ApiSort(value = 500)
@Api(tags = "品牌")
public class BrandController {

    @Autowired
    public BrandService brandService;

    /**
     * 批量更新品牌信息
     *
     * @param
     * @return
     */
    @PostMapping("/updateBatchById")
    @ApiOperation(value = "批量更新品牌信息")
    public R update(@RequestBody List<Brand> brands) {
        for (Brand brand : brands) {
            if (brand.getSort()==null){
                brand.setSort(0);
            }
        }
        brandService.updateBatchById(brands);
        return R.success();
    }

    @PostMapping("/listByParameters")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "name", value = "品牌名", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "品牌类型", dataTypeClass = Integer.class),
    })
    public PageR<Brand> listByParameters(@RequestBody JSONObject jsonObject) {
        PageUtils page = brandService.queryPage(jsonObject, Wrappers.lambdaQuery(Brand.class));
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Brand> findById(String id) {
        Brand brand = brandService.getById(id);
        return R.success(brand);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody Brand brand) {
        brandService.create(brand);
        return R.success();
    }


    @PostMapping("/createList")
    @ApiOperation(value = "新增")
    public R createList(@RequestBody Brand brand) {
        brandService.create(brand);
        return R.success();
    }


    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody Brand brand) {
        brandService.update(brand);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        brandService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        brandService.deleteBatch(ids);
        return R.success();
    }


    /**
     * 通过图片条件获取该条件所有图片并分页
     *
     * @param
     * @return
     */
    @PostMapping("/findByBrandLogo")
    @ApiOperation(value = "根据条件获取图片并分页")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "classId", value = "分类Id", dataTypeClass = Integer.class),
            @DynamicParameter(name = "className", value = "分类名称", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "品牌状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "mallType", value = "商城类型", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "name", value = "品牌名称", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "品牌类型", dataTypeClass = Integer.class),
    })
    public PageR<Brand> findByBrandLogo(@RequestBody JSONObject jsonObject) {
        PageUtils page = brandService.queryBrandLogo(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }

    @PostMapping("/updateByPublish")
    @ApiOperation(value = "批量启用")
    public R updatePublish(@RequestBody List<String> ids) {
        brandService.updateByPublish(ids, "1");
        return R.success();
    }

    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量停用")
    public R updateNotPublish(@RequestBody List<String> ids) {
        brandService.updateByPublish(ids, "0");
        return R.success();
    }


}

