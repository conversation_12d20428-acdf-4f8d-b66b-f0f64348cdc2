package scrbg.meplat.mall.entity.pcwpmq;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
/**
 * PCWP机构表实体类
 */
@Data
@TableName("pcwp_org")
public class PcwpOrg {
    /**
     * 主键（PCWP机构ID）
     */
    @TableField("orgid")
    private String orgid;

    /**
     * 机构名称
     */
    @TableField("orgname")
    private String orgname;

    /**
     * PCWP上级机构ID
     */
    @TableField("parentorgid")
    private String parentorgid;

    /**
     * 机构类型: -1:未知 1:集团 2:分公司 3:子公司 4:经理部 5:项目部 6:股份 7:事业部
     */
    @TableField("orgtype")
    private Integer orgtype;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 状态: 0: 不可用, 1: 可用
     */
    @TableField("status")
    private Integer status;

    /**
     * 最后修改时间（最后更新时间）
     */
    @TableField("lastmodifytime")
    private String lastmodifytime;

    /**
     * 创建时间
     */
    @TableField("createtime")
    private String createtime;

    /**
     * HR的机构ID（区分大小写）
     */
    @TableField("hrorgid")
    private String hrorgid;

    /**
     * HR上级机构ID（区分大小写）
     */
    @TableField("hrparentorgid")
    private String hrparentorgid;

    /**
     * 简称
     */
    @TableField("shortname")
    private String shortname;

    /**
     * 简码
     */
    @TableField("shortcode")
    private String shortcode;

    /**
     * 机构属性: 0: 托管, 1: 直属
     */
    @TableField("orgproperty")
    private Integer orgproperty;

    /**
     * 老系统机构ID
     */
    @TableField("oldorgid")
    private String oldorgid;

    /**
     * 老系统机构名称
     */
    @TableField("oldorgname")
    private String oldorgname;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    @TableField("mdmstate")
    private Integer mdmstate;
}