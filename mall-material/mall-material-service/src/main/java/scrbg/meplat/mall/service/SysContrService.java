package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.SysContr;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SysContr;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：我的控制 服务类
 * @作者: ye
 * @日期: 2024-04-28
 */
public interface SysContrService extends IService<SysContr> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysContr> queryWrapper);

        void create(SysContr sysContr);
        void update(SysContr sysContr);
        SysContr getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
