package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.order.AddSettlementAmountParentDTO;
import scrbg.meplat.mall.dto.order.CheckReconciliationIsCancellationVO;
import scrbg.meplat.mall.dto.reconciliation.MaterialReconciliationUpdateDTO;
import scrbg.meplat.mall.entity.MaterialReconciliation;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @描述：物资验收 服务类
 * @作者: ye
 * @日期: 2023-07-26
 */
public interface MaterialReconciliationService extends IService<MaterialReconciliation> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> queryWrapper);

    String create(MaterialReconciliation materialReconciliation,int isNotPush);

    void update(MaterialReconciliation materialReconciliation);

    MaterialReconciliation getById(String id);

    void delete(String id);

    /**
     * 新增对账单
     *
     * @param materialReconciliation
     * @return
     */
    String createMaterialReconciliation(MaterialReconciliation materialReconciliation);

    /**
     * 回滚对账单新增
     *
     * @param keyId
     */
    void rollBackReconciliationCreate(String keyId);

    /**
     * 根据编号获取明细
     *
     * @param sn
     * @return
     */
    MaterialReconciliation materialReconciliationGetBySn(String sn);

    /**
     * 批量提交审核
     *
     * @param ids
     */
    void materialReconciliationSubmitCheck(List<String> ids);

    /**
     * 审核对账单
     *
     * @param dto
     * @param keyId
     * @param farArg
     * @return
     */
    void materialReconciliationAuditPlan(AuditDTO dto, String keyId, StringBuilder farArg,int isNotPush);

    /**
     * 修改对账
     *
     * @param dto
     */
    void materialReconciliationUpdate(MaterialReconciliationUpdateDTO dto,int isNotPush);

    /**
     * 删除对账
     *
     * @param reconciliationId
     * @param idStr
     */
    void materialReconciliationDelete(String reconciliationId, String idStr, int isNotPush);

    /**
     * 删除草稿清除对账单关联id
     *
     * @param reconciliationId
     */
    void clearRelevanceId(String reconciliationId);

    /**
     * 确认单据
     *
     * @param reconciliationId
     */
    void materialReconciliationAffirm(String reconciliationId);

    /**
     * 判断是否可作废
     *
     * @param reconciliationId
     * @return
     */
    Boolean isCancellationByRelevanceId(String reconciliationId);

    /**
     * 作废单据
     *
     * @param reconciliationId
     * @param result
     * @param keyId
     */
    void materialReconciliationCancellation(String reconciliationId, String result, String keyId, int isNotPush);

    /**
     * 导出账单（采购员）
     *
     * @param reconciliationId
     * @param response
     */
    void outputExcel(String reconciliationId, HttpServletResponse response);

    /**
     * 查询对账单列表（供应商）
     *
     * @param jsonObject
     * @param materialReconciliationLambdaQueryWrapper
     * @return
     */
    PageUtils supplierListByEntity(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper);

    /**
     * 供应商新增对账
     *
     * @param dto
     * @return
     */

    String materialReconciliationSupplierCreate(MaterialReconciliation dto, StringBuilder result,int isNotPush);

    /**
     * 批量提交
     *
     * @param ids
     */
    void materialReconciliationSubmit(List<String> ids);

    /**
     * 修改对账供应商
     *
     * @param dto
     */
    void materialReconciliationSupplierUpdate(MaterialReconciliationUpdateDTO dto, StringBuilder result,int isNotPush);

    /**
     * 供应商确认单据
     *
     * @param reconciliationId
     */
    void materialReconciliationSupplierAffirm(String reconciliationId);

    /**
     * 对账单增加已结算金额
     *
     * @param dto
     */
    void addSettlementAmount(AddSettlementAmountParentDTO dto);

    /**
     * 回滚对账单结算金额
     *
     * @param keyId
     * @param flag
     */
    void rollBackAddSettlementAmount(String keyId, Integer flag);


    /**
     * 主动推送对账单
     *
     * @param reconciliationId
     * @return
     */
    void materialReconciliationPushAcceptance(String reconciliationId);

    /**
     * 根据对账单id查询对账单状态
     *
     * @param reconciliationId
     * @return
     */
    CheckReconciliationIsCancellationVO checkReconciliationIsCancellation(String reconciliationId);


    /**
     * 定时查询待推送数据
     *
     * @param
     */
    void pushMaterialReconciliationData();

    /**
     * 定时查询待推送数据
     *
     * @param materialReconciliation
     */
    void pushMaterialReconciliationDataOne(MaterialReconciliation materialReconciliation);

    /**
     * 商品结算报表（供应商方）
     *
     * @param jsonObject
     * @param wrapper
     * @return
     */
    PageUtils getShopManageSettlementFromList(JSONObject jsonObject, QueryWrapper<MaterialReconciliation> wrapper);


    /**
     * 商品结算报表（平台管理方）
     *
     * @param jsonObject
     * @param wrapper
     * @return
     */
    PageUtils getPlatformSettlementFromList(JSONObject jsonObject, QueryWrapper<MaterialReconciliation> wrapper);


    /**
     * 大宗临购新增对账单
     *
     * @param dto
     * @param farArg
     * @return
     */
    String materialReconciliationSTCreate(MaterialReconciliation dto, StringBuilder farArg);

    /**
     * 供应商新增大宗临购对账
     *
     * @param dto
     * @param farArg
     * @return
     */
    String materialReconciliationSTSupplierCreate(MaterialReconciliation dto, StringBuilder farArg,int isNotPush);

    /**
     * 大宗临购修改对账
     *
     * @param dto
     */
    void materialReconciliationSTUpdate(MaterialReconciliationUpdateDTO dto);

    /**
     * 修改大宗临购（供应商）
     *
     * @param dto
     */
    void materialReconciliationSTSupplierUpdate(MaterialReconciliationUpdateDTO dto,int isNotPush);

    /**
     * 删除大宗临购
     *
     * @param reconciliationId
     * @param idStr
     */
    void materialReconciliationSTDelete(String reconciliationId, String idStr);

    /**
     * 大宗临购作废单据
     *
     * @param reconciliationId
     * @param result
     * @param keyId
     */
    void materialReconciliationSTCancellation(String reconciliationId, String result, String keyId);

    /**
     * 审核大宗临购对账单
     *
     * @param dto
     * @param keyId
     * @param farArg
     * @return
     */
    Integer materialReconciliationSTAuditPlan(AuditDTO dto, String keyId, StringBuilder farArg);

    /**
     * 大宗临购主动推送对账单
     *
     * @param reconciliationId
     */
    void materialReconciliationSTPushAcceptance(String reconciliationId);

    /**
     * 导出大宗临购对账账单
     *
     * @param reconciliationId
     * @param response
     */
    void outputSTExcel(String reconciliationId, HttpServletResponse response);

    /**
     * 批量提交大宗临购审核
     *
     * @param ids
     */
    void materialReconciliationSTSubmitCheck(List<String> ids);


    /**
     * 根据远程机构id生成对账单编号
     *
     * @param orgId
     * @return
     */
    String createMaterialPlanNo(String orgId);

    List<MaterialReconciliation> ListByIds(List<String> reconciliationIds);

    void updateMaterialInfo(ArrayList<Map<String, Object>> dtls);

    PageUtils materialReconciliationLedgerSupplierBill(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper);
}
