package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.dto.content.UpdateContentStateDTO;
import scrbg.meplat.mall.entity.Content;
import scrbg.meplat.mall.entity.ContentLog;

import java.util.List;

/**
 * @描述：内容 服务类
 * @作者: sund
 * @日期: 2022-11-07
 */
public interface ContentService extends IService<Content> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Content> queryWrapper);

    void create(Content content);

    void update(Content content);

    Content getById(String id);

    void delete(String id);

    /**
     * 批量导入内容
     */
    void importBatchContent(List<Content> content);

    /**
     * 根据内容id修改内容信息
     */
    void updateContentInfo(Content content);

    /**
     * 根据ids批量逻辑删除
     */
    void removeLogicBatch(List<String> ids);

    /**
     * 根据内容id批量修改内容发布状态
     */
    void updateContentState(UpdateContentStateDTO dto);

    /**
     * 批量发布
     * @param ids
     * @param s
     */
    void updateByPublish(List<String> ids, String s);


    PageUtils queryContentPage(JSONObject jsonObject, LambdaQueryWrapper<Content> objectLambdaQueryWrapper);

    PageUtils queryPublicContentPage(JSONObject jsonObject, LambdaQueryWrapper<Content> objectLambdaQueryWrapper);

    void saveContent(Content content);

    List<Content> findAllContent(String programaKey,String state);

    void updateContent(Content content);

    List<ContentLog> findListByContentId(String id);

}
