package scrbg.meplat.mall.mapper;

import org.apache.ibatis.annotations.Delete;
import scrbg.meplat.mall.entity.ShudaoEnterprise;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

/**
 * @描述： Mapper 接口
 * @作者: ye
 * @日期: 2024-01-29
 */
@Mapper
@Repository
public interface ShudaoEnterpriseMapper extends BaseMapper<ShudaoEnterprise> {

    @Delete("delete from shudao_enterprise where shudao_enterprise_id = #{id}")
    void deleteReal(String id);
}