package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.dto.free.TotalCountFreeVO;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.PlatformYearFeeMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;
import scrbg.meplat.mall.vo.user.LoginVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @描述：平台年费表 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
@Service
public class PlatformYearFeeServiceImpl extends ServiceImpl<PlatformYearFeeMapper, PlatformYearFee> implements PlatformYearFeeService {

    @Autowired
    PlatformYearFeeRecordService platformYearFeeRecordService;

    @Autowired
    StationMessageService stationMessageService;

    @Autowired
    ShoppingCartService shoppingCartService;

    @Autowired
    ProductService productService;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    UserService userService;

    @Autowired
    ShopService shopService;

    @Autowired
    SystemParamService systemParamService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformYearFee> q) {
        Integer serveType = (Integer) jsonObject.get("serveType");
        Integer outTime = (Integer) jsonObject.get("outTime");
        String shopName = (String) jsonObject.get("shopName");
        String enterpriseName = (String) jsonObject.get("enterpriseName");
        String startGmtCreate = (String) jsonObject.get("startGmtCreate");
        String platformYearFeeNu = (String) jsonObject.get("platformYearFeeNu");
        String endGmtCreate = (String) jsonObject.get("endGmtCreate");
        String startGmtModified = (String) jsonObject.get("startGmtModified");
        String endGmtModified = (String) jsonObject.get("endGmtModified");
        String startServeEndTime = (String) jsonObject.get("startServeEndTime");
        String endServeEndTime = (String) jsonObject.get("endServeEndTime");
        String keywords = (String) jsonObject.get("keywords");
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        ArrayList ids = (ArrayList) jsonObject.get("ids");
        q.in(CollectionUtils.isNotEmpty(ids), PlatformYearFee::getPlatformYearFeeId, ids);
        Integer freeQueryType = (Integer) jsonObject.get("freeQueryType");
        q.eq(freeQueryType != null, PlatformYearFee::getServeType, freeQueryType);
        q.eq(serveType != null, PlatformYearFee::getServeType, serveType);
        q.eq(outTime != null, PlatformYearFee::getOutTime, outTime);
        q.like(platformYearFeeNu != null, PlatformYearFee::getPlatformYearFeeNu, platformYearFeeNu);
        q.orderByDesc(orderBy == 0, PlatformYearFee::getGmtCreate);
        q.orderByDesc(orderBy == 1, PlatformYearFee::getGmtModified);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(PlatformYearFee::getPlatformYearFeeNu, keywords)
                        .or()
                        .like(PlatformYearFee::getEnterpriseName, keywords)
                        .or()
                        .like(PlatformYearFee::getShopName, keywords);
            });
        }
        q.between(StringUtils.isNotEmpty(startServeEndTime) && StringUtils.isNotEmpty(endServeEndTime), PlatformYearFee::getServeEndTime, startServeEndTime, endServeEndTime);
        q.between(StringUtils.isNotEmpty(startGmtCreate) && StringUtils.isNotEmpty(endGmtCreate), PlatformYearFee::getGmtCreate, startGmtCreate, endGmtCreate);
        q.like(StringUtils.isNotEmpty(enterpriseName) && StringUtils.isNotEmpty(enterpriseName), PlatformYearFee::getEnterpriseName, enterpriseName);
        q.like(StringUtils.isNotEmpty(shopName) && StringUtils.isNotEmpty(shopName), PlatformYearFee::getShopName, shopName);
        q.between(StringUtils.isNotEmpty(startGmtModified) && StringUtils.isNotEmpty(endGmtModified), PlatformYearFee::getGmtModified, startGmtModified, endGmtModified);

        IPage<PlatformYearFee> page = this.page(new Query<PlatformYearFee>().getPage(jsonObject), q);
        return new PageUtils(page);
    }

    @Override
    public void create(PlatformYearFee platformYearFee) {
        super.save(platformYearFee);//调用父类方法即可，也可以baseMapper.insert
    }

    @Override
    public void update(PlatformYearFee platformYearFee) {
        super.updateById(platformYearFee);
    }

    @Override
    public PlatformYearFee getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    /**
     * 定时任务：更新已过期年费状态并处理相关服务
     * 每天检查前一天到期的年费记录，将其标记为过期并停用相应服务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateYearFeeOutTime() {
        // 定时器今天执行，查询到期时间是昨天的，设置为欠费
        List<PlatformYearFee> list = lambdaQuery()
                .isNotNull(PlatformYearFee::getServeEndTime) //确保服务结束时间不为空
//                .eq(PlatformYearFee::getPlatformYearFeeId,"1913117527663751169")
                .eq(PlatformYearFee::getServeEndTime, LocalDate.now().minusDays(1)) //查询昨天到期的记录
                .list();
        List<Map<String, String>> ids = new ArrayList<>();
        // 处理每条到期记录
        for (PlatformYearFee platformYearFee : list) {
            LocalDate serveEndTime = platformYearFee.getServeEndTime();
            LocalDate currentDate = LocalDate.now();
            // 只有当前时间在截止时间之后属于过期
            if (currentDate.isAfter(serveEndTime)) { // 之后
                platformYearFee.setOutTime(0);
                if (platformYearFee.getShopId() != null) {
                    HashMap<String, String> map = new HashMap<>();
                    map.put("shopId", platformYearFee.getShopId());
                    map.put("enterpriseId", platformYearFee.getEnterpriseId());
                    map.put("serviceType", platformYearFee.getServeType().toString());
                    map.put("serveEndTime", serveEndTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    ids.add(map);
                }
                update(platformYearFee);
            }
        }
        // 处理店铺如果过期，直接让对应服务停止
        for (Map<String, String> map : ids) {
            String shopId = map.get("shopId");
            String enterpriseId = map.get("enterpriseId");
            String serviceType = map.get("serviceType");
            StationMessageReceiveVO vo = new StationMessageReceiveVO();

            if ("1".equals(serviceType)) {// 店铺服务年费
                // 更新店铺下所有商品为不显示状态
                productService.lambdaUpdate().eq(Product::getShopId, shopId)
                        .set(Product::getShowState, 1).update();
                shopService.lambdaUpdate().eq(Shop::getShopId, map.get("shopId"))// 更新店铺状态为停用
                        .set(Shop::getState, 0).update();
                // 设置站内信通知标题和内容
                vo.setTitle("店铺服务年费到期提醒");
                vo.setContent("您好，您的店铺服务已到期，请前往缴费中心缴费。");
                vo.setContent("尊敬的用户：您好！您的店铺服务年费已到期。因此，您目前将无法接收新的订单。" +
                        "为了尽快恢复店铺的正常运营，请您尽快重新缴纳年费。缴费成功后，您的店铺将立即恢复接收新订单的功能。" +
                        "请知悉并尽快处理，以免对您的业务造成不必要的影响。如有任何疑问或需要帮助，请随时联系我们" +
                        "\n\n您的店铺服务年费到期时间：" + map.get("serveEndTime"));

            } else {
                vo.setTitle("招标服务到期通知！");
                vo.setContent("您好，您的店铺服务已到期，请前往缴费中心缴费。");
            }
            ArrayList<String> id = new ArrayList<>(1);// 发送站内信通知
            id.add(enterpriseId);
            vo.setEnterpriseIdList(id);
            stationMessageService.createSystemBatch(vo);
        }
    }

    /**
     * 年费到期提醒定时任务
     * 根据配置的提前提醒天数，发送年费到期提醒通知
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void yearFeeExpirationReminder() {
        try {
            SystemParam systemParam = systemParamService
                    .lambdaQuery()
                    .eq(SystemParam::getCode, PublicEnum.PlatformYearFeeExpirationReminderDays.getRemark()).one();
//            String reminderDaysStr = PublicEnum.PlatformYearFeeExpirationReminderDays.getRemark();
            String reminderDaysStr = systemParam != null ? systemParam.getKeyValue() : null;
            if (StringUtils.isEmpty(reminderDaysStr)) {
                reminderDaysStr = "30";
            }
            int reminderDays = Integer.parseInt(reminderDaysStr);// 计算目标到期日期（当前日期减去提醒天数）
            LocalDate targetDate = LocalDate.now().minusDays(reminderDays);
            // 查询即将到期的年费记录
            List<PlatformYearFee> expiringFees = lambdaQuery()
                    .isNotNull(PlatformYearFee::getServeEndTime)
                    .eq(PlatformYearFee::getOutTime, 1)  // 服务未过期
                    .eq(PlatformYearFee::getState, 0)    // 状态正常
                    .eq(PlatformYearFee::getIsDelete, 0) // 未删除
                    .eq(PlatformYearFee::getServeEndTime, targetDate) // N天后到期
                    .list();
            // 获取自营店铺ID
            String isBusinessShopId = stringRedisTemplate.opsForValue().get("isBusinessShopId");
            for (PlatformYearFee fee : expiringFees) {
                // 如果企业ID为空，跳过此记录
                if (fee.getEnterpriseId() == null) {
                    continue;
                }
                // 跳过自营店铺，仅对第三方商铺收取年费
                if (isBusinessShopId != null && isBusinessShopId.equals(fee.getEnterpriseId())) {
                    continue;
                }
                try {
                    //数据插入PlatformYearFeeRecord数据
                    PlatformYearFeeRecord record = new PlatformYearFeeRecord();
//                    record.setPaymentRecordId(IdWorker.getIdStr()); // 生成主键ID
                    record.setPaymentRecordUn(IdWorker.getIdStr()); // 生成缴费编号
                    record.setShopId(fee.getShopId());
                    record.setShopName(fee.getShopName());
                    record.setEnterpriseId(fee.getEnterpriseId());
                    record.setEnterpriseName(fee.getEnterpriseName());
                    // 设置记录类型为年费提醒（这里1表示店铺年费）
                    record.setRecordType(1);
                    // 设置为系统自动生成的提醒记录，待缴费状态
                    record.setState(0); // 假设0为草稿/待缴费状态
                    // 记录年费的有效期
                    LocalDate newStartDate = fee.getServeEndTime().plusDays(1);
                    record.setServeStartTime(newStartDate); // 当前年费结束时间后延一天作为新的开始时间
                    // 计算年费的有效期一年（或根据配置获取）
                    LocalDate newEndDate = fee.getServeEndTime().plusYears(1);
                    record.setServeEndTime(newEndDate);
                    // 根据配置获取年费金额（这里仅为示例，实际应从系统配置或其他地方获取）
                    SystemParam systemParam2 = systemParamService
                            .lambdaQuery()
                            .eq(SystemParam::getCode, PublicEnum.PlatformShopYearFeeAmount.getRemark()).one();
                    BigDecimal yearFeeAmount = new BigDecimal(systemParam2.getKeyValue());
                    record.setPayAmount(yearFeeAmount);
                    record.setPaymentDuration(1);//缴费时长一年
                    record.setPaymentDurationType(4);//1天2周3月4年
                    // 标记为系统自动创建的记录
                    record.setPayStatus(1); // 假设1表示续费
                    record.setPayType(1);// 假设1表示线下支付方式
                    // 记录备注
                    record.setRemarks("系统自动创建的年费到期提醒记录，需要续费");
                    // 保存年费提醒记录
                    platformYearFeeRecordService.create(record);

                    String message = "";
                    String title = "";
                    if (fee.getServeType() == 1 && fee.getShopId() != null) { // 店铺年费
                        // 获取店铺信息
                        Shop shop = shopService.getById(fee.getShopId());
                        String shopName = shop != null ? shop.getShopName() : "未知店铺";
                        title = "店铺服务年费续费提醒";
                        message = String.format("尊敬的用户：您好！您的店铺服务年费即将到期。为了确保您的服务不受影响，并继续享受我们提供的优质保障，请您尽快续费。\n\n您的店铺服务年费到期时间：%s",
                                fee.getServeEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    }
                    // 发送站内信
                    StationMessageReceiveVO messageVO = new StationMessageReceiveVO();
                    messageVO.setTitle(title);
                    messageVO.setContent(message);

                    ArrayList<String> enterpriseIds = new ArrayList<>();
                    enterpriseIds.add(fee.getEnterpriseId());
                    messageVO.setEnterpriseIdList(enterpriseIds);
                    stationMessageService.createSystemBatch(messageVO);// 调用站内信服务发送消息
                } catch (Exception e) {
                    log.error("发送年费到期提醒失败，企业ID: " + fee.getEnterpriseId() + ", 年费ID: " + fee.getPlatformYearFeeId(), e);
                }
            }
        } catch (Exception e) {
            log.error("年费到期提醒定时任务执行异常", e);
            throw e;
        }
    }

    /**
     * 检查是否过期
     */
    @Override
    public void checkYearShopIsOut(String shopId) {
        PlatformYearFee platformYearFee = lambdaQuery().eq(PlatformYearFee::getShopId, shopId)
                .eq(PlatformYearFee::getServeType, 1)
                .one();
        if (platformYearFee == null) throw new BusinessException("未查询到年度服务费数据！");
        LocalDate serveEndTime = platformYearFee.getServeEndTime();
        if (serveEndTime == null) {
            throw new BusinessException("供应商店铺服务已到期，已停止服务。");
        }
        LocalDate currentDate = LocalDate.now();
        // 只有当前时间在截止时间之后属于过期
        if (currentDate.isAfter(serveEndTime) || currentDate.equals(serveEndTime)) { // 之后
            throw new BusinessException("供应商店铺服务已到期，已停止服务。");
        }
    }

    /**
     * 检查招标是否过期（权限控制使用）
     */
    @Override
    public void checkYearStandardIsOut(LoginVO loginVO) {
        EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, loginVO.getLocalOrgId())
                .select(EnterpriseInfo::getEnterpriseId,
                        EnterpriseInfo::getEnterpriseName,
                        EnterpriseInfo::getShuDaoFlag,
                        EnterpriseInfo::getEnterpriseType,
                        EnterpriseInfo::getInteriorId,
                        EnterpriseInfo::getIsNoSupplierAudit).one();
        if (supplier == null) {
            throw new BusinessException("未查询到机构信息！");
        }
        // 如果是蜀道/个人企业不检验
        if (supplier.getShuDaoFlag() == 1 || supplier.getInteriorId() != null || supplier.getEnterpriseType() == 2 || loginVO.getShopId() == null) {
            return;
        }
        PlatformYearFee platformYearFee = lambdaQuery().eq(PlatformYearFee::getShopId, loginVO.getShopId())
                .eq(PlatformYearFee::getServeType, 2)
                .one();
        if (platformYearFee != null) {
            if (platformYearFee.getServeEndTime() == null) {
            } else {
                LocalDate serveEndTime = platformYearFee.getServeEndTime();
                LocalDate currentDate = LocalDate.now();
                if (currentDate.isAfter(serveEndTime) || currentDate.equals(serveEndTime)) {
                    throw new BusinessException(700104, "电子招标服务已到期，请前往供应商平台-服务及费用管理缴费！");
                } else {
                    // 未过期
                    LocalDate newDate = LocalDate.now().plusDays(30);
                    if (newDate.isAfter(serveEndTime)) {
                        throw new BusinessException(700105, "电子招标服务即将到期！请前往供应商平台-服务及费用管理续费，到期时间：" + platformYearFee.getServeEndTime());
                    }
                }
            }
        }
    }

    /**
     * 检查是否还有一个月过期，登陆或者进去首页提示
     */
    @Override
    public void checkYearMonthIsOut(LoginVO loginVO) {
        if (loginVO.getIsExternal() == 1) {
            EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, loginVO.getLocalOrgId())
                    .select(EnterpriseInfo::getEnterpriseId,
                            EnterpriseInfo::getEnterpriseName,
                            EnterpriseInfo::getShuDaoFlag,
                            EnterpriseInfo::getEnterpriseType,
                            EnterpriseInfo::getInteriorId,
                            EnterpriseInfo::getIsNoSupplierAudit).one();
            if (supplier == null) {
                throw new BusinessException("未查询到机构信息！");
            }
            // 如果是蜀道/个人企业不检验
            if (supplier.getShuDaoFlag() == 1 || supplier.getInteriorId() != null || supplier.getEnterpriseType() == 2 || loginVO.getShopId() == null) {
                return;
            }
            // 查询招标
            Integer coun2t = userService.lambdaQuery().eq(User::getUserId, loginVO.getUserId())
                    .eq(User::getIsShowBid, 1).count();
            if (coun2t > 0) {
                PlatformYearFee platformBidFree = lambdaQuery().eq(PlatformYearFee::getEnterpriseId, loginVO.getLocalOrgId())
                        .eq(PlatformYearFee::getServeType, 2)
                        .one();
                if (platformBidFree == null) {
                    loginVO.setIsShowBid(1);
                } else {
                    LocalDate serveEndTime = platformBidFree.getServeEndTime();
                    if (serveEndTime == null) {
                        loginVO.setIsShowBid(1);
                    } else {
//                        LocalDate currentDate = LocalDate.now();
//                        // 只有当前时间在截止时间之后属于过期
//                        if (currentDate.isAfter(serveEndTime) || currentDate.equals(serveEndTime)) { // 之后
//                            loginVO.setIsShowBid(1);
//                        }
                    }
                }
            }
            // 这里不再校验店铺是否审核通过，如果审核通过会自动新增年费记录表。
            PlatformYearFee platformYearFee = lambdaQuery().eq(PlatformYearFee::getShopId, loginVO.getShopId())
                    .eq(PlatformYearFee::getServeType, 1)
                    .one();
            if (platformYearFee != null) {
                if (platformYearFee.getServeEndTime() == null) {
                    throw new BusinessException(700199, "店铺初审已通过！是否前往缴费？");
                } else {
                    LocalDate serveEndTime = platformYearFee.getServeEndTime();
                    LocalDate currentDate = LocalDate.now();
                    if (currentDate.isAfter(serveEndTime) || currentDate.equals(serveEndTime)) {
                        // 不提示
                        throw new BusinessException(700101, "店铺服务已到期，请前往供应商平台-服务及费用管理缴费！");
                    } else {
                        // 未过期
                        LocalDate newDate = LocalDate.now().plusDays(30);
                        if (newDate.isAfter(serveEndTime)) {
                            throw new BusinessException(700102, "店铺服务即将到期！请前往供应商平台-服务及费用管理续费，到期时间：" + platformYearFee.getServeEndTime());
                        }
                    }
                }
            } else {
                throw new BusinessException(700199, "店铺初审已通过！是否前往缴费？");
            }
        }
    }

    /**
     * 批量导入当前已审核的店铺
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importThisShopYearFee() {
        List<Shop> list = shopService.lambdaQuery().eq(Shop::getAuditStatus, 1)
                .eq(Shop::getIsInternalShop, 0)
                .select(Shop::getShopId, Shop::getShopName, Shop::getEnterpriseId).list();
        for (Shop shop : list) {
            EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, shop.getEnterpriseId())
                    .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getShuDaoFlag).one();
            if (one.getInteriorId() != null || one.getShuDaoFlag() == 1) {
                continue;
            }
            PlatformYearFee pt = new PlatformYearFee();
            pt.setPlatformYearFeeNu(IdWorker.getIdStr());
            pt.setShopId(shop.getShopId());
            pt.setShopName(shop.getShopName());
            pt.setEnterpriseId(one.getEnterpriseId());
            pt.setEnterpriseName(one.getEnterpriseName());
            pt.setServeEndTime(null);
            pt.setServeType(1);
            pt.setOutTime(0);
            save(pt);
        }
    }

    @Override
    public PlatformYearFee findBySn(String sn) {
        PlatformYearFee one = lambdaQuery().eq(PlatformYearFee::getPlatformYearFeeNu, sn).one();
        if (one != null) {
            List<PlatformYearFeeRecord> dtlList = new ArrayList<>();
            if (one.getServeType() == 1) {
                dtlList = platformYearFeeRecordService.lambdaQuery().eq(PlatformYearFeeRecord::getShopId, one.getShopId())
                        .orderByDesc(PlatformYearFeeRecord::getGmtCreate)
                        .list();
            } else {
                dtlList = platformYearFeeRecordService.lambdaQuery().eq(PlatformYearFeeRecord::getEnterpriseId, one.getEnterpriseId()).list();
            }
            one.setDtls(dtlList);
        }
        return one;
    }

    /**
     * 平台根据编号获取数据
     *
     * @param sn
     * @return
     */
    @Override
    public PlatformYearFee platformFindBySn(String sn) {
        PlatformYearFee one = lambdaQuery().eq(PlatformYearFee::getPlatformYearFeeNu, sn).one();
        if (one != null) {
            List<PlatformYearFeeRecord> dtlList = new ArrayList<>();
            if (one.getServeType() == 1) {
                dtlList = platformYearFeeRecordService.lambdaQuery().eq(PlatformYearFeeRecord::getShopId, one.getShopId()).ne(PlatformYearFeeRecord::getState, 0).list();
            } else {
                dtlList = platformYearFeeRecordService.lambdaQuery().eq(PlatformYearFeeRecord::getEnterpriseId, one.getEnterpriseId()).ne(PlatformYearFeeRecord::getState, 0).list();
            }
            one.setDtls(dtlList);
        }
        return one;
    }

    /**
     * 平台统计报表
     *
     * @param jsonObject
     * @return
     */
    @Override
    public PageUtils totalCountFree(JSONObject jsonObject) {
        Object page = jsonObject.get("page");
        if (page == null) {
            jsonObject.put("page", 1);
        }
        Page<TotalCountFreeVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        IPage<TotalCountFreeVO> resIPage = baseMapper.totalCountFree(pages, jsonObject);
        return new PageUtils<>(resIPage);
    }

    @Override
    public PlatformYearFee getByEnterpriseId(String enterpriseId) {
        return lambdaQuery().eq(PlatformYearFee::getEnterpriseId, enterpriseId)
                .one();
    }
}