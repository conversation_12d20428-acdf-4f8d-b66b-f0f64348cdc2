package scrbg.meplat.mall.util;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @create 2023-01-06 11:21
 */
public class RegexUtils {

    /**
     * 手机号正则
     */
    public static final String PHONE_REGEX = "^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\\d{8}$";

    /**
     * 是否是无效手机格式
     * @param phone 要校验的手机号
     * @return true:是无效，false：不是无效
     */
    public static boolean isPhoneInvalid(String phone){
        if(phone == null || phone.length() != 11) {
            return true;
        }
//        return mismatch(phone, PHONE_REGEX);
        return false;
    }

    // 校验是否不符合正则格式
    private static boolean mismatch(String str, String regex){
        if (StrUtil.isBlank(str)) {
            return true;
        }
        return !str.matches(regex);
    }

}
