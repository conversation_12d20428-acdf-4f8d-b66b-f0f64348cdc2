package scrbg.meplat.mall.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import scrbg.meplat.mall.entity.SupplierReconciliation;
import scrbg.meplat.mall.entity.SupplierReconciliationDtl;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SupplierReconciliationDtl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.entity.SupplierReconciliationDtlExcel;
import scrbg.meplat.mall.vo.supplier.SupplierReconciliationVo;

import java.util.List;

/**
 * @描述：物资验收明细 服务类
 * @作者: ye
 * @日期: 2023-08-15
 */
public interface SupplierReconciliationDtlService extends IService<SupplierReconciliationDtl> {

    /**
     * 查询发货单和退货单可生成的二级对账单
     * @param jsonObject
     * @param queryWrapper
     * @return
     */
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SupplierReconciliationDtl> queryWrapper);

    /**
     * 新增二级对账单项
     * @param supplierReconciliationDtl
     */
    void create(SupplierReconciliationDtl supplierReconciliationDtl);

    /**
     * 修改二级对账单项
     * @param supplierReconciliationDtl
     */
    void update(SupplierReconciliationDtl supplierReconciliationDtl);

    /**
     * 根据id查询二级对账单项
     * @param id
     * @return
     */
    SupplierReconciliationDtl getById(String id);

    /**
     * 删除二级对账单项
     * @param id
     */
    void delete(String id);

    /**
     * 作废二级对账单项
     * @param id
     */
    void cancellation(String id);

    /**
     * 批量删除二级对账单项
     * @param ids
     */
    void deleteBatch(List<String> ids);

    /**
     * 查询发货单和退货单可生成的二级对账单
     * @param jsonObject
     * @return
     */
    PageUtils getReconciliationDtlList(JSONObject jsonObject, QueryWrapper<SupplierReconciliationDtl> supplierReconciliationQueryWrapper);

    /**
     * 保存二级对账单和明细
     * @param dto
     * @param dtls
     */
    void saveList(SupplierReconciliation dto, List<SupplierReconciliationDtl> dtls);

    /**
     * 根据对账单ID查询二级对账单项
     * @param billId
     * @return
     */
    List<SupplierReconciliationDtl> getListByBillId(String billId);

    /**
     * 根据对账单ID作废二级对账单项
     * @param billId
     */
    void nullifyDataByBillId(String billId);

    /**
     * 根据对账单ID批量查询收料明细
     *
     * @param billId 对账单ID
     * @return 导出对账单明细列表
     */
    List<SupplierReconciliationDtlExcel> getReceiptDetailByBillId(String billId);
}
