package scrbg.meplat.mall.service.impl;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ProductCompareItemMapper;
import scrbg.meplat.mall.service.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-12-12
 */
@Service
public class ProductCompareItemServiceImpl extends ServiceImpl<ProductCompareItemMapper, ProductCompareItem> implements ProductCompareItemService {
    @Resource
    private ProductCompareService compareService;
    @Resource
    private ProductService productService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private ShopService shopService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductCompareItem> queryWrapper) {
        IPage<ProductCompareItem> page = this.page(
                new Query<ProductCompareItem>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ProductCompareItem productCompareItem) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(productCompareItem);
    }

    @Override
    public void update(ProductCompareItem productCompareItem) {
        super.updateById(productCompareItem);
    }


    @Override
    public ProductCompareItem getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ProductCompareItem> createByProductIds(List<String> productIds,String compareId) {
        if (CollectionUtils.isEmpty(productIds)) {
            throw new BusinessException("请选择比价商品");
        }
        // 限制商品数量2-4
        if (productIds.size() < 2 || productIds.size() > 4) {
            throw new BusinessException("请选择2到4个待比价商品");
        }
        List<ProductCompareItem> addList = new ArrayList<>();
        // 先加主表
        // 比价商品名称列表
        ProductCompare productCompare;
        StringBuilder compareName = new StringBuilder();
        if (StringUtils.isBlank(compareId)){
            productCompare = compareService.createUseCurrentUser();
        }else {
            //  更新用
            productCompare = compareService.getById(compareId);
            productCompare.setCompareTime(new Date());

        }
        for (String productId : productIds) {
            Product product = productService.getById(productId);
            if (ObjectUtils.isEmpty(product)) {
                throw new BusinessException("商品不存在");
            }
            compareName.append(product.getProductName()).append("/");
            // 获取商品属性
            ProductCompareItem item = new ProductCompareItem();
            item.setCompareId(productCompare.getCompareId());
            BeanUtils.copyProperties(product, item);
            // 处理sku属性
            ProductSku productSku = productSkuService.lambdaQuery().eq(ProductSku::getProductId, product.getProductId()).one();
            BeanUtils.copyProperties(productSku, item);

            // 处理店铺是否自营是否支持路桥结算
            Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, product.getShopId()).one();
            BeanUtils.copyProperties(shop, item);
            item.setGmtCreate(new Date());
            item.setGmtModified(new Date());
            addList.add(item);
        }
        // 更新主表
        productCompare.setProductNames(compareName.toString());
        compareService.updateById(productCompare);
        // 重复校验
        List<ProductCompareItem> repeat = compareIsRepeat(addList);
        //if (CollectionUtils.isEmpty(repeat)){
        //    throw new BusinessException("当天存在相同的比价记录");
        //}
        // 批量新增
        if (CollectionUtils.isEmpty(repeat)) {
            boolean saveStatus = super.saveBatch(addList);
            if (!saveStatus) {
                throw new BusinessException("比价新增失败");
            }
        }else {
            // 返回原来的并手动回滚事物(撤回主表的一条记录)
            addList = repeat;
            if (StringUtils.isBlank(compareId)) {
                productCompare.setIsDelete(-1);
                //compareService.updateById(productCompare);
                compareService.delete(productCompare.getCompareId());
            }
            //TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return addList;
    }

    /**
     * 当前登录人的 比价商品重复校验
     *
     * @param productCompareItems
     * @return true 重复 false 不重复
     */
    private  List<ProductCompareItem>  compareIsRepeat(List<ProductCompareItem> productCompareItems){
        // 查询当前登录人，在当天的比价记录
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        QueryWrapper<ProductCompareItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProductCompareItem::getFounderId, user.getUserId());
        // 当天的时间区间
// 当前时间
        LocalDateTime now = LocalDateTime.now();

// 前一天的此刻
        LocalDateTime oneDayAgo = now.minusDays(1);

// 查询从前一天的此刻到现在的数据
        queryWrapper.lambda().between(ProductCompareItem::getGmtCreate, oneDayAgo, now);
        List<ProductCompareItem> list = super.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }else {
            Map<String, List<ProductCompareItem>> listMap = list.stream().collect(Collectors.groupingBy(ProductCompareItem::getCompareId));
            Set<String> keySet = listMap.keySet();
            for (String s : keySet) {
                List<ProductCompareItem> compareItems = listMap.get(s);
                if (compareItems.size() != productCompareItems.size()){
                    continue;
                }else {
                    List<ProductCompareItem>  repeatList  = new ArrayList<>(4);
                    for (ProductCompareItem item : compareItems) {
                        // 全部相等才进行哈希比较
                        for (ProductCompareItem productCompareItem : productCompareItems) {
                            // 待比较的
                            if (item.equals(productCompareItem)){
                                repeatList.add(item);
                                //return repeatList;
                            }
                        }
                        if (repeatList.size() == productCompareItems.size()){
                            return repeatList;
                        }
                    }

                }
            }
        }
        List<ProductCompareItem>  repeatList2  = new ArrayList<>(4);
        return repeatList2;
    }

    @Override
    public List<ProductCompareItem> reCompare(String id, List<String> productIds) {
        // 先删除原有比价明细
        QueryWrapper<ProductCompareItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProductCompareItem::getCompareId, id);
        super.remove(queryWrapper);
        // 重新比价
        List<ProductCompareItem> byProductIds = createByProductIds(productIds,id);
        return byProductIds;
    }
}
