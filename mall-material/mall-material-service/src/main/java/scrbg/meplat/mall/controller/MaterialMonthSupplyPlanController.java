package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.service.MaterialMonthSupplyPlanService;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlan;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @描述：计划表控制类
 * @作者: ye
 * @日期: 2023-06-27
 */
@RestController
@RequestMapping("/materialMonthSupplyPlan")
@Api(tags = "计划表")
public class MaterialMonthSupplyPlanController {

    @Autowired
    public MaterialMonthSupplyPlanService materialMonthSupplyPlanService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<MaterialMonthSupplyPlan> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = materialMonthSupplyPlanService.queryPage(jsonObject, new LambdaQueryWrapper<MaterialMonthSupplyPlan>());
        return PageR.success(page);
    }

    @PostMapping("/shopMange/listByEntity")
    @ApiOperation(value = "供应商查看计划")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<MaterialMonthSupplyPlan> shopMangeListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = materialMonthSupplyPlanService.shopMangeListByEntity(jsonObject, new LambdaQueryWrapper<MaterialMonthSupplyPlan>());
        return PageR.success(page);
    }

    @PostMapping("/shopMange/secondLevelPlanMonthList")
    @ApiOperation(value = "二级供应商查询计划")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<MaterialMonthSupplyPlan> secondLevelPlanMonthList(@RequestBody JSONObject jsonObject) {
        PageUtils page = materialMonthSupplyPlanService.secondLevelPlanMonthList(jsonObject, new QueryWrapper<MaterialMonthSupplyPlan>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<MaterialMonthSupplyPlan> findById(String id) {
        MaterialMonthSupplyPlan materialMonthSupplyPlan = materialMonthSupplyPlanService.getById(id);
        return R.success(materialMonthSupplyPlan);
    }


//    @GetMapping("/closeMaterialMonthSupplyPlanByPlanNo")
//    @NotResubmit
//    @Transactional
//    @ApiOperation(value = "完结订单")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R<List<Orders>> closeMaterialMonthSupplyPlanByPlanNo(String id) {
//        MaterialMonthSupplyPlan materialMonthSupplyPlan = materialMonthSupplyPlanService.getById(id);
//        return R.success(materialMonthSupplyPlan);
//    }
    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @NotResubmit
    public R save(@RequestBody MaterialMonthSupplyPlan materialMonthSupplyPlan) {
        materialMonthSupplyPlanService.create(materialMonthSupplyPlan);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @NotResubmit
    public R update(@RequestBody MaterialMonthSupplyPlan materialMonthSupplyPlan) {
        materialMonthSupplyPlanService.update(materialMonthSupplyPlan);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        materialMonthSupplyPlanService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        materialMonthSupplyPlanService.removeByIds(ids);
        return R.success();
    }

//    @PostMapping("/planExport")
    @GetMapping("/planExport")
    @ApiOperation(value = "导出")
    @LogRecord(title = "大宗月供应计划",businessType = BusinessType.EXPORT,operatorType = OperatorType.MANAGE)
    public R planExport( String id, HttpServletResponse response) {
        materialMonthSupplyPlanService.planExport(id,response);
        return R.success();
    }

    @GetMapping("/planSecondLevelExport")
    @ApiOperation(value = "二级供应商导出")
    @LogRecord(title = "大宗月供应计划",businessType = BusinessType.EXPORT,operatorType = OperatorType.MANAGE)

    public R planSecondLevelExport( String id, HttpServletResponse response) {
        materialMonthSupplyPlanService.planSecondLevelExport(id,response);
        return R.success();
    }
}

