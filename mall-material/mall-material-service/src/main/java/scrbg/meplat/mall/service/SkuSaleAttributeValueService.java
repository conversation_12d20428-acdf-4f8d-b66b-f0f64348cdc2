package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SkuSaleAttributeValue;

/**
 * @描述：sku销售属性&值 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface SkuSaleAttributeValueService extends IService<SkuSaleAttributeValue> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SkuSaleAttributeValue> queryWrapper);

    void create(SkuSaleAttributeValue skuSaleAttributeValue);

    void update(SkuSaleAttributeValue skuSaleAttributeValue);

    SkuSaleAttributeValue getById(String id);

    void delete(String id);
}
