package scrbg.meplat.mall.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import scrbg.meplat.mall.entity.OrderShip;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.entity.SynthesizeTemporary;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.mapper.OrderShipMapper;
import scrbg.meplat.mall.mapper.OrdersMapper;
import scrbg.meplat.mall.mapper.SynthesizeTemporaryMapper;
import scrbg.meplat.mall.mapper.plan.PlanMapper;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @描述：编号生成工具类（静态调用版本）
 * @作者: system
 * @日期: 2025-05-27
 * 编码规则说明：
 * 1. 商品编码：以两位字母+数字0000000~9999999，字母从ACEFGHJKMNPQRTUVWXY内取
 * 2. 订单编码：
 *    - 零星采购订单：DSLXDD+年月日+001~999
 *    - 大宗临购订单：DSDZDD+年月日+001~999
 *    - 周转材料订单：DSZZDD+年月日+001~999（新增）
 *    - 大宗月供订单：SCDZDD+年月日+001~999（弃用）
 *    - 二级供应商订单：在一级订单编码基础上+G01
 * 3. 发货单编码：
 *    - 零星采购发货单：DSLXFH+年月日+001~999
 *    - 大宗临购发货单：DSDZFH+年月日+001~999
 *    - 周转材料发货单：DSZZFH+年月日+001~999（新增）
 *    - 大宗月供发货单：SCDZFH+年月日+001~999（弃用）
 *    - 二级供应商发货单：在一级发货单编码基础上+G01
 * 4. 大宗临购清单编码：DZLGQD+年月日+001~999
 * 5. 计划编码：
 *    - 零星采购计划：DSLXJH+年月日+001~999（新增）
 *    - 大宗临购计划：DSDZJH+年月日+001~999（新增）
 *    - 周转材料计划：DSZZJH+年月日+001~999（新增）
 */
@Slf4j
@Component
public class BillNumberGenerator {

    // 静态变量用于存储Mapper实例
    private static PlanMapper planMapper;
    private static OrdersMapper orderMapper;
    private static OrderShipMapper orderShipMapper;
    private static SynthesizeTemporaryMapper synthesizeTemporaryMapper;

    // 注入Mapper依赖
    private final PlanMapper planMapperInstance;
    private final OrdersMapper orderMapperInstance;
    private final OrderShipMapper orderShipMapperInstance;
    private final SynthesizeTemporaryMapper synthesizeTemporaryMapperInstance;

    public BillNumberGenerator(PlanMapper planMapper,
                               OrdersMapper orderMapper,
                               OrderShipMapper orderShipMapper,
                               SynthesizeTemporaryMapper synthesizeTemporaryMapper) {
        this.planMapperInstance = planMapper;
        this.orderMapperInstance = orderMapper;
        this.orderShipMapperInstance = orderShipMapper;
        this.synthesizeTemporaryMapperInstance = synthesizeTemporaryMapper;
    }

    /**
     * 初始化静态变量
     */
    @PostConstruct
    public void init() {
        BillNumberGenerator.planMapper = this.planMapperInstance;
        BillNumberGenerator.orderMapper = this.orderMapperInstance;
        BillNumberGenerator.orderShipMapper = this.orderShipMapperInstance;
        BillNumberGenerator.synthesizeTemporaryMapper = this.synthesizeTemporaryMapperInstance;
    }

    // 商品编码字母池
    private static final String[] PRODUCT_CODE_LETTERS = {
            "A", "C", "E", "F", "G", "H", "J", "K", "M", "N", "P", "Q", "R", "T", "U", "V", "W", "X", "Y"
    };

    /**
     * 生成商品编码
     * 规则：两位字母+数字0000000~9999999
     */
    public static String generateProductCode() {
        // 这里可以实现更复杂的逻辑，比如按分类分配字母等
        // 简单实现：随机选择两个字母
        String firstLetter = PRODUCT_CODE_LETTERS[0]; // 第一个字母固定为A
        String secondLetter = getNextAvailableLetter();
        Integer numberPart = getNextProductNumber(firstLetter + secondLetter);

        return firstLetter + secondLetter + String.format("%07d", numberPart);
    }

    /**
     * 生成计划编号
     * @param planType 计划类型 (0零星采购 1大宗临购 2周转材料)
     * @return 计划编号
     */
    public static String generatePlanNumber(Integer planType) {
        String prefix = getPlanPrefix(planType);
        String dateStr = getCurrentDateString();
        String sequence = generateSequence(prefix, dateStr, BillNumberGenerator::getMaxPlanSequence);

        return prefix + dateStr + sequence;
    }

    /**
     * 生成订单编号
     * @param orderType 订单类型 (0零星采购 1大宗临购 2周转材料)
     * @param isSecondLevel 是否二级供应商订单
     * @return 订单编号
     */
    public static String generateOrderNumber(Integer orderType, boolean isSecondLevel) {
        String prefix = getOrderPrefix(orderType);
        String dateStr = getCurrentDateString();
        String sequence = generateSequence(prefix, dateStr, BillNumberGenerator::getMaxOrderSequence);
        String baseNumber = prefix + dateStr + sequence;

        return isSecondLevel ? baseNumber + "G01" : baseNumber;
    }

    /**
     * 生成发货单编号
     * @param orderType 订单类型 (0零星采购 1大宗临购 2周转材料)
     * @param isSecondLevel 是否二级供应商发货单
     * @return 发货单编号
     */
    public static String generateDeliveryNumber(Integer orderType, boolean isSecondLevel) {
        String prefix = getDeliveryPrefix(orderType);
        String dateStr = getCurrentDateString();
        String sequence = generateSequence(prefix, dateStr, BillNumberGenerator::getMaxOrderShipSequence);
        String baseNumber = prefix + dateStr + sequence;

        return isSecondLevel ? baseNumber + "G01" : baseNumber;
    }

    /**
     * 生成大宗临购清单编号
     * @return 大宗临购清单编号
     */
    public static String generateBulkPurchaseListNumber() {
        String prefix = "DZLGQD";
        String dateStr = getCurrentDateString();
        String sequence = generateSequence(prefix, dateStr, BillNumberGenerator::getMaxBulkListSequence);

        return prefix + dateStr + sequence;
    }

    /**
     * 获取计划前缀
     */
    private static String getPlanPrefix(Integer planType) {
        switch (planType) {
            case 0: return "DSLXJH"; // 零星采购计划
            case 1: return "DSDZJH"; // 大宗临购计划
            case 2: return "DSZZJH"; // 周转材料计划
            default: throw new IllegalArgumentException("无效的计划类型: " + planType);
        }
    }

    /**
     * 获取订单前缀
     */
    private static String getOrderPrefix(Integer orderType) {
        switch (orderType) {
            case 0: return "DSLXDD"; // 零星采购订单
            case 1: return "DSDZDD"; // 大宗临购订单
            case 2: return "DSZZDD"; // 周转材料订单（新增）
            default: throw new IllegalArgumentException("无效的订单类型: " + orderType);
        }
    }

    /**
     * 获取发货单前缀
     */
    private static String getDeliveryPrefix(Integer orderType) {
        switch (orderType) {
            case 0: return "DSLXFH"; // 零星采购发货单
            case 1: return "DSDZFH"; // 大宗临购发货单
            case 2: return "DSZZFH"; // 周转材料发货单（新增）
            default: throw new IllegalArgumentException("无效的发货单类型: " + orderType);
        }
    }

    /**
     * 获取当前日期字符串 yyyyMMdd
     */
    private static String getCurrentDateString() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 生成序号（001~999）
     */
    private static String generateSequence(String prefix, String dateStr, SequenceProvider sequenceProvider) {
        String todayPrefix = prefix + dateStr;
        Integer maxSequence = sequenceProvider.getMaxSequence(todayPrefix);
        int nextSequence = (maxSequence == null ? 0 : maxSequence) + 1;

        if (nextSequence > 999) {
            throw new RuntimeException(String.format("当日编号已达上限(999)，前缀: %s", todayPrefix));
        }

        return String.format("%03d", nextSequence);
    }

    /**
     * 获取计划最大序号
     */
    private static Integer getMaxPlanSequence(String todayPrefix) {
        if (planMapper == null) {
            throw new RuntimeException("PlanMapper未初始化，请确保Spring容器已启动");
        }

        QueryWrapper<Plan> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("bill_no", todayPrefix);
        queryWrapper.apply("DATE(gmt_create) = CURDATE()");
        queryWrapper.orderByDesc("bill_no");
        queryWrapper.last("LIMIT 1");

        Plan lastPlan = planMapper.selectOne(queryWrapper);
        return extractSequenceFromBillNo(lastPlan != null ? lastPlan.getBillNo() : null);
    }

    /**
     * 获取订单最大序号
     */
    private static Integer getMaxOrderSequence(String todayPrefix) {
        if (orderMapper == null) {
            throw new RuntimeException("OrderMapper未初始化，请确保Spring容器已启动");
        }

        QueryWrapper<Orders> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("order_sn", todayPrefix);
        queryWrapper.apply("DATE(gmt_create) = CURDATE()");
        queryWrapper.orderByDesc("order_sn");
        queryWrapper.last("LIMIT 1");

        Orders lastOrder = orderMapper.selectOne(queryWrapper);
        return extractSequenceFromBillNo(lastOrder != null ? lastOrder.getOrderSn() : null);
    }

    /**
     * 获取发货单最大序号
     */
    private static Integer getMaxOrderShipSequence(String todayPrefix) {
        if (orderShipMapper == null) {
            throw new RuntimeException("OrderShipMapper未初始化，请确保Spring容器已启动");
        }

        QueryWrapper<OrderShip> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("bill_sn", todayPrefix);
        queryWrapper.apply("DATE(gmt_create) = CURDATE()");
        queryWrapper.orderByDesc("bill_sn");
        queryWrapper.last("LIMIT 1");

        OrderShip orderShip = orderShipMapper.selectOne(queryWrapper);
        return extractSequenceFromBillNo(orderShip != null ? orderShip.getBillSn() : null);
    }

    /**
     * 获取大宗清单最大序号
     */
    private static Integer getMaxBulkListSequence(String todayPrefix) {
        if (synthesizeTemporaryMapper == null) {
            throw new RuntimeException("SynthesizeTemporaryMapper未初始化，请确保Spring容器已启动");
        }

        QueryWrapper<SynthesizeTemporary> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("synthesize_temporary_sn", todayPrefix);
        queryWrapper.apply("DATE(gmt_create) = CURDATE()");
        queryWrapper.orderByDesc("synthesize_temporary_sn");
        queryWrapper.last("LIMIT 1");

        SynthesizeTemporary synthesizeTemporary = synthesizeTemporaryMapper.selectOne(queryWrapper);
        return extractSequenceFromBillNo(synthesizeTemporary != null ? synthesizeTemporary.getSynthesizeTemporarySn() : null);
    }

    /**
     * 从编号中提取序号
     */
    private static Integer extractSequenceFromBillNo(String billNo) {
        if (billNo == null || billNo.length() < 3) {
            return null;
        }

        // 提取最后3位序号（排除可能的后缀如G01）
        String numberPart = billNo;
        if (billNo.endsWith("G01")) {
            numberPart = billNo.substring(0, billNo.length() - 3);
        }

        if (numberPart.length() >= 3) {
            String sequenceStr = numberPart.substring(numberPart.length() - 3);
            try {
                return Integer.valueOf(sequenceStr);
            } catch (NumberFormatException e) {
                log.warn("解析编号序号失败: {}", billNo);
                return null;
            }
        }

        return null;
    }

    /**
     * 获取下一个可用字母（商品编码用）
     */
    private static String getNextAvailableLetter() {
        // 这里可以实现更复杂的逻辑
        // 简单实现：返回第二个字母
        return PRODUCT_CODE_LETTERS[1];
    }

    /**
     * 获取下一个商品编号数字部分
     */
    private static Integer getNextProductNumber(String letterPrefix) {
        // 这里需要查询数据库获取最大编号
        // 简化实现，返回随机数
        return (int) (System.currentTimeMillis() % 9999999);
    }

    /**
     * 序号提供者接口
     */
    @FunctionalInterface
    private interface SequenceProvider {
        Integer getMaxSequence(String todayPrefix);
    }
}