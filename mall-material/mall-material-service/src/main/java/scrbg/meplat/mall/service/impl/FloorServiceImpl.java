package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.entity.Floor;
import scrbg.meplat.mall.entity.Links;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.FloorMapper;
import scrbg.meplat.mall.service.FloorService;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @描述：客户端商品展示楼层 服务类
 * @作者: 胡原武
 * @日期: 2022-11-10
 */
@Service
public class FloorServiceImpl extends ServiceImpl<FloorMapper, Floor> implements FloorService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Floor> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String floorName = (String) innerMap.get("floorName");
        String columnId = (String) innerMap.get("columnId");
        String columnName = (String) innerMap.get("columnName");
        String floorNameText = (String) innerMap.get("floorNameText");
        String floorProductType = (String) innerMap.get("floorProductType");
        String keywords = (String) innerMap.get("keywords");
        Integer mallType = (Integer) innerMap.get("mallType");
        Integer usePage = (Integer) innerMap.get("usePage");
        Integer state = (Integer) innerMap.get("state");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String programaKey = (String) innerMap.get("programaKey");


        if (!StringUtils.isEmpty(floorName)) {
            queryWrapper.like(Floor::getFloorName, floorName);
        }
        if (!StringUtils.isEmpty(columnName)) {
            queryWrapper.like(Floor::getColumnName, columnName);
        }
        if (!StringUtils.isEmpty(floorNameText)) {
            queryWrapper.like(Floor::getFloorNameText, floorNameText);
        }
        if (!StringUtils.isEmpty(floorProductType)) {
            queryWrapper.like(Floor::getFloorProductType, floorProductType);
        }
        if (!StringUtils.isEmpty(columnId)) {
            queryWrapper.eq(Floor::getColumnId, columnId);
        }
        if (!StringUtils.isEmpty(keywords)) {
            queryWrapper.and((t) -> {
                t.like(Floor::getFloorName, keywords)
                        .or()
                        .like(Floor::getRemarks, keywords)
                        .or()
                        .like(Floor::getColumnName, keywords);

            });
        }
        if (mallType != null) {
            queryWrapper.eq(Floor::getMallType, mallType);
        }
        if (usePage != null) {
            queryWrapper.eq(Floor::getUsePage, usePage);
        }
        if (state != null) {
            queryWrapper.eq(Floor::getState, state);
        }

        //排序方式(排序值升序)
        if (orderBy == null) {
        }
        else if (orderBy == PublicEnum.ORDER_BY_SORT.getCode()) {
            queryWrapper.orderByDesc(Floor::getSort).orderByDesc(Floor::getGmtCreate);
            //排序方式(修改时间降序)
        }else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(Floor::getGmtModified);
            //排序方式(发布时间降序)
        }else if (orderBy == PublicEnum.ORDER_BY_GMT_RELEASE.getCode()) {
            queryWrapper.orderByDesc(Floor::getGmtRelease);
        }
        IPage<Floor> page = this.page(
                new Query<Floor>().getPage(jsonObject),
                queryWrapper
        );
        // 需要将classPath字符串转为数组返回
        List<Floor> records = page.getRecords();
        for (Floor record : records) {
            if (!ObjectUtils.isEmpty(record.getClassPath())){
            record.setClassPaths(Arrays.asList(record.getClassPath().split("/")));
            }
        }
        return new PageUtils(page);
    }

    @Override
    public void create(Floor floor) {
        floor.setState(PublicEnum.IS_NO.getCode());
        List<String> classPaths =  floor.getClassPaths();
        if (CollectionUtils.isEmpty(classPaths)) {
            throw new BusinessException(400, "分类不能为空！");
        }
        String cp = "";
        if (!CollectionUtils.isEmpty(classPaths)) {
            for (String s : classPaths) {
                cp += s + "/";
            }
        }
        floor.setClassPath(cp.substring(0, cp.length() - 1));
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(floor);
    }

    @Override
    public void update(Floor floor) {
        List<String> classPaths =  floor.getClassPaths();
        if (CollectionUtils.isEmpty(classPaths)) {
            throw new BusinessException(400, "分类不能为空！");
        }
        String cp = "";
        if (!CollectionUtils.isEmpty(classPaths)) {
            for (String s : classPaths) {
                cp += s + "/";
            }
        }
        floor.setClassPath(cp.substring(0, cp.length() - 1));
        super.updateById(floor);
    }


    @Override
    public Floor getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void updateByPublish(List<String> ids, String type) {
        List<Floor> resutls = listByIds(ids);
        for (Floor floor : resutls) {
            if ("1".equals(type)) {
                floor.setState(1);
            } else {
                floor.setState(0);
            }
        }
        super.saveOrUpdateBatch(resutls);
    }

}
