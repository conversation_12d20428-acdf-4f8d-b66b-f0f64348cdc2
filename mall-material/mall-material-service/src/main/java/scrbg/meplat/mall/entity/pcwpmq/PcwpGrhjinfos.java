package scrbg.meplat.mall.entity.pcwpmq;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 个人获奖信息表实体类
 */
@Data
@TableName("pcwp_grhjinfos")
public class PcwpGrhjinfos {
    /**
     * 主键（唯一值）
     */
    @TableField("id")
    private String id;

    /**
     * 人员姓名
     */
    @TableField("pname")
    private String pname;

    /**
     * 人员编码
     */
    @TableField("pnumber")
    private String pnumber;

    /**
     * 奖项名称
     */
    @TableField("hjmc")
    private String hjmc;

    /**
     * 获奖时间
     */
    @TableField("hjsj")
    private String hjsj;

    /**
     * 颁发单位
     */
    @TableField("bfdw")
    private String bfdw;

    /**
     * 最后更新时间
     */
    @TableField("lastupdatetime")
    private String lastupdatetime;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    @TableField("mdmstate")
    private Integer mdmstate;
}