package scrbg.meplat.mall.util;

import org.apache.commons.codec.binary.Hex;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.spec.AlgorithmParameterSpec;
import scrbg.meplat.mall.exception.BusinessException;

import java.text.ParseException;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 
 */
public class PasswordUtils {
    //密码长度为8到20位,必须包含字母和数字，字母区分大小写
    private static String regEx1 = "^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$";
    //密码中必须包含字母、数字、特称字符，至少8个字符，最多16个字符
    private static String regEx2 = "^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9])(.{8,20})$";

    /**
     * 密码长度为8到20位,必须包含字母和数字，字母区分大小写
     * @param password
     * @return
     */
    public static boolean checkPassword(String password){
        password = password.trim();
        Pattern Password_Pattern = Pattern.compile(regEx1);
        Matcher matcher = Password_Pattern.matcher(password);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    /**
     * 密码中必须包含字母、数字、特称字符，至少8个字符，最多16个字符
     * @param password
     * @return
     */
    public static boolean password(String password){
        password = password.trim();
        Pattern Password_Pattern = Pattern.compile(regEx2);
        Matcher matcher = Password_Pattern.matcher(password);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    /**
     * 密码登录后端加密工具
     * @param word 需要加密的字符串
     * @param keyStr 可为空
     * @param ivStr 可为空
     * @return
     */
    public static String encrypt(String word, String keyStr, String ivStr) {
        try {
            keyStr = keyStr != null ? keyStr : "hnbWVJUbruSQhcqMFy2rWlSmbW1n8kea";
            ivStr = ivStr != null ? ivStr : "VWz35S5kltEAJ1Q1";
            byte[] key = keyStr.getBytes(StandardCharsets.UTF_8);
            byte[] iv = ivStr.getBytes(StandardCharsets.UTF_8);

            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            AlgorithmParameterSpec paramSpec = new IvParameterSpec(iv);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");   //PKCS5Padding 和PKCS7Padding在此情况下效果是相同的

            byte[] srcs = word.getBytes(StandardCharsets.UTF_8);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, paramSpec);
            byte[] encrypted = cipher.doFinal(srcs);

            return Hex.encodeHexString(encrypted);   //转换为16进制字符串
        }
        catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    public static void main(String[] args)throws ParseException {
        //14c00e2fcc55183b115e8d1e786eecfd
        if(PasswordUtils.password("ZBYYpt@232123")) {
            System.out.println("通过！");
        }else {
            System.out.println("不通过！");
        }
    }
}

