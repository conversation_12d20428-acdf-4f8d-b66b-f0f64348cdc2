package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.FileRecord;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-03-27 17:05
 */
public interface MinioService {

    /**
     * 文件上传
     * @param map
     * @return
     */
    List<FileRecord> minioUpload(Map map);

    /**
     * 根据记录id下载
     * @param recordId
     * @return
     */
    Object downloadById(String recordId);
}
