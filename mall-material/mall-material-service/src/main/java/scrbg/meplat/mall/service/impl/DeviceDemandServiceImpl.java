package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.DeviceDemand.UpdateDemandCheckStateDTO;
import scrbg.meplat.mall.entity.DeviceDemand;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.shop.deviceDemand.DeviceDemandEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.DeviceDemandMapper;
import scrbg.meplat.mall.mapper.ShopMapper;
import scrbg.meplat.mall.service.DeviceDemandService;
import scrbg.meplat.mall.service.ProductCategoryService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.pageUtils;
import scrbg.meplat.mall.vo.product.device.PlatformDeviceListVO;
import scrbg.meplat.mall.vo.product.material.CategoryClassIdAndClassNameVO;
import scrbg.meplat.mall.vo.product.website.demand.DeviceDemandHomeVO;
import scrbg.meplat.mall.vo.product.website.demand.DeviceDemandInfoVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @描述：设备需求 服务类
 * @作者: y
 * @日期: 2022-11-21
 */
@Service
public class DeviceDemandServiceImpl extends ServiceImpl<DeviceDemandMapper, DeviceDemand> implements DeviceDemandService{

    @Autowired
    ShopMapper shopMapper;

    @Autowired
    ProductCategoryService productCategoryService;
    @Autowired
    MallConfig mallConfig;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<DeviceDemand> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String classId = (String) innerMap.get("classId");
        Integer checkState = (Integer) innerMap.get("checkState");
        Integer demandType = (Integer) innerMap.get("demandType");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Integer releaseType = (Integer) innerMap.get("releaseType");
        String relevanceId = (String) innerMap.get("relevanceId");
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        String startModifiedDate = (String) innerMap.get("startModifiedDate");
        String endModifiedDate = (String) innerMap.get("endModifiedDate");
        String demandName = (String) innerMap.get("demandName");
        String spec = (String) innerMap.get("spec");
        String brandName = (String) innerMap.get("brandName");
        Integer isMy = (Integer) innerMap.get("isMy");
        q.like(StringUtils.isNotEmpty(demandName),DeviceDemand::getName,demandName);
        q.like(StringUtils.isNotEmpty(spec),DeviceDemand::getSpec,spec);
        q.like(StringUtils.isNotEmpty(brandName),DeviceDemand::getBrandName,brandName);
        q.between(StringUtils.isNotEmpty(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), DeviceDemand::getGmtCreate, startCreateDate, endCreateDate);
        q.between(StringUtils.isNotEmpty(startModifiedDate) && StringUtils.isNotEmpty(endModifiedDate), DeviceDemand::getGmtModified, startModifiedDate, endModifiedDate);
        q.eq(DeviceDemand::getMallType, mallConfig.mallType);
        q.orderByDesc(orderBy == 1,DeviceDemand::getGmtModified);
        q.orderByDesc(orderBy == 2,DeviceDemand::getGmtCreate);
        if(StringUtils.isNotBlank(keywords)){
            q.and((t) -> {
                t.like(DeviceDemand::getName,keywords)
                        .or()
                        .like(DeviceDemand::getSpec,keywords)
                        .or()
                        .like(DeviceDemand::getBrandName,keywords)
                        .or()
                        .like(DeviceDemand::getClassName,keywords);

            });
        }
        if(isMy != null && isMy == 1){
            q.eq(DeviceDemand::getRelevanceId, ThreadLocalUtil.getCurrentUser().getUserId());
            q.eq(DeviceDemand::getReleaseType,1);
        }
        q.ge(StringUtils.isNotBlank(abovePrice),DeviceDemand::getBudgetAmount,abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice),DeviceDemand::getBudgetAmount,belowPrice);
        q.eq(StringUtils.isNotBlank(classId),DeviceDemand::getClassId,classId);
        q.eq(StringUtils.isNotBlank(relevanceId),DeviceDemand::getRelevanceId,relevanceId);
        q.eq(demandType != null,DeviceDemand::getDemandType,demandType);
        q.select(DeviceDemand.class, f -> {
            return  !f.getProperty().equals("remarks");
        });
        if(checkState != null && checkState == 20){
            q.in(DeviceDemand::getCheckState,DeviceDemandEnum.CHECK_STATE_YES.getCode(),DeviceDemandEnum.CHECK_STATE_NO.getCode());
        }else {
            q.eq(checkState != null,DeviceDemand::getCheckState,checkState);
        }
        q.eq(releaseType != null,DeviceDemand::getReleaseType,releaseType);
        q.eq(DeviceDemand::getIsDelete, PublicEnum.IS_DELETE_NO.getCode());
        IPage<DeviceDemand> page = this.page(
        new Query<DeviceDemand>().getPage(jsonObject),
        q
        );
        return new PageUtils(page);
    }

    @Override
    public void create(DeviceDemand deviceDemand) {
        // 分类名称
        ProductCategory productCategory = productCategoryService.getProductCategoryById(deviceDemand.getClassId(), null);
        if (productCategory != null) deviceDemand.setClassName(productCategory.getClassName());

        deviceDemand.setState(0);
        deviceDemand.setCheckState(0);
        // TODO 暂时是用户
        deviceDemand.setReleaseType(1);
        deviceDemand.setRelevanceId(ThreadLocalUtil.getCurrentUser().getUserId());
        deviceDemand.setLaunch(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(deviceDemand);
    }

    @Override
    public void update(DeviceDemand deviceDemand) {
        super.updateById(deviceDemand);
    }


    @Override
    public DeviceDemand getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 批量修改审核状态
     * @param dto
     */
    @Override
    public void updateBatchCheckState(UpdateDemandCheckStateDTO dto) {
        List<String> ids = dto.getIds();
        Integer checkState = dto.getCheckState();
        int yesCode = DeviceDemandEnum.CHECK_STATE_YES.getCode();
        int noCode = DeviceDemandEnum.CHECK_STATE_NO.getCode();
        // 通过
        if(checkState == yesCode){
            boolean update = lambdaUpdate()
                    .in(DeviceDemand::getDemandId, ids)
                    .set(DeviceDemand::getCheckState, yesCode)
                    .set(DeviceDemand::getFailReason,null)
                    .set(DeviceDemand::getGmtModified,new Date())
                    .set(DeviceDemand::getReleaseDate,new Date())
                    .set(DeviceDemand::getState, DeviceDemandEnum.STATE_YES.getCode())
                    .update();
            if(!update){
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(),"操作失败！");
            }
        }
        if(checkState == noCode){
            boolean update = lambdaUpdate()
                    .in(DeviceDemand::getDemandId, ids)
                    .set(DeviceDemand::getGmtModified,new Date())
                    .set(DeviceDemand::getReleaseDate,null)
                    .set(DeviceDemand::getCheckState, noCode)
                    .set(DeviceDemand::getFailReason,dto.getFailReason())
                    .set(DeviceDemand::getState, DeviceDemandEnum.STATE_YES.getCode())
                    .update();
            if(!update){
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(),"操作失败！");
            }
        }
    }

    /**
     * 获取需求列表
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    @Override
    public PageUtils usedPageList(JSONObject jsonObject, LambdaQueryWrapper<DeviceDemand> lambdaQuery) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer mallType = mallConfig.mallType;
        innerMap.put("mallType", mallType);

        innerMap.put("releaseType", 1);
        String orderBy = (String) innerMap.get("orderBy");
        if(StringUtils.isEmpty(orderBy)){
            innerMap.put("orderBy",0);
        }else {
            if(orderBy.equals("1-desc")){
                innerMap.put("orderBy",11);
            }
            if(orderBy.equals("1-asc")){
                innerMap.put("orderBy",10);
            }
            if(orderBy.equals("2-desc")){
                innerMap.put("orderBy",21);
            }
            if(orderBy.equals("2-asc")){
                innerMap.put("orderBy",20);
            }
            if(orderBy.startsWith("0")){
                innerMap.put("orderBy",0);
            }
        }
        int count = baseMapper.listHomeDemandPageCount(innerMap);
        pageUtils.pageDispose(jsonObject,count);
        Page<DeviceDemandHomeVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<DeviceDemandHomeVO> vos = baseMapper.listHomeDemandPage(pages,innerMap);
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    /**
     * 需求详细
     * @param demandId
     * @return
     */
    @Override
    public DeviceDemandInfoVO demandInfo(String demandId) {
        DeviceDemand demand = getById(demandId);
        if(demand == null) {
            throw new BusinessException(DeviceDemandEnum.RESULT_CODE_500401.getCode(),DeviceDemandEnum.RESULT_CODE_500401.getRemark());
        }
        DeviceDemandInfoVO vo = new DeviceDemandInfoVO();

        if(demand.getReleaseType() == 0){
            LambdaQueryWrapper<Shop> q = Wrappers.lambdaQuery(Shop.class);
            q.eq(Shop::getShopId,demand.getRelevanceId())
                    .eq(Shop::getState,1)
                    .eq(Shop::getAuditStatus,1)
                    .select(Shop::getShopName,Shop::getShopDescrible);
            Shop shop = shopMapper.selectOne(q);
            if(shop == null){
                throw new BusinessException(DeviceDemandEnum.RESULT_CODE_500402.getCode(),DeviceDemandEnum.RESULT_CODE_500402.getRemark());
            }
            vo.setReleaseName(shop.getShopName());
            // TODO 企业简介查询
            vo.setDescribe(shop.getShopDescrible());
        }
        BeanUtils.copyProperties(demand,vo);
        // TODO 是否要加密电话
//        if(vo.getLinkmanPhone() != null){
//            String newPhone = EncryptionUtil.encryptionPhone(vo.getLinkmanPhone());
//            vo.setLinkmanPhone(newPhone);
//        }
        // 分类路径
        List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(demand.getClassId());
        vo.setClassPathVOS(categoryParentPath);
        return vo;
    }

    /**
     * 查询我发布的需求
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils userDeviceDemand(JSONObject jsonObject, LambdaQueryWrapper<DeviceDemand> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        q.eq(DeviceDemand::getRelevanceId,ThreadLocalUtil.getCurrentUser().getUserId());
        q.eq(DeviceDemand::getReleaseType,1);
        IPage<DeviceDemand> page = this.page(
                new Query<DeviceDemand>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Override
    public PageUtils attendList(JSONObject jsonObject, QueryWrapper<DeviceDemand> deviceDemandQueryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        Integer page = (Integer) innerMap.get("page");
        Integer limit = (Integer) innerMap.get("limit");
        Integer mallType = mallConfig.mallType;
        innerMap.put("mallType", mallType);
        innerMap.put("userId", userId);
        if (page == null || limit == null) {
            page = 1;
            limit = 10;
        }
        int start = (page - 1) * limit;
        innerMap.put("start", start);
        innerMap.put("limit", limit);
        IPage<DeviceDemand> pages = new Query<DeviceDemand>().getPage(jsonObject);
        List<DeviceDemand> vos = baseMapper.attendLists(pages,innerMap);
        PageUtils pageUtils = new PageUtils(pages);
        pageUtils.setList(vos);
        return pageUtils;
    }
}
