package scrbg.meplat.mall.entity.pcwpmq;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 任职项目经历表实体类
 */
@Data
@TableName("pcwp_rzjlinfos")
public class PcwpRzjlinfos {
    /**
     * 主键（唯一值）
     */
    @TableField("id")
    private String id;

    /**
     * 人员姓名
     */
    @TableField("pname")
    private String pname;

    /**
     * 人员编码（关联人员主表）
     */
    @TableField("pnumber")
    private String pnumber;

    /**
     * 任职形式（如：正式/兼职/借调）
     */
    @TableField("rzxs")
    private String rzxs;

    /**
     * 任职岗位（如：项目经理/技术负责人）
     */
    @TableField("rzgw")
    private String rzgw;

    /**
     * 部门编码（关联部门表）
     */
    @TableField("bmbm")
    private String bmbm;

    /**
     * 部门名称
     */
    @TableField("bmmc")
    private String bmmc;

    /**
     * 项目名称（冗余存储）
     */
    @TableField("xmmc")
    private String xmmc;

    /**
     * 项目编码（关联项目表）
     */
    @TableField("xmbm")
    private String xmbm;

    /**
     * 生效时间（任职开始日期）
     */
    @TableField("startdate")
    private String startDate;

    /**
     * 失效时间（任职结束日期，空表示仍在职）
     */
    @TableField("enddate")
    private String endDate;

    /**
     * 最后更新时间（系统自动维护）
     */
    @TableField("lastupdatetime")
    private String lastUpdateTime;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    @TableField("mdmstate")
    private Integer mdmState;
}
