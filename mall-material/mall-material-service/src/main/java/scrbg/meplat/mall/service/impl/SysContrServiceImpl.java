package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.SysContr;
import scrbg.meplat.mall.mapper.SysContrMapper;
import scrbg.meplat.mall.service.SysContrService;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：我的控制 服务类
 * @作者: ye
 * @日期: 2024-04-28
 */
@Service
public class SysContrServiceImpl extends ServiceImpl<SysContrMapper, SysContr> implements SysContrService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysContr> queryWrapper) {
        IPage<SysContr> page = this.page(
        new Query<SysContr>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(SysContr sysContr) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(sysContr);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(SysContr sysContr) {
        super.updateById(sysContr);
    }


    @Override
    public SysContr getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     @Transactional(rollbackFor = Exception.class)
     @NotResubmit
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
         }
}
