package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.PlatformYearFee;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.PlatformYearFee;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.vo.user.LoginVO;

import java.util.List;

/**
 * @描述：平台年费表 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
public interface PlatformYearFeeService extends IService<PlatformYearFee> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformYearFee> queryWrapper);

    void create(PlatformYearFee platformYearFee);

    void update(PlatformYearFee platformYearFee);

    PlatformYearFee getById(String id);

    void delete(String id);

    void deleteBatch(List<String> ids);

    /**
     * 定时查询是否店铺过期
     */
    void updateYearFeeOutTime();


    /**
     * 检查店铺是否过期（权限控制使用）
     */
    void checkYearShopIsOut(String shopId);



    /**
     * 检查招标是否过期（权限控制使用）
     */
    void checkYearStandardIsOut(LoginVO loginVO);



    /**
     * 检查是否还有一个月过期，登陆或者进去首页提示
     */
    void checkYearMonthIsOut(LoginVO loginVO);

    void yearFeeExpirationReminder();


    /**
     * 批量导入当前已审核的店铺
     * 导入店铺
     */
    void importThisShopYearFee();



    PlatformYearFee findBySn(String sn);


    /**
     * 平台根据编号获取数据
     * @param sn
     * @return
     */
    PlatformYearFee platformFindBySn(String sn);

    /**
     * 平台统计报表
     * @param jsonObject
     * @return
     */
    PageUtils totalCountFree(JSONObject jsonObject);

    /**
     * 根据企业ID获取数据
     *
     */
    PlatformYearFee getByEnterpriseId(String enterpriseId);

}
