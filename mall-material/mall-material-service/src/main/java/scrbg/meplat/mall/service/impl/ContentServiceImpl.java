package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.content.UpdateContentStateDTO;
import scrbg.meplat.mall.entity.Content;
import scrbg.meplat.mall.entity.ContentLog;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.entity.Links;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.content.ContentEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ContentLogMapper;
import scrbg.meplat.mall.mapper.ContentMapper;
import scrbg.meplat.mall.service.ContentService;
import scrbg.meplat.mall.service.FileService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.util.*;

/**
 * @描述：内容 服务类
 * @作者: sund
 * @日期: 2022-11-07
 */
@Service
public class ContentServiceImpl extends ServiceImpl<ContentMapper, Content> implements ContentService {
    @Autowired
    FileService fileService;
    @Autowired
    MallConfig mallConfig;
    @Autowired
    private ContentService contentService;

    @Autowired
    ContentLogMapper contentLogMapper;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Content> lambdaQuery) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");

        Integer state = (Integer) innerMap.get("state");

        lambdaQuery.orderByDesc(Content::getGmtModified);
        if (state != null) {
            lambdaQuery.eq(Content::getState, state);
        }
        if (!StringUtils.isEmpty(keywords)) {
            lambdaQuery.like(Content::getTitle, keywords);
        }


        IPage<Content> page = this.page(
                new Query<Content>().getPage(jsonObject),
                lambdaQuery
        );

        return new PageUtils(page);
    }


    @Override
    public void create(Content content) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(content);
    }

    @Override
    public void update(Content content) {
        if (content.getTop()!=null){
            if (content.getTop().equals("1")){
                lambdaUpdate().set(Content::getTop,2).update();
            }
        }
        if (content.getFiles()!=null&&content.getFiles().size()>0){
            List<File> files = content.getFiles();
            if (files!=null&&files.size()>0){
                for (File file : files) {
                    fileService.deleteBatchFileByRelevanceIdAndTypeAndProgramaKey(content.getContentId(),file.getRelevanceType(),content.getProgramaKey());
                    file.setRelevanceId(content.getContentId());
                    file.setProgramaKey(content.getProgramaKey());
                }
                boolean b = fileService.saveBatch(files);
                if(!b){
                    throw new BusinessException(400, "附件资料保存失败！");
                }
            }
        }
        super.updateById(content);
    }


    @Override
    public Content getById(String id) {
        Content byId = super.getById(id);
        if (byId!=null){
//            List<File> files = fileService.listReIdAndTypeAndPKey(byId.getContentId(), 5, byId.getProgramaKey());
            List<File> files = fileService.listReIdAndTypeAndPKey(byId.getContentId(), 5, byId.getProgramaKey());
            byId.setFiles(files);
        }


        return byId;
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    private void saveLog(@RequestBody Content content, int i) {
        //保存操作日志
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        ContentLog contentLog = new ContentLog();
        contentLog.setContentId(content.getContentId());
        contentLog.setType(i);
        contentLog.setGmtCreate(new Date());
        contentLog.setGmtModified(new Date());
        contentLog.setFounderId(user.getUserId());
        contentLog.setFounderName(user.getUserName());
        contentLog.setModifyId(user.getUserId());
        contentLog.setModifyName(user.getUserName());
        contentLogMapper.insert(contentLog);
    }

    /**
     * Description: 内容批量导入
     * date: 2022/11/7 10:26
     *
     * @author: sund
     * @since JDK 1.8
     */
    @Override
    public void importBatchContent(List<Content> content) {
        contentService.saveBatch(content);
    }

    /**
     * Description: 根据内容id修改内容信息
     * date: 2022/11/7 14:03
     *
     * @since JDK 1.8
     */
    @Override
    public void updateContentInfo(Content content) {
        baseMapper.updateById(content);
    }

    /**
     * Description: 根据ids进行逻辑删除
     * date: 2022/11/7 14:18
     *
     * @since JDK 1.8
     */
    @Override
    public void removeLogicBatch(List<String> ids) {
        lambdaUpdate().in(Content::getContentId, ids).set(Content::getIsDelete, ContentEnum.IS_DELETE_YES.getCode()).update();
    }

    /**
     * 根据内容id集合批量修改内容发布状态
     * Description:
     * date: 2022/11/7 15:16
     */
    @Override
    public void updateContentState(UpdateContentStateDTO dto) {
        List<String> contnetIds = dto.getContentId();
        int state = dto.getState();
        // 发布
        int code1 = ContentEnum.STATE_PUTAWAY.getCode();
        // 未发布
        int code2 = ContentEnum.STATE_SOLD_OUT.getCode();
        if (code1 == state) {
            lambdaUpdate().in(Content::getContentId, contnetIds).set(Content::getState, code1).set(Content::getGmtRelease,new Date()).update();
        }
        if (code2 == state) {
            lambdaUpdate().in(Content::getContentId, contnetIds).set(Content::getState, code2).set(Content::getGmtRelease,new Date()).update();
        }
    }


    /**
     * 批量发布取消发布
     *
     * @param ids
     * @param type
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByPublish(List<String> ids, String type) {
        List<Content> resutls = listByIds(ids);
        for (Content content : resutls) {
            if ("1".equals(type)) {
                if (content.getGmtRelease()==null){
                    content.setGmtRelease(new Date());
                }else {
                    if (content.getGmtRelease().compareTo(new Date())<0){
                        content.setGmtRelease(new Date());
                    }
                }
                content.setState(ContentEnum.STATE_PUTAWAY.getCode());
                saveLog(content,2);
            } else {
                content.setGmtRelease(null);
                content.setState(ContentEnum.STATE_SOLD_OUT.getCode());
                saveLog(content,3);
            }
        }
        super.saveOrUpdateBatch(resutls);
    }

    @Override
    public PageUtils queryContentPage(JSONObject jsonObject, LambdaQueryWrapper<Content> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String title = (String) innerMap.get("title");
        Integer home = (Integer) innerMap.get("home");
        Integer top = (Integer) innerMap.get("top");
        Integer state = (Integer) innerMap.get("state");
        Integer mallType = Integer.valueOf(mallConfig.mallType);
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String programaKey = (String) innerMap.get("programaKey");

        if (!StringUtils.isEmpty(title)) {
            queryWrapper.like(Content::getTitle, title);
        }
        if (!StringUtils.isEmpty(keywords)) {
            queryWrapper.like(Content::getTitle, keywords);
        }
        if (mallType != null) {
            queryWrapper.eq(Content::getMallType, mallType);
        }
        if (home != null) {
            queryWrapper.eq(Content::getHome, home);
        }
        if (top != null) {
            queryWrapper.eq(Content::getTop, top);
        }
        if (state != null) {
            queryWrapper.eq(Content::getState, state);
        }
        if (state != null) {
            queryWrapper.eq(Content::getState, state);
        }
        //栏目KEY
        if (org.apache.commons.lang.StringUtils.isNotEmpty(programaKey)) {
            queryWrapper.eq(Content::getProgramaKey, programaKey.trim());
        }
        //排序方式(排序值升序)
        if (orderBy == null) {
        }
        else if (orderBy == PublicEnum.ORDER_BY_SORT.getCode()) {
            queryWrapper.orderByDesc(Content::getSort).orderByDesc(Content::getGmtCreate);
        //排序方式(修改时间降序)
        }else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(Content::getGmtModified);
        //排序方式(发布时间降序)
        }else if (orderBy == PublicEnum.ORDER_BY_GMT_RELEASE.getCode()) {
            queryWrapper.orderByDesc(Content::getGmtRelease);
        }
      /*  queryWrapper.select(Content.class, f -> {
            return !f.getProperty().equals("content");
        });*/
            IPage<Content> page = this.page(
                    new Query<Content>().getPage(jsonObject),
                    queryWrapper
         );
        List<Content> records = page.getRecords();
        if (records!=null&&records.size()>0){
            for (Content record : records) {
           List<File> files=fileService.listReIdAndTypeAndPKey(record.getContentId(),5,record.getProgramaKey());
           record.setFiles(files);
            }

        }


        return new PageUtils(page);
    }

    @Override
    public PageUtils queryPublicContentPage(JSONObject jsonObject, LambdaQueryWrapper<Content> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String title = (String) innerMap.get("title");
        Integer home = (Integer) innerMap.get("home");
        Integer top = (Integer) innerMap.get("top");
        Integer state = (Integer) innerMap.get("state");
        Integer mallType = Integer.valueOf(mallConfig.mallType);
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String programaKey = (String) innerMap.get("programaKey");

        if (!StringUtils.isEmpty(title)) {
            queryWrapper.like(Content::getTitle, title);
        }
        if (!StringUtils.isEmpty(keywords)) {
            queryWrapper.like(Content::getTitle, keywords);
        }
        queryWrapper.lt(Content::getGmtRelease,new Date());
        if (mallType != null) {
            queryWrapper.eq(Content::getMallType, mallType);
        }
        if (home != null) {
            queryWrapper.eq(Content::getHome, home);
        }
        if (top != null) {
            queryWrapper.eq(Content::getTop, top);
        }
        if (state != null) {
            queryWrapper.eq(Content::getState, state);
        }else {
            queryWrapper.eq(Content::getState, 1);
        }
        //栏目KEY
        if (org.apache.commons.lang.StringUtils.isNotEmpty(programaKey)) {
            queryWrapper.eq(Content::getProgramaKey, programaKey.trim());
        }
        //排序方式(排序值升序)
        if (orderBy == null) {
            queryWrapper.orderByAsc(Content::getTop).orderByDesc(Content::getSort).orderByDesc(Content::getGmtRelease);
        }
        else if (orderBy == PublicEnum.ORDER_BY_SORT.getCode()) {
            queryWrapper.orderByDesc(Content::getSort).orderByDesc(Content::getGmtCreate);
            //排序方式(修改时间降序)
        }else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(Content::getGmtModified);
            //排序方式(发布时间降序)
        }else if (orderBy == PublicEnum.ORDER_BY_GMT_RELEASE.getCode()) {
            queryWrapper.orderByAsc(Content::getTop).orderByDesc(Content::getSort).orderByDesc(Content::getGmtRelease);
        }
      /*  queryWrapper.select(Content.class, f -> {
            return !f.getProperty().equals("content");
        });*/
        IPage<Content> page = this.page(
                new Query<Content>().getPage(jsonObject),
                queryWrapper
        );

        List<Content> records = page.getRecords();
        if (records!=null&&records.size()>0){
            for (Content record : records) {
                List<File> files=fileService.listReIdAndTypeAndPKey(record.getContentId(),5,record.getProgramaKey());
                record.setFiles(files);
            }

        }

        return new PageUtils(page);
    }

    @Override
    public List<Content> findAllContent(String  programaKey, String state) {
        QueryWrapper<Content> wrapper = new QueryWrapper();
        wrapper.eq("programa_key", programaKey);
        wrapper.eq("mall_type", mallConfig.mallType);

        if (state!=null){
            wrapper.eq("state", state);
        }
        List<Content> list = contentService.list(wrapper);

        if (list!=null&&list.size()>0){
            for (Content content : list) {
                List<File> files = new ArrayList<>();
                files = fileService.listReIdAndTypeAndPKey(content.getContentId(), 5, programaKey);
                content.setFiles(files);
            }

         }

        return list;
    }

    @Override
    public void saveContent(Content content) {
        if (content.getTop()!=null){
            if (content.getTop().equals("1")){
                lambdaUpdate().set(Content::getTop,2).update();
            }
        }
        super.save(content);

        Content one = lambdaQuery().eq(Content::getTitle, content.getTitle()).one();
        //保存操作日志
        saveLog(one, 1);
        if (content.getFiles()!=null&&content.getFiles().size()>0){
            List<File> files = content.getFiles();
            if (files!=null&&files.size()>0){
                for (File file : files) {
                    file.setRelevanceId(content.getContentId());
                    if (file.getRelevanceType()==null){
                        file.setRelevanceType(5);
                    }
                    file.setProgramaKey(content.getProgramaKey());
                    file.setMallType(mallConfig.mallType);
                }
                boolean b = fileService.saveBatch(files);
                if(!b){
                    throw new BusinessException(400, "附件资料保存失败！");
                }
            }
        }

    }

    @Override
    public void updateContent(Content content) {
        if (content.getTop()!=null){
            if (content.getTop().equals("1")){
                lambdaUpdate().set(Content::getTop,2).update();
            }
        }
        List<File> files = content.getFiles();
        baseMapper.updateById(content);
        saveLog(content,4);
        fileService.deleteBatchFileByRelevanceIdAndTypeAndProgramaKey(content.getContentId(),5,content.getProgramaKey());
        if (files!=null&&files.size()>0){
            for (File file : files) {
                file.setMallType(mallConfig.mallType);
                file.setRelevanceId(content.getContentId());
                file.setProgramaKey(content.getProgramaKey());
            }
           fileService.saveOrUpdateBatch(files);

        }
    }

    @Override
    public List<ContentLog> findListByContentId(String id) {

        QueryWrapper<ContentLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("content_id",id);
        queryWrapper.orderByDesc("gmt_create");
        return contentLogMapper.selectList(queryWrapper);
    }
}
