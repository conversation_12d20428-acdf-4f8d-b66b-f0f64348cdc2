package scrbg.meplat.mall.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.R;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.common.redis.RedisKey;
import scrbg.meplat.mall.common.wxApp.WxAppConfig;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.dto.user.OrgAndSon;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.CodeEnum;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.user.AuthArrUtil;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.EnterpriseInfoMapper;
import scrbg.meplat.mall.mapper.ReceiptPersonMapper;
import scrbg.meplat.mall.mapper.ShopMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.vo.user.LoginVO;
import scrbg.meplat.mall.vo.user.OrganizationVO;
import scrbg.meplat.mall.vo.user.WxLoginVo;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/14
 */
@Service
@Log4j2
public class WxAppLoginServiceImpl extends ServiceImpl<ReceiptPersonMapper, ReceiptPerson> implements WxAppLoginService {

    @Autowired
    UserActivityService userActivityService;
    @Autowired
    private MallConfig mallConfig;

    @Autowired
    public SystemParamService systemParamService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    RabbitTemplate rabbitTemplate;
    @Resource
    private JwtTokenUtil jwtUtil;

    @Autowired
    UserService userService;
    @Autowired
    ShopService shopService;


    @Autowired
    private RestTemplate restTemplate;


    @Autowired
    FileService fileService;

    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private SysRoleService sysRoleService;


    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    @Autowired
    private RestTemplateUtils restTemplateUtils;
    //@Value("${wx.appid}")
    /***
     * 收料小程序APPID
     */
    private final static String WX_SL_APP_ID = "wx5c5fafe2cce5f1d1";
    /**
     * 供应商登录appid
     */
    private final static String WX_SUPPLIER_APP_ID = "wx0d14681b97452f02";
    //@Value("${wx.secret}")
    /***
     * 收料小程序APPSECRET
     */
    private final static String WX_SL_APP_SECRET = "48fdf7f074b453f92e4b1efcb2409633";
    /***
     * 供应商登录密钥
     */
    private final static String WX_SUPPLIER_APP_SECRET = "901b90a7ebdc48a953ee16bea0d7f464";

    /**
     * 收料员
     * 通过微信openID登录
     *
     * @param openId 微信openID
     * @return 验证信息token（区分前缀WxAppConfig.wxAppPrefix）和收料员用户信息{userInfo}
     */
    @Override
    public Map loginSlByOpenId(String openId) {
        int mallType = mallConfig.mallType;
        if (StringUtils.isBlank(openId)) {
            throw new BusinessException("请携带微信OpenId");
        }
        ReceiptPerson receiptPeople = lambdaQuery().eq(ReceiptPerson::getOpenId, openId).one();
        if (null == receiptPeople) {
            throw new BusinessException(403,"未绑定微信，请使用手机号登录");
        } else {
            if (receiptPeople.getState()==0){
                throw new BusinessException("收料员账号已停用");
            }
            // 收料表存在用户，返回 token
            String token = jwtUtil.generateToken(receiptPeople.getPhone());
            stringRedisTemplate.opsForValue()
                    .set(RedisKey.WX_APP_SL_USER_MAP_KEY + mallType + "_" + receiptPeople.getPhone()
                            , JSON.toJSONString(receiptPeople),
                            mallConfig.loginOutTime,
                            TimeUnit.MINUTES);
            Map<String, Object> map = new HashMap<>();
            receiptPeople.setToken(WxAppConfig.wxAppPrefixSL + token);
            //map.put("token", WxAppConfig.wxAppPrefixSL + token);
            map.put("userInfo", receiptPeople);
            return map;
        }

    }

    /**
     * 收料员
     * 使用手机号验证码完成用户登录
     *
     * @param phone  手机号
     * @param code   验证码
     * @param openId 微信openID
     * @return 验证信息token（区分前缀WxAppConfig.wxAppPrefix）和收料员用户信息{userInfo}
     */
    public Map wxLoginSlByPhoneCode(String phone, String code, String openId) {
        int mallType = mallConfig.mallType;
        boolean isRight = verifyCode(phone, code, openId, RedisKey.WX_APP_SL_LOGIN_PHONE_CODE_KEY);
        if (isRight) {
            // 验证码正确==》收料员首次登录，保存微信用户openID
            ReceiptPerson person = lambdaQuery().eq(ReceiptPerson::getPhone, phone).one();
            if (person.getState()==0){
                throw new BusinessException("收料员账号已停用");
            }
            person.setOpenId(openId);
            super.updateById(person);
            String token = jwtUtil.generateToken(person.getPhone());
            stringRedisTemplate.opsForValue()
                    .set(RedisKey.WX_APP_SL_USER_MAP_KEY + mallType + "_" + person.getPhone()
                            , JSON.toJSONString(person),
                            mallConfig.loginOutTime,
                            TimeUnit.MINUTES);
            Map<String, Object> map = new HashMap<>();
            person.setToken(WxAppConfig.wxAppPrefixSL + token);
            //map.put("token", WxAppConfig.wxAppPrefixSL + token);
            map.put("userInfo", person);
            return map;
        }
        return null;
    }

    /***
     * 校验验证码
     * @param phone
     * @param code
     * @param openId
     * @param key 验证码前缀
     * @return
     */
    private boolean verifyCode(String phone, String code, String openId, String key) {
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException("手机号码格式错误！");
        }
        if (StringUtils.isBlank(code)) {
            throw new BusinessException("请输入验证码！");
        }
        if (StringUtils.isBlank(openId)) {
            throw new BusinessException("请携带微信openId");
        }
        String rCode = stringRedisTemplate.opsForValue().get(key + phone);
        if (rCode == null) {
            throw new BusinessException("验证码已失效，请重新发送！");
        }
        if (!code.equals(rCode.split("_")[0])) {
            throw new BusinessException("验证码错误！");
        }
        return true;
    }

    /**
     * 通过手机号发送短信验证码
     *
     * @param wxLoginVo 手机号
     * @param request
     */
    @Override
    public void sendWxLoginCode(WxLoginVo wxLoginVo, String type, HttpServletRequest request) {
        String phone = wxLoginVo.getPhone();
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException("手机号码格式错误！");
        }
        if(StringUtils.isEmpty(wxLoginVo.getPrivateKeyId())) {
            throw new BusinessException("非法请求！");
        }
        String rCode = stringRedisTemplate.opsForValue().get("register:privateId" + phone);
        if (rCode == null) {
            throw new BusinessException("获取失败，请重新获取！");
        }
        log.info(this.getClass().getName()+":sendWxLoginCode:"+rCode);
        stringRedisTemplate.delete("register:privateId" + phone);
//        String decrypt = AESUtil.encrypt(rCode);
//        if(!decrypt.equals(decrypt)) {
//            throw new BusinessException("非法请求！");
//        }else {
//            stringRedisTemplate.delete("register:privateId" + phone);
//        }
        if (type.contains("sl")) {
            // 收料登录时候判断用户手机号是否存在数据库中
            Integer count = lambdaQuery().eq(ReceiptPerson::getPhone, phone).count();
            if (count == 0) {
                log.error("未注册登陆发送短信手机号：" + wxLoginVo.getPhone());
                throw new BusinessException(phone + "该用户未注册！");
            }else {
                ifUserSendCode(phone, request);
            }
        }
        if(type.equals("supplierCode")) {
            // 查询用户是否存在
            Integer count = userService.lambdaQuery().eq(User::getUserMobile, phone).count();
            if (count == 0) {
                log.error("未注册登陆发送短信手机号：" + phone);
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "该用户未注册，请注册后再试！");
            }else {
                ifUserSendCode(phone, request);
            }
        }

        saveAndSendCode(phone, getConstantByType(type));
    }

    private void ifUserSendCode(String phone, HttpServletRequest request) {
        User user = userService.lambdaQuery().eq(User::getUserMobile, phone).one();
        // 检验最大次数
        UserActivity u = new UserActivity();
        if(user != null) {
            u.setUserId(user.getUserId());
            u.setUserName(user.getRealName());
            u.setOrgId(user.getEnterpriseId());
        }
        u.setLoginTime(new Date());
        try {
            String ipAddress = request.getHeader("X-Forwarded-For").split(":")[0];
            u.setLoginIp(ipAddress);
        }catch (Exception e) {
        }
        u.setUserMobile(phone);
        u.setBusType(1);
        userActivityService.save(u);
        int maxCodeNum = PublicEnum.MAX_SEND_CODE.getCode();
        Integer count1 = userActivityService.lambdaQuery().eq(UserActivity::getUserMobile, phone)
                .eq(UserActivity::getBusType, 1)
                .apply("DATE_FORMAT(login_time, '%Y-%m-%d')='"+ LocalDate.now() + "'").count();
        if(count1 > maxCodeNum) {
            throw new BusinessException("发送短信次数过多！请联系管理员！");
        }
    }
    /***
     * 发送手机验证码，保存验证码到redis
     * @param phone
     * @param key 验证码前缀
     */
    private void saveAndSendCode(String phone, String key) {
        //String key = getConstantByType(type);
        String redisCode = stringRedisTemplate.opsForValue().get(key + phone);
        if (StringUtils.isNotBlank(redisCode) && System.currentTimeMillis() - Long.parseLong(redisCode.split("_")[1]) < RedisKey.WX_APP_LOGIN_PHONE_CODE_TTL * 6 * 10000) {
            // 调用接口小于60秒间隔不允许重新发送新的验证码
            throw new BusinessException("请勿频繁发送！");
        }
        // 生成短信验证码
        String code = CommonUtil.getCode();
        // 发送验证码
        sendCodeUtil(phone, code);
        //将验证码存入redis
        stringRedisTemplate.opsForValue().set(key + phone, code + "_" + System.currentTimeMillis(), RedisKey.WX_APP_LOGIN_PHONE_CODE_TTL, TimeUnit.MINUTES);
        log.warn("本次WxAppLogin发送验证码:" + code);
    }

    /**
     * 根据传入参数类型自动选择RedisKey下的常量,验证码前缀和用户信息前缀。
     * 用于验证码发送时和退出登录时key获取的方法复用
     *
     * @param type
     * @return
     */
    public String getConstantByType(String type) {
        switch (type) {
            case "slUser":
                return RedisKey.WX_APP_SL_USER_MAP_KEY;
            case "slCode":
                return RedisKey.WX_APP_SL_LOGIN_PHONE_CODE_KEY;
            case "supplierUser":
                return RedisKey.WX_APP_SUPPLIER_USER_MAP_KEY;
            case "supplierCode":
                return RedisKey.WX_APP_SUPPLIER_LOGIN_PHONE_CODE_KEY;
        }
        return "";
    }

    /***
     * 供应商通过验证码登录成功，首次需要保存openid
     * @param phone
     * @param code
     * @param openId
     * @return
     */
    @Override
    public Map wxLoginSupplierByPhoneCode(String phone, String code, String openId) {
        boolean isRight = verifyCode(phone, code, openId, RedisKey.WX_APP_SUPPLIER_LOGIN_PHONE_CODE_KEY);
        if (isRight) {
            int mallType = mallConfig.mallType;
            // 验证码正确
            LoginVO loginVO = supplierLogin(phone, null, mallType, openId);
            String token = jwtUtil.generateToken(loginVO.getUserMobile());
            stringRedisTemplate.opsForValue()
                    .set(RedisKey.WX_APP_SUPPLIER_USER_MAP_KEY + mallType + "_" + loginVO.getUserMobile()
                            , JSON.toJSONString(loginVO),
                            mallConfig.loginOutTime,
                            TimeUnit.MINUTES);
            Map<String, Object> map = new HashMap<>();
            loginVO.setToken(WxAppConfig.wxAppPrefixSupplier + token);
            map.put("userInfo", loginVO);
            return map;
        }
        return null;
    }

    @Override
    public Map loginSupplierByOpenId(String openid) {
        if (StringUtils.isBlank(openid)) {
            throw new BusinessException("微信登录请携带微信openid");
        }
        User user = userService.lambdaQuery().eq(User::getWxOpenId, openid).one();
        if (user == null) {
            throw new BusinessException("未绑定微信，请使用手机号登录");
        }
        int mallType = mallConfig.mallType;
        LoginVO loginVO = supplierLogin(user.getUserMobile(), null, mallType, null);
        String token = jwtUtil.generateToken(loginVO.getUserMobile());
        stringRedisTemplate.opsForValue()
                .set(RedisKey.WX_APP_SUPPLIER_USER_MAP_KEY + mallType + "_" + loginVO.getUserMobile()
                        , JSON.toJSONString(loginVO),
                        mallConfig.loginOutTime,
                        TimeUnit.MINUTES);
        Map<String, Object> map = new HashMap<>();
        loginVO.setToken(WxAppConfig.wxAppPrefixSupplier + token);
        map.put("userInfo", loginVO);
        return map;
    }

    /**
     * 验证手机号删除redis用户信息
     *
     * @param wxLoginVo phone手机号
     */
    @Override
    public void logout(WxLoginVo wxLoginVo, String type) {
        String key = getConstantByType(type);
        if (StringUtils.isBlank(wxLoginVo.getPhone())) {
            throw new BusinessException("退出登录，手机号不可为空");
        }
        if (RegexUtils.isPhoneInvalid(wxLoginVo.getPhone())) {
            throw new BusinessException("手机号格式错误！");
        }
        int mallType = mallConfig.mallType;
        if (stringRedisTemplate.hasKey(key + mallType + "_" + wxLoginVo.getPhone())) {
            stringRedisTemplate.delete(key + mallType + "_" + wxLoginVo.getPhone());
        }
    }


    public String getWxOpenId(String appid, String secert, String code) {
        String url = String.format("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code", appid, secert, code);
        String accessTokenStr = HttpUtil.get(url, 5000);
        JSONObject accessToken = JSONUtil.parseObj(accessTokenStr);
        String openId = accessToken.getStr("openid");
        if (openId == null){
            throw new BusinessException("微信小程序openid获取错误"+accessTokenStr);
        }
        return openId;
    }

    /***
     * 根据传输code获取openid
     * @param code
     * @return
     */
    @Override
    public String getSlWxOpenIdByCode(String code) {
        return getWxOpenId(WX_SL_APP_ID, WX_SL_APP_SECRET, code);
    }

    @Override
    public String getSupplierOpenIdByCode(String code) {
        return getWxOpenId(WX_SUPPLIER_APP_ID, WX_SUPPLIER_APP_SECRET, code);
    }

    /**
     * 短信发送方法
     *
     * @param phone 手机号
     * @param code  验证码
     */
    private void sendCodeUtil(String phone, String code) {
        String sendExchange = mallConfig.sendCodeExchange;
        String message = "{\"Content\":\"登陆验证码：" + code + "，请不要把验证码泄漏给其他人\",\"PhoneNumbers\":[\"" + phone + "\"]}";
        try {
            rabbitTemplate.convertAndSend(sendExchange, "", message);
        } catch (Exception e) {
            throw new BusinessException("验证码发送失败");
        }
    }


    /***
     * 供应商登录方法(拷贝系统供应商登录方法)
     */
    @Transactional(rollbackFor = Exception.class)

    public LoginVO supplierLogin(String account, String password, Integer mallType,String openid) {
        // 标识是否是携带微信openid登录的
        Boolean isWx = org.apache.commons.lang.StringUtils.isNotBlank(openid);
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        Integer userCount = userService.lambdaQuery()
                .and((t) -> t.eq(User::getAccount, account)
                        .or()
                        .eq(User::getUserMobile, account))
                .eq(mallType == 0, User::getMaterialState, 0)
                .eq(mallType == 1, User::getDeviceState, 0).count();
        if (userCount > 0) {
            throw new BusinessException(400, "该用户已被停用，禁止登陆！");
        }
        boolean isPhone = false;
        boolean isHaveInteriorId = false;
        // 校验是不是手机号
        if (!RegexUtils.isPhoneInvalid(account) && password == null) {
            isPhone = true;
        }
        com.scrbg.common.utils.R<Map> r = null;
        HttpHeaders thisHeaders = new HttpHeaders();
        thisHeaders.add("sysCode", "msp");
        HashMap<String, Object> thisMap = new HashMap<>();
        thisMap.put("sysCode", "msp");
        String content = JSON.toJSONString(thisMap);
        HttpEntity<String> thisRequest = new HttpEntity<>(content, thisHeaders);
        if (isPhone) {
            // 如果是手机号
            String url = mallConfig.prodPcwp2Url + "/identity/auth/createExternalToken?phoneNo=" + account + "&sysCode=msp";
            r = restTemplate.postForObject(url, thisRequest, com.scrbg.common.utils.R.class);
        } else {
            String url = mallConfig.prodPcwp2Url + "/identity/auth/signin?account="
                    + account + "&password=" + password + "&identityType=5&sysCode=msp";
            r = restTemplate.postForObject(url, null, com.scrbg.common.utils.R.class);
        }
        if (r.getCode() == 200) {
            LoginVO vo = BeanUtils.mapToBean(r.getData(), LoginVO.class);
            vo.setOriginalUserName(vo.getUserName());
            vo.setFarUserId(vo.getUserId());
            Integer isInterior = 0;
            if (vo.getIsExternal() == null) {
                vo.setIsExternal(1);
                isInterior = 0;
            }
            if (vo.getIsExternal() == 0) {
                isInterior = 1;
            }
            vo.setIsInterior(isInterior);
            // 是内部用户
            if (isInterior == 1) {
                if (stringRedisTemplate.hasKey(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId())) {
                    LoginVO loginVO = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId()), LoginVO.class);
                    log.warn("已登陆，直接登陆成功！");
                    return loginVO;
                }
                // 内部都是企业
                vo.setEnterpriseType(1);
                User qUser = userService.lambdaQuery().eq(User::getInteriorId, vo.getUserId()).one();
                String orgUrl = mallConfig.prodPcwp2Url + "/hr/org/getOrgByUserId?userId=" + vo.getFarUserId();
//                String orgUrl = "http://pcwp2-api.scrbg.com/hr/org/getOrgByUserId?userId=" + vo.getFarUserId();
                com.scrbg.common.utils.R orgR = restTemplate.getForObject(orgUrl, com.scrbg.common.utils.R.class);
                if (orgR.getCode() == 200) {
                    ArrayList orgArr = (ArrayList) orgR.getData();
                    // 第一个就是当前企业
                    if (!CollectionUtils.isEmpty(orgArr)) {
                        Map orgMap = (Map) orgArr.get(0);
                        vo.setOrgInfo(orgMap);
                        // 设置当前机构
                        String orgId = (String) orgMap.get("orgId");
                        String orgName = (String) orgMap.get("orgName");
                        vo.setOrgId(orgId);
                        vo.setOrgName(orgName);
                        HttpHeaders headers = new HttpHeaders();
                        headers.add("token", vo.getToken());
                        headers.add("org", com.alibaba.fastjson.JSONObject.toJSONString(orgMap));
                        if (mallType == 1) {
                            headers.add("sysCode", "egp");
                        }
                        if (mallType == 0) {
                            headers.add("sysCode", "msp");
                        }
//                        System.out.println("当前机构的json：" + JSONObject.toJSONString(orgMap));

                        // 配置角色数据
                        configRoteInfo(mallType, vo, headers);

//                        System.out.println("九宫格菜单：" + menuR.toString());
                        // 本机构以及其下机构列表
                        String orgCUrl = mallConfig.prodPcwp2Url + "/hr/org/getChildrenOrg?orgId=" + orgId
                                + "&orgName=" + orgName;
                        ResponseEntity<com.scrbg.common.utils.R> mapResponseEntity = restTemplateUtils.get(orgCUrl, headers, com.scrbg.common.utils.R.class);
                        R thisAndChild = mapResponseEntity.getBody();
                        if (thisAndChild.getCode() == 200) {
                            ArrayList childList = (ArrayList) thisAndChild.getData();
                            ArrayList<String> orgIds = new ArrayList<>();
                            ArrayList<OrgAndSon> orgAndSons = new ArrayList<>();
                            if (!CollectionUtils.isEmpty(childList)) {
                                for (Object o : childList) {
                                    Map ch = (Map) o;
                                    orgIds.add((String) ch.get("orgId"));
                                    OrgAndSon orgAndSon = new OrgAndSon();
                                    orgAndSon.setOrgId((String) ch.get("orgId"));
                                    orgAndSon.setOrgName((String) ch.get("orgName"));
                                    orgAndSon.setShortCode((String) ch.get("shortCode"));
                                    orgAndSons.add(orgAndSon);
                                }
                            }
                            // 当前机构id以及子机构id
                            vo.setOrgIds(orgIds);
                            vo.setOrgAndSon(orgAndSons);
                        }
//                        System.out.println("查询当前机构以及子机构：" + mapResponseEntity.getBody());
                        // 设置机构列表
                        ArrayList<OrganizationVO> organizationVOS = new ArrayList<>();
                        boolean orgFlag = false;
                        for (Object o : orgArr) {
                            Map map = (Map) o;
                            OrganizationVO organizationVO = new OrganizationVO();
                            organizationVO.setOrgId((String) map.get("orgId"));
                            organizationVO.setOrgName((String) map.get("orgName"));
                            organizationVO.setShortCode((String) map.get("shortCode"));
                            organizationVO.setOrgType((Integer) map.get("orgType"));
//                            Integer isShpAuthority = vo.getIsShpAuthority();
//                            if (isShpAuthority != null && isShpAuthority == 1) {
                            // 物资
                            if (mallType == 0) {
                                if (mallConfig.isBusinessOrg==1){
                                    Boolean flag = systemParamService.getIsBusinessOrg(organizationVO.getOrgId());
                                    if (flag) {
                                        orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                    }
                                }else {
                                    if (mallConfig.isMPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                        orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                    }
                                }
                            }
                            // 装备
                            if (mallType == 1) {
                                if (mallConfig.isDPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                    orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                }
                            }
//                            }
                            organizationVOS.add(organizationVO);
                        }
                        if (orgFlag) {
                            vo.setIsPlatformAdmin(1);
                        } else {
                            vo.setIsPlatformAdmin(0);
                        }
                        if ("wisesoft".equals(account)) {
                            vo.setIsPlatformAdmin(1);
                        }
                        vo.setOrganizationVOS(organizationVOS);
                    }
                } else {
                    throw new BusinessException(orgR.getCode(), orgR.getMessage());
                }
                if (qUser == null) {
                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();
                    if (enterpriseInfo == null) {
                        // 企业不存在
                        EnterpriseInfo enterpriseInfo1 = new EnterpriseInfo();
                        // 企业编号
                        enterpriseInfo1.setCreationTime(new Date());
                        enterpriseInfo1.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                        enterpriseInfo1.setInteriorId(vo.getOrgId());
                        enterpriseInfo1.setEnterpriseName(vo.getOrgName());
                        enterpriseInfo1.setState(PublicEnum.IS_YES.getCode());
                        enterpriseInfo1.setIsSupplier(PublicEnum.IS_NO.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            enterpriseInfo1.setIsMaterialMall(PublicEnum.IS_YES.getCode());
                        } else {
                            enterpriseInfo1.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                        }
                        enterpriseInfo1.setEnterpriseType(1);
                        enterpriseInfo1.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                        int insert = enterpriseInfoMapper.insert(enterpriseInfo1);
                        if (insert != 0) {
                            vo.setLocalOrgId(enterpriseInfo1.getEnterpriseId());
                            vo.setEnterpriseName(enterpriseInfo1.getEnterpriseName());
                            vo.setEnterpriseType(enterpriseInfo1.getEnterpriseType());
                            // 如果用户不存在则保存内部用户数据
                            User user = new User();
                            if (isPhone) {
                                user.setUserMobile(account);
                            } else {
                                user.setAccount(account);
                            }
                            if (!isPhone) {
                                user.setPassword(password);
                            }
                            user.setInteriorId(vo.getUserId());
                            user.setNickName(vo.getUserName());
                            user.setEnterpriseId(enterpriseInfo1.getEnterpriseId());
                            user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                            // 状态
                            user.setIsInternalUser(isInterior);
                            if (isWx){
                                user.setWxOpenId(openid);
                            }
                            user.setState(PublicEnum.IS_YES.getCode());
                            if (mallType == PublicEnum.MATERIALS.getCode()) {
                                user.setIsMaterial(PublicEnum.IS_YES.getCode());
                            } else {
                                user.setIsDevice(PublicEnum.IS_YES.getCode());
                            }
                            boolean save = userService.save(user);
                            if (save) {
                                vo.setUserId(user.getUserId());
                                vo.setUserName(user.getNickName());
                                vo.setUserNumber(user.getUserNumber());
                            }
                        }
                    } else {
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                        }
                        // 企业存在用户不存在
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        // 如果用户不存在则保存内部用户数据
                        User user = new User();
                        if (isPhone) {
                            user.setUserMobile(account);
                        } else {
                            user.setAccount(account);
                        }
                        if (!isPhone) {
                            user.setPassword(password);
                        }
                        if (isWx){
                            user.setWxOpenId(openid);
                        }
                        user.setInteriorId(vo.getUserId());
                        user.setNickName(vo.getUserName());
                        user.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                        user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                        user.setIsInternalUser(isInterior);
                        user.setState(PublicEnum.IS_YES.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            user.setIsMaterial(PublicEnum.IS_YES.getCode());
                        } else {
                            user.setIsDevice(PublicEnum.IS_YES.getCode());
                        }
                        boolean save = userService.save(user);
                        if (save) {
                            vo.setUserId(user.getUserId());
                            vo.setUserName(user.getNickName());
                            vo.setUserNumber(user.getUserNumber());
                        }
                    }
                } else {
                    // 用户存在
                    vo.setUserId(qUser.getUserId());
                    vo.setUserName(qUser.getNickName());
                    vo.setUserNumber(qUser.getUserNumber());

                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();
                    if (enterpriseInfo != null) {
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                        }
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        // 每次登陆修改回当前机构
                        LambdaUpdateChainWrapper<User> setL = userService.lambdaUpdate().eq(User::getUserId, qUser.getUserId())
                                .set(User::getEnterpriseId, enterpriseInfo.getEnterpriseId());
                        // 从token 登陆进来可能不会保存用户账号，如果这里用户但是账号或手机号不存在则需要保存
                        String account1 = qUser.getAccount();
                        String userMobile = qUser.getUserMobile();
                        if (org.springframework.util.StringUtils.isEmpty(account1) && org.springframework.util.StringUtils.isEmpty(userMobile)) {
                            // 如果账号手机号都不存在则保存
                            setL.set(isPhone, User::getUserMobile, account);
                            setL.set(!isPhone, User::getAccount, account);
                        }
                        setL.set(isWx,User::getWxOpenId,openid);
                        setL.update();
                        if (vo.getIsShpAuthority() == 1) {
                            Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                    .eq(Shop::getMallType, mallType)
                                    .one();
                            if (shop != null) {
                                vo.setShopId(shop.getShopId());
                                vo.setShopType(shop.getShopType());
                                vo.setShopName(shop.getShopName());
                            }
                        }
                    }
                }
            } else {
                vo.setIsSubmitOrder(1);
                vo.setIsCheck(0);
                vo.setIsPlatformAdmin(0);
                vo.setIsTender(0);
                // 外部用户
                // 外部用户都是手机号登陆
                User userOne = userService.lambdaQuery()
//                        .eq(!isPhone,User::getAccount, account)
                        .eq(User::getUserMobile, account)
                        .eq(User::getIsInternalUser, 0)
                        .one();
                // 用户存在
                if (userOne != null) {
                    if (org.springframework.util.StringUtils.isEmpty(userOne.getInteriorId())) {
                        isHaveInteriorId = true;
                    }
                    vo.setUserId(userOne.getUserId());
                    vo.setUserNumber(userOne.getUserNumber());
                    vo.setUserMobile(userOne.getUserMobile());
                    // 都有电商权限
                    vo.setIsShpAuthority(1);
                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getEnterpriseId, userOne.getEnterpriseId()).one();
                    if (enterpriseInfo != null) {
//                        if (enterpriseInfo.getIsSupplier() == 0 || enterpriseInfo.getIsNoSupplierAudit() == 1 || enterpriseInfo.getIsSupplier() == 1) {
//                            throw new BusinessException(400, "账号未通过供应商审核，账号被锁定无法登陆！");
//                        }
                        vo.setSocialCreditCode(enterpriseInfo.getSocialCreditCode());
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                        }
                        vo.setIsPcwp(enterpriseInfo.getIsPcwp());
                        if (mallType == 0) {
                            Shop shopDate = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                    .one();
                            if (shopDate != null) {
                                vo.setShopId(shopDate.getShopId());
                                vo.setShopType(shopDate.getShopType());
                                vo.setShopName(shopDate.getShopName());
                            }
                        }
                        if (mallType == 1) {
                            Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                    .eq(Shop::getMallType, mallType)
                                    .one();
                            if (shop != null) {
                                vo.setShopId(shop.getShopId());
                                vo.setShopType(shop.getShopType());
                                vo.setShopName(shop.getShopName());
                                if (mallType == 1) {
                                    vo.setIsOtherAuth(com.alibaba.fastjson.JSONObject.parseObject(shop.getIsOtherAuth(), Map.class));
                                }
                            }
                        }
                    } else {
                        throw new BusinessException(400, "用户名或密码错误！");
                    }
                } else {
                    throw new BusinessException(400, "用户名或密码错误！");
                }
            }
            stringRedisTemplate.opsForValue()
                    .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);
            boolean material = false;
            boolean device = false;
            // 修改
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                material = true;
            } else {
                device = true;
            }
            LambdaUpdateChainWrapper<User> eq = userService.lambdaUpdate()
                    .eq(User::getUserId, vo.getUserId());
            eq.set(isHaveInteriorId, User::getInteriorId, vo.getFarUserId());
            eq.set(material, User::getIsMaterial, PublicEnum.IS_YES.getCode())
                    .set(device, User::getIsDevice, PublicEnum.IS_YES.getCode())
                    .setSql("login_count = login_count + 1")
                    .set(isWx,User::getWxOpenId,openid)
                    .set(User::getGmtLogin, new Date()).update();

            if (vo.getOrgInfo() != null) {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(EnterpriseInfo::getShortCode, vo.getOrgInfo().get("shortCode"))
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            } else {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            }
            return vo;
        } else {
            throw new BusinessException(r.getCode(), r.getMessage());
        }
    }
    private void configRoteInfo(Integer mallType, LoginVO vo, HttpHeaders headers) {
        String isShopAuthStr = null;
        String isCheckStr = null;
        String isDeviceRecycle = null;
        String isMaterialFlow = null;
        String isSubmitOrder = null;
        String isMonthPlanAudit = null;
        if (mallType == 1) {
            isShopAuthStr = "装备采购平台-商城";
            isCheckStr = "装备采购平台-纪检";
            isDeviceRecycle = "二手设备回收运营中心";
            isMaterialFlow = "物流运营中心";
            isSubmitOrder = "装备下单权限";
        }
        if (mallType == 0) {
            isShopAuthStr = "物资采购平台-商城";
            isCheckStr = "物资采购平台-纪检";
            isSubmitOrder = "物资下单权限";
            isMonthPlanAudit = "物资月供计划审核";
        }
        // 获取角色列表
        String rotes = mallConfig.prodPcwp2Url + "/hr/role/getUserHasRoles?orgId=" + vo.getOrgId() + "&userId=" + vo.getFarUserId();
        R rotesR = null;
        try {
            rotesR = restTemplateUtils.get(rotes, headers, R.class).getBody();
        } catch (Exception e) {
            log.error("角色接口异常！" + e.getMessage());
            throw new BusinessException("角色权限远程接口异常！");
        }
        if (rotesR.getCode() != null && rotesR.getCode() == 200) {
            ArrayList rotesList = (ArrayList) rotesR.getData();
            vo.setRoles(rotesList);
            // 是否有物流运营中心权限
            boolean isMaterialFlowB = rotesList.contains(isMaterialFlow);
            if (isMaterialFlowB) {
                vo.setIsMaterialFlow(1);
            } else {
                vo.setIsMaterialFlow(0);
            }
            // 是否有二手设备回收运营中心权限
            boolean isDeviceRecycleB = rotesList.contains(isDeviceRecycle);
            if (isDeviceRecycleB) {
                vo.setIsDeviceRecycle(1);
            } else {
                vo.setIsDeviceRecycle(0);
            }
            // 是否有月供审核权限
            boolean isMonthPlanAuditB = rotesList.contains(isMonthPlanAudit);
            if (isMonthPlanAuditB) {
                vo.setIsMonthPlanAudit(1);
            } else {
                vo.setIsMonthPlanAudit(0);
            }
            // 是否有下单权限
            boolean isSubmitOrderB = rotesList.contains(isSubmitOrder);
            if (isSubmitOrderB) {
                vo.setIsSubmitOrder(1);
            } else {
                vo.setIsSubmitOrder(0);
            }
            // 是否有电商管理权限
            boolean isShopAuthStrB = rotesList.contains(isShopAuthStr);
            if (isShopAuthStrB) {
                vo.setIsShpAuthority(1);
            } else {
                vo.setIsShpAuthority(0);
            }
            // 是否有纪检权限
            boolean isCheckStrB = rotesList.contains(isCheckStr);
            if (isCheckStrB) {
                vo.setIsCheck(1);
            } else {
                vo.setIsCheck(0);
            }
        } else {
            log.error("角色接口异常！" + rotesR);
            throw new BusinessException(500, "角色权限远程接口异常！");
        }
        // 获取九宫格菜单用户查询是否有招标权限
        String menuUrl = mallConfig.prodPcwp2Url + "/permission/menu/getSpeedDial";
        R menuR = restTemplateUtils.get(menuUrl, headers, R.class).getBody();
        String menuS = menuR.toString();
        if (menuS.indexOf("1436221334647472128") != -1) {
            vo.setIsTender(1);
        } else {
            vo.setIsTender(0);
        }
    }
    private boolean isOrgFlag(Integer mallType, LoginVO vo, OrganizationVO organizationVO) {
        boolean orgFlag = false;
        String isShopAuthStr = null;
        if (mallType == 1) {
            isShopAuthStr = "装备采购平台-商城";
        }
        if (mallType == 0) {
            isShopAuthStr = "物资采购平台-商城";
        }
        HttpHeaders headers2 = new HttpHeaders();
        if (mallType == 1) {
            headers2.add("sysCode", "egp");
        }
        if (mallType == 0) {
            headers2.add("sysCode", "msp");
        }
        headers2.add("token", vo.getToken());
        headers2.add("org", com.alibaba.fastjson.JSONObject.toJSONString(organizationVO));
        // 获取角色列表
        String rotes = mallConfig.prodPcwp2Url + "/hr/role/getUserHasRoles?orgId=" + organizationVO.getOrgId() + "&userId=" + vo.getFarUserId();
        R rotesR = null;
        try {
            rotesR = restTemplateUtils.get(rotes, headers2, R.class).getBody();
        } catch (Exception e) {
            log.error("角色接口异常！" + e.getMessage());
            throw new BusinessException("角色权限远程接口异常！");
        }
        log.warn("自营机构角色信息：" + rotesR);
        if (rotesR.getCode() != null && rotesR.getCode() == 200) {
            ArrayList rotesList = (ArrayList) rotesR.getData();
            // 是否有电商管理权限
            boolean isShopAuthStrB = rotesList.contains(isShopAuthStr);
            if (isShopAuthStrB) {
                orgFlag = true;
            }
        } else {
            log.error("角色接口异常！" + rotesR);
            throw new BusinessException(500, "角色权限远程接口异常！");
        }
        return orgFlag;
    }

    @Override
    public Map<String, Object> loginByPhone(String phone) {
        int mallType = mallConfig.mallType;
        ReceiptPerson person = lambdaQuery().eq(ReceiptPerson::getPhone, phone).one();
        if (ObjectUtils.isEmpty(person)){
            throw new BusinessException("收料手机号不存在，请联系管理员添加！");
        }
        String token = jwtUtil.generateToken(person.getPhone());
        stringRedisTemplate.opsForValue()
                .set(RedisKey.WX_APP_SL_USER_MAP_KEY + mallType + "_" + person.getPhone()
                        , JSON.toJSONString(person),
                        mallConfig.loginOutTime,
                        TimeUnit.MINUTES);
        Map<String, Object> map = new HashMap<>(1);
        person.setToken(WxAppConfig.wxAppPrefixSL + token);

        map.put("userInfo", person);
        return map;
    }

    @Override
    public Map loginSupplierByPhone(String phone) {
        int mallType = mallConfig.mallType;
        // 验证码正确
        LoginVO loginVO = supplierLogin(phone, null, mallType, "openId");
        if (ObjectUtils.isEmpty(loginVO)){
            throw new BusinessException("供应商不存在");
        }
        String token = jwtUtil.generateToken(loginVO.getUserMobile());
        stringRedisTemplate.opsForValue()
                .set(RedisKey.WX_APP_SUPPLIER_USER_MAP_KEY + mallType + "_" + loginVO.getUserMobile()
                        , JSON.toJSONString(loginVO),
                        mallConfig.loginOutTime,
                        TimeUnit.MINUTES);
        Map<String, Object> map = new HashMap<>();
        loginVO.setToken(WxAppConfig.wxAppPrefixSupplier + token);
        map.put("userInfo", loginVO);
        return map;
    }
}
