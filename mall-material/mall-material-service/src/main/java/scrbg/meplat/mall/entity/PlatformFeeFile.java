package scrbg.meplat.mall.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：通用附件
 * @作者: ye
 * @日期: 2024-01-24
 */
@ApiModel(value="通用附件")
@Data
@TableName("platform_fee_file")
public class PlatformFeeFile extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "附件id")
    private String fileId;

    @ApiModelProperty(value = "附件名称")

    private String name;


    @ApiModelProperty(value = "关联id")

    private String relevanceId;


    @ApiModelProperty(value = "关联类型（1年费缴费记录2交易服务器对账单3交易服务费缴费记录）")

    private Integer relevanceType;


    @ApiModelProperty(value = "附件地址")

    private String url;


    @ApiModelProperty(value = "附件远程id")

    private String fileFarId;


    @ApiModelProperty(value = "媒体类型 1:图片2视频3附件")

    private Integer fileType;

    public static PlatformFeeFile empty() {
        PlatformFeeFile platformFeeFile = new PlatformFeeFile();
        platformFeeFile.setFounderId("");     
        platformFeeFile.setFounderName("");
        platformFeeFile.setGmtCreate(new Date());
        platformFeeFile.setGmtModified(new Date());
        platformFeeFile.setIsDelete(0);
        platformFeeFile.setMallType(0);
        platformFeeFile.setModifyId("");
        platformFeeFile.setModifyName("");
        platformFeeFile.setName("");
        platformFeeFile.setRelevanceType(1);
        platformFeeFile.setRemarks("");
        platformFeeFile.setSort(0);
        return platformFeeFile;
    }
















}