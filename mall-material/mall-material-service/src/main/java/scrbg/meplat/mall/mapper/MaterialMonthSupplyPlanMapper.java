package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;

/**
 * @描述：计划表 Mapper 接口
 * @作者: ye
 * @日期: 2023-06-27
 */

@Mapper
@Repository
public interface MaterialMonthSupplyPlanMapper extends BaseMapper<MaterialMonthSupplyPlan> {

    ArrayList<MaterialMonthSupplyPlan> secondLevelPlanMonthList(@Param("page") IPage<MaterialMonthSupplyPlan> page, @Param("ew")QueryWrapper<MaterialMonthSupplyPlan> q);
}
