package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.CategoryColumns;
import scrbg.meplat.mall.service.CategoryColumnsService;

import java.util.List;

/**
 * @描述：控制类
 * @作者: y
 * @日期: 2022-12-07
 */
@RestController
@RequestMapping("/platform/categoryColumns")
@ApiSort(value = 500)
@Api(tags = "栏目")
public class CategoryColumnsController {

    @Autowired
    public CategoryColumnsService categoryColumnsService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "栏目状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "columnName", value = "栏目名称", dataTypeClass = String.class),
            @DynamicParameter(name = "remarks", value = "备注", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<CategoryColumns> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = categoryColumnsService.queryPage(jsonObject, new LambdaQueryWrapper<CategoryColumns>());
        return PageR.success(page);
    }

    @PostMapping("/updateByPublish")
    @ApiOperation(value = "批量启用")
    public R updatePublish(@RequestBody List<String> ids) {
        categoryColumnsService.updateByPublish(ids, "1");
        return R.success();
    }

    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量停用")
    public R updateNotPublish(@RequestBody List<String> ids) {
        categoryColumnsService.updateByPublish(ids, "0");
        return R.success();
    }

    /**
     * 批量更新栏目信息
     *
     * @param
     * @return
     */
    @PostMapping("/updateBatchById")
    @ApiOperation(value = "批量更新栏目信息")
    public R update(@RequestBody List<CategoryColumns> categoryColumns) {
        categoryColumnsService.updateBatchById(categoryColumns);
        return R.success();
    }

    @PostMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "columnId", value = "栏目id", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<CategoryColumns> findById(@RequestBody String id) {
        CategoryColumns categoryColumns = categoryColumnsService.getById(id);
        return R.success(categoryColumns);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody CategoryColumns categoryColumns) {
        categoryColumnsService.create(categoryColumns);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody CategoryColumns categoryColumns) {
        categoryColumnsService.update(categoryColumns);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        categoryColumnsService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        categoryColumnsService.removeByIds(ids);
        return R.success();
    }
}

