package scrbg.meplat.mall.util;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;

public class TaxCalculator {

    /**
     * 计算不含税金额
     * @param totalAmount 含税金额
     * @param taxRate 税率（百分必1-100数）
     * @return
     */
    public static BigDecimal calculateNotTarRateAmount(BigDecimal totalAmount, BigDecimal taxRate) {
        if(totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(0);
        }
        BigDecimal divide = taxRate.divide(new BigDecimal(100)).setScale(4, BigDecimal.ROUND_HALF_UP);
        BigDecimal add = new BigDecimal(1).add(divide);
        return totalAmount.divide(add,new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算含税金额
     * @param totalAmount 不含税金额
     * @param taxRate 税率（百分必1-100数）
     * @return
     */
    public static BigDecimal calculateYesTarRateAmount(BigDecimal totalAmount, BigDecimal taxRate) {
        if(totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(0);
        }
        BigDecimal divide = taxRate.divide(new BigDecimal(100)).setScale(4, BigDecimal.ROUND_HALF_UP);
        BigDecimal add = new BigDecimal(1).add(divide);
        return totalAmount.multiply(add,new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP);
    }


    /**
     * 速率明细计算方式一  明细总不含税金额
     * @param totalAmount  含税总金额
     * @param itemRate      税率
     * @param itemCount 明细数量
     *         noPrise     不含税价格     *
     * @return
     */
    public static BigDecimal noTarRateItemAmount(BigDecimal totalAmount,BigDecimal noPrise, BigDecimal itemCount, BigDecimal itemRate ) {
        BigDecimal addNoTotalAmount= noPrise.multiply(itemCount).setScale(2, RoundingMode.HALF_UP);
        BigDecimal backNoTotalAmount= TaxCalculator.calculateNotTarRateAmount(totalAmount, itemRate).setScale(2, RoundingMode.HALF_UP);
       return backNoTotalAmount;
    }

    /**
     *
     * @param addNoTotalAmount 累计总不含税金额
     * @param totalAmount      总含税金额
     * @param  dtoRate        税率
     * @return
     */
    public static BigDecimal noTarRateAmount(BigDecimal addNoTotalAmount,BigDecimal totalAmount, BigDecimal dtoRate ) {
        BigDecimal backNoTotalAmount= TaxCalculator.calculateNotTarRateAmount(totalAmount, dtoRate).setScale(2, RoundingMode.HALF_UP);
        return addNoTotalAmount;
    }




    //通过含税和不含税反推税率
    public static BigDecimal getTaxRateByTaxAndNoTax(BigDecimal TaxAmount,BigDecimal NoTaxAmount ) {
        BigDecimal taxRate = (TaxAmount.subtract(NoTaxAmount)).divide(NoTaxAmount,2, RoundingMode.HALF_UP);
        return taxRate.multiply(BigDecimal.valueOf(100));

    }

    public static BigDecimal convertToBigDecimal(Object value) {
        if (value == null) return null;
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        throw new IllegalArgumentException("无法转换非数值类型到 BigDecimal: " + value.getClass());
    }
}
