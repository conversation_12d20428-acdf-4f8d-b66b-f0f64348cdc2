package scrbg.meplat.mall.enums.invoice;

public enum InvoiceEnum {

    STATE_STAY_PUTAWAY(0,"已申请"),
    STATE_PUTAWAY(1,"已开票");

    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    InvoiceEnum() {
    }

    InvoiceEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    }

