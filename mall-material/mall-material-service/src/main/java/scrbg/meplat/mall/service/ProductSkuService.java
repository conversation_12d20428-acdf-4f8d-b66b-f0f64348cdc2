package scrbg.meplat.mall.service;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;

import scrbg.meplat.mall.entity.ProductSku;

/**
 * @描述：商品sku 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface ProductSkuService extends IService<ProductSku> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductSku> queryWrapper);

    void create(ProductSku productSku);

    void update(ProductSku productSku);

    ProductSku getById(String id);

    ProductSku getByProductId(String productId);

    void delete(String id);

    /**
     * 根据商品id获取规格信息
     *
     * @param productId
     * @param state
     * @return
     */
    List<ProductSku> getProductSkuByProductId(String productId, Integer state);

    /**
     * 根据商品id获取规格分页信息
     *
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils listSkuByProductId(JSONObject jsonObject, LambdaQueryWrapper<ProductSku> lambdaQuery);


    /**
     * 根据id查询商品sku信息
     * @param skuId
     * @param state
     * @return
     */
    ProductSku getProductSkuById(String skuId,Integer state);


    /**
     * 查询本地数据 ，删除为删除的商品规格数据
     * @param skuId
     * @return
     */
    ProductSku getLocalDataList(String skuId);
}
