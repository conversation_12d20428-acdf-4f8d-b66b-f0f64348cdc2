package scrbg.meplat.mall.service.impl;

import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.entity.SysMenu;
import scrbg.meplat.mall.entity.SysRole;
import scrbg.meplat.mall.mapper.SysRoleMapper;
import scrbg.meplat.mall.service.SysMenuService;
import scrbg.meplat.mall.service.SysRoleService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;


import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import java.util.stream.Collectors;

/**
 * @描述：角色表 服务类
 * @作者: ye
 * @日期: 2023-12-21
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Autowired
    private SysMenuService sysMenuService;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysRole> queryWrapper) {
        IPage<SysRole> page = this.page(
                new Query<SysRole>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(SysRole sysRole) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(sysRole);
    }

    @Override
    public void update(SysRole sysRole) {
        super.updateById(sysRole);
    }


    @Override
    public SysRole getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    @Override
    public SysRole getRoleByName(String roleName) {
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", roleName);
        return baseMapper.selectOne(queryWrapper);
    }
    /**
     * 根据pcwp角色列表查询角色以及对应
     * @param roleNames
     * @return
     */
    @Override
    public List<MallRole> getRoleMenuListByRoleNames(List<String> roleNames) {
        if(CollectionUtils.isEmpty(roleNames)) {
            return new ArrayList<>();
        }else {
//            List<MallRole> roleMenuListByRoleNames = baseMapper.getRoleMenuListByRoleNames(roleNames);
            List<MallRole> roleMenuListByRoleNames = baseMapper.getRoleMenuListByRoleNameLists(roleNames);
            return roleMenuListByRoleNames;
        }
    }

    @Autowired
    MallConfig mallConfig;
    /**
     * 根据角色名称和所属平台获取菜单列表树形
     * @param roleNames
     * @param categoryType
     * @return
     */
    @Override
    public List<SysMenu> getMenuListByRoleNames(List<String> roleNames, Integer categoryType) {
        if(CollectionUtils.isEmpty(roleNames) || categoryType == null) {
            return new ArrayList<>();
        }else {
            List<SysMenu> roleMenuListByRoleNames;
            if(mallConfig.profilesActive.equals("dev")) {
                roleMenuListByRoleNames =  baseMapper.getMenuListByRoleNames(roleNames,categoryType,null);
            }else {
               roleMenuListByRoleNames = baseMapper.getMenuListByRoleNames(roleNames,categoryType,0);
            }
            if(CollectionUtils.isEmpty(roleMenuListByRoleNames)) {
                return roleMenuListByRoleNames;
            }else {
                List<SysMenu> childrens = getChildrens(roleMenuListByRoleNames);
                return childrens;
            }
        }
    }


    private List<SysMenu> getChildrens(List<SysMenu> entities) {
        List<SysMenu> level1Menus = new ArrayList<>();
        if (entities.size() > 0) {
            level1Menus = entities.stream().
                    filter(entity -> StringUtils.isEmpty(entity.getParentMenuId()))
                    .map(entity -> {
                        entity.setChildren(getChildren(entity, entities));
                        return entity;
                    })
                    .sorted((entity1, entity2) ->
                            (entity2.getSort() == null ? 0 : entity2.getSort()) -
                                    (entity1.getSort() == null ? 0 : entity1.getSort()))
                    .collect(Collectors.toList());
        }
        return level1Menus;
    }

    private List<SysMenu> getChildren(SysMenu root, List<SysMenu> all) {
        List<SysMenu> children = all.stream().
                filter(entity -> root.getMenuId().equals(entity.getParentMenuId()))
                .map(entity -> {
                    entity.setChildren(getChildren(entity, all));
                    return entity;
                })
                .sorted((entity1, entity2) ->
                        (entity2.getSort() == null ? 0 : entity2.getSort()) -
                                (entity1.getSort() == null ? 0 : entity1.getSort()))
                .collect(Collectors.toList());
        return children;
    }





}
