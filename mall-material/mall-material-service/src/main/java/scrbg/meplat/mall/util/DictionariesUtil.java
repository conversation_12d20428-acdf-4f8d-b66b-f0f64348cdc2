package scrbg.meplat.mall.util;

/**
 * <AUTHOR>
 * @create 2022-12-16 16:53
 */
public class DictionariesUtil {

//    public static List<SystemParamsVO> getQualityMap(){
//        ArrayList<SystemParamsVO> vos = new ArrayList<>();
//        vos.add(new SystemParamsVO(0,"新机"));
//        vos.add(new SystemParamsVO(5,"5成新"));
//        vos.add(new SystemParamsVO(6,"6成新"));
//        vos.add(new SystemParamsVO(7,"7成新"));
//        vos.add(new SystemParamsVO(8,"8成新"));
//        vos.add(new SystemParamsVO(9,"9成新"));
//        return vos;
//    }
}
