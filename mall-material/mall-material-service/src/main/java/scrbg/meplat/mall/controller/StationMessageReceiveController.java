package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.StationMessageReceive;
import scrbg.meplat.mall.service.StationMessageReceiveService;
import scrbg.meplat.mall.vo.product.material.StationMessageReceiveVo;

import java.util.List;
import java.util.Map;

/**
 * @描述：站点接收消息控制类
 * @作者: y
 * @日期: 2022-11-24
 */
@RestController
@RequestMapping("/stationMessageReceive")
@Api(tags = "站点接收消息")
public class StationMessageReceiveController {

    @Autowired
    public StationMessageReceiveService stationMessageReceiveService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<StationMessageReceive> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = stationMessageReceiveService.queryPage(jsonObject, new QueryWrapper<StationMessageReceiveVo>());
        return PageR.success(page);
    }
    @PostMapping("/listReceiveByMgId")
    @ApiOperation(value = "根据实体属性分页查询(根据消息Id查询接收者信息)")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<StationMessageReceive> receivePageList(@RequestBody JSONObject jsonObject) {
        PageUtils page = stationMessageReceiveService.receivePageList(jsonObject, new LambdaQueryWrapper<StationMessageReceive>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<StationMessageReceive> findById(String id) {
        StationMessageReceive stationMessageReceive = stationMessageReceiveService.getById(id);
        return R.success(stationMessageReceive);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody StationMessageReceive stationMessageReceive) {
        stationMessageReceiveService.create(stationMessageReceive);
        return R.success();
    }

    @GetMapping("/getMessageNum")
    @ApiOperation(value = "查询新消息数量")
    public R getMessageNum(Integer receiveType) {
        List<StationMessageReceive>  list= stationMessageReceiveService.getMessageNum(receiveType);
        return R.success(list);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody StationMessageReceive stationMessageReceive) {
        stationMessageReceiveService.update(stationMessageReceive);
        return R.success();
    }

    @GetMapping("/updateStateById")
    @ApiOperation(value = "根据Id修改已读信息")
    public R updateById(String id) {
        stationMessageReceiveService.changStateById(id);
        return R.success();
    }
    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        stationMessageReceiveService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "IDS", required = true,
                    dataType = "list", paramType = "query")
    })
    public R deleteBatch(@RequestBody List<String> ids) {
        stationMessageReceiveService.removeByIds(ids);
        return R.success();
    }


    // 获取需要提醒的消息
    @GetMapping("/getRemindMessage")
    @ApiOperation(value = "获取需要提醒的消息")
    public R getRemindMessage() {
        Map<String,Object> list = stationMessageReceiveService.getRemindMessage();
        return R.success(list);
    }
}

