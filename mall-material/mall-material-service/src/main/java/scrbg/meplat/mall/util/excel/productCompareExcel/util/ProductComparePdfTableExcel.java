package scrbg.meplat.mall.util.excel.productCompareExcel.util;

import com.itextpdf.text.Font;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ooxml.POIXMLDocumentPart;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import scrbg.meplat.mall.util.excel.excel2pdf.Excel;
import scrbg.meplat.mall.util.excel.excel2pdf.POIImage;
import scrbg.meplat.mall.util.excel.excel2pdf.POIUtil;
import scrbg.meplat.mall.util.excel.productCompareExcel.util.*;


import java.io.IOException;
import java.net.MalformedURLException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by cary on 6/15/17.
 */
public class ProductComparePdfTableExcel {
    protected ExcelObject excelObject;
    protected Excel excel;
    protected boolean setting = false;

    /**
     * <p>Description: Constructor</p>
     *
     * @param excelObject
     */
    public ProductComparePdfTableExcel(ExcelObject excelObject) {
        this.excelObject = excelObject;
        this.excel = excelObject.getExcel();
    }

    /**
     * <p>Description: 获取转换过的Excel内容Table</p>
     *
     * @return PdfPTable
     * @throws BadElementException
     * @throws MalformedURLException
     * @throws IOException
     */
    public PdfPTable getTable() throws BadElementException, MalformedURLException, IOException {
        Sheet sheet = this.excel.getSheet();
        return toParseContent(sheet);
    }
    // 方法用于获取所有图片并返回一个Map，键为图片的位置，值为图片数据
    private static Map<String, PictureData> getPictureData(XSSFSheet sheet) {
        Map<String, PictureData> map = new HashMap<String, PictureData>();
        List<POIXMLDocumentPart> list = sheet.getRelations();
        for (POIXMLDocumentPart part : list) {
            if (part instanceof XSSFDrawing) {
                XSSFDrawing drawing = (XSSFDrawing) part;
                List<XSSFShape> shapes = drawing.getShapes();
                for (XSSFShape shape : shapes) {
                    if (shape instanceof XSSFPicture) {
                        XSSFPicture picture = (XSSFPicture) shape;
                        XSSFClientAnchor anchor = picture.getPreferredSize();
                        String key = anchor.getCol1() + "-" + anchor.getRow1();
                        map.put(key, picture.getPictureData());
                    }
                }
            }
        }

        return map;
    }
//    private PdfPCell createPdfPCell(int colIndex, int rowIndex, Cell cell) {
//
//    }

    protected PdfPTable toParseContent(Sheet sheet) throws BadElementException, MalformedURLException, IOException {
        int rows = sheet.getPhysicalNumberOfRows();
       // 获取所有图片并返回一个Map，键为图片的位置，值为图片数据
        Map<String, PictureData> pictureMap = getPictureData((XSSFSheet) sheet);

        List<PdfPCell> cells = new ArrayList<PdfPCell>();
        float[] widths = null;
        float mw = 0;
        for (int i = 0; i < rows; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }
            // 如果一行里面连续5个单元格都是空的，则这一行需要合并(设置无边框)
            AtomicInteger blankCellCount = new AtomicInteger(0);  // 用于计数连续空白单元格数量
            AtomicBoolean isMerge = new AtomicBoolean(false);  // 用于标记是否需要合并
            row.cellIterator().forEachRemaining(cell -> {
                if (cell.getCellType()== CellType.STRING) {
                    if (StringUtils.isBlank(cell.getStringCellValue())) {
                        blankCellCount.incrementAndGet();  // 空白单元格计数加一
                    } else {
                        blankCellCount.set(0);  // 如果不是空白单元格，重置计数器为零
                    }
                    // 如果一行连续五个空白单元格，设置isMerge为true（后面隐藏边框）
                    if (blankCellCount.get() >= 5) {
                        isMerge.set(true);
                    }
                }
            });
            int columns = row.getLastCellNum();

            float[] cws = new float[columns];
            for (int j = 0; j < columns; j++) {
                Cell cell = row.getCell(j);
                if (cell == null) {
                    cell = row.createCell(j);
                }
                float cw = getPOIColumnWidth(cell);
                cws[cell.getColumnIndex()] = cw;
                // 找到图片的位置，将图片转换为PDF中的图片
                String pos = j + "-" + cell.getRowIndex();
                if (pictureMap.containsKey(pos)) {
                    Image image = Image.getInstance(pictureMap.get(pos).getData());
                    // 设置缩放比例
                    //image.scaleAbsolute(95, 100);
                    // 缩放比例根据单元格的宽度和高度进行计算
                    float cellWidth = cws[cell.getColumnIndex()];
                    float cellHeight = row.getHeightInPoints() *0.85f; // Points 转换为像素
                    float scaleX = cellWidth / image.getWidth();
                    float scaleY = cellHeight  / image.getHeight();
                    float scale = Math.min(scaleX, scaleY);
                    // 设置图片大小
                    image.scaleAbsolute(image.getWidth() * scale, image.getHeight() * scale); //以原始宽高的0.9倍进行缩放
                    row.getHeight();
                    //image.setWidthPercentage(5);
                    PdfPCell pdfpCell = new PdfPCell(image);
                    pdfpCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    pdfpCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    // 缩放图片
                    cells.add(pdfpCell);
                    continue;
                }
//                if(isUsed(cell.getColumnIndex(), row.getRowNum())){
//                    continue;
//                }

                PdfPCell pdfpCell = new PdfPCell();
                pdfpCell.setBackgroundColor(new BaseColor(POIUtil.getRGB(
                        cell.getCellStyle().getFillForegroundColorColor())));
                pdfpCell.setVerticalAlignment(getVAlignByExcel(cell.getCellStyle().getVerticalAlignment().getCode()));
                pdfpCell.setHorizontalAlignment(getHAlignByExcel(cell.getCellStyle().getAlignment().getCode()));

                if (sheet.getDefaultRowHeightInPoints() != row.getHeightInPoints()) {
                    pdfpCell.setFixedHeight(this.getPixelHeight(row.getHeightInPoints()));
                }
                pdfpCell.setPhrase(getPhrase(cell));
                addImageByPOICell(pdfpCell, cell, cw);
                addBorderByExcel(pdfpCell, cell.getCellStyle(),isMerge);
                // 执行此方法在poi导出为 Workbook 是 SXSSFWorkbook的类型时，此方法会导致转换cell 为""
                //cell.setCellType(CellType.STRING);

                CellRangeAddress range = getColspanRowspanByExcel(row.getRowNum(), cell.getColumnIndex());
                int rowspan = 1;
                int colspan = 1;
                if (range != null) {
                    rowspan = range.getLastRow() - range.getFirstRow() + 1;
                    colspan = range.getLastColumn() - range.getFirstColumn() + 1;
                }
                pdfpCell.setColspan(colspan);
                pdfpCell.setRowspan(rowspan);

                cells.add(pdfpCell);
                j += colspan - 1;
            }

            float rw = 0;
            for (int j = 0; j < cws.length; j++) {
                rw += cws[j];
            }
            if (rw > mw || mw == 0) {
                widths = cws;
                mw = rw;
            }
        }

        PdfPTable table = new PdfPTable(widths);
        table.setWidthPercentage(100);
//        table.setLockedWidth(true);
        for (PdfPCell pdfpCell : cells) {
            table.addCell(pdfpCell);
        }
        return table;
    }


    protected Phrase getPhrase(Cell cell) {
        if (this.setting || this.excelObject.getAnchorName() == null) {
            return new Phrase(String.valueOf(getCellValue(cell)), getFontByExcel(cell.getCellStyle()));
        }
        Anchor anchor = new Anchor(String.valueOf(getCellValue(cell)), getFontByExcel(cell.getCellStyle()));
        anchor.setName(this.excelObject.getAnchorName());
        this.setting = true;
        return anchor;
    }

    private BaseColor getColor(short index) {
        if (HSSFColor.HSSFColorPredefined.RED.getIndex() == index) {
            return BaseColor.RED;
        } else if (HSSFColor.HSSFColorPredefined.AUTOMATIC.getIndex() == index || HSSFColor.HSSFColorPredefined.BLACK.getIndex() == index) {
            return BaseColor.BLACK;
        } else {
            return BaseColor.BLACK; // DEFAULT COLOR
        }
    }


    /**
     * 获取单元格值
     *
     * @return 单元格值
     */
    public Object getCellValue(Cell cell) {
        Object val = "";
        try {
            if (cell != null) {
                if (cell.getCellType() == CellType.NUMERIC || cell.getCellType() == CellType.FORMULA) {
                    val = cell.getNumericCellValue();
                    // TODO 不做格式化处理
                    if (HSSFDateUtil.isCellDateFormatted(cell)) {
                        // POI Excel 日期格式转换
                        val = DateUtil.getJavaDate((Double) val);
                    } else {
                        if ((Double) val % 1 > 0) {
                            val = new DecimalFormat("0.00").format(val);
                        } else {
                            val = new DecimalFormat("0").format(val);
                        }
                    }
                } else if (cell.getCellType() == CellType.STRING) {
                    val = cell.getStringCellValue();
                } else if (cell.getCellType() == CellType.BOOLEAN) {
                    val = cell.getBooleanCellValue();
                } else if (cell.getCellType() == CellType.ERROR) {
                    val = cell.getErrorCellValue();
                }
            }
        } catch (Exception e) {
            return val;
        }
        return val;
    }


    protected void addImageByPOICell(PdfPCell pdfpCell, Cell cell, float cellWidth) throws BadElementException, MalformedURLException, IOException {
        POIImage poiImage = new POIImage().getCellImage(cell);
        byte[] bytes = poiImage.getBytes();
        if (bytes != null) {
//           double cw = cellWidth;
//           double ch = pdfpCell.getFixedHeight();
//
//           double iw = poiImage.getDimension().getWidth();
//           double ih = poiImage.getDimension().getHeight();
//
//           double scale = cw / ch;
//
//           double nw = iw * scale;
//           double nh = ih - (iw - nw);
//
//           POIUtil.scale(bytes , nw  , nh);
            pdfpCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            pdfpCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            Image image = Image.getInstance(bytes);
            pdfpCell.setImage(image);
        }
    }

    protected float getPixelHeight(float poiHeight) {
        float pixel = poiHeight / 28.6f * 26f;
        return pixel;
    }

    /**
     * <p>Description: 此处获取Excel的列宽像素(无法精确实现,期待有能力的朋友进行改善此处)</p>
     *
     * @param cell
     * @return 像素宽
     */
    protected int getPOIColumnWidth(Cell cell) {
        int poiCWidth = excel.getSheet().getColumnWidth(cell.getColumnIndex());
        // com.itextpdf.text.pdf.PdfPTable.calculateWidths,此方法已经等比例转换了。不知道为什么还需要转换
        // int colWidthpoi = poiCWidth;
        // int widthPixel = 0;
        // if (colWidthpoi >= 416) {
        //     widthPixel = (int) (((colWidthpoi - 416.0) / 256.0) * 8.0 + 13.0 + 0.5);
        // } else {
        //     widthPixel = (int) (colWidthpoi / 416.0 * 13.0 + 0.5);
        // }
        return poiCWidth;
    }

    protected CellRangeAddress getColspanRowspanByExcel(int rowIndex, int colIndex) {
        CellRangeAddress result = null;
        Sheet sheet = excel.getSheet();
        int num = sheet.getNumMergedRegions();
        for (int i = 0; i < num; i++) {
            CellRangeAddress range = sheet.getMergedRegion(i);
            if (range.getFirstColumn() == colIndex && range.getFirstRow() == rowIndex) {
                result = range;
            }
        }
        return result;
    }

    protected boolean isUsed(int colIndex, int rowIndex) {
        boolean result = false;
        Sheet sheet = excel.getSheet();
        int num = sheet.getNumMergedRegions();
        for (int i = 0; i < num; i++) {
            CellRangeAddress range = sheet.getMergedRegion(i);
            int firstRow = range.getFirstRow();
            int lastRow = range.getLastRow();
            int firstColumn = range.getFirstColumn();
            int lastColumn = range.getLastColumn();
            if (firstRow < rowIndex && lastRow >= rowIndex) {
                if (firstColumn <= colIndex && lastColumn >= colIndex) {
                    result = true;
                }
            }
        }
        return result;
    }

    protected Font getFontByExcel(CellStyle style) {
        Font result = new Font(Resource.BASE_FONT_CHINESE, 8, Font.NORMAL);
        Workbook wb = excel.getWorkbook();

        short index = style.getFontIndex();
        org.apache.poi.ss.usermodel.Font font = wb.getFontAt(index);
        if (font.getBold()) {
            result.setStyle(Font.BOLD);
        }

        HSSFColor color = HSSFColor.getIndexHash().get(font.getColor());

        if (color != null) {
            int rbg = POIUtil.getRGB(color);
            result.setColor(new BaseColor(rbg));
        }

        FontUnderline underline = FontUnderline.valueOf(font.getUnderline());
        if (underline == FontUnderline.SINGLE) {
            String ulString = Font.FontStyle.UNDERLINE.getValue();
            result.setStyle(ulString);
        }
        // 获取原来的Excel中的字体大小，替换上面的默认8号字体
        org.apache.poi.ss.usermodel.Font font2 = wb.getFontAt(index);
        short fontHeightInPoints = font2.getFontHeightInPoints();
        result.setSize(fontHeightInPoints);
        // 获取Excel中的原文子体颜色
        short color2 = font2.getColor();
        BaseColor baseColor = getColor(color2);
        result.setColor(baseColor);
        return result;
    }

    protected void addBorderByExcel(PdfPCell cell, CellStyle style,AtomicBoolean isMerge) {
        if (isMerge.get()) {
            // 设置透明边框
            cell.setBorderColorLeft(new BaseColor(254,253,252));
            cell.setBorderColorRight(new BaseColor(254,253,252));
            cell.setBorderColorTop(new BaseColor(254,253,252));
            cell.setBorderColorBottom(new BaseColor(254,253,252));
            cell.setBorder(Rectangle.NO_BORDER);
            return;
        }else {
            Workbook wb = excel.getWorkbook();
            cell.setBorderColorLeft(new BaseColor(POIUtil.getBorderRBG(wb, style.getLeftBorderColor())));
            cell.setBorderColorRight(new BaseColor(POIUtil.getBorderRBG(wb, style.getRightBorderColor())));
            cell.setBorderColorTop(new BaseColor(POIUtil.getBorderRBG(wb, style.getTopBorderColor())));
            cell.setBorderColorBottom(new BaseColor(POIUtil.getBorderRBG(wb, style.getBottomBorderColor())));
        }
    }

    protected int getVAlignByExcel(short align) {
        int result = 0;
        if (align == VerticalAlignment.BOTTOM.getCode()) {
            result = Element.ALIGN_BOTTOM;
        }
        if (align == VerticalAlignment.CENTER.getCode()) {
            result = Element.ALIGN_MIDDLE;
        }
        if (align == VerticalAlignment.JUSTIFY.getCode()) {
            result = Element.ALIGN_JUSTIFIED;
        }
        if (align == VerticalAlignment.TOP.getCode()) {
            result = Element.ALIGN_TOP;
        }
        return result;
    }

    protected int getHAlignByExcel(short align) {
        int result = 0;
        if (align == HorizontalAlignment.LEFT.getCode()) {
            result = Element.ALIGN_LEFT;
        }
        if (align == HorizontalAlignment.RIGHT.getCode()) {
            result = Element.ALIGN_RIGHT;
        }
        if (align == HorizontalAlignment.JUSTIFY.getCode()) {
            result = Element.ALIGN_JUSTIFIED;
        }
        if (align == HorizontalAlignment.CENTER.getCode()) {
            result = Element.ALIGN_CENTER;
        }
        return result;
    }
}