package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.AskAnswer;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.mapper.AskAnswerMapper;
import scrbg.meplat.mall.service.AskAnswerService;
import scrbg.meplat.mall.service.UserService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.user.userCenter.AskAnswerVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @描述：问答 服务类
 * @作者: y
 * @日期: 2022-11-22
 */
@Service
public class AskAnswerServiceImpl extends ServiceImpl<AskAnswerMapper, AskAnswer> implements AskAnswerService{

    @Autowired
    UserService userService;
    @Autowired
    MallConfig mallConfig;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<AskAnswer> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String relevanceId = (String) innerMap.get("relevanceId");
        Integer type = (Integer) innerMap.get("type");
        if(StringUtils.isNotBlank(keywords)){
            q.and((t) -> {
                t.like(AskAnswer::getTitle,keywords)
                        .or()
                        .like(AskAnswer::getSendName,keywords);

            });
        }
        q.eq(type != null,AskAnswer::getType,type);
        q.eq(StringUtils.isNotBlank(relevanceId),AskAnswer::getRelevanceId,relevanceId);
        q.orderByDesc(AskAnswer::getGmtModified);
        q.eq(AskAnswer::getMallType, mallConfig.mallType);
        IPage<AskAnswer> page = this.page(
        new Query<AskAnswer>().getPage(jsonObject),
        q
        );
        return new PageUtils(page);
    }
    @Override
    public void create(AskAnswer askAnswer) {
        //调用父类方法即可
        //也可以baseMapper.insert
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        askAnswer.setSendId(userId);
        User byId = userService.getById(userId);
        if(byId != null){
            askAnswer.setSendName(byId.getNickName());
        }
        askAnswer.setSendDate(new Date());
        super.save(askAnswer);
    }

    @Override
    public void update(AskAnswer askAnswer) {
        super.updateById(askAnswer);
    }


    @Override
    public AskAnswer getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public PageUtils listByDeviceDemand(JSONObject jsonObject, QueryWrapper<AskAnswerVo> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String userId = user.getUserId();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String relevanceId = (String) innerMap.get("relevanceId");
        Integer type = (Integer) innerMap.get("type");
        if (type!=null){
            if (type==2){
                q.eq("a.type", 0);
            }else {
                q.eq("a.type", type);
            }
        }
        q.eq(StringUtils.isNotBlank(relevanceId),"a.relevance_id",relevanceId);
        q.orderByDesc("a.gmt_create");

        q.eq("a.mall_type", mallConfig.mallType);

        q.eq("a.founder_id", userId);
        IPage<AskAnswerVo> pages = new Query<AskAnswerVo>().getPage(jsonObject);
        List<AskAnswerVo> list=baseMapper.listByDeviceDemand(pages,q);
        pages.setRecords(list);
        return  new PageUtils(pages);
    }
}
