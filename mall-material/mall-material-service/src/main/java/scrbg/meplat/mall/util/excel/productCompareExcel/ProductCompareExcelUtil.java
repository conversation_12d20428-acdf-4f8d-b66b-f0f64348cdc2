package scrbg.meplat.mall.util.excel.productCompareExcel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.bytearray.ByteArrayImageConverter;
import com.alibaba.excel.converters.url.UrlImageConverter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.RichTextStringData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.IoUtils;
import com.alibaba.excel.write.merge.LoopMergeStrategy;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.itextpdf.text.BadElementException;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.excel.excel2pdf.Excel2Pdf;
import scrbg.meplat.mall.util.excel.excel2pdf.ExcelObject;
import scrbg.meplat.mall.util.poi.exp.PoiExporter;
import scrbg.meplat.mall.vo.productCompare.CompareDto;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @program: maill_api
 * @description: 比价自定义Excel生成
 * @author: 代文翰
 * @create: 2023-12-13 16:05
 **/
public class ProductCompareExcelUtil {
    // 日志
    private static final Logger logger = LoggerFactory.getLogger(PoiExporter.class);
    public static int urlConnectTimeout = 1000;
    public static int urlReadTimeout = 5000;
    // 生成Excel并返回字节数组输出流

    //  合并的比价明细生成的Excel
    public static  ByteArrayOutputStream createMergeExcel(List<CompareDto> compareDtos) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        for (CompareDto dto : compareDtos) {

        }
        return byteArrayOutputStream;
    }

    /**
     * 表头的样式策略
     * @return
     */
    private static WriteCellStyle getHeadStyle() {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为红色
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 16);
        headWriteCellStyle.setWriteFont(headWriteFont);
        return headWriteCellStyle;
    }

    /**
     * 内容的样式策略
     * @return
     */
    private static WriteCellStyle getContentStyle() {
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontName("宋体");
        contentWriteFont.setFontHeightInPoints((short) 14);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //设置 自动换行
        contentWriteCellStyle.setWrapped(true);
        //设置 垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        return contentWriteCellStyle;
    }

    /**
     * 表头和内容的样式策略
     * @return
     */
    private static HorizontalCellStyleStrategy getHorizontalCellStyleStrategy() {
        return new HorizontalCellStyleStrategy(getHeadStyle(), getContentStyle());
    }
    /**
     * 单个的比价明细生成的Excel
     * @param compareDtos
     * @return
     */
    public static ByteArrayOutputStream createExcel(List<CompareDto> compareDtos) {

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 构建 ExcelWriter
        LoopMergeStrategy loopMergeStrategy = new LoopMergeStrategy(2, 0);
        ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream)
                .inMemory(true)
                .registerConverter(new ByteArrayImageConverter())
                .needHead(true)
                .registerWriteHandler(new SheetHandler())
                //.registerWriteHandler(getHorizontalCellStyleStrategy())
                .registerWriteHandler(new CustomMergeStrategy())
                .build();
        // 构建自定义的 WriteSheet
        WriteSheet writeSheet = EasyExcel.writerSheet("我的比价").build();
        // 写入表头数据
        List<List<WriteCellData>> content = new ArrayList<>();
        for (CompareDto compareDto : compareDtos) {
            // 写入内容数据
            List<WriteCellData> titleRow = new ArrayList<>();
            WriteCellData title = new WriteCellData(compareDto.getTitle());
            WriteCellStyle writeCellStyle = new WriteCellStyle();
            writeCellStyle.setWrapped(true);
            writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            WriteFont writeFontTitle = new WriteFont();
            writeFontTitle.setFontHeightInPoints((short) 28);
            writeCellStyle.setWriteFont(writeFontTitle);
            title.setWriteCellStyle(writeCellStyle);
            titleRow.add(title);
            content.add(titleRow);
            // 时间
            List<WriteCellData> timeRow = new ArrayList<>();
            WriteCellData time = new WriteCellData(compareDto.getTime());
            timeRow.add(time);
            content.add(timeRow);

            List<List<String>> lists = compareDto.getProductInfos();
            for (List<String> list : lists) {
                // 一行的数据
                List<WriteCellData> row = new ArrayList<>();
                boolean redFlag = false;
                String firstElement = list.get(0);
                for (int i = 2; i < list.size(); i++) {
                    if (!firstElement.equals(list.get(i)) && StringUtils.isNotBlank(list.get(i))) {
                        redFlag = true;
                        break;
                    }
                }
                for (String s : list) {
                    // 判断是图片链接https开头文件类型结尾
                    if (isValidImageUrl(s)) {
                        // 图片
                        // 通过网络获取图片
                        try {
                            URL url = new URL(s);
                            // 打开连接，并强转为HttpURLConnection
                            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                            //URLConnection urlConnection = url.openConnection();
                            connection.setConnectTimeout(urlConnectTimeout);
                            connection.setReadTimeout(urlReadTimeout);
                            // 设置请求方式为GET
                            connection.setRequestMethod("GET");
                            // 连接
                            connection.connect();
                            // 获取响应码
                            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                                // 读取输入流
                                InputStream inputStream = connection.getInputStream();
                                // 将输入流转为字节数组
                                byte[] bytes = IoUtils.toByteArray(inputStream);
                                WriteCellData<Void> voidWriteCellData = new WriteCellData<>(bytes);
                                row.add(voidWriteCellData);
                                inputStream.close();
                            } else {
                                WriteCellData writeCellData = new WriteCellData("图片不存在");
                                row.add(writeCellData);
                                logger.error("请求失败，错误码：" + connection.getResponseCode());
                            }
                        } catch (IOException e) {
                            continue;
                            //throw new RuntimeException(e);
                        }
                    } else {
                        // 文本
                        WriteCellData writeCellData = new WriteCellData(s);
                        WriteFont writeFont = new WriteFont();
                        writeFont.setFontHeightInPoints((short) 16);
                        if (redFlag && !StringUtils.equals(s,"图标")) {
                            writeFont.setColor(IndexedColors.RED.getIndex());
                        } else {
                            writeFont.setColor(IndexedColors.BLACK.getIndex());
                        }

                        WriteCellStyle style = new WriteCellStyle();
                        // 一行数据都不同才使用红色字体
                        style.setWriteFont(writeFont);
                        style.setVerticalAlignment(VerticalAlignment.CENTER);
                        style.setHorizontalAlignment(HorizontalAlignment.CENTER);
                        style.setWrapped(true);
                        writeCellData.setWriteCellStyle(style);
                        row.add(writeCellData);
                    }
                }
                content.add(row);
                //List<WriteCellData> rowData = new ArrayList<>();
                //rowData.add(new WriteCellData(""));
                //content.add(rowData);
                //content.add(rowData);
            }
            List<WriteCellData> rowData = new ArrayList<>();
            WriteCellData writeCellData = new WriteCellData("");
            WriteCellStyle style = new WriteCellStyle();
            style.setBorderBottom(BorderStyle.NONE);
            writeCellData.setWriteCellStyle(style);
            //writeCellData.setWriteCellStyle();
            rowData.add(writeCellData);
            rowData.add(writeCellData);
            rowData.add(writeCellData);
            rowData.add(writeCellData);
            rowData.add(writeCellData);
            //content.add(rowData);
            //content.add(rowData);

        }

        excelWriter.write(content, writeSheet);

        // 完成写入并关闭流
        excelWriter.finish();
        // 文件写出
        //try {
        //    FileOutputStream fileOutputStream = new FileOutputStream("/Volumes/westDisk/testExcel/test.xlsx");
        //    try {
        //        byteArrayOutputStream.writeTo(fileOutputStream);
        //    } catch (IOException e) {
        //        throw new BusinessException(e.getMessage());
        //    }
        //} catch (FileNotFoundException e) {
        //    throw new RuntimeException(e);
        //}
        return byteArrayOutputStream;
    }
    public static boolean isValidImageUrl(String url) {
        String regex = "^(https?://).*\\.(png|jpe?g|gif)$";
        Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(url);
        return matcher.matches();
    }

    public static void main(String[] args) {
        String url = "https://mmcp.scrbg.com";
        String url1 = "https://mmcp.scrbg.com/mall/material/image/20231020/3425a26bdbfa4c41b4d037f873494cf4-20231020.png";
        System.out.println(isValidImageUrl(url));
        System.out.println(isValidImageUrl(url1));
    }
    public static void main1(String[] args) {
        CompareDto compareDto = new CompareDto();
        compareDto.setTitle("物资采购平台商品对比");
        compareDto.setTime("时间:"+ DateUtil.getYYYYMMDD(new Date()));
        // 全部比价数据
        List<List<String>> productInfos = new ArrayList<>();
        // 一行的数据
        List<String> rowProductInfos0 = new ArrayList<>();
        String col = "图标";
        String co0 = "https://mmcp.scrbg.com/mall/material/image/20231020/3425a26bdbfa4c41b4d037f873494cf4-20231020.png";
        String co1 = "https://mmcp.scrbg.com/mall/material/image/20231020/3425a26bdbfa4c41b4d037f873494cf4-20231020.png";
        String co2 = "https://mmcp.scrbg.com/mall/material/image/20231020/3425a26bdbfa4c41b4d037f873494cf4-20231020.png";
        String co3 = "https://mmcp.scrbg.com/mall/material/image/20231020/3425a26bdbfa4c41b4d037f873494cf4-20231020.png";
        rowProductInfos0.add(col);
        rowProductInfos0.add(co0);
        rowProductInfos0.add(co1);
        rowProductInfos0.add(co2);
        rowProductInfos0.add(co3);
        productInfos.add(rowProductInfos0);
        List<String> rowProductInfos1 = new ArrayList<>();
        String col20 = "物资名称";
        String co121 = "零星气锤";
        String co122 = "零星气锤";
        String co123 = "零星气锤";
        String co124 = "零星气锤";
        rowProductInfos1.add(col20);
        rowProductInfos1.add(co121);
        rowProductInfos1.add(co122);
        rowProductInfos1.add(co123);
        rowProductInfos1.add(co124);
        productInfos.add(rowProductInfos1);
        List<String> rowProductInfos2 = new ArrayList<>();
        String col30 = "规格型号";
        String co131 = "把";
        String co132 = "只";
        String co133 = "个";
        String co134 = "个";
        rowProductInfos2.add(col30);
        rowProductInfos2.add(co131);
        rowProductInfos2.add(co132);
        rowProductInfos2.add(co133);
        rowProductInfos2.add(co134);
        productInfos.add(rowProductInfos2);
        List<String> rowProductInfos3 = new ArrayList<>();
        String col40 = "品牌";
        String co141 = "五金";
        String co142 = "塑胶";
        String co143 = "象牙";
        String co144 = "玻璃";
        rowProductInfos3.add(col40);
        rowProductInfos3.add(co141);
        rowProductInfos3.add(co142);
        rowProductInfos3.add(co143);
        rowProductInfos3.add(co144);
        productInfos.add(rowProductInfos3);
        List<String> rowProductInfos4 = new ArrayList<>();
        String col50 = "单位";
        String co151 = "个";
        String co152 = "把";
        String co153 = "把";
        String co154 = "把";
        rowProductInfos4.add(col50);
        rowProductInfos4.add(co151);
        rowProductInfos4.add(co152);
        rowProductInfos4.add(co153);
        rowProductInfos4.add(co154);
        productInfos.add(rowProductInfos4);
        List<String> rowProductInfos5 = new ArrayList<>();
        String col60 = "销售价";
        String co161 = "11.09";
        String co162 = "12.99";
        String co163 = "13.00";
        String co164 = "12.98";
        rowProductInfos5.add(col60);
        rowProductInfos5.add(co161);
        rowProductInfos5.add(co162);
        rowProductInfos5.add(co163);
        rowProductInfos5.add(co164);
        productInfos.add(rowProductInfos5);
        List<String> rowProductInfos6 = new ArrayList<>();
        String col70 = "供应商";
        String co171 = "物资公司";
        String co172 = "路桥自营店";
        String co173 = "五道口材料";
        String co174 = "供应链公司";
        rowProductInfos6.add(col70);
        rowProductInfos6.add(co171);
        rowProductInfos6.add(co172);
        rowProductInfos6.add(co173);
        rowProductInfos6.add(co174);
        productInfos.add(rowProductInfos6);
        compareDto.setProductInfos(productInfos);
        // 生成Excel
        ByteArrayOutputStream byteArrayOutputStream = createExcel(new ArrayList<>());
        // 读取输入流
        InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        // 生成PDF
        ExcelObject excelObject = new ExcelObject("", inputStream);
        try {
            new Excel2Pdf(excelObject, new FileOutputStream(new File("/Volumes/westDisk/testExcel/testPoi2.pdf"))).convert();
        } catch (DocumentException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        //   关闭流
        try {
            if (byteArrayOutputStream != null){
                byteArrayOutputStream.close();
            }
            if (inputStream != null){
                inputStream.close();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }
}
