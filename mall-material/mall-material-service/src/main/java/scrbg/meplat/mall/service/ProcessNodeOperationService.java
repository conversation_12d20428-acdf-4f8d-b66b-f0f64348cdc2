package scrbg.meplat.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;

import scrbg.meplat.mall.entity.ProcessNodeOperation;
/**
 * 流程节点操作记录表 服务类
 * <AUTHOR>
 * @date: 2025年6月20日 上午9:51:25
 */
public interface ProcessNodeOperationService extends IService<ProcessNodeOperation> {

        void create(ProcessNodeOperation processNodeOperation);

        void update(ProcessNodeOperation processNodeOperation);

        ProcessNodeOperation getById(String id);

        void delete(String id);

}
