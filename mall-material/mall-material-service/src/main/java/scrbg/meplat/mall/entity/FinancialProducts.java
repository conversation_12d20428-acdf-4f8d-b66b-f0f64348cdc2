package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @描述：金融产品
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "金融产品")
@Data
@TableName("financial_products")
public class FinancialProducts extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "金融产品id")

    private String financialId;

    @ApiModelProperty(value = "金融产品名称")

    private String financialName;

    @ApiModelProperty(value = "图片路径")

    private String pictureUrl;

    @ApiModelProperty(value = "产品类型")

    private Integer productsType;

    @ApiModelProperty(value = "订单总价格")

    private BigDecimal totalAmount;

    @ApiModelProperty(value = "产品利率")

    private BigDecimal interestRate;

    @ApiModelProperty(value = "融资期限")

    private String financingPeriod;

    @ApiModelProperty(value = "还款方式")

    private Integer repaymentType;

    @ApiModelProperty(value = "放款时长")

    private String lendingDuration;

    @ApiModelProperty(value = "申请材料")

    private String applicationMaterials;

    @ApiModelProperty(value = "申请条件")

    private String applicationConditions;

    @ApiModelProperty(value = "产品简介")

    private String productIntroduction;

    @ApiModelProperty(value = "使用区域")

    private String useArea;

    @ApiModelProperty(value = "产品特色")

    private String productFeatures;

    @ApiModelProperty(value = "应用场景")

    private String applicationScenario;

    @ApiModelProperty(value = "店铺id")

    private String shopId;


    @ApiModelProperty(value = "0:已发布 1:未发布")

    private Integer state;


}
