package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.PlatformDealFee;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.PlatformDealFee;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.entity.PlatformDealFeeRecord;

import java.util.List;

/**
 * @描述：平台交易费 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
public interface PlatformDealFeeService extends IService<PlatformDealFee> {

    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFee> queryWrapper);

    void create(PlatformDealFee platformDealFee);

    void update(PlatformDealFee platformDealFee);

    PlatformDealFee getById(String id);

    void delete(String id);

    void deleteBatch(List<String> ids);

    /**
     * 供应商查询交易记录
     *
     * @param jsonObject
     * @param q
     * @return
     */
    PageUtils listByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFee> q);

    /**
     * 每天定时校验欠费是否过期
     */
    void checkOutArrearageDate();

    /**
     * 更具交易明细编号查询交易记录
     *
     * @param sn
     * @return
     */
    PlatformDealFee findBySn(String sn);

    /***
     * 设置欠费时间
     */
    void setServerStopDate(PlatformDealFee platformDealFee);
}