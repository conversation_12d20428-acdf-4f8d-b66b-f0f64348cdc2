package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：角色表
 * @作者: ye
 * @日期: 2023-12-21
 */
@ApiModel(value = "角色表")
@Data
@TableName("sys_role")
public class SysRole extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "角色id")
    private String roleId;

    @ApiModelProperty(value = "角色编号")

    private String code;


    @ApiModelProperty(value = "角色名称")

    private String name;


    @ApiModelProperty(value = "状态（是否启用（0否1是））")

    private Integer state;


    @ApiModelProperty(value = "关键字")

    private String keyword;


    @ApiModelProperty(value = "机构数据查看权限（1本机及子级2只看本级）")

    private Integer orgSearch;


    @ApiModelProperty(value = "所属平台（1后台管理平台2供应商管理平台3履约管理平台）")

    private Integer categoryType;


}