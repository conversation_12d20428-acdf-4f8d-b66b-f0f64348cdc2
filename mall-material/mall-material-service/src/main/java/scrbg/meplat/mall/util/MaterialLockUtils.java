package scrbg.meplat.mall.util;

public  class MaterialLockUtils {

    //物资收货锁
    public static final String SHIPBillNO_LOCK = "materialShipLock";
    //推送计划
    public static final String PUSH_LX_ORDER_LOCK = "pushLxOrder";
    public static final String PUSH_LX_PIAN_LOCK = "pushLxPlan";
    public static final String PUSH_DZ_PIAN_LOCK = "pushLxPlan";

    //物资缓存key
    public static final String CLASS_ID_AND_RELEVANCE_N0_LOCK_KEY = "ClassIdAndRelevanceNoLock:";
    public static final String MATERIAL_NULL = "materialNull";




    //生成获取随机时间的方法，避免缓存雪崩，随机时间在1-5秒之间，时间长度为3分钟
    public static long getRandomTime(){
        return (long) (Math.random()*3*60*1000+ 1000);
    }

}
