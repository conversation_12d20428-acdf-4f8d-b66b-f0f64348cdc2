package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.entity.ProductComment;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.ShopComment;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.mapper.ShopCommentMapper;
import scrbg.meplat.mall.service.ProductCommentService;
import scrbg.meplat.mall.service.ShopCommentService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.SystemParamService;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.pageUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;

/**
 * @描述：商铺评价 服务类
 * @作者: ye
 * @日期: 2025-05-21
 */
@Service
public class ShopCommentServiceImpl extends ServiceImpl<ShopCommentMapper, ShopComment> implements ShopCommentService {

    @Autowired
    private ShopService shopService;
    @Autowired
    private ProductCommentService productCommentService;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private ShopCommentMapper shopCommentMapper;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShopComment> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        if (innerMap.get("keywords") != null) {
            innerMap.put("keywords", (String) innerMap.get("keywords").toString().trim());
        }
        int count = shopCommentMapper.findShopCommentCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);
        IPage<ShopComment> iPage = new Query<ShopComment>().getPage(jsonObject);
        Page<ShopComment> pages = new Page<>(iPage.getCurrent(), iPage.getSize());
        //dto传值给queryWrapper作为查询条件
        List<ShopComment> vos = shopCommentMapper.findShopComment(pages, innerMap);
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public R create(ShopComment shopComment) {
        String currentMonth = YearMonth.now().toString();
        shopComment.setMonth(currentMonth);
        LambdaQueryChainWrapper<ShopComment> queryWrapper = lambdaQuery()
                .eq(ShopComment::getMonth, currentMonth)
                .eq(ShopComment::getShopId, shopComment.getShopId());
        List<ShopComment> shopComments = queryWrapper.list();
        if (shopComments.size() > 0) {
            List<String> shopIds = new ArrayList<>();
            shopComments.stream().forEach(shop -> {
                shopIds.add(shop.getShopId());
            });
            boolean update = super.lambdaUpdate().
                    in(ShopComment::getShopId, shopIds)
                    .set(ShopComment::getIsDelete, "1").update();
        }
        //查询店铺品品质、保供能力、诚信履约、服务水平的计算权重
        List<SystemParam> systemParams = systemParamService.lambdaQuery().eq(SystemParam::getCode, "shopComment").list();
        //计算店铺服务评分
        BigDecimal commentServiceScore = systemParams.stream().map(systemParam -> {
            String fieldName = systemParam.getKeyValue();
            try {
                BigDecimal score = new BigDecimal(getFieldValue(shopComment, fieldName).toString());
                BigDecimal weight = new BigDecimal(systemParam.getKeyValue2());
                return score.multiply(weight);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        shopComment.setCommentServiceScore(commentServiceScore);
        boolean result = super.save(shopComment);
        if (result) {
            return R.success(null, "添加成功！");
        } else {
            return R.failed("添加失败！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(ShopComment shopComment) {
        super.updateById(shopComment);
    }

    @Override
    public ShopComment getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    @Override
    public void shopCommentCalculate() {
        //查询店铺品品质、保供能力、诚信履约、服务水平的计算权重
        List<SystemParam> systemParams = systemParamService.lambdaQuery().eq(SystemParam::getCode, "shopComment").list();
        //查询所有店铺
        List<Shop> shopList = shopService.lambdaQuery().eq(Shop::getAuditStatus, "1").list();
        shopList.forEach(shop -> {
            String currentMonth = YearMonth.now().toString();
            List<ShopComment> shopComments = lambdaQuery().eq(ShopComment::getMonth,currentMonth).eq(ShopComment::getShopId,shop.getShopId()).list();
            if(shopComments.size()==0){
                ShopComment shopComment = new ShopComment();
                shopComment.setMonth(currentMonth);
                LocalDate today = LocalDate.now();
                LocalDate commentEndDate = today.minusMonths(1).withDayOfMonth(today.minusMonths(1).lengthOfMonth());
                LocalDate commentStartDate = commentEndDate.minusMonths(11).withDayOfMonth(1);
                Date startDate = DateUtil.atStartOfDay(commentStartDate);
                Date endDate = DateUtil.atEndOfDay(commentEndDate);
                List<ProductComment> productComments = productCommentService.getProductCommentByShopId(shop.getShopId(), startDate, endDate);
                if (productComments.size() > 0) {
                    //计算店铺商品品质、保供能力、诚信履约、服务水平综合评分
                    OptionalDouble averageLevel = productComments.stream().map(ProductComment::getCommentLevel)
                            .filter(Objects::nonNull)
                            .mapToDouble(Integer::doubleValue)
                            .average();
                    OptionalDouble averageSupply = productComments.stream().map(ProductComment::getCommentSupply)
                            .filter(Objects::nonNull)
                            .mapToDouble(Integer::doubleValue)
                            .average();
                    OptionalDouble averageIntegrity = productComments.stream().map(ProductComment::getCommentIntegrity)
                            .filter(Objects::nonNull)
                            .mapToDouble(Integer::doubleValue)
                            .average();
                    OptionalDouble averageService = productComments.stream().map(ProductComment::getCommentService)
                            .filter(Objects::nonNull)
                            .mapToDouble(Integer::doubleValue)
                            .average();
                    //商品品质、保供能力、诚信履约、服务水平综合评分赋值
                    shopComment.setCommentLevel(averageLevel.isPresent()
                            ? BigDecimal.valueOf(averageLevel.getAsDouble())
                            : BigDecimal.ZERO);
                    shopComment.setCommentSupply(averageSupply.isPresent()
                            ? BigDecimal.valueOf(averageSupply.getAsDouble())
                            : BigDecimal.ZERO);
                    shopComment.setCommentIntegrity(averageIntegrity.isPresent()
                            ? BigDecimal.valueOf(averageIntegrity.getAsDouble())
                            : BigDecimal.ZERO);
                    shopComment.setCommentService(averageService.isPresent()
                            ? BigDecimal.valueOf(averageService.getAsDouble())
                            : BigDecimal.ZERO);
                    //计算店铺服务评分
                    BigDecimal commentServiceScore = systemParams.stream().map(systemParam -> {
                        String fieldName = systemParam.getKeyValue();
                        try {
                            BigDecimal score = new BigDecimal(getFieldValue(shopComment, fieldName).toString());
                            BigDecimal weight = new BigDecimal(systemParam.getKeyValue2());
                            return score.multiply(weight);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }).reduce(BigDecimal.ZERO, BigDecimal::add);
                    shopComment.setCommentServiceScore(commentServiceScore);
                } else {
                    shopComment.setCommentLevel(BigDecimal.ZERO);
                    shopComment.setCommentSupply(BigDecimal.ZERO);
                    shopComment.setCommentIntegrity(BigDecimal.ZERO);
                    shopComment.setCommentService(BigDecimal.ZERO);
                    shopComment.setCommentServiceScore(BigDecimal.ZERO);
                }
                shopComment.setShopId(shop.getShopId());
                shopComment.setShopName(shop.getShopName());
                shopComment.setMainBusiness(shop.getMainBusiness());
                shopComment.setCommentStart(startDate);
                shopComment.setCommentEnd(endDate);
                boolean update = super.lambdaUpdate().
                        eq(ShopComment::getShopId, shop.getShopId())
                        .set(ShopComment::getIsDelete, "1").update();
                boolean save = super.save(shopComment);
            }
        });
    }

    /**
     * 通过反射获取对象的字段值
     */
    public static Object getFieldValue(Object obj, String fieldName) throws NoSuchFieldException, IllegalAccessException {
        // 获取对象的类
        Class<?> clazz = obj.getClass();
        // 处理私有字段，需要设置可访问
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        // 获取字段值
        return field.get(obj);
    }
}
