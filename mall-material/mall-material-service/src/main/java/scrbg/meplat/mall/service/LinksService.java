package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.Links;

import java.util.List;

/**
 * @描述：友情链接 服务类
 * @作者: sund
 * @日期: 2022-11-09
 */
public interface LinksService extends IService<Links> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Links> queryWrapper);

    void create(Links links);

    void update(Links links);

    Links getById(String id);

    void delete(String id);


    void updateLinksState(String id, int state);
    PageUtils queryByConfigPage(JSONObject jsonObject, LambdaQueryWrapper<Links> adPictureLambdaQueryWrapper);

    /**
     * 根据链接id修改链接状态
     */
    void updateByPublish(List<String> ids, String type);
}
