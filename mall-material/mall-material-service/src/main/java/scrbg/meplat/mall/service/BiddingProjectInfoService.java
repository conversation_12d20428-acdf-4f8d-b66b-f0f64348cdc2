package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.BiddingProjectInfo;

/**
 * @描述：招标项目信息 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
public interface BiddingProjectInfoService extends IService<BiddingProjectInfo> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingProjectInfo> queryWrapper);

    void create(BiddingProjectInfo biddingProjectInfo);

    void update(BiddingProjectInfo biddingProjectInfo);

    BiddingProjectInfo getById(String id);

    void delete(String id);
}
