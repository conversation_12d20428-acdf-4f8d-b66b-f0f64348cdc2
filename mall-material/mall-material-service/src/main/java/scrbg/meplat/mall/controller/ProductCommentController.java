package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.ProductComment;
import scrbg.meplat.mall.service.ProductCommentService;

import java.util.List;

/**
 * @描述：商品评价控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/productComment")
//@Api(tags = "商品评价")
public class ProductCommentController {

    @Autowired
    public ProductCommentService productCommentService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<ProductComment> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = productCommentService.queryPage(jsonObject, new LambdaQueryWrapper<ProductComment>());
        return PageR.success(page);
    }

    @PostMapping("/commentManageList")
    @ApiOperation(value = "评价管理列表查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<ProductComment> listByCommentManage(@RequestBody JSONObject jsonObject) {
        PageUtils page = productCommentService.commentManageList(jsonObject, new LambdaQueryWrapper<ProductComment>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<ProductComment> findById(String id) {
        ProductComment productComment = productCommentService.getById(id);
        return R.success(productComment);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody ProductComment productComment) {
        productCommentService.create(productComment);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "评价管理-修改")
    public R update(@RequestBody ProductComment productComment) {
        productCommentService.update(productComment);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        productCommentService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        productCommentService.removeByIds(ids);
        return R.success();
    }
}

