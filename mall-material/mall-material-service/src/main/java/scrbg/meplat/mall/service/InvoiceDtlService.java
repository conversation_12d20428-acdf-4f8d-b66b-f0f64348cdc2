package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.InvoiceDtl;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.InvoiceDtl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-11-13
 */
public interface InvoiceDtlService extends IService<InvoiceDtl> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<InvoiceDtl> queryWrapper);

        void create(InvoiceDtl invoiceDtl);
        void update(InvoiceDtl invoiceDtl);
        InvoiceDtl getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

    void deleteInvoiceId(String id);

    List<InvoiceDtl> getListByInvoiceId(String invoiceId);
}
