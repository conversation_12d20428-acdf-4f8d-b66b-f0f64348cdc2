package scrbg.meplat.mall.common.redis;

/**
 * <AUTHOR>
 * @create 2022-12-06 11:17
 */
public class RedisKey {

    /**
     * 分类缓存
     */
    public static final String CATEGORY = "category";

    /**
     * 保存登陆用户信息
     */
    public static final String USER_MAP = "user_";
    public static final String USER_MAP_KEY = "user:id_";
    public static final String USER_KEY = "malluser:id_";

    /**
     * 发送短信验证码
     */
    public static final String LOGIN_CODE_KEY = "login:code:";
    public static final Long LOGIN_CODE_TTL   = 1L;

    /**
     * 注册发送验证码
     */
    public static final String REGISTER_CODE_KEY = "register:code:";
    public static final Long REGISTER_CODE_TTL   = 1L;

    /**
     * 修改密码
     */

    public static final String UPDATE_PASSWORD_CODE_KEY = "updatePassword:code:";
    public static final Long UPDATE_PASSWORD_CODE_TTL   = 1L;

    /**
     * 修改手机号
     */
    public static final String UPDATE_MOBILE_CODE_KEY = "updateMobile:code:";
    public static final Long UPDATE_MOBILE_CODE_TTL   = 1L;
    /**
    * WX收料员登录验证码前缀
    *
     */
    public static final  String WX_APP_SL_LOGIN_PHONE_CODE_KEY = "wxAppSlLogin:code:";
    /**
     * wx供货商登录验证码前缀
     */
    public static final  String WX_APP_SUPPLIER_LOGIN_PHONE_CODE_KEY = "wxAppSupplierLogin:code:";
    /**
    * WX验证码统一过期时间
    */
    public static final Long WX_APP_LOGIN_PHONE_CODE_TTL   = 3L;

    /**
     * WX收料员用户登录信息
     */
    public static final String WX_APP_SL_USER_MAP_KEY = "wxSlUser:phone_";
    /***
     * WX供货商用户登录信息
     */
    public static final String WX_APP_SUPPLIER_USER_MAP_KEY = "wxSupplierUser:phone_";

}
