package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.OrderReturnItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * @描述：退货项表 服务类
 * @作者: ye
 * @日期: 2023-08-07
 */
public interface OrderReturnItemService extends IService<OrderReturnItem> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderReturnItem> queryWrapper);

        void create(OrderReturnItem orderReturnItem);

        void update(OrderReturnItem orderReturnItem);

        OrderReturnItem getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

    List<OrderReturnItem> getReturnNo(String orderReturnNo);

    void deleteReturnNo(String returnNo);

    List<OrderReturnItem> selectAllByOrderReturmId(String orderReturnId);

    /**
     *  根据订单来源和订单项id 查询商品退货数量
     * @param orderItemId  ding  订单项id
     * @param isOut  订单来源   1 pcwp退货  0商城退货
     * @return
     */
    BigDecimal getDataByOrderItmId(String orderItemId, int isOut);



//    /**
//     *  根据订单来源和订单项id 查询商品退货数量
//     * @param orderItemId  ding  订单项id
//     * @param isOut  订单来源   1 pcwp退货  0商城退货
//     * @return
//     */
//    BigDecimal getDataListByOrderItmId(String orderItemId);

    void deleteByOrderReturnId(String s);

    /**
     * 查询订单项可对账数据
     * @param sourceDtlId
     * @param i  0可对账  1不可对账
     * @return  IsReconciliation
     */
    OrderReturnItem getDataByIdAndIsReconciliation(String sourceDtlId, int i);

    //设置退货订单金额
    void setOrderReturn(OrderItem byId, OrderReturnItem orderReturnItem,BigDecimal taxRate);


    /**
     订单总量：订单总量减商城退货总量
     */
    BigDecimal getDataByIdAndIsReconciliation(List<String> orderItems);


    BigDecimal getDataByOrderItmIds(List<String> orderItemIds, int i);

    /**
     * 商城退货成功数量
     * @param orderItemId 订单项
     * @param isOut 0 商城  1 pcwp
     * @param state  3 成功
     * @return
     */
    BigDecimal getDataByOrderItmIdAndState(String orderItemId, int isOut, int state);
}
