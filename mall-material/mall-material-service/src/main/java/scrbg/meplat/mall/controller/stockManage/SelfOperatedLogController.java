package scrbg.meplat.mall.controller.stockManage;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.entity.SelfOperatedStoreRecord;
import scrbg.meplat.mall.service.stockManage.SelfOperatedLogService;

@RestController
@RequestMapping("/stock/selfOperatedLog")
@ApiSort(value = 500)
@Api(tags = "自营店出入库记录（后台）")
public class SelfOperatedLogController{
    private SelfOperatedLogService logService;

    @Autowired
    public void setLogService(SelfOperatedLogService logService) {
        this.logService = logService;
    }

    /**
     * 根据主键删除
     *
     * @param id
     * @return
     */
    @GetMapping("/delete/{id}")
    @ApiOperation(value = "根据主键删除")
    @LogRecord(title = "自营店出入库管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)
    public R delete(@PathVariable String id) {
        logService.delete(id);
        return R.success();
    }

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<SelfOperatedStoreRecord> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils<SelfOperatedStoreRecord> page = logService.queryPage(jsonObject, new LambdaQueryWrapper<SelfOperatedStoreRecord>());
        return PageR.success(page);
    }
    @GetMapping("/getSelfInfo/{id}")
    @ApiOperation(value = "根据主键查询")
    public R<SelfOperatedStoreRecord> getSelfInfo(@PathVariable String id) {
        SelfOperatedStoreRecord record = logService.getSelfInfo(id);
        return R.success(record);
    }
    @PostMapping("/getProductInfo")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
    })
    public PageR<SelfOperatedStoreRecord> getProductInfo(@RequestBody JSONObject jsonObject) {
        PageUtils<SelfOperatedStoreRecord> page = logService.getProductInfo(jsonObject);
        return PageR.success(page);
    }
    @PostMapping("/saveLog")
    @ApiOperation(value = "保存入库记录")
    public R save(@RequestBody SelfOperatedStoreRecord selfOperatedStoreRecord) {
        logService.saveLog(selfOperatedStoreRecord);
        return R.success();
    }
}
