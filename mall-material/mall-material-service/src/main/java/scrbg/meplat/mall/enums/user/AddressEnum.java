package scrbg.meplat.mall.enums.user;

/**
 * @package: scrbg.meplat.mall.enums.enums.user
 * @author: 胡原武
 * @date: 2022.11.08
 */
public enum AddressEnum {

    IS_DEFAULT_ADDRESS_YES(1,"默认地址"),
    IS_DEFAULT_ADDRESS_NO(0,"非默认地址"),

    IS_STATE_YES(1,"正常"),
    IS_STATE_NO(0,"无效");



    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    AddressEnum() {
    }

    AddressEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
