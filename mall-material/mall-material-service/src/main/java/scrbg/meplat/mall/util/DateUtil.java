package scrbg.meplat.mall.util;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import scrbg.meplat.mall.exception.BusinessException;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

public class DateUtil {

    static private MyDateFormat myDateFormat;

    static private String format = "yyyy-MM-dd";
    static private String format2 = "yyyy/MM/dd";

    static private String storageFormat = "yyyy-MM-dd";

    static private boolean easy = false;

    public static MyDateFormat getMyDateFormat(String format) {
        DateUtil.myDateFormat = new MyDateFormat(format);
        return DateUtil.myDateFormat;
    }

    public static String getyyymmddHHmmss(Date date) {

        // 设置日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年 MM月 dd日 HH:mm:ss");
        String dateString = sdf.format(date);
        // 将时间转换为字符串
        return dateString;


    }


    public static String getymd(LocalDate currentDate) {
        // 获取当前日期


        // 创建一个 DateTimeFormatter 对象，指定格式为 yyyy/MM/dd
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");

        // 使用 formatter 将 LocalDate 转换为字符串
        String formattedDate = currentDate.format(formatter);

        // 输出结果
       return formattedDate;
    }

    /**
     * yyyy 年MM 月dd 日 类型的日期
     *
     * @param date
     * @return yyyy 年MM 月dd 日
     */
    public static String getYYYYMMDD(Date date) {
        // 设置日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy 年MM 月dd 日");
        String dateString = sdf.format(date);
        // 将时间转换为字符串
        return dateString;
    }

    public static String getyyymmdd(Date date) {

        // 设置日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = sdf.format(date);
        // 将时间转换为字符串
        return dateString;


    }

    public static String getYYYYmm(Date date) {

        // 设置日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年 MM月");
        String dateString = sdf.format(date);
        // 将时间转换为字符串
        return dateString;


    }

    public static String getYYYYmmdd(Date date, String str) {

        // 设置日期格式
        SimpleDateFormat sdf = new SimpleDateFormat(str);
        String dateString = sdf.format(date);
        // 将时间转换为字符串
        return dateString;


    }

    public static String getyyymmddHHmmssStr(Date date) {

        // 设置日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = sdf.format(date);
        // 将时间转换为字符串
        return dateString;


    }

    public static String getyyyymmddHHmmss(Date date) {

        // 设置日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = sdf.format(date);
        // 将时间转换为字符串
        return dateString;


    }

    public static void setParams(String format, String storageFormat, boolean easy) {
        DateUtil.format = format;
        DateUtil.storageFormat = storageFormat;
        DateUtil.easy = easy;
    }

    public static void setParams(String format, String storageFormat) {
        DateUtil.format = format;
        DateUtil.storageFormat = storageFormat;
    }

    /**
     * 格式化Excel时间(yyyy-MM-dd)
     *
     * @param str
     * @return
     */
    public static String formatExcelDate(String str) {
        //数据的格式
        int yearIndex = StringUtils.indexOf(format, "yyyy");
        int monthIndex = StringUtils.indexOf(format, "MM");
        int dayIndex = StringUtils.indexOf(format, "dd");
        String year = format.substring(yearIndex + 4, monthIndex);
        String month = format.substring(monthIndex + 2, dayIndex);
        //存储的格式
        int storageYearIndex = StringUtils.indexOf(storageFormat, "yyyy");
        int storageMonthIndex = StringUtils.indexOf(storageFormat, "MM");
        int storageDayIndex = StringUtils.indexOf(storageFormat, "dd");
        String storageYear = storageFormat.substring(storageYearIndex + 4, storageMonthIndex);
        String storageMonth = storageFormat.substring(storageMonthIndex + 2, storageDayIndex);
        if (StringUtils.isBlank(str)) {
            return "";
        } else if (StringUtils.isNumeric(str)) {
            /*数字*/
            Calendar calendar = new GregorianCalendar(1900, 0, -1);
            Date gregorianDate = calendar.getTime();
            return DateUtil.getMyDateFormat(storageFormat).format(DateUtils.addDays(gregorianDate, Integer.parseInt(str)));
        } else if (str.matches("^(\\d{4}" + year + "\\d{1,2})$") && easy) {
            /*yyyy/MM*/
            Date date = DateUtil.getMyDateFormat("yyyy" + year + "MM").parse(str);
            return DateUtil.getMyDateFormat("yyyy" + storageYear + "MM").format(date);
        } else if (str.matches("^(\\d{4}" + year + "\\d{1,2}" + month + "\\d{1,2})$")) {
            /*yyyy/MM/dd*/
            Date date = DateUtil.getMyDateFormat("yyyy" + year + "MM" + month + "dd").parse(str);
            return DateUtil.getMyDateFormat("yyyy" + storageYear + "MM" + storageMonth + "dd").format(date);
        } else {
            return "";
        }
    }

    public static String formatExcelDate(String str, boolean easyTemp) {
        //数据的格式
        int yearIndex = StringUtils.indexOf(format, "yyyy");
        int monthIndex = StringUtils.indexOf(format, "MM");
        int dayIndex = StringUtils.indexOf(format, "dd");
        String year = format.substring(yearIndex + 4, monthIndex);
        String month = format.substring(monthIndex + 2, dayIndex);
        //存储的格式
        int storageYearIndex = StringUtils.indexOf(storageFormat, "yyyy");
        int storageMonthIndex = StringUtils.indexOf(storageFormat, "MM");
        int storageDayIndex = StringUtils.indexOf(storageFormat, "dd");
        String storageYear = storageFormat.substring(storageYearIndex + 4, storageMonthIndex);
        String storageMonth = storageFormat.substring(storageMonthIndex + 2, storageDayIndex);
        if (StringUtils.isBlank(str)) {
            return "";
        } else if (StringUtils.isNumeric(str)) {
            /*数字*/
            Calendar calendar = new GregorianCalendar(1900, 0, -1);
            Date gregorianDate = calendar.getTime();
            return DateUtil.getMyDateFormat(storageFormat).format(DateUtils.addDays(gregorianDate, Integer.parseInt(str)));
        } else if (str.matches("^(\\d{4}" + year + "\\d{1,2})$") && easyTemp) {
            /*yyyy/MM*/
            Date date = DateUtil.getMyDateFormat("yyyy" + year + "MM").parse(str);
            return DateUtil.getMyDateFormat("yyyy" + storageYear + "MM").format(date);
        } else if (str.matches("^(\\d{4}" + year + "\\d{1,2}" + month + "\\d{1,2})$")) {
            /*yyyy/MM/dd*/
            Date date = DateUtil.getMyDateFormat("yyyy" + year + "MM" + month + "dd").parse(str);
            return DateUtil.getMyDateFormat("yyyy" + storageYear + "MM" + storageMonth + "dd").format(date);
        } else {
            return "";
        }
    }

    /**
     * 自动填充0
     *
     * @param format
     * @param str
     * @return
     */
    public static String autoAdd0(String format, String str) {
        Date date = DateUtil.getMyDateFormat(format).parse(str);
        return DateUtil.getMyDateFormat(format).format(date);
    }

    /*日期内部类*/
    public static class MyDateFormat {
        private SimpleDateFormat simpleDateFormat;

        public MyDateFormat(String format) {
            this.simpleDateFormat = new SimpleDateFormat(format);
        }

        public String format(Date date) {
            if (date == null) {
                return "";
            } else {
                return simpleDateFormat.format(date);
            }
        }

        public Date parse(String str) {
            if (str == null) {
                return null;
            } else {
                try {
                    return simpleDateFormat.parse(str);
                } catch (ParseException e) {
                    return null;
                }
            }
        }
    }

    public static Date LocalDateTimeChangData(String time) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SS");

        try {
            Date date = dateFormat.parse(time);
            System.out.println(date.toString());
            return date;
        } catch (ParseException e) {
            throw new BusinessException(1021, "时间格式转化失败");
        }


    }

    //获取本年的最后两位树
    public static Integer getLastTwoYear() {
        int year = Calendar.getInstance().get(Calendar.YEAR);

        // 获取年份的最后两位数
        int lastTwoDigits = year % 100;

        return lastTwoDigits;

    }

    // 将LocalDate转换为Date（开始时间：00:00:00）
    public static Date atStartOfDay(LocalDate localDate) {
        return Date.from(
                localDate.atStartOfDay(ZoneId.systemDefault()).toInstant()
        );
    }

    // 将LocalDate转换为Date（结束时间：23:59:59）
    public static Date atEndOfDay(LocalDate localDate) {
        return Date.from(
                localDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant()
        );
    }
}
