package scrbg.meplat.mall.enums.enterprise;

/**
 * @package: scrbg.meplat.mall.enums.enterprise
 * @author: 胡原武
 * @date: 2022.11.25
 */
public enum EnterpriseEnum {

    IS_SUPPLIER_YES(2,"是供应商"),
    IS_SUPPLIER_UNKNOW(1,"非供应商"),
    IS_SUPPLIER_NO(0,"非供应商");

    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    EnterpriseEnum() {
    }

    EnterpriseEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}
