package scrbg.meplat.mall.util.zip;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
 
import javax.servlet.ServletException;
 
import org.apache.commons.io.FileUtils;

/**
 * 本地
 */
public class LocalZipUtils {
 
  private LocalZipUtils() {
    throw new IllegalStateException("Utility class");
  }
 
  /**
   * 将文件/目录进行压缩
   * @param sourceFile 原文件/目录
   * @param targetZipFile 压缩后目标文件
   * @throws IOException 
   */
  public static void zipFiles(File sourceFile, File targetZipFile) throws IOException {
    ZipOutputStream outputStream = null;
    try {
      outputStream = new ZipOutputStream(new FileOutputStream(targetZipFile));
      addEntry("", sourceFile, outputStream);
    } catch (Exception e) {
      throw new IOException(e);
    } finally {
      outputStream.close();
    }
  }
 
  /**
   * 将文件写入到zip文件中
   * @param source
   * @param outputstream
   * @throws IOException
   * @throws ServletException
   */
  private static void addEntry(String base, File source, ZipOutputStream outputstream)
      throws IOException, ServletException {
    FileInputStream is = null;
    try {
      String entry = base + source.getName();
      if (source.isDirectory()) {
        for (File file : source.listFiles()) {
          // 递归导入文件
          addEntry(entry + File.separator, file, outputstream);
        }
      } else {
 
        is = FileUtils.openInputStream(source);
        if (is != null) {
          outputstream.putNextEntry(new ZipEntry(entry));
 
          int len = 0;
          byte[] buffer = new byte[10 * 1024];
          while ((len = is.read(buffer)) > 0) {
            outputstream.write(buffer, 0, len);
            outputstream.flush();
          }
          outputstream.closeEntry();
        }
      }
 
    } catch (IOException e) {
      throw e;
    } finally {
      if (is != null) {
        is.close();
      }
    }
 
  }
 
}