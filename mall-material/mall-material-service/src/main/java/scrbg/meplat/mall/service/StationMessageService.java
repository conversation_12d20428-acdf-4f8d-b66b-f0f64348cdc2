package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import scrbg.meplat.mall.entity.StationMessage;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;

/**
 * @描述：站点消息 服务类
 * @作者: y
 * @日期: 2022-11-24
 */
public interface StationMessageService extends IService<StationMessage> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<StationMessage> stationMessageServiceLambdaQueryWrapper);

    /**
     * 查询收件信息列表
     * @param jsonObject
     * @param queryWrapper
     * @return
     */
    PageUtils queryPageList(JSONObject jsonObject, QueryWrapper<StationMessage> queryWrapper);

    void create(StationMessage stationMessage);

    void update(StationMessage stationMessage);

    StationMessage getById(String id);

    void delete(String id);

    void createBatch(StationMessageReceiveVO vo);

    void createSystemBatch(StationMessageReceiveVO vo);

    PageUtils queryEnterprisePageList(JSONObject jsonObject, QueryWrapper<StationMessage> wrapper);

    PageUtils queryPageListSystem(JSONObject jsonObject, QueryWrapper<StationMessage> stationMessageQueryWrapper);

    void checkSupplierInfo(Integer isSupplier, String enterpriseId);


}
