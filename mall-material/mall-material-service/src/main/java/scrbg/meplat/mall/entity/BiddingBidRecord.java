package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：竞价记录
 * @作者: ye
 * @日期: 2023-07-19
 */
@ApiModel(value = "竞价记录")
@Data
@TableName("bidding_bid_record")
public class BiddingBidRecord extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "竞价记录id")
    private String bidRecordId;

    @ApiModelProperty(value = "竞价记录编号")
    private String bidRecordSn;

    @ApiModelProperty(value = "竞价采购id")

    private String biddingId;


    @ApiModelProperty(value = "竞价采购编号")

    private String biddingSn;


    @ApiModelProperty(value = "联系电话")

    private String contactPhone;


    @ApiModelProperty(value = "联系人")

    private String contactPerson;


    @ApiModelProperty(value = "供应商id（本地）")

    private String supplierId;


    @ApiModelProperty(value = "供应商名称（本地）")

    private String supplierName;


    @ApiModelProperty(value = "不含税总金额")

    private BigDecimal bidAmount;


    @ApiModelProperty(value = "含税总金额")

    private BigDecimal bidRateAmount;


    @ApiModelProperty(value = "竞价时间")

    private Date bidTime;


    @ApiModelProperty(value = "驳回原因")

    private String rejectReason;


    @ApiModelProperty(value = "状态(0默认1已提交5中标待审核6中标审核通过7审核失败;8作废)")

    private Integer state;

    @ApiModelProperty(value = "提交时间")

    private Date submitDate;


    @ApiModelProperty(value = "附件远程id")
    @TableField(exist = false)
    private String fileFarId;
    @ApiModelProperty(value = "附件名称")
    @TableField(exist = false)
    private String fileName;


    @ApiModelProperty(value = "价格类型（1浮动价格2固定价格）大宗临购使用")

    private Integer billType;
    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;

    /**
     * 竞价标题
     */
    @TableField(exist = false)
    private String title;

    @ApiModelProperty(value = "竞价采购类型（1公开竞价2邀请竞价）")
    @TableField(exist = false)
    private Integer type;

    @ApiModelProperty(value = "截止时间")
    @TableField(exist = false)
    private Date endTime;

    @ApiModelProperty(value = "商品类型：0物资 （所有商品都是物资，只有下单才会根据分类自动生成不同的订单）最新改动：商品类型：0 低值易耗品 1大宗临购 2、周转材料")
    @TableField(exist = false)
    private Integer productType;

    @ApiModelProperty(value = "竞价来源类型（1订单2商品（物资基础库）3清单）")
    @TableField(exist = false)
    private Integer biddingSourceType;

    @ApiModelProperty(value = "发布时间")
    @TableField(exist = false)
    private Date startTime;


    /**
     * 0:未中标；1：已中标
     */
    private Integer bidState;





}