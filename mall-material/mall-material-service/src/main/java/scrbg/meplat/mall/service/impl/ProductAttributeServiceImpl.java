package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.ProductAttribute;
import scrbg.meplat.mall.mapper.ProductAttributeMapper;
import scrbg.meplat.mall.service.ProductAttributeService;

/**
 * @描述：商品属性 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class ProductAttributeServiceImpl extends ServiceImpl<ProductAttributeMapper, ProductAttribute> implements ProductAttributeService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductAttribute> queryWrapper) {
        IPage<ProductAttribute> page = this.page(
                new Query<ProductAttribute>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ProductAttribute productAttribute) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(productAttribute);
    }

    @Override
    public void update(ProductAttribute productAttribute) {
        super.updateById(productAttribute);
    }


    @Override
    public ProductAttribute getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
