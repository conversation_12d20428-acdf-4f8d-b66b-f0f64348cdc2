package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.dto.bidding.AuditBidingDTO;
import scrbg.meplat.mall.entity.BiddingPurchase;
import scrbg.meplat.mall.service.BiddingBidRecordService;
import scrbg.meplat.mall.entity.BiddingBidRecord;
import scrbg.meplat.mall.vo.bidding.BiddingBidRecordVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @描述：竞价记录控制类
 * @作者: ye
 * @日期: 2023-07-11
 */
@RestController
@RequestMapping("/biddingBidRecord")
@Api(tags = "竞价记录")
public class BiddingBidRecordController {

    @Autowired
    public BiddingBidRecordService biddingBidRecordService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<BiddingPurchase> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = biddingBidRecordService.queryPage(jsonObject, new LambdaQueryWrapper<BiddingPurchase>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<BiddingBidRecord> findById(String id) {
        BiddingBidRecord biddingBidRecord = biddingBidRecordService.getById(id);
        return R.success(biddingBidRecord);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody BiddingBidRecord biddingBidRecord) {
        biddingBidRecordService.create(biddingBidRecord);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody BiddingBidRecord biddingBidRecord) {
        biddingBidRecordService.update(biddingBidRecord);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        biddingBidRecordService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        biddingBidRecordService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("/selectListByBiddingId")
    @ApiOperation(value = "根据竞价id查询所有的竞价记录")
    public List<BiddingBidRecord> selectListByBiddingId(String biddingId) {
        List<BiddingBidRecord> list=  biddingBidRecordService.selectListByBiddingId(biddingId);
        return list;
    }
    @GetMapping("/exportBidSummary")
    @ApiOperation(value = "根据竞价编号查询所有的竞价记录")
    public void exportBidSummary(String bidSn, HttpServletResponse response){
        biddingBidRecordService.exportBidSummary(bidSn,response);
    }

    @GetMapping("/getBiddingRecordInfo")
    @ApiOperation(value = "获取竞价记录详细信息")
    public R<BiddingBidRecordVO> getBiddingRecordInfo(String biddingId) {
        BiddingBidRecordVO vo = biddingBidRecordService.getBiddingRecordInfo(biddingId);
        return R.success(vo);
    }

    @PostMapping("/updateAuditStatus")
    @ApiOperation(value = "竞价记录审批")
    public R updateAuditStatus(@RequestBody AuditBidingDTO auditBidingDTO) {
        biddingBidRecordService.updateAuditStatus(auditBidingDTO);
        return R.success();
    }


}

