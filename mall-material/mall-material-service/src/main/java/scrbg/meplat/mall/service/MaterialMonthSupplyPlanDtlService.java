package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtl;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.vo.supplier.PushPlanToSupplierVo;

import java.util.List;

/**
 * @描述：计划明细 服务类
 * @作者: ye
 * @日期: 2023-06-27
 */
public interface MaterialMonthSupplyPlanDtlService extends IService<MaterialMonthSupplyPlanDtl> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMonthSupplyPlanDtl> queryWrapper);

    void create(MaterialMonthSupplyPlanDtl materialMonthSupplyPlanDtl);

    void update(MaterialMonthSupplyPlanDtl materialMonthSupplyPlanDtl);

    MaterialMonthSupplyPlanDtl getById(String id);

    void delete(String id);

    void pushPlanToSupplierVo(PushPlanToSupplierVo pushPlanToSupplierVo);


    void closePushDtl(String id);

    /**
     * 获取计划二级供应商id
     * @param planDtl  计划明细id
     * @return
     */
    String getTwoSupplierIdByPlanDtl(String planDtl);

    List<MaterialMonthSupplyPlanDtl> getListByPlanId(String planId);

    List<MaterialMonthSupplyPlanDtl> getSecondLevelListByPlanId(String planId);

    /**
     * 获取大宗采购合同月供计划已使用数量
     */
    void getMonthPlanQtyCount();



}
