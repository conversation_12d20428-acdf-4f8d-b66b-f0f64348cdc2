package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import scrbg.meplat.mall.entity.InvoiceDtl;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.entity.MaterialReconciliationDtl;
import scrbg.meplat.mall.mapper.MaterialReconciliationDtlMapper;
import scrbg.meplat.mall.service.MaterialReconciliationDtlService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.service.MaterialReconciliationService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.pageUtils;
import scrbg.meplat.mall.vo.platform.ListShipByAffirmListVO;
import scrbg.meplat.mall.vo.platform.ReconciliationLedgerListVo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @描述：物资验收明细 服务类
 * @作者: ye
 * @日期: 2023-07-26
 */
@Service
public class MaterialReconciliationDtlServiceImpl extends ServiceImpl<MaterialReconciliationDtlMapper, MaterialReconciliationDtl> implements MaterialReconciliationDtlService{

    @Autowired
    public MaterialReconciliationService materialReconciliationService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliationDtl> queryWrapper) {
        IPage<MaterialReconciliationDtl> page = this.page(
        new Query<MaterialReconciliationDtl>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(MaterialReconciliationDtl materialReconciliationDtl) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(materialReconciliationDtl);
    }

    @Override
    public void update(MaterialReconciliationDtl materialReconciliationDtl) {
        super.updateById(materialReconciliationDtl);
    }


    @Override
    public MaterialReconciliationDtl getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }


    @Override
    public PageUtils ListByBillIds(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliationDtl> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        List<String> reconciliationIds = (List<String>) innerMap.get("reconciliationIds");
        queryWrapper.in(MaterialReconciliationDtl::getReconciliationId,reconciliationIds);
        IPage<MaterialReconciliationDtl> page = this.page(
                new Query<MaterialReconciliationDtl>().getPage(jsonObject),
                queryWrapper
        );
        List<MaterialReconciliationDtl> records = page.getRecords();
        ArrayList<MaterialReconciliationDtl> materialReconciliationDtls = new ArrayList<>();
        if (records!=null&&records.size()>0){
            Map<String, List<MaterialReconciliationDtl>> reconciliation = records.stream().collect(Collectors.groupingBy(MaterialReconciliationDtl::getReconciliationId));
            reconciliation.forEach((reconciliationId, list) -> {
                MaterialReconciliation info = materialReconciliationService.getById(reconciliationId);
                for (MaterialReconciliationDtl reconciliationDtl : list) {
                    reconciliationDtl.setReconciliationNo(info.getReconciliationNo());
                    reconciliationDtl.setTaxRate(info.getTaxRate());
                    materialReconciliationDtls.add(reconciliationDtl);
                }
            });
        }
        return new PageUtils(page.setRecords(materialReconciliationDtls));
    }

    @Override
    public PageUtils materialReconciliationLedger(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper) {
        int count = baseMapper.listLedgerCount(jsonObject.getInnerMap());
        pageUtils.pageDispose(jsonObject, count);
        Page<ReconciliationLedgerListVo> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ReconciliationLedgerListVo> vos = baseMapper.ledgerList(pages, jsonObject);
        BigDecimal CountAmount = baseMapper.selCountAmount(jsonObject);
        BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(jsonObject);
         if (vos.size() > 0) {
             vos.get(0).setCountAmount(CountAmount);
             vos.get(0).setCountNoRateAmount(countNoRateAmount);
         }
        pages.setRecords(vos);
        return new PageUtils(pages);
    }
    @Override
    public PageUtils materialReconciliationLedgerSupplier(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper) {
        // 通过订单id关联shopId
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String shopId = (String) jsonObject.getInnerMap().get("shopId");
        if (StringUtils.isNotEmpty(shopId)) {
            //materialReconciliationLambdaQueryWrapper.eq("o.shop_id", shopId);
            jsonObject.getInnerMap().put("shopId", shopId);
        } else {
            //materialReconciliationLambdaQueryWrapper.eq("o.shop_id", user.getShopId());
            jsonObject.getInnerMap().put("shopId", user.getShopId());
        }
        int count = baseMapper.listLedgerCount(jsonObject.getInnerMap());
        pageUtils.pageDispose(jsonObject, count);
        Page<ReconciliationLedgerListVo> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ReconciliationLedgerListVo> vos = baseMapper.ledgerList(pages, jsonObject);
        BigDecimal CountAmount = baseMapper.selCountAmount(jsonObject);
        BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(jsonObject);
        if (vos.size() > 0) {
            vos.get(0).setCountAmount(CountAmount);
            vos.get(0).setCountNoRateAmount(countNoRateAmount);
        }
        pages.setRecords(vos);
        return new PageUtils(pages);
    }
}
