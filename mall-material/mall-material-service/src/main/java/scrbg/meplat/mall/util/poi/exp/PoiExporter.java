package scrbg.meplat.mall.util.poi.exp;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Picture;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.util.poi.exception.PoiElErrorCode;
import scrbg.meplat.mall.util.poi.exp.context.PoiExporterContext;
import scrbg.meplat.mall.util.poi.exp.function.FunctionRegister;
import scrbg.meplat.mall.util.poi.exp.processor.RowProcessorStrategy;
import scrbg.meplat.mall.util.poi.log.Log;

import javax.imageio.ImageIO;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * excel导出类
 */
public class PoiExporter {
	private static final Logger logger = LoggerFactory.getLogger(PoiExporter.class);

	/**
	 * 向StandardEvaluationContext中注册内部函数
	 */
	static {
		FunctionRegister.registerInternalFunction();
	}

	public static void export(XSSFWorkbook wb, Map<String, Object> rootObjectMap) {
		Long start = System.currentTimeMillis();
		PoiExporterContext peContext = new PoiExporterContext(new SpelExpressionParser(), rootObjectMap);
		// 分sheet进行处理
		for (int i = 0; i < wb.getNumberOfSheets(); i++) {
			XSSFSheet sheet = wb.getSheetAt(i);
			// 开始行结束行
			int j = sheet.getFirstRowNum();
			// 每行
			while (j <= sheet.getLastRowNum()) {
				try {
					XSSFRow row = sheet.getRow(j);
					if (row == null) {
						j++;
						continue;
					}
					int dealRows = RowProcessorStrategy.getRowProcessor(row).dealRow(row, peContext);
					if(dealRows == 0) {
						dealRows = 1;
					}
					j = j + dealRows;
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}

		long end = System.currentTimeMillis();
		logger.info(Log.op("PoiEl#parse").msg("PoiEl解析模板耗时[{0}]ms", (end - start)).toString());
	}

	/**
	 * 导出到指定地方 des
	 * @param templateFile
	 * @param rootObjectMap
	 * @param des
	 * @return
	 */
	public static XSSFWorkbook export2Destination(File templateFile, Map<String, Object> rootObjectMap, OutputStream des){
		InputStream in = null;
		try {
			in = new FileInputStream(templateFile);
		} catch (FileNotFoundException e) {
			throw new BusinessException("excel导出失败"+ e.getMessage());
		}
		return export2Destination(in, rootObjectMap, des);
	}

	/**
	 * 导出到指定地方 des
	 * @param templateInputStream 模板
	 * @param rootObjectMap 数据
	 * @param des 导出的位置
	 * @return
	 */
	public static XSSFWorkbook export2Destination(InputStream templateInputStream, Map<String, Object> rootObjectMap, OutputStream des){
		XSSFWorkbook wb = null;
		try {
			wb = new XSSFWorkbook(templateInputStream);
		} catch (IOException e) {
			throw new BusinessException(e.getMessage());
		}
		PoiExporter.export(wb, rootObjectMap);
		// 关闭资源
		try {
			wb.write(des);
		} catch (IOException e) {
			throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
		}catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				des.flush();
				des.close();
			}catch (Exception e) {
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}
		}
		return wb;
	}


	public static void export2DestinationQrCode(String text,InputStream templateInputStream, Map<String, Object> rootObjectMap, OutputStream des) {
		XSSFWorkbook wb = null;
		try {
			wb = new XSSFWorkbook(templateInputStream);
		} catch (IOException e) {
			throw new BusinessException(e.getMessage());
		}
		PoiExporter.export(wb, rootObjectMap);
		processQrCode(wb,generateQRImage(text));
		// 关闭资源
		try {
			wb.write(des);
		} catch (IOException e) {
			throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
		}catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				des.flush();
				des.close();
			}catch (Exception e) {
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}
		}
	}

	public static void processQrCode(XSSFWorkbook wb,ByteArrayOutputStream outputStream) {
		// 将图片写入Excel
		XSSFSheet sheet = wb.getSheetAt(0);
		int pictureIdx = wb.addPicture(outputStream.toByteArray(), Workbook.PICTURE_TYPE_PNG);
		XSSFDrawing drawing = (XSSFDrawing) sheet.createDrawingPatriarch();
		// 创建绘图工具
		CreationHelper helper = wb.getCreationHelper();
		// 定义二维码的位置和大小
		ClientAnchor anchor = helper.createClientAnchor();
		anchor.setCol1(0); // 起始列 A
		anchor.setRow1(0); // 起始行 1
		anchor.setCol2(0); // 结束列 A（不包括下一列）
		anchor.setRow2(0); // 结束行 1（不包括下一行）
		// 设置偏移量（1/1024 单元格宽度/高度）
		anchor.setDx1(0);
		anchor.setDy1(0);
		anchor.setDx2(20 * 1024); // 20/256 列宽
		anchor.setDy2(20 * 256); // 20/256 行高
		// Y轴偏移量
		Picture picture = drawing.createPicture(anchor, pictureIdx);
		picture.resize(); // 自动调整大小以适应锚点
	}
	private static ByteArrayOutputStream generateQRImage(String text) {
		// 设置二维码参数
		Map<EncodeHintType, Object> hints = new HashMap<>();
		hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.L);
		hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
		hints.put(EncodeHintType.MARGIN, 0); // 关键：设置边距为 0
		QRCodeWriter qrCodeWriter = new QRCodeWriter();
		BitMatrix bitMatrix = null;
		try {
			bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, 100, 100);
		} catch (WriterException e) {
			throw new RuntimeException(e);
		}
		// 将BufferedImage写入字节数组
		ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
		try {
			ImageIO.write(MatrixToImageWriter.toBufferedImage(bitMatrix), "png", byteArrayOutputStream);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		return byteArrayOutputStream;
	}
}
