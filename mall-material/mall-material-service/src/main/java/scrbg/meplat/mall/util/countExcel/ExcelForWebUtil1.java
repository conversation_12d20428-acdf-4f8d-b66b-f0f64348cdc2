//package scrbg.meplat.mall.util.countExcel;
//
//import net.sf.jxls.exception.ParsePropertyException;
//import net.sf.jxls.transformer.XLSTransformer;
//import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
//import org.apache.poi.ss.usermodel.Workbook;
//
//import javax.servlet.ServletOutputStream;
//import javax.servlet.http.HttpServletResponse;
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.FileOutputStream;
//import java.io.IOException;
//import java.util.Map;
//
//public class ExcelForWebUtil1 {
//
//    /**
//     * 根据模板，导出excel
//     *
//     * @throws ParsePropertyException
//     * @throws InvalidFormatException
//     * @throws IOException
//     *
//     */
//    /**
//     *
//     * @param response
//     * @param beanParams    数据
//     * @param templementFile 模板名称
//     * @param dir 模板路径
//     * @param fileName 文件名称
//     * @throws ParsePropertyException
//     * @throws InvalidFormatException
//     * @throws IOException
//     */
//    public static void exportExcel(HttpServletResponse response, Map<String, Object> beanParams, String templementFile,
//            String dir, String fileName) throws ParsePropertyException, InvalidFormatException, IOException {
//
//        String url = dir + File.separator + templementFile;
//
//        FileInputStream fin = new FileInputStream(url);
//
//        XLSTransformer transformer = new XLSTransformer();
//
//        Workbook workBook = transformer.transformXLS(fin, beanParams);
//
//        response.setHeader("Content-Disposition",
//                "attachment; filename=\"" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + "\"");
//
//        ServletOutputStream out = response.getOutputStream();
//        workBook.write(out);
//        out.flush();
//        out.close();
//        fin.close();
//        out = null;
//        fin = null;
//        workBook = null;
//        transformer = null;
//    }
//
//    public static void saveExcel(String savePath, String savefileName, Map<String, Object> beanParams,
//            String templementFile, String dir) throws ParsePropertyException, InvalidFormatException, IOException {
//        String url = dir + File.separator + templementFile;
//        FileInputStream fin = new FileInputStream(url);
//        XLSTransformer transformer = new XLSTransformer();
//        Workbook workBook = transformer.transformXLS(fin, beanParams);
//        File f = new File(savePath + File.separator + savefileName);
//        if (f.isFile()) {
//            f.createNewFile();
//        }
//        FileOutputStream fos = new FileOutputStream(f);
//        workBook.write(fos);
//    }
//
//    public static void testSaveExcel(String savePath, Map<String, Object> beanParams,
//                                 String templementFile) throws ParsePropertyException, InvalidFormatException, IOException {
//        FileInputStream fin = new FileInputStream(templementFile);
//        XLSTransformer transformer = new XLSTransformer();
//        Workbook workBook = transformer.transformXLS(fin, beanParams);
//        File f = new File(savePath);
//        if (f.isFile()) {
//            f.createNewFile();
//        }
//        FileOutputStream fos = new FileOutputStream(f);
//        workBook.write(fos);
//        fos.close();
//        fin.close();
//    }
//
//    public static void main(String[] args) {
//        System.out.println(File.separator);
//    }
//
//}
