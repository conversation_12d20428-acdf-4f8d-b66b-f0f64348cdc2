package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：
 * @作者: ye
 * @日期: 2023-07-07
 */
@ApiModel(value = "")
@Data
@TableName("statistics_data")
public class StatisticsData extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "统计id")
    private String statisticsId;

    @ApiModelProperty(value = "店铺数")

    private Long shopNum;


    @ApiModelProperty(value = "零星采购发货金额")

    private BigDecimal orderShipTotalAmount;


    @ApiModelProperty(value = "零星采购订单数")

    private Long ordersNum;


    @ApiModelProperty(value = "企业数据")

    private Long enterpriseNum;


    @ApiModelProperty(value = "商品上架总数")

    private Long upProductNum;


    @ApiModelProperty(value = "商品总数")

    private Long productNum;


    @ApiModelProperty(value = "订单金额")

    private BigDecimal ordersTotalAmount;


    @ApiModelProperty(value = "用户数")

    private Long userNum;

    @ApiModelProperty(value = "供应商")

    private Long supplierNum;

    @TableField(exist = false)
    @ApiModelProperty(value = "导出时间")

    private String gmtCreateStr;


}
