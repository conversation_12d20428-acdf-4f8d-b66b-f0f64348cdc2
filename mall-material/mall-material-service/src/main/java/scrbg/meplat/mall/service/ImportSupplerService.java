package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ImportSuppler;
/**
 * @描述：导入供应商失败的结果 服务类
 * @作者: ye
 * @日期: 2023-05-09
 */
public interface ImportSupplerService extends IService<ImportSuppler> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ImportSuppler> queryWrapper);

        void create(ImportSuppler importSuppler);

        void update(ImportSuppler importSuppler);

        ImportSuppler getById(String id);

        void delete(String id);
}