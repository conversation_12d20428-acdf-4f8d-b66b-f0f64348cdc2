package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.OrderSelectContact;
/**
 * @描述：订单关联合同 服务类
 * @作者: ye
 * @日期: 2023-04-23
 */
public interface OrderSelectContactService extends IService<OrderSelectContact> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectContact> queryWrapper);

        void create(OrderSelectContact orderSelectContact);

        void update(OrderSelectContact orderSelectContact);

        OrderSelectContact getById(String id);

        void delete(String id);
}