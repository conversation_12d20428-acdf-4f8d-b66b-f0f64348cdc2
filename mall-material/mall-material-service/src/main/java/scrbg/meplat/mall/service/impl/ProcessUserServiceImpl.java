package scrbg.meplat.mall.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import scrbg.meplat.mall.entity.ProcessUser;
import scrbg.meplat.mall.mapper.ProcessUserMapper;
import scrbg.meplat.mall.service.ProcessUserService;

/**
 * 流程配置表 服务类
 * <AUTHOR>
 * @date: 2025年6月20日 上午10:06:41
 */
@Service
public class ProcessUserServiceImpl extends ServiceImpl<ProcessUserMapper, ProcessUser> implements ProcessUserService {

    @Autowired
    ProcessUserService processUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProcessUser processUser) {
        super.save(processUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProcessUser processUser) {
        super.updateById(processUser);
    }

    @Override
    public ProcessUser getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        super.removeById(id);
    }

}
