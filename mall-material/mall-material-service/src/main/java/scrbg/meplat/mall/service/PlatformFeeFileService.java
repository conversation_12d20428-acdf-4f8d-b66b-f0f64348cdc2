package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.PlatformFeeFile;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.PlatformFeeFile;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：通用附件 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
public interface PlatformFeeFileService extends IService<PlatformFeeFile> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformFeeFile> queryWrapper);

        void create(PlatformFeeFile platformFeeFile);
        void update(PlatformFeeFile platformFeeFile);
        PlatformFeeFile getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);


}
