package scrbg.meplat.mall.controller.website;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.Content;
import scrbg.meplat.mall.mapper.ProductMapper;
import scrbg.meplat.mall.service.AdPictureService;
import scrbg.meplat.mall.service.ContentService;
import scrbg.meplat.mall.service.LinksService;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.shopManage.reportForms.ProductFromVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2022-11-10 17:36
 */
@RestController
@RequestMapping("/w/homePage")
@ApiSort(value = 100)
@Api(tags = "首页内容（前台）")
public class HomePageController {
    @Autowired
    ProductMapper productMapper;

    @Autowired
    private LinksService linksService;
    @Autowired
    private ContentService contentService;
    @Autowired
    private AdPictureService adPictureService;
    @Autowired
    private MallConfig mallConfig;

/*

    @PostMapping("/links/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<Links> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = linksService.queryPage(jsonObject, new LambdaQueryWrapper<Links>());
        return PageR.success(page);
    }
*/

    /**
     * 导出
     *
     * @param response
     */
    @GetMapping(value = "/testData")
    public void export( HttpServletResponse response) {
        QueryWrapper<ProductFromVo> q = new QueryWrapper<>();
        q.eq("serial_num","050879");
        List<ProductFromVo> list = productMapper.productFromListLoad2(q);
        try {
            String src = "D:\\TIB";
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("dataList",list);
            ExcelForWebUtil.exportExcel(response, dataMap, "商品报表模板.xlsx", src, "商品报表.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    /**
     * 首页新闻--通过条件获取该条件所有新闻
     *
     * @param
     * @return
     */
    @PostMapping("/more/findContenByCondition")
    @ApiOperation(value = "通过条件获取该条件所有新闻")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "title", value = "标题名称", dataTypeClass = String.class),
            @DynamicParameter(name = "home", value = "首页显示", dataTypeClass = Integer.class),
            @DynamicParameter(name = "top", value = "顶部显示", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "发布状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "mallType", value = "商城类型", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
            @DynamicParameter(name = "programaKey", value = "栏目KEY", dataTypeClass = String.class),
    })
    public PageR<Content> findContenByCondition(@RequestBody JSONObject jsonObject) {
        PageUtils page = contentService.queryPublicContentPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }


    @PostMapping("/more/notification/findContenByCondition")
    @ApiOperation(value = "通过条件获取该条件所有公告")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "title", value = "标题名称", dataTypeClass = String.class),
            @DynamicParameter(name = "home", value = "首页显示", dataTypeClass = Integer.class),
            @DynamicParameter(name = "top", value = "顶部显示", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "发布状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "mallType", value = "商城类型", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序方式", dataTypeClass = Integer.class),
            @DynamicParameter(name = "programaKey", value = "栏目KEY", dataTypeClass = String.class),
    })
    public PageR<Content> findContenByConditionList(@RequestBody JSONObject jsonObject) {
        PageUtils page = contentService.queryPublicContentPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }

    /**
     * 首页底部--通过栏目KEY获取该条件所有栏目
     *
     * @param
     * @return
     */

    @GetMapping("/more/findContenById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Content> findContenById(String id) {
        Content content = contentService.getById(id);
        if (content != null && content.getState() == 1) {
            return R.success(content);
        }
        return R.success();
    }


    @PostMapping("/getMaterialHomeInfo")
    @ApiOperation(value = "获取物资商场首页")
    @ApiImplicitParam(name = "maxAdImg", value = "maxAdImg", required = false,
            dataType = "Integer", paramType = "query")
    public R getMaterialHomeInfo(@RequestBody JSONObject jsonObject) {
        R r = adPictureService.queryHomeInfo(jsonObject, 0);
        return R.success(r);
    }

    @PostMapping("/getDeviceHomeInfo")
    @ApiOperation(value = "获取设备商场首页")
    @ApiImplicitParam(name = "maxAdImg", value = "maxAdImg", required = false,
            dataType = "Integer", paramType = "query")
    public R getDeviceHomeInfo(@RequestBody JSONObject jsonObject) {
        R r = adPictureService.queryHomeInfo(jsonObject, 1);
        return R.success(r);
    }

}
