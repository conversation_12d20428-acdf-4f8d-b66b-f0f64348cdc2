package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.MaterialMasterPlan;

/**
 * @描述：物资总计划 服务类
 * @作者: y
 * @日期: 2022-11-10
 */
public interface MaterialMasterPlanService extends IService<MaterialMasterPlan> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMasterPlan> queryWrapper);

    void create(MaterialMasterPlan materialMasterPlan);

    void update(MaterialMasterPlan materialMasterPlan);

    MaterialMasterPlan getById(String id);

    void delete(String id);
}
