package scrbg.meplat.mall.service.impl;

import java.math.BigDecimal;
import java.math.MathContext;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.entity.PlatformBalanceOperate;
import scrbg.meplat.mall.entity.PlatformDealFee;
import scrbg.meplat.mall.entity.PlatformDealFeeDtl;
import scrbg.meplat.mall.entity.ShopBusiness;
import scrbg.meplat.mall.entity.SupplierReconciliation;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.PlatformDealFeeDtlMapper;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.PlatformBalanceOperateService;
import scrbg.meplat.mall.service.PlatformDealFeeDtlService;
import scrbg.meplat.mall.service.PlatformDealFeeService;
import scrbg.meplat.mall.service.ShopBusinessService;
import scrbg.meplat.mall.service.SystemParamService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

/**
 * @描述：平台交易费明细 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
@Service
public class PlatformDealFeeDtlServiceImpl extends ServiceImpl<PlatformDealFeeDtlMapper, PlatformDealFeeDtl> implements PlatformDealFeeDtlService {

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    PlatformDealFeeDtlMapper platformDealFeeDtlMapper;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    SystemParamService systemParamService;

    @Autowired
    PlatformDealFeeService platformDealFeeService;

    @Autowired
    PlatformBalanceOperateService platformBalanceOperateService;

    @Autowired
    PlatformDealFeeDtlService platformDealFeeDtlService;

    @Autowired
    ShopBusinessService shopBusinessService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeDtl> queryWrapper) {
        IPage<PlatformDealFeeDtl> page = this.page(
                new Query<PlatformDealFeeDtl>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(PlatformDealFeeDtl platformDealFeeDtl) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(platformDealFeeDtl);
    }

    @Override
    public void update(PlatformDealFeeDtl platformDealFeeDtl) {
        super.updateById(platformDealFeeDtl);
    }


    @Override
    public PlatformDealFeeDtl getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    /**
     * 供应商查询待缴费的明细
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils notPayDealFeeDtlList(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeDtl> q) {
        Integer serveType = (Integer) jsonObject.get("serveType");
        UserLogin user = ThreadLocalUtil.getCurrentUser();

        // 查询本供应商和待缴费的
        q.eq(PlatformDealFeeDtl::getEnterpriseId, user.getEnterpriseId());
        q.eq(PlatformDealFeeDtl::getFinishPayFee, 0);
        q.eq(PlatformDealFeeDtl::getPayFeeState, 0); // 不包括对账单已作废的数据

        String keywords = (String) jsonObject.get("keywords");
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(PlatformDealFeeDtl::getProjectEnterpriseName, keywords)
                        .or()
                        .like(PlatformDealFeeDtl::getRelevanceNu, keywords);
            });
        }

        q.eq(serveType != null, PlatformDealFeeDtl::getServeType, serveType);

        IPage<PlatformDealFeeDtl> page = this.page(
                new Query<PlatformDealFeeDtl>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }


    /**
     * 校验本次交易是否欠费
     * operateType 操作类型，0 新增草稿校验，1 审核通过检验
     */
    @Override
    public void checkDealAmount(MaterialReconciliation mr, int operateType) {
        String supplierName = mr.getSupplierName();
        Integer businessType = mr.getBusinessType();
        EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseName, supplierName)
                .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getArrearage,
                        EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getArrearageDateNum, EnterpriseInfo::getArrearageDateType,
                        EnterpriseInfo::getShuDaoFlag, EnterpriseInfo::getIsNoSupplierAudit, EnterpriseInfo::getInteriorId).one();
        if (supplier == null) {
            throw new BusinessException("未查询到供应商！");
        }
        // 如果是蜀道/内部企业无需校验
        if (supplier.getShuDaoFlag() == 1 || supplier.getInteriorId() != null) return;
        Integer serveType = 1;
        PlatformDealFee platformDealFee = platformDealFeeService.lambdaQuery()
                .eq(PlatformDealFee::getEnterpriseId, supplier.getEnterpriseId())
                .eq(PlatformDealFee::getServeType, 1)
                .one();
        if (platformDealFee == null) {
            // 如果为空直接新增
            platformDealFee = new PlatformDealFee();
            platformDealFee.setEnterpriseId(supplier.getEnterpriseId());
            platformDealFee.setPlatformDealFeeNu(IdWorker.getIdStr());
            platformDealFee.setEnterpriseName(supplier.getEnterpriseName());
            platformDealFee.setServeType(serveType);
            platformDealFee.setRemainingSum(new BigDecimal(0));
            platformDealFeeService.save(platformDealFee);
        }
        // 可欠费额度
        BigDecimal arrearage = supplier.getArrearage();
        // 获取所有的待缴费金额
        QueryWrapper<PlatformDealFeeDtl> q = new QueryWrapper<>();
        q.eq("enterprise_id", supplier.getEnterpriseId())
                .eq("serve_type", serveType)
                .eq("finish_pay_fee", 0)
                .eq("state", 0)
                .select("sum(residue_pay_fee) as totalAmount");
        List<Map<String, Object>> maps = baseMapper.selectMaps(q);
        BigDecimal totalAmount = new BigDecimal(0);
        if (maps.get(0) != null) {
            Map<String, Object> stringObjectMap = maps.get(0);
            totalAmount = new BigDecimal(stringObjectMap.get("totalAmount").toString());
        }
        // 是审核才统计额度
        if (operateType == 1) {
            // 先拿到直接是免费金额总金额
//            QueryWrapper<PlatformDealFeeDtl> q2 = new QueryWrapper<>();
//            q2.eq("enterprise_id", supplier.getEnterpriseId())
//                    .eq("serve_type", serveType)
//                    .eq("pay_type", 2)
//                    .eq("state", 0)
//                    .select("sum(deal_amount) as totalAmount");
//            List<Map<String, Object>> maps2 = baseMapper.selectMaps(q2);
//            BigDecimal dealAmount = new BigDecimal(0);
//            if (maps2.get(0) != null) {
//                dealAmount = new BigDecimal(maps2.get(0).get("totalAmount").toString());
//            }


            // 拿到占用免费金额部分
            QueryWrapper<PlatformDealFeeDtl> ep = new QueryWrapper<>();
            ep.eq("enterprise_id", supplier.getEnterpriseId())
                    .eq("serve_type", serveType)
                    .eq("state", 0)
                    .select("sum(use_exempt_free) as useExemptFree");
            List<Map<String, Object>> epMap = baseMapper.selectMaps(ep);
            BigDecimal useExemptFree = new BigDecimal(0);
            if (epMap.get(0) != null) {
                useExemptFree = new BigDecimal(epMap.get(0).get("useExemptFree").toString());
            }
            // 拿到最大免费额度
            String queryKey = serveType == 1 ? PublicEnum.PLATFORM_DEAL_MAX_QUOTA_DP.getRemark() : PublicEnum.PLATFORM_DEAL_MAX_QUOTA_LY.getRemark();
            SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode, queryKey).one();
            BigDecimal totalEd = new BigDecimal(systemParam.getKeyValue());


            // 免费交易金额
//            BigDecimal addEFree = useExemptFree.add(mr.getReconciliationAmount());
            if (useExemptFree.compareTo(totalEd) == -1) {
                // 如果总免费金额小于限制额度，处理多种情况
                // 拿到本次要保存的金额
                BigDecimal addAmount = useExemptFree.add(mr.getReconciliationAmount());
                if (addAmount.compareTo(totalEd) == 1) {
                    // 本次算上超过了免费额度
                    // 先校验历史是否欠费
                    if (totalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        // 审核通过校验历史是否欠费
                        addMCheck(supplier, platformDealFee, totalAmount, arrearage);
                    }
                    // 需要收费的金额
                    BigDecimal yesFree = addAmount.subtract(totalEd);
                    addDtlInfoList(mr, supplier, serveType, platformDealFee, 1, yesFree);

                    // 保存之后重新统计待缴费金额是否欠费，设置欠费时间
                    QueryWrapper<PlatformDealFeeDtl> qc = new QueryWrapper<>();
                    qc.eq("enterprise_id", supplier.getEnterpriseId())
                            .eq("serve_type", serveType)
                            .eq("finish_pay_fee", 0)
                            .eq("state", 0)
                            .select("sum(residue_pay_fee) as totalAmount");
                    List<Map<String, Object>> mapsc = baseMapper.selectMaps(qc);
                    BigDecimal newTotalAmount = null;
                    if (mapsc.get(0) == null) {
                        newTotalAmount = new BigDecimal(0);
                    } else {
                        newTotalAmount = new BigDecimal(mapsc.get(0).get("totalAmount").toString());
                    }
//                    // 校验本次费用是否超过
//                    boolean flag = checkOutTime(supplier, platformDealFee, newTotalAmount);
//                    if (flag) {
//                        // 并且设置欠费时间
//                        if (platformDealFee.getArrearageDateTime() == null) {
//                            platformDealFee.setArrearageDateTime(new Date());
//                            platformDealFeeService.update(platformDealFee);
//                        }
//                    }
                    if (newTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        // 只要还存在欠费，说明余额扣减了还是欠费，直接设置欠费时间
                        if (platformDealFee.getArrearageDateTime() == null) {
                            platformDealFee.setArrearageDateTime(new Date());
                            platformDealFeeService.update(platformDealFee);
                        }
                    }
                } else {
                    // 如果本次金额加上还是小于等于免费额度，则直接保存免费交易，
                    addDtlInfoList(mr, supplier, serveType, platformDealFee, 0, null);
                }
            } else {
                // 如果以前待缴费金额不为0
                if (totalAmount.compareTo(BigDecimal.ZERO) != 0) {
                    // 审核通过校验历史是否欠费
                    addMCheck(supplier, platformDealFee, totalAmount, arrearage);
                }

                // 大于等于免费交易额度，这次保存收费
                addDtlInfoList(mr, supplier, serveType, platformDealFee, 1, null);

                // 保存之后重新统计待缴费金额是否欠费，设置欠费时间
                QueryWrapper<PlatformDealFeeDtl> qc = new QueryWrapper<>();
                qc.eq("enterprise_id", supplier.getEnterpriseId())
                        .eq("serve_type", serveType)
                        .eq("finish_pay_fee", 0)
                        .eq("state", 0)
                        .select("sum(residue_pay_fee) as totalAmount");
                List<Map<String, Object>> mapsc = baseMapper.selectMaps(qc);
                BigDecimal newTotalAmount = null;
                if (mapsc.get(0) == null) {
                    newTotalAmount = new BigDecimal(0);
                } else {
                    newTotalAmount = new BigDecimal(mapsc.get(0).get("totalAmount").toString());
                }
//                // 校验本次费用是否超过（这是校验是否停止服务）
//                boolean flag = checkOutTime(supplier, platformDealFee, newTotalAmount);
//                if (flag) {
//                    // 并且设置欠费时间
//                    if (platformDealFee.getArrearageDateTime() == null) {
//                        platformDealFee.setArrearageDateTime(new Date());
//                        platformDealFeeService.update(platformDealFee);
//                    }
//                }
                if (newTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                    // 只要还存在欠费，说明余额扣减了还是欠费，直接设置欠费时间
                    if (platformDealFee.getArrearageDateTime() == null) {
                        platformDealFee.setArrearageDateTime(new Date());
                        platformDealFeeService.update(platformDealFee);
                    }
                }
            }
        }
        // 如果只是新增草稿
        if (operateType == 0) {
            if (totalAmount.compareTo(BigDecimal.ZERO) != 0) {
                // 新增校验
                addMCheck(supplier, platformDealFee, totalAmount, arrearage);
            }
        }

    }

    /**
     * 校验本次交易是否欠费-二级供应商对账
     */
    @Override
    public void checkTwoDealAmount(SupplierReconciliation mr) {
        String supplierName = mr.getTwoSupplierName();
        Integer businessType = mr.getBusinessType();
        EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseName, supplierName)
                .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getArrearage,
                        EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getArrearageDateNum, EnterpriseInfo::getArrearageDateType,
                        EnterpriseInfo::getShuDaoFlag, EnterpriseInfo::getIsNoSupplierAudit, EnterpriseInfo::getInteriorId).one();
        if (supplier == null) {
            throw new BusinessException("未查询到供应商！");
        }
        // 如果是蜀道/内部企业无需校验
        if (supplier.getShuDaoFlag() == 1 || supplier.getInteriorId() != null) return;
        Integer serveType = 1;//交易服务费
        PlatformDealFee platformDealFee = platformDealFeeService.lambdaQuery()
                .eq(PlatformDealFee::getEnterpriseId, supplier.getEnterpriseId())
                .eq(PlatformDealFee::getServeType, serveType)
                .one();
        if (platformDealFee == null) {
            // 如果为空直接新增
            platformDealFee = new PlatformDealFee();
            platformDealFee.setEnterpriseId(supplier.getEnterpriseId());
            platformDealFee.setPlatformDealFeeNu(IdWorker.getIdStr());
            platformDealFee.setEnterpriseName(supplier.getEnterpriseName());
            platformDealFee.setServeType(serveType);
            platformDealFee.setRemainingSum(new BigDecimal(0));
            platformDealFeeService.save(platformDealFee);
        }
        // 可欠费额度
        BigDecimal arrearage = supplier.getArrearage();
        // 获取所有的待缴费金额
        QueryWrapper<PlatformDealFeeDtl> q = new QueryWrapper<>();
        q.eq("enterprise_id", supplier.getEnterpriseId())
                .eq("serve_type", serveType)
                .eq("finish_pay_fee", 0)
                .eq("state", 0)
                .select("sum(residue_pay_fee) as totalAmount");
        List<Map<String, Object>> maps = baseMapper.selectMaps(q);
        BigDecimal totalAmount = new BigDecimal(0);
        if (maps.get(0) != null) {
            Map<String, Object> stringObjectMap = maps.get(0);
            totalAmount = new BigDecimal(stringObjectMap.get("totalAmount").toString());
        }
        // 是审核才统计额度
        // 拿到占用免费金额部分
        QueryWrapper<PlatformDealFeeDtl> ep = new QueryWrapper<>();
        ep.eq("enterprise_id", supplier.getEnterpriseId())
                .eq("serve_type", serveType)
                .eq("state", 0)
                .select("sum(use_exempt_free) as useExemptFree");
        List<Map<String, Object>> epMap = baseMapper.selectMaps(ep);
        BigDecimal useExemptFree = new BigDecimal(0);
        if (epMap.get(0) != null) {
            useExemptFree = new BigDecimal(epMap.get(0).get("useExemptFree").toString());
        }
        // 拿到最大免费额度
        String queryKey = serveType == 1 ? PublicEnum.PLATFORM_DEAL_MAX_QUOTA_DP.getRemark() : PublicEnum.PLATFORM_DEAL_MAX_QUOTA_LY.getRemark();
        SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode, queryKey).one();
        BigDecimal totalEd = new BigDecimal(systemParam.getKeyValue());

        // 免费交易金额
        if (useExemptFree.compareTo(totalEd) == -1) {
            // 如果总免费金额小于限制额度，处理多种情况
            // 拿到本次要保存的金额
            BigDecimal addAmount = useExemptFree.add(mr.getRateAmount());
            if (addAmount.compareTo(totalEd) == 1) {
                // 本次算上超过了免费额度
                // 先校验历史是否欠费
                if (totalAmount.compareTo(BigDecimal.ZERO) != 0) {
                    // 审核通过校验历史是否欠费
                    addMCheck(supplier, platformDealFee, totalAmount, arrearage);
                }
                // 需要收费的金额
                BigDecimal yesFree = addAmount.subtract(totalEd);
                addTwoDtlInfoList(mr, supplier, serveType, platformDealFee, 1, yesFree);

                // 保存之后重新统计待缴费金额是否欠费，设置欠费时间
                QueryWrapper<PlatformDealFeeDtl> qc = new QueryWrapper<>();
                qc.eq("enterprise_id", supplier.getEnterpriseId())
                        .eq("serve_type", serveType)
                        .eq("finish_pay_fee", 0)
                        .eq("state", 0)
                        .select("sum(residue_pay_fee) as totalAmount");
                List<Map<String, Object>> mapsc = baseMapper.selectMaps(qc);
                BigDecimal newTotalAmount = null;
                if (mapsc.get(0) == null) {
                    newTotalAmount = new BigDecimal(0);
                } else {
                    newTotalAmount = new BigDecimal(mapsc.get(0).get("totalAmount").toString());
                }
                if (newTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                    // 只要还存在欠费，说明余额扣减了还是欠费，直接设置欠费时间
                    if (platformDealFee.getArrearageDateTime() == null) {
                        platformDealFee.setArrearageDateTime(new Date());
                        platformDealFeeService.update(platformDealFee);
                    }
                }
            } else {
                // 如果本次金额加上还是小于等于免费额度，则直接保存免费交易，
                addTwoDtlInfoList(mr, supplier, serveType, platformDealFee, 0, null);
            }
        } else {
            // 如果以前待缴费金额不为0
            if (totalAmount.compareTo(BigDecimal.ZERO) != 0) {
                // 审核通过校验历史是否欠费
                addMCheck(supplier, platformDealFee, totalAmount, arrearage);
            }
            // 大于等于免费交易额度，这次保存收费
            addTwoDtlInfoList(mr, supplier, serveType, platformDealFee, 1, null);

            // 保存之后重新统计待缴费金额是否欠费，设置欠费时间
            QueryWrapper<PlatformDealFeeDtl> qc = new QueryWrapper<>();
            qc.eq("enterprise_id", supplier.getEnterpriseId())
                    .eq("serve_type", serveType)
                    .eq("finish_pay_fee", 0)
                    .eq("state", 0)
                    .select("sum(residue_pay_fee) as totalAmount");
            List<Map<String, Object>> mapsc = baseMapper.selectMaps(qc);
            BigDecimal newTotalAmount = null;
            if (mapsc.get(0) == null) {
                newTotalAmount = new BigDecimal(0);
            } else {
                newTotalAmount = new BigDecimal(mapsc.get(0).get("totalAmount").toString());
            }
            if (newTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                // 只要还存在欠费，说明余额扣减了还是欠费，直接设置欠费时间
                if (platformDealFee.getArrearageDateTime() == null) {
                    platformDealFee.setArrearageDateTime(new Date());
                    platformDealFeeService.update(platformDealFee);
                }
            }
        }
    }

    @Override
    public List<PlatformDealFeeDtl> getByEnterpriseId(String enterpriseId) {
        QueryWrapper<PlatformDealFeeDtl> qc = new QueryWrapper<>();
        qc.eq("project_enterprise_id", enterpriseId).or().eq("enterprise_id", enterpriseId);
        List<PlatformDealFeeDtl> platformDealFeeDtls = baseMapper.selectList(qc);
        return platformDealFeeDtls;
    }

    /**
     * 二级供应商-审核通过保存缴费信息
     * freem3
     *
     * @param mr
     * @param supplier
     * @param serveType
     * @param platformDealFee
     * @param isOE            是否超过额度
     * @param yesFree         不等于null说明有收费部分
     */
    private void addTwoDtlInfoList(SupplierReconciliation mr, EnterpriseInfo supplier, Integer serveType, PlatformDealFee platformDealFee, Integer isOE, BigDecimal yesFree) {

        PlatformDealFeeDtl dtl = new PlatformDealFeeDtl();
        dtl.setPlatformDealFeeId(platformDealFee.getPlatformDealFeeId());
        dtl.setRelevanceId(mr.getRelevanceId());
        dtl.setRelevanceNu(mr.getRelevanceSn());
        dtl.setRelevanceType(1);
        dtl.setServeType(serveType);
        EnterpriseInfo pOrg = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, mr.getSupplierOrgId())
                .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getEnterpriseName).one();
        if (pOrg == null) throw new BusinessException("未查询到采购单位！");
        dtl.setProjectEnterpriseId(pOrg.getEnterpriseId());
        dtl.setProjectEnterpriseName(pOrg.getEnterpriseName());
        dtl.setEnterpriseId(supplier.getEnterpriseId());
        dtl.setEnterpriseName(supplier.getEnterpriseName());
        // 使用含税金额作为对账金额，与MaterialReconciliation的处理方式保持一致
        BigDecimal reconciliationAmount = mr.getRateAmount();
        dtl.setDealAmount(reconciliationAmount);
        SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode,
                PublicEnum.PLATFORM_SHOP_FEE_DEAL_RATIO.getRemark()).one();
        BigDecimal ratio = new BigDecimal(systemParam.getKeyValue());
        dtl.setFeeRatio(ratio);
        BigDecimal divRatio = ratio.divide(new BigDecimal(1000), new MathContext(18)).setScale(6, BigDecimal.ROUND_HALF_UP);
        BigDecimal mFee = null;
        if (yesFree == null) {
            mFee = reconciliationAmount.multiply(divRatio).setScale(2, BigDecimal.ROUND_HALF_UP);
            dtl.setExceedFree(reconciliationAmount);
        } else {
            // 使用需要缴费金额作为收费
            mFee = yesFree.multiply(divRatio).setScale(2, BigDecimal.ROUND_HALF_UP);
            dtl.setExceedFree(yesFree);
            dtl.setUseExemptFree(reconciliationAmount.subtract(yesFree));
        }
        dtl.setServeFee(mFee);
        dtl.setPayFee(new BigDecimal(0));
        dtl.setResiduePayFee(mFee);
        dtl.setFinishPayFee(0);
        SystemParam systemParam2 = systemParamService.lambdaQuery().eq(SystemParam::getCode,
                PublicEnum.PLATFORM_DEAL_MAX_QUOTA_DP.getRemark()).one();
        BigDecimal notFeeMaxAmount = new BigDecimal(systemParam2.getKeyValue());
        dtl.setDealFeeAmount(notFeeMaxAmount);
        dtl.setUseBalance(new BigDecimal(0));
        dtl.setDealFeeQuota(supplier.getArrearage());
        dtl.setFeeEndDateNum(supplier.getArrearageDateNum());
        dtl.setFeeEndDateType(supplier.getArrearageDateType());
        if (isOE == 0) { // 没有超过额度
            dtl.setUseExemptFree(reconciliationAmount);
            BigDecimal bo = new BigDecimal(0);
            dtl.setPayFee(bo);
            dtl.setResiduePayFee(bo);
            dtl.setFinishPayFee(1);
            dtl.setPayType(2);
            dtl.setServeFee(bo);
            dtl.setExceedFree(bo);
            save(dtl);
        } else {
            // 处理如果存在余额明细抵消明细
            BigDecimal remainingSum = platformDealFee.getRemainingSum();
            // 如果不存在余额，直接保存即可
            if (remainingSum.compareTo(BigDecimal.ZERO) == 0) {
                save(dtl);
            } else {
                // 如果存在余额开始消耗
                PlatformBalanceOperate pbo = new PlatformBalanceOperate();
                pbo.setTitle("审核对账单使用余额自动缴费交易");
                pbo.setRelevanceId(platformDealFee.getPlatformDealFeeId());
                pbo.setRelevanceType(1);
                pbo.setBeforeAmount(platformDealFee.getRemainingSum());
                // 余额大于服务费用
                if (remainingSum.compareTo(mFee) == 1) {
                    pbo.setAmount(mFee.negate());
                    platformDealFee.setRemainingSum(platformDealFee.getRemainingSum().subtract(mFee));
                    dtl.setPayFee(mFee);
                    dtl.setResiduePayFee(new BigDecimal(0)); // 剩余缴费金额
                    dtl.setFinishPayFee(1); // 是否完成缴费
                    dtl.setUseBalance(mFee); // 使用余额金额
                } else if (remainingSum.compareTo(mFee) == 0) {
                    pbo.setAmount(mFee.negate());
                    platformDealFee.setRemainingSum(platformDealFee.getRemainingSum().subtract(mFee));
                    dtl.setPayFee(mFee);
                    dtl.setResiduePayFee(new BigDecimal(0)); // 剩余缴费金额
                    dtl.setFinishPayFee(1); // 是否完成缴费
                    dtl.setUseBalance(mFee); // 使用余额金额
                } else {
                    pbo.setAmount(platformDealFee.getRemainingSum().negate());
                    // 余额小于需缴费金额
                    platformDealFee.setRemainingSum(new BigDecimal(0));
                    dtl.setPayFee(remainingSum);
                    dtl.setResiduePayFee(mFee.subtract(remainingSum)); // 剩余缴费金额
                    dtl.setFinishPayFee(0); // 是否完成缴费
                    dtl.setUseBalance(remainingSum); // 使用余额金额
                }
                platformDealFeeService.update(platformDealFee);
                save(dtl);
                pbo.setAfterAmount(platformDealFee.getRemainingSum());
                pbo.setOperateId(dtl.getPlatformDealFeeDtlId());
                pbo.setOperateUn(dtl.getRelevanceNu());
                pbo.setOperateType(2);
                platformBalanceOperateService.save(pbo);
            }
        }
    }

    /**
     * 校验是否欠费，返回布尔，用于本次交易后设置欠费时间
     * freem6
     * @param supplier
     * @param platformDealFee
     * @param thisFree
     * @return
     */
    private static boolean checkOutTime(EnterpriseInfo supplier, PlatformDealFee platformDealFee, BigDecimal thisFree) {
        if (thisFree.compareTo(BigDecimal.ZERO) == 0) {
            return false;
        }
        BigDecimal arrearage = supplier.getArrearage();
        boolean flag = false;
        // 判断他审核的本次的金额会不会导致欠费，只是为了处理欠费时间
        if (arrearage.compareTo(BigDecimal.ZERO) == 0) {
            flag = true;
        } else {
            if (thisFree.compareTo(arrearage) == 1) {
                flag = true;
            } else {
                if (platformDealFee.getArrearageDateTime() == null) {
                    return true;
                }
                // 未超过欠费额度
                Integer arrearageDateNum = supplier.getArrearageDateNum();
                Integer arrearageDateType = supplier.getArrearageDateType();
                if (arrearageDateNum != null && platformDealFee.getArrearageDateTime() != null) {
                    // 说明设置了欠费时间，并且已经欠费
                    Date arrearageDateTime = platformDealFee.getArrearageDateTime();
                    int day = 0;
                    if (arrearageDateType == 1) {
                        day = arrearageDateNum;
                    } else if (arrearageDateType == 2) {
                        day = arrearageDateNum * 30;
                    } else if (arrearageDateType == 3) {
                        day = arrearageDateNum * 365;
                    } else {
                        throw new BusinessException("欠费时长时间类型错误！");
                    }
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(arrearageDateTime);
                    calendar.add(Calendar.DATE, day);
                    Date newDate = calendar.getTime();

                    Date currentDate = new Date();  // 获取当前日期
                    if (currentDate.after(newDate)) { // 当前时间在欠费截止时间之后
                        flag = true;
                    } else if (currentDate.before(newDate)) {
                    } else {
                        flag = true;
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 缴费是否欠费直接抛出异常
     * freem5
     * @param supplier
     * @param platformDealFee
     * @param totalAmount
     * @param arrearage
     */
    private static void addMCheck(EnterpriseInfo supplier, PlatformDealFee platformDealFee, BigDecimal totalAmount, BigDecimal arrearage) {
        // 说明存在带教费
        if (arrearage.compareTo(BigDecimal.ZERO) == 0) {
            throw new BusinessException("供应商存在待缴费交易服务费！已停止服务。");
        } else {
            if (totalAmount.compareTo(arrearage) == 1) {
                throw new BusinessException("供应商已超过可欠费交易服务费额度！已停止服务。");
            } else { // 未超过欠费额度
                Integer arrearageDateNum = supplier.getArrearageDateNum();
                Integer arrearageDateType = supplier.getArrearageDateType();
                if (arrearageDateNum != null && platformDealFee.getArrearageDateTime() != null) {
                    // 说明设置了欠费时间，并且已经欠费
                    Date arrearageDateTime = platformDealFee.getArrearageDateTime();
                    int day = 0;
                    if (arrearageDateType == 1) {
                        day = arrearageDateNum;
                    } else if (arrearageDateType == 2) {
                        day = arrearageDateNum * 30;
                    } else if (arrearageDateType == 3) {
                        day = arrearageDateNum * 365;
                    } else {
                        throw new BusinessException("欠费时长时间类型错误！");
                    }
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(arrearageDateTime);
                    calendar.add(Calendar.DATE, day);
                    Date newDate = calendar.getTime();

                    Date currentDate = new Date();  // 获取当前日期
                    if (currentDate.after(newDate)) { // 当前时间在欠费截止时间之后
                        throw new BusinessException("供应商已超过可欠费交易额度截止时间！已停止服务。");
                    } else if (currentDate.before(newDate)) {
                    } else {
                        throw new BusinessException("供应商已超过可欠费交易额度截止时间！已停止服务。");
                    }
                }
            }
        }
    }

    /**
     * 审核通过保存缴费信息
     * freem3
     * @param mr
     * @param supplier
     * @param serveType
     * @param platformDealFee
     * @param isOE            是否超过额度
     * @param yesFree         不等于null说明有收费部分
     */
    private void addDtlInfoList(MaterialReconciliation mr, EnterpriseInfo supplier, Integer serveType, PlatformDealFee platformDealFee, Integer isOE, BigDecimal yesFree) {
        PlatformDealFeeDtl dtl = new PlatformDealFeeDtl();
        dtl.setPlatformDealFeeId(platformDealFee.getPlatformDealFeeId());
        dtl.setRelevanceId(mr.getReconciliationId());
        dtl.setRelevanceNu(mr.getReconciliationNo());
        dtl.setRelevanceType(1);
        dtl.setServeType(serveType);
        EnterpriseInfo pOrg = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getInteriorId, mr.getPurchasingOrgId())
                .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getEnterpriseName).one();
        if (pOrg == null) throw new BusinessException("未查询到采购单位！");
        dtl.setProjectEnterpriseId(pOrg.getEnterpriseId());
        dtl.setProjectEnterpriseName(pOrg.getEnterpriseName());
        dtl.setEnterpriseId(supplier.getEnterpriseId());
        dtl.setEnterpriseName(supplier.getEnterpriseName());
        BigDecimal reconciliationAmount = mr.getReconciliationAmount();
        dtl.setDealAmount(reconciliationAmount);
        SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode,
                PublicEnum.PLATFORM_SHOP_FEE_DEAL_RATIO.getRemark()).one();
        BigDecimal ratio = new BigDecimal(systemParam.getKeyValue());
        dtl.setFeeRatio(ratio);//平台店铺交易费收取比例（单位%）
        BigDecimal divRatio = ratio.divide(new BigDecimal(1000), new MathContext(18)).setScale(6, BigDecimal.ROUND_HALF_UP);
        BigDecimal mFee = null;
        if (yesFree == null) {
            mFee = reconciliationAmount.multiply(divRatio).setScale(2, BigDecimal.ROUND_HALF_UP);
            dtl.setExceedFree(reconciliationAmount);
        } else {
            // 使用需要缴费金额作为收费
            mFee = yesFree.multiply(divRatio).setScale(2, BigDecimal.ROUND_HALF_UP);
            dtl.setExceedFree(yesFree);
            dtl.setUseExemptFree(reconciliationAmount.subtract(yesFree));
        }
        dtl.setServeFee(mFee);
        dtl.setPayFee(new BigDecimal(0));
        dtl.setResiduePayFee(mFee);
        dtl.setFinishPayFee(0);
        if (isOE == 0) {
            dtl.setUseExemptFree(reconciliationAmount);
            BigDecimal bo = new BigDecimal(0);
            dtl.setPayFee(bo);
            dtl.setResiduePayFee(bo);
            dtl.setFinishPayFee(1);
            dtl.setPayType(2);
            dtl.setServeFee(bo);
            dtl.setExceedFree(bo);
        }
        String keyStr2 = null;
        if (serveType == 1) {
            keyStr2 = PublicEnum.PLATFORM_DEAL_MAX_QUOTA_DP.getRemark();
        } else {
            keyStr2 = PublicEnum.PLATFORM_DEAL_MAX_QUOTA_LY.getRemark();
        }
        SystemParam systemParam2 = systemParamService.lambdaQuery().eq(SystemParam::getCode, keyStr2).one();
        BigDecimal notFeeMaxAmount = new BigDecimal(systemParam2.getKeyValue());
        dtl.setDealFeeAmount(notFeeMaxAmount);
        dtl.setUseBalance(new BigDecimal(0));
        dtl.setDealFeeQuota(supplier.getArrearage());
        dtl.setFeeEndDateNum(supplier.getArrearageDateNum());
        dtl.setFeeEndDateType(supplier.getArrearageDateType());

        if (isOE == 0) {
            save(dtl);
        } else {
            // 处理如果存在余额明细抵消明细
            BigDecimal remainingSum = platformDealFee.getRemainingSum();
            // 如果不存在余额，直接保存即可
            if (remainingSum.compareTo(BigDecimal.ZERO) == 0) {
                save(dtl);
            } else {
                // 如果存在余额开始消耗
                PlatformBalanceOperate pbo = new PlatformBalanceOperate();
                pbo.setTitle("审核对账单使用余额自动缴费交易");
                pbo.setRelevanceId(platformDealFee.getPlatformDealFeeId());
                pbo.setRelevanceType(1);
                pbo.setBeforeAmount(platformDealFee.getRemainingSum());
                // 余额大于服务费用
                if (remainingSum.compareTo(mFee) == 1) {
                    pbo.setAmount(mFee.negate());
                    platformDealFee.setRemainingSum(platformDealFee.getRemainingSum().subtract(mFee));
                    dtl.setPayFee(mFee);
                    dtl.setResiduePayFee(new BigDecimal(0)); // 剩余缴费金额
                    dtl.setFinishPayFee(1); // 是否完成缴费
                    dtl.setUseBalance(mFee); // 使用余额金额
                } else if (remainingSum.compareTo(mFee) == 0) {
                    pbo.setAmount(mFee.negate());
                    platformDealFee.setRemainingSum(platformDealFee.getRemainingSum().subtract(mFee));
                    dtl.setPayFee(mFee);
                    dtl.setResiduePayFee(new BigDecimal(0)); // 剩余缴费金额
                    dtl.setFinishPayFee(1); // 是否完成缴费
                    dtl.setUseBalance(mFee); // 使用余额金额
                } else {
                    pbo.setAmount(platformDealFee.getRemainingSum().negate());
                    // 余额小于需缴费金额
                    platformDealFee.setRemainingSum(new BigDecimal(0));
                    dtl.setPayFee(remainingSum);
                    dtl.setResiduePayFee(mFee.subtract(remainingSum)); // 剩余缴费金额
                    dtl.setFinishPayFee(0); // 是否完成缴费
                    dtl.setUseBalance(remainingSum); // 使用余额金额
                }
                platformDealFeeService.update(platformDealFee);
                save(dtl);
                pbo.setAfterAmount(platformDealFee.getRemainingSum());
                pbo.setOperateId(dtl.getPlatformDealFeeDtlId());
                pbo.setOperateUn(dtl.getRelevanceNu());
                pbo.setOperateType(2);
                platformBalanceOperateService.save(pbo);
            }
        }
    }



    /**
     * 保存交易明细（审核对账单时收取） 废弃
     *
     * @param mr
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDealFeeDtl(MaterialReconciliation mr) {
//        EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseName, mr.getSupplierName())
//                .select(EnterpriseInfo::getEnterpriseId,
//                        EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getArrearage, EnterpriseInfo::getArrearageDateNum,
//                        EnterpriseInfo::getArrearageDateType,
//                        EnterpriseInfo::getShuDaoFlag, EnterpriseInfo::getIsNoSupplierAudit, EnterpriseInfo::getInteriorId).one();
//        if (supplier == null) {
//            throw new BusinessException("未查询到供应商！");
//        }
//        // 如果是蜀道/内部企业无需校验
//        if (supplier.getShuDaoFlag() == 1 || supplier.getInteriorId() != null) {
//            return;
//        }
//        Integer serveType = null;
//        if (mr.getBusinessType() == 1) { // 合同
//            serveType = 2;
//        } else {
//            serveType = 1;
//        }
//        PlatformDealFee platformDealFee = platformDealFeeService.lambdaQuery()
//                .eq(PlatformDealFee::getEnterpriseId, supplier.getEnterpriseId())
//                .eq(PlatformDealFee::getServeType, serveType)
//                .one();
//        if (platformDealFee == null) {
//            throw new BusinessException("未查询到平台交易主数据！");
//        }
//
//        PlatformDealFeeDtl dtl = new PlatformDealFeeDtl();
//        dtl.setPlatformDealFeeId(platformDealFee.getPlatformDealFeeId());
//        dtl.setRelevanceId(mr.getReconciliationId());
//        dtl.setRelevanceNu(mr.getReconciliationNo());
//        dtl.setRelevanceType(1);
//        dtl.setServeType(serveType);
//        EnterpriseInfo pOrg = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getInteriorId, mr.getPurchasingOrgId())
//                .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getEnterpriseName).one();
//        if (pOrg == null) throw new BusinessException("未查询到采购单位！");
//        dtl.setProjectEnterpriseId(pOrg.getEnterpriseId());
//        dtl.setProjectEnterpriseName(pOrg.getEnterpriseName());
//        dtl.setEnterpriseId(supplier.getEnterpriseId());
//        dtl.setEnterpriseName(supplier.getEnterpriseName());
//        BigDecimal reconciliationAmount = mr.getReconciliationAmount();
//        dtl.setDealAmount(reconciliationAmount);
//        String keyStr1 = null;
//        if (serveType == 1) {
//            keyStr1 = PublicEnum.PLATFORM_SHOP_FEE_DEAL_RATIO.getRemark();
//        } else {
//            keyStr1 = PublicEnum.PLATFORM_CONTRACT_FEE_DEAL_RATIO.getRemark();
//        }
//        SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode, keyStr1).one();
//        String keyValue = systemParam.getKeyValue();
//        BigDecimal ratio = new BigDecimal(keyValue);
//        dtl.setFeeRatio(ratio);
//        BigDecimal divRatio = ratio.divide(new BigDecimal(100), new MathContext(18)).setScale(4, BigDecimal.ROUND_HALF_UP);
//        BigDecimal mFee = reconciliationAmount.multiply(divRatio).setScale(2, BigDecimal.ROUND_HALF_UP);
//        dtl.setServeFee(mFee);
//        dtl.setPayFee(new BigDecimal(0));
//        dtl.setResiduePayFee(mFee);
//        dtl.setFinishPayFee(0);
//        String keyStr2 = null;
//        if (serveType == 1) {
//            keyStr2 = PublicEnum.PLATFORM_DEAL_MAX_QUOTA_DP.getRemark();
//        } else {
//            keyStr2 = PublicEnum.PLATFORM_DEAL_MAX_QUOTA_LY.getRemark();
//        }
//        SystemParam systemParam2 = systemParamService.lambdaQuery().eq(SystemParam::getCode, keyStr2).one();
//        BigDecimal notFeeMaxAmount = new BigDecimal(systemParam2.getKeyValue());
//        dtl.setDealFeeAmount(notFeeMaxAmount);
//        dtl.setUseBalance(new BigDecimal(0));
//        dtl.setDealFeeQuota(supplier.getArrearage());
//        dtl.setFeeEndDateNum(supplier.getArrearageDateNum());
//        dtl.setFeeEndDateType(supplier.getArrearageDateType());
//
//        // 处理如果存在余额明细抵消明细
//        BigDecimal remainingSum = platformDealFee.getRemainingSum();
//        // 如果不存在余额，直接保存即可
//        if (remainingSum.compareTo(BigDecimal.ZERO) == 0) {
//            save(dtl);
//        } else {
//            // 如果存在余额开始消耗
//
//            PlatformBalanceOperate pbo = new PlatformBalanceOperate();
//            pbo.setTitle("审核对账单使用余额自动缴费交易");
//            pbo.setRelevanceId(platformDealFee.getPlatformDealFeeId());
//            pbo.setRelevanceType(1);
//            pbo.setBeforeAmount(platformDealFee.getRemainingSum());
//            // 余额大于服务费用
//            if (remainingSum.compareTo(mFee) == 1) {
//                pbo.setAmount(mFee.negate());
//                platformDealFee.setRemainingSum(platformDealFee.getRemainingSum().subtract(mFee));
//                dtl.setPayFee(mFee);
//                dtl.setResiduePayFee(new BigDecimal(0)); // 剩余缴费金额
//                dtl.setFinishPayFee(1); // 是否完成缴费
//                dtl.setUseBalance(mFee); // 使用余额金额
//            } else if (remainingSum.compareTo(mFee) == 0) {
//                pbo.setAmount(mFee.negate());
//                platformDealFee.setRemainingSum(platformDealFee.getRemainingSum().subtract(mFee));
//                dtl.setPayFee(mFee);
//                dtl.setResiduePayFee(new BigDecimal(0)); // 剩余缴费金额
//                dtl.setFinishPayFee(1); // 是否完成缴费
//                dtl.setUseBalance(mFee); // 使用余额金额
//            } else {
//                pbo.setAmount(platformDealFee.getRemainingSum().negate());
//                // 余额小于需缴费金额
//                platformDealFee.setRemainingSum(new BigDecimal(0));
//                dtl.setPayFee(remainingSum);
//                dtl.setResiduePayFee(mFee.subtract(remainingSum)); // 剩余缴费金额
//                dtl.setFinishPayFee(0); // 是否完成缴费
//                dtl.setUseBalance(remainingSum); // 使用余额金额
//            }
//            platformDealFeeService.update(platformDealFee);
//            save(dtl);
//            pbo.setAfterAmount(platformDealFee.getRemainingSum());
//            pbo.setOperateId(dtl.getPlatformDealFeeDtlId());
//            pbo.setOperateUn(dtl.getRelevanceNu());
//            pbo.setOperateType(2);
//            platformBalanceOperateService.save(pbo);
//        }
    }

    @Override
    public PageUtils<PlatformDealFeeDtl> ListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeDtl> q) {
        String platformDealFeeId = (String) jsonObject.get("platformDealFeeId");
        String relevanceNu = (String) jsonObject.get("relevanceNu");
        String enterpriseId = (String) jsonObject.get("enterpriseId");
        Integer serveType = (Integer) jsonObject.get("serveType");
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        // 是否查询没有作废的
        Integer isNotAbandon = (Integer) jsonObject.get("isNotAbandon");
        String projectEnterpriseName = (String) jsonObject.get("projectEnterpriseName");
        String enterpriseName = (String) jsonObject.get("enterpriseName");
        ArrayList ids = (ArrayList) jsonObject.get("ids");
        String periodStartDate = (String) jsonObject.get("periodStartDate");
        String periodEndDate = (String) jsonObject.get("periodEndDate");
        if (!CollectionUtils.isEmpty(ids)) {
            q.in(PlatformDealFeeDtl::getPlatformDealFeeDtlId, ids);
        }
        if (isNotAbandon != null) {
            if (isNotAbandon == 1) {
                q.eq(PlatformDealFeeDtl::getState, 0);
            }
        }

        Integer freeQueryType = (Integer) jsonObject.get("freeQueryType");
        if (freeQueryType != null) {
            if (freeQueryType == 3) {
                q.eq(PlatformDealFeeDtl::getServeType, 1);
            } else if (freeQueryType == 4) {
                q.eq(PlatformDealFeeDtl::getServeType, 2);
            }
        }
        // 是否欠费
        Integer isArrearage = (Integer) jsonObject.get("isArrearage");
        // 是否超过免费额度
        Integer isOutFree = (Integer) jsonObject.get("isOutFree");
        if (isArrearage != null) {
            if (isArrearage == 1) {
                q.gt(PlatformDealFeeDtl::getResiduePayFee, 0);
            }
            if (isArrearage == 0) {
                q.eq(PlatformDealFeeDtl::getResiduePayFee, 0);
            }
        }
        if (isOutFree != null) {
            if (isOutFree == 1) {
                q.gt(PlatformDealFeeDtl::getExceedFree, 0);
            }
            if (isOutFree == 0) {
                q.eq(PlatformDealFeeDtl::getExceedFree, 0);
            }
        }

        q.eq(PlatformDealFeeDtl::getPayFeeState, 0); // 不包括对账单已作废的数据
        if (StringUtils.isNotBlank(projectEnterpriseName)) {
            q.like(PlatformDealFeeDtl::getProjectEnterpriseName, projectEnterpriseName);
        }
        if (StringUtils.isNotBlank(enterpriseName)) {
            q.like(PlatformDealFeeDtl::getEnterpriseName, enterpriseName);
        }
        if (StringUtils.isNotBlank(platformDealFeeId)) {
            q.like(PlatformDealFeeDtl::getPlatformDealFeeId, platformDealFeeId);
        }
        if (StringUtils.isNotBlank(relevanceNu)) {
            q.like(PlatformDealFeeDtl::getPlatformDealFeeId, relevanceNu);
        }
        if (StringUtils.isNotBlank(enterpriseId)) {
            q.eq(PlatformDealFeeDtl::getEnterpriseId, enterpriseId);
        }
        String keywords = (String) jsonObject.get("keywords");
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(PlatformDealFeeDtl::getEnterpriseName, keywords)
                        .or()
                        .like(PlatformDealFeeDtl::getRelevanceNu, keywords);
            });
        }

        // 添加periodStartDate和periodEndDate查询条件
        if (StringUtils.isNotBlank(periodStartDate)) {
            q.ge(PlatformDealFeeDtl::getGmtCreate, periodStartDate);
        }
        if (StringUtils.isNotBlank(periodEndDate)) {
            q.le(PlatformDealFeeDtl::getGmtCreate, periodEndDate);
        }

        q.eq(serveType != null, PlatformDealFeeDtl::getServeType, serveType);
        if (orderBy != null) {
            q.orderByDesc(orderBy == 1, PlatformDealFeeDtl::getGmtCreate);
            q.orderByDesc(orderBy == 2, PlatformDealFeeDtl::getGmtModified);
        } else {
            q.orderByDesc(PlatformDealFeeDtl::getGmtModified);
        }
        IPage<PlatformDealFeeDtl> page = this.page(
                new Query<PlatformDealFeeDtl>().getPage(jsonObject),
                q
        );
        if (freeQueryType != null && (freeQueryType == 3 || freeQueryType == 4)) {
            int size = page.getRecords().size();
            if (size > 0) {
                PlatformDealFeeDtl platformDealFeeDtl = page.getRecords().get(0);
                if (freeQueryType == 3) {
                    freeQueryType = 1;
                }
                if (freeQueryType == 4) {
                    freeQueryType = 2;
                }
                // 总服务费（不包含作废）
                QueryWrapper<PlatformDealFeeDtl> q3 = new QueryWrapper<>();
                q3.eq("serve_type", freeQueryType)
                        .eq("pay_type", 1)
                        .eq("state", 0)
                        .select("sum(serve_fee) as dealAmount");
                Map<String, Object> map3 = platformDealFeeDtlMapper.selectMaps(q3).get(0);
                BigDecimal dealAmount = new BigDecimal(0);
                if (map3 != null) {
                    dealAmount = new BigDecimal(map3.get("dealAmount").toString());
                }
                platformDealFeeDtl.setTotalFree(dealAmount);

                // 待缴费金额（不包含作废的）
                QueryWrapper<PlatformDealFeeDtl> q1 = new QueryWrapper<>();
                q1.eq("serve_type", freeQueryType)
                        .eq("finish_pay_fee", 0)
                        .eq("state", 0) // 排除作废
                        .select("sum(residue_pay_fee) as residuePayFee");
                Map<String, Object> map1 = platformDealFeeDtlMapper.selectMaps(q1).get(0);
                BigDecimal residuePayFee = new BigDecimal(0);
                if (map1 != null && map1.get("residuePayFee") != null) {
                    residuePayFee = new BigDecimal(map1.get("residuePayFee").toString());
                }
                platformDealFeeDtl.setPaymentAmount(residuePayFee);
            }
        }
        return new PageUtils<PlatformDealFeeDtl>(page);
    }

    /**
     * 对账单作废，作废交易明细
     * freem2
     *
     * @param mr
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealFeeCancellation(MaterialReconciliation mr) {
        EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseName, mr.getSupplierName())
                .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getEnterpriseName,
                        EnterpriseInfo::getArrearage, EnterpriseInfo::getArrearageDateNum, EnterpriseInfo::getArrearageDateType,
                        EnterpriseInfo::getShuDaoFlag, EnterpriseInfo::getIsNoSupplierAudit,
                        EnterpriseInfo::getInteriorId).one();
        if (supplier == null) {
            throw new BusinessException("未查询到供应商！");
        }
        // 如果是蜀道/内部企业无需校验
        if (supplier.getShuDaoFlag() == 1 || supplier.getInteriorId() != null) {
            return;
        }
        Integer serveType = null;
        if (mr.getBusinessType() == 1) { // 合同
            serveType = 2;
        } else {
            serveType = 1;
        }
        String reconciliationId = mr.getReconciliationId();
        PlatformDealFeeDtl platformDealFeeDtl = platformDealFeeDtlService.lambdaQuery().eq(PlatformDealFeeDtl::getRelevanceId, reconciliationId)
                .eq(PlatformDealFeeDtl::getRelevanceType, 1)
                .eq(PlatformDealFeeDtl::getPayType, 1) // 只是正常
                .eq(PlatformDealFeeDtl::getServeType, serveType)
                .one();
        if (platformDealFeeDtl == null) {
            platformDealFeeDtl = platformDealFeeDtlService.lambdaQuery().eq(PlatformDealFeeDtl::getRelevanceId, reconciliationId)
                    .eq(PlatformDealFeeDtl::getRelevanceType, 1)
                    .eq(PlatformDealFeeDtl::getPayType, 2)
                    .eq(PlatformDealFeeDtl::getServeType, serveType)
                    .one();
            if (platformDealFeeDtl == null) {
                throw new BusinessException("未查询到该笔交易明细数据！");
            }
        }
        // 如果存在缴费金额，直接退回余额
        if (platformDealFeeDtl.getPayFee().compareTo(BigDecimal.ZERO) == 1 && platformDealFeeDtl.getPayType() == 1) {
            PlatformDealFee platformDealFee = platformDealFeeService.lambdaQuery()
                    .eq(PlatformDealFee::getEnterpriseId, supplier.getEnterpriseId())
                    .eq(PlatformDealFee::getServeType, serveType)
                    .one();
            PlatformBalanceOperate pbo = new PlatformBalanceOperate();
            pbo.setTitle("对账单作废审核将已缴费交易金额退回余额");
            pbo.setRelevanceId(platformDealFee.getPlatformDealFeeId());
            pbo.setRelevanceType(1);
            pbo.setAmount(platformDealFeeDtl.getPayFee());
            pbo.setBeforeAmount(platformDealFee.getRemainingSum());
            pbo.setAfterAmount(platformDealFee.getRemainingSum().add(platformDealFeeDtl.getPayFee()));
            pbo.setOperateId(platformDealFeeDtl.getPlatformDealFeeDtlId());
            pbo.setOperateUn(platformDealFeeDtl.getRelevanceNu());
            pbo.setOperateType(2);
            pbo.setGmtCreate(new Date());
            platformBalanceOperateService.save(pbo);
            // 待缴费数据为空，直接放入余额
            platformDealFee.setRemainingSum(platformDealFee.getRemainingSum().add(platformDealFeeDtl.getPayFee()));
            platformDealFeeService.update(platformDealFee);
        }
        platformDealFeeDtl.setState(1);
        update(platformDealFeeDtl);


        if (platformDealFeeDtl.getPayType() == 1) {
            // 对账单作废自动缴费
            zfUseAmount(mr, supplier, serveType);
            // 如果没有欠费设置为null
            checkIsFree(supplier, serveType);
        }
    }

    /**
     * 新的作废逻辑：变更交易记录为作废状态，新增抵消记录。
     *
     * @param reconciliation 对账单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addNegativeFeeCancellation(MaterialReconciliation reconciliation) {
        EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseName, reconciliation.getSupplierName())
                .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getEnterpriseName,
                        EnterpriseInfo::getArrearage, EnterpriseInfo::getArrearageDateNum, EnterpriseInfo::getArrearageDateType,
                        EnterpriseInfo::getShuDaoFlag, EnterpriseInfo::getIsNoSupplierAudit,
                        EnterpriseInfo::getInteriorId).one();
        if (supplier == null) {
            throw new BusinessException("未查询到供应商！");
        }
        // 如果是蜀道/内部企业无需校验
        if (supplier.getShuDaoFlag() == 1 || supplier.getInteriorId() != null) {
            return;
        }
        Integer serveType = null;
        if (reconciliation.getBusinessType() == 1) { // 合同
            serveType = 2;
        } else {
            serveType = 1;
        }
        String reconciliationId = reconciliation.getReconciliationId();
        PlatformDealFeeDtl platformDealFeeDtl = platformDealFeeDtlService.lambdaQuery().eq(PlatformDealFeeDtl::getRelevanceId, reconciliationId)
                .eq(PlatformDealFeeDtl::getRelevanceType, 1)
                .eq(PlatformDealFeeDtl::getPayType, 1) // 只是正常
                .eq(PlatformDealFeeDtl::getServeType, serveType)
                .one();
        if (platformDealFeeDtl == null) {
            platformDealFeeDtl = platformDealFeeDtlService.lambdaQuery().eq(PlatformDealFeeDtl::getRelevanceId, reconciliationId)
                    .eq(PlatformDealFeeDtl::getRelevanceType, 1)
                    .eq(PlatformDealFeeDtl::getPayType, 2)
                    .eq(PlatformDealFeeDtl::getServeType, serveType)
                    .one();
            if (platformDealFeeDtl == null) {
                throw new BusinessException("未查询到该笔交易明细数据！");
            }
        }
        // 更新主表记录（余额）
        PlatformDealFee platformDealFee = platformDealFeeService.lambdaQuery()
                .eq(PlatformDealFee::getEnterpriseId, supplier.getEnterpriseId())
                .eq(PlatformDealFee::getServeType, serveType)
                .one();
        platformDealFee.setRemainingSum(platformDealFee.getRemainingSum().add(platformDealFeeDtl.getPayFee()));
        // 原交易明细变更状态
        platformDealFeeDtl.setState(1);
        update(platformDealFeeDtl);
        // 再构建一条反记录
        PlatformDealFeeDtl negatedDealFee = new PlatformDealFeeDtl();
        BeanUtils.copyProperties(platformDealFeeDtl, negatedDealFee);
        negatedDealFee.setPrePlatformDealFeeDtlId(platformDealFeeDtl.getPlatformDealFeeDtlId());
        negatedDealFee.setPlatformDealFeeDtlId(null);
        // 处理费用金额
        negatedDealFee.setState(1);
        // 已缴费金额
        negatedDealFee.setPayFee(negatedDealFee.getPayFee().negate());
        // 剩余缴费金额
        negatedDealFee.setResiduePayFee(negatedDealFee.getResiduePayFee().negate());
        negatedDealFee.setDealAmount(negatedDealFee.getDealFeeAmount().negate());
        if (negatedDealFee.getPayType() == 1) {
            // 正常缴费
            // 服务费重置
            negatedDealFee.setServeFee(negatedDealFee.getServeFee().negate());
            //negatedDealFee.setResiduePayFee(negatedDealFee.getResiduePayFee().negate());
            //negatedDealFee.setExceedFree(negatedDealFee.getExceedFree().negate());
            //negatedDealFee.setPayFee(negatedDealFee.getPayFee().negate());
        } else {
            // 占用的免费额度
            negatedDealFee.setUseExemptFree(negatedDealFee.getUseExemptFree() != null ? negatedDealFee.getUseExemptFree().negate() : new BigDecimal(0));
        }

        // 保存反记录到数据库
        save(negatedDealFee);
    }

    /**
     * 新的作废逻辑：变更交易记录为作废状态，新增抵消记录。
     * @param reconciliation 对账单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTwoNegativeFeeCancellation(SupplierReconciliation reconciliation) {

        EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseName, reconciliation.getTwoSupplierName())
                .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getEnterpriseName,
                        EnterpriseInfo::getArrearage, EnterpriseInfo::getArrearageDateNum, EnterpriseInfo::getArrearageDateType,
                        EnterpriseInfo::getShuDaoFlag, EnterpriseInfo::getIsNoSupplierAudit,
                        EnterpriseInfo::getInteriorId).one();
        if (supplier == null) {
            throw new BusinessException("未查询到二级供应商！");
        }
        //自营店铺
        ShopBusiness shopBusiness = shopBusinessService.lambdaQuery().eq(ShopBusiness::getState,1).one();
        // 自营店铺无需校验
        if (shopBusiness != null && supplier.getEnterpriseId().equals(shopBusiness.getEnterpriseId())) {
            return;
        }
        Integer serveType = 1;//交易服务费
        String relevanceId = reconciliation.getRelevanceId();
        PlatformDealFeeDtl platformDealFeeDtl = platformDealFeeDtlService.lambdaQuery().eq(PlatformDealFeeDtl::getRelevanceId, relevanceId)
                .eq(PlatformDealFeeDtl::getRelevanceType, 1)
                .one();
        if (platformDealFeeDtl == null) {
            throw new BusinessException("未查询到该笔交易明细数据！");
        }
        // 更新主表记录（余额）
        PlatformDealFee platformDealFee = platformDealFeeService.lambdaQuery()
                .eq(PlatformDealFee::getEnterpriseId, supplier.getEnterpriseId())
                .eq(PlatformDealFee::getServeType, serveType)
                .one();
        platformDealFee.setRemainingSum(platformDealFee.getRemainingSum().add(platformDealFeeDtl.getPayFee()));
        // 原交易明细变更状态
        platformDealFeeDtl.setState(1);
        this.update(platformDealFeeDtl);
        // 再构建一条反记录
        PlatformDealFeeDtl negatedDealFee = new PlatformDealFeeDtl();
        BeanUtils.copyProperties(platformDealFeeDtl, negatedDealFee);
        negatedDealFee.setPrePlatformDealFeeDtlId(platformDealFeeDtl.getPlatformDealFeeDtlId());
        negatedDealFee.setPlatformDealFeeDtlId(null);
        // 处理费用金额
        negatedDealFee.setState(1);
        // 已缴费金额
        negatedDealFee.setPayFee(negatedDealFee.getPayFee().negate());
        // 剩余缴费金额
        negatedDealFee.setResiduePayFee(negatedDealFee.getResiduePayFee().negate());
        negatedDealFee.setDealAmount(negatedDealFee.getDealFeeAmount().negate());
        if (negatedDealFee.getPayType() == 1) {
            // 正常缴费
            // 服务费重置
            negatedDealFee.setServeFee(negatedDealFee.getServeFee().negate());
            //negatedDealFee.setResiduePayFee(negatedDealFee.getResiduePayFee().negate());
            //negatedDealFee.setExceedFree(negatedDealFee.getExceedFree().negate());
            //negatedDealFee.setPayFee(negatedDealFee.getPayFee().negate());
        } else {
            // 占用的免费额度
            negatedDealFee.setUseExemptFree(negatedDealFee.getUseExemptFree() != null ? negatedDealFee.getUseExemptFree().negate() : new BigDecimal(0));
        }
        // 保存反记录到数据库
        save(negatedDealFee);
    }



    /**
     * 对账单作废退回余额自动缴费后校验是否没有欠费置空欠费时间
     * freem9
     * @param supplier
     * @param serveType
     */
    private void checkIsFree(EnterpriseInfo supplier, Integer serveType) {
        PlatformDealFee p2 = platformDealFeeService.lambdaQuery()
                .eq(PlatformDealFee::getEnterpriseId, supplier.getEnterpriseId())
                .eq(PlatformDealFee::getServeType, serveType)
                .one();
        QueryWrapper<PlatformDealFeeDtl> q = new QueryWrapper<>();
        q.eq("enterprise_id", supplier.getEnterpriseId())
                .eq("serve_type", serveType)
                .eq("finish_pay_fee", 0)
                .eq("state", 0)
                .select("sum(residue_pay_fee) as totalAmount");
        List<Map<String, Object>> maps = platformDealFeeDtlMapper.selectMaps(q);
        BigDecimal totalAmount = null;
        if (maps.get(0) != null) {
            totalAmount = new BigDecimal(maps.get(0).get("totalAmount").toString());
        } else {
            totalAmount = new BigDecimal(0);
        }
        // 如果没有欠费的金额
        if (totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            platformDealFeeService.lambdaUpdate().eq(PlatformDealFee::getPlatformDealFeeId, p2.getPlatformDealFeeId())
                    .set(PlatformDealFee::getArrearageDateTime, null).update();
        } else {
            // 如果有就是欠费无需操作

//            // 还存在欠费金额
//            BigDecimal arrearage = supplier.getArrearage();
//            if (arrearage.compareTo(BigDecimal.ZERO) == 0) {
//                // 不操作，还是欠费
//            } else {
//                // 判断是否超过可欠费额度
//                if (totalAmount.compareTo(arrearage) == 1) {
//                    // 不操作，还是欠费
//                } else {
//                    // 未超过欠费额度，判断时间
//                    Integer arrearageDateNum = supplier.getArrearageDateNum();
//                    Integer arrearageDateType = supplier.getArrearageDateType();
//                    if (arrearageDateNum != null && p2.getArrearageDateTime() != null) {
//                        // 说明设置了欠费时间，并且已经欠费
//                        Date arrearageDateTime = p2.getArrearageDateTime();
//                        int day = 0;
//                        if (arrearageDateType == 1) {
//                            day = arrearageDateNum;
//                        } else if (arrearageDateType == 2) {
//                            day = arrearageDateNum * 30;
//                        } else if (arrearageDateType == 3) {
//                            day = arrearageDateNum * 365;
//                        } else {
//                            throw new BusinessException("欠费时长时间类型错误！");
//                        }
//                        Calendar calendar = Calendar.getInstance();
//                        calendar.setTime(arrearageDateTime);
//                        calendar.add(Calendar.DATE, day);
//                        Date newDate = calendar.getTime();
//
//                        Date currentDate = new Date();  // 获取当前日期
//                        if (currentDate.after(newDate)) { // 当前时间在欠费截止时间之后
//                        } else if (currentDate.before(newDate)) {
//                            platformDealFeeService.lambdaUpdate().eq(PlatformDealFee::getPlatformDealFeeId, p2.getPlatformDealFeeId())
//                                    .set(PlatformDealFee::getArrearageDateTime, null).update();
//                        }
//                    } else {
//                        // 没有无限期
//                        platformDealFeeService.lambdaUpdate().eq(PlatformDealFee::getPlatformDealFeeId, p2.getPlatformDealFeeId())
//                                .set(PlatformDealFee::getArrearageDateTime, null).update();
//                    }
//
//                }
//            }
        }
    }

    /**
     * 对账单作废自动缴费
     * freem1
     * @param mr
     * @param supplier
     * @param serveType
     */
    private void zfUseAmount(MaterialReconciliation mr, EnterpriseInfo supplier, Integer serveType) {
        List<PlatformDealFeeDtl> notPayDealDtlList = getNotPayDealDtlList(supplier.getEnterpriseId(), serveType);
        if (!CollectionUtils.isEmpty(notPayDealDtlList)) {
            PlatformDealFee p2 = platformDealFeeService.lambdaQuery()
                    .eq(PlatformDealFee::getEnterpriseId, supplier.getEnterpriseId())
                    .eq(PlatformDealFee::getServeType, serveType)
                    .one();
            // 拿到最新余额
            BigDecimal remainingSum = p2.getRemainingSum();
            ArrayList<Map> maps = new ArrayList<>();
            for (PlatformDealFeeDtl dtl : notPayDealDtlList) {
                // 如果消耗完毕直接return
                if (remainingSum.compareTo(BigDecimal.ZERO) == 0) {
                    return;
                }
                BigDecimal residuePayFee = dtl.getResiduePayFee();
                HashMap<String, Object> objectHashMap = new HashMap<>();
                // 剩余缴费金额大于余额
                if (residuePayFee.compareTo(remainingSum) == 1) {
                    BigDecimal subtract = residuePayFee.subtract(remainingSum);
                    dtl.setResiduePayFee(subtract);
                    dtl.setPayFee(dtl.getPayFee().add(remainingSum));
                    BigDecimal add = dtl.getUseBalance().add(subtract);
                    dtl.setUseBalance(add); // 使用余额金额
                    objectHashMap.put("useBalance", remainingSum);
                    remainingSum = new BigDecimal(0);
                } else if (residuePayFee.compareTo(remainingSum) == 0) {
                    dtl.setFinishPayFee(1);
                    dtl.setPayFee(dtl.getPayFee().add(remainingSum));
                    dtl.setResiduePayFee(new BigDecimal(0));
                    BigDecimal add = dtl.getUseBalance().add(remainingSum);
                    dtl.setUseBalance(add); // 使用余额金额
                    objectHashMap.put("useBalance", remainingSum);
                    remainingSum = new BigDecimal(0);
                } else {
                    // 余额大于本次缴费金额
                    dtl.setFinishPayFee(1);
                    remainingSum = remainingSum.subtract(residuePayFee);
                    dtl.setResiduePayFee(new BigDecimal(0));
                    dtl.setPayFee(dtl.getPayFee().add(residuePayFee));
                    BigDecimal add = dtl.getUseBalance().add(residuePayFee);
                    objectHashMap.put("useBalance", residuePayFee);
                    dtl.setUseBalance(add); // 使用余额金额

                }
                objectHashMap.put("platformDealFeeDtlId", dtl.getPlatformDealFeeDtlId());
                maps.add(objectHashMap);
                platformDealFeeDtlService.update(dtl);
            }

            // 得到最新的金额
            BigDecimal newAmount = p2.getRemainingSum().subtract(remainingSum);

            // 记录和放入余额
            PlatformBalanceOperate pbo = new PlatformBalanceOperate();
            pbo.setTitle("作废退回余额后自动使用余额缴费");
            pbo.setRelevanceId(p2.getPlatformDealFeeId());
            pbo.setRelevanceType(1);
            pbo.setAmount(newAmount.negate());
            pbo.setBeforeAmount(p2.getRemainingSum());
            pbo.setAfterAmount(remainingSum);
            pbo.setOperateId(mr.getReconciliationId());
            pbo.setOperateUn(mr.getReconciliationNo());
            pbo.setOperateType(3);
            pbo.setFreeDtl(JSON.toJSONString(maps));
            pbo.setGmtCreate(new Date(new Date().getTime() + 1000));
            platformBalanceOperateService.save(pbo);
            p2.setRemainingSum(remainingSum);
            platformDealFeeService.update(p2);
        }
    }

    /**
     * 查询待缴费的明细
     * @return
     */
    @Override
    public List<PlatformDealFeeDtl> getNotPayDealDtlList(String enterpriseId, Integer serveType) {
        List<PlatformDealFeeDtl> list = lambdaQuery().eq(PlatformDealFeeDtl::getEnterpriseId, enterpriseId)
                .eq(PlatformDealFeeDtl::getServeType, serveType)
                .eq(PlatformDealFeeDtl::getFinishPayFee, 0)
                .eq(PlatformDealFeeDtl::getState, 0)
                .orderByAsc(PlatformDealFeeDtl::getGmtCreate)
                .list();
        return list;
    }

    /**
     * 查询季度待缴费的明细
     * @return
     */
    @Override
    public List<PlatformDealFeeDtl> getNotPayQuarterlyDealDtlList(String enterpriseId, Integer serveType,
                                                                  LocalDate quarterStartDate, LocalDate quarterEndDate) {
        List<PlatformDealFeeDtl> list = lambdaQuery().eq(PlatformDealFeeDtl::getEnterpriseId, enterpriseId)
                .eq(PlatformDealFeeDtl::getServeType, serveType)
                .eq(PlatformDealFeeDtl::getFinishPayFee, 0)
                .eq(PlatformDealFeeDtl::getState, 0)
                .ge(quarterStartDate != null, PlatformDealFeeDtl::getGmtCreate, quarterStartDate)  // 大于等于季度开始日期
                .le(quarterEndDate != null, PlatformDealFeeDtl::getGmtCreate, quarterEndDate)
                .orderByAsc(PlatformDealFeeDtl::getGmtCreate)
                .list();
        return list;
    }
}