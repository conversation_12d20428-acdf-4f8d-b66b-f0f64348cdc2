package scrbg.meplat.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.ProductShelfLog;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ProductShelfLogMapper;
import scrbg.meplat.mall.service.ProductShelfLogService;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;
import scrbg.meplat.mall.vo.product.material.ProductShelfLogVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ProductShelfLogServiceImpl extends ServiceImpl<ProductShelfLogMapper, ProductShelfLog> implements ProductShelfLogService {
    @Override
    public void deleteByLogId(String logId) {
        this.removeById(logId);
    }

    @Override
    public PageUtils queryByProductId(JSONObject jsonObject) {
        LambdaQueryWrapper<ProductShelfLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductShelfLog::getProductId, jsonObject.getString("productId"));
        queryWrapper.orderByDesc(ProductShelfLog::getOperationTime);
        IPage<ProductShelfLog> page = this.page(new Query<ProductShelfLog>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public void saveProductShelfLog(ProductShelfLog productShelfLog) {
        productShelfLog.setOperationTime(new Date());
        this.save(productShelfLog);
    }

    @Override
    public void exportShelfLogExcelFile(List<String> ids, HttpServletResponse response) {
        List<ProductShelfLog> list = lambdaQuery().in(ProductShelfLog::getProductId,ids).orderByDesc(ProductShelfLog::getOperationTime).list();
        List<ProductShelfLogVO> logVOS = list.stream().map(productShelfLog -> {
            ProductShelfLogVO logVO = new ProductShelfLogVO();
            if(productShelfLog.getOperationType() == 0){
                logVO.setOperationTypeStr("删除");
            }
            if(productShelfLog.getOperationType() == 1){
                logVO.setOperationTypeStr("上架");
            }
            if(productShelfLog.getOperationType() == 2){
                logVO.setOperationTypeStr("下架");
            }
            logVO.setProductName(productShelfLog.getProductId());
            logVO.setOperationTime(productShelfLog.getOperationTime());
            logVO.setStock(productShelfLog.getStock());
            logVO.setSort(productShelfLog.getSort());
            return logVO;
        }).collect(Collectors.toList());
        try {
            EasyExcelUtils.writeWeb("上架记录", ProductShelfLogVO.class, logVOS, "上架记录", response);
        } catch (IOException e) {
            throw new BusinessException("导出异常");
        }

    }
}
