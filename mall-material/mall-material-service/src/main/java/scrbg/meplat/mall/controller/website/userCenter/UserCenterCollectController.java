package scrbg.meplat.mall.controller.website.userCenter;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.enums.product.CollectEnum;
import scrbg.meplat.mall.service.ProductCollectService;
import scrbg.meplat.mall.util.ThreadLocalUtil;

import java.util.List;

/**
 * @package: scrbg.meplat.mall.controller.website.userCenter
 * @author: 胡原武
 * @date: 2022.11.29
 */

@RestController
@RequestMapping("/userCenter/collect")
@ApiSort(value = 200)
@Api(tags = "用户关注(个人中心)")
public class UserCenterCollectController {

    @Autowired
    public ProductCollectService productCollectService;


    /**
     * 根据用户ID查询用户收藏数量
     *
     * @return
     */
    @GetMapping("/selectCountByUserId")
    @ApiOperation(value = "根据userId查询收藏数量（1:店铺  2:商品）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectType", value = "收藏类型（1:店铺  2:商品）", required = true,
                    dataType = "int", paramType = "query")
    })
    public R selectCountByUserId(int collectType) {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        if (collectType == CollectEnum.COLLECT_TYPE_SHOP.getCode()) {
            int count = productCollectService.selectCountByUserId(userId, CollectEnum.COLLECT_TYPE_SHOP.getCode());
            return R.success(count, "店铺收藏数量获取成功");
        }
        if (collectType == CollectEnum.COLLECT_TYPE_PRODUCT.getCode()) {
            int count = productCollectService.selectCountByUserId(userId, CollectEnum.COLLECT_TYPE_PRODUCT.getCode());
            return R.success(count, "商品收藏数量获取成功");
        }
        return R.failed("收藏类型数据传输错误！");
    }

    /**
     * 根据用户ID查询用户收藏信息
     *
     * @return
     */
    @GetMapping("/selectByUserId")
    @ApiOperation(value = "根据userId查询收藏信息 （1:店铺  2:商品）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectType", value = "收藏类型（1:店铺  2:商品）", required = true,
                    dataType = "int", paramType = "query")
    })
    public R selectShopByUserId(int collectType) {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        if (collectType == CollectEnum.COLLECT_TYPE_SHOP.getCode()) {
            List<Object> shopList = productCollectService.selectByUserId(userId, CollectEnum.COLLECT_TYPE_SHOP.getCode());
            return R.success(shopList, "店铺收藏信息获取成功");
        }
        if (collectType == CollectEnum.COLLECT_TYPE_PRODUCT.getCode()) {
            List<Object> productList = productCollectService.selectByUserId(userId, CollectEnum.COLLECT_TYPE_PRODUCT.getCode());
            return R.success(productList, "商品收藏信息获取成功");
        }
        return R.failed("收藏类型数据传输错误！");
    }
}
