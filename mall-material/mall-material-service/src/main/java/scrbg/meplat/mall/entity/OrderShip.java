package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @描述：
 * @作者: ye
 * @日期: 2023-05-22
 */
@ApiModel(value = "发货单")
@Data
@TableName("order_ship")
public class OrderShip extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "单据id")
    private String billId;

    @ApiModelProperty(value = "清单类型（1浮动价格2固定价格）")
    private Integer billType;

    @ApiModelProperty(value = "订单id(1普通订单2多供方订单)")
    private String orderId;

    @ApiModelProperty(value = "单据编号")
    private String billSn;

    @ApiModelProperty(value = "发货时间")
    private Date shipData;

    @ApiModelProperty(value = "发货人id")
    private String shipUserId;

    @ApiModelProperty(value = "发货人姓名")
    private String shipUserName;

    @ApiModelProperty(value = "订单类别（1普通订单2多供方订单3已拆分子订单）")
    private Integer orderClass;

    @ApiModelProperty(value = "订单编号(主订单)")
    private String orderSn;

    @ApiModelProperty(value = "采购员组织机构Id")
    private String orgId;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "发货店铺名称")
    private String shopName;

    @ApiModelProperty(value = "收货人手机号")
    private String receivePhone;

    @ApiModelProperty(value = "下单时间")
    private Date flishTime;

    @ApiModelProperty(value = "主订单供应商企业id")
    private String supplierId;

    @ApiModelProperty(value = "主订单供应商企业名称")
    private String supplierName;

    @ApiModelProperty(value = "主订单供应商社会信用代码")
    private String supplierCode;

    @ApiModelProperty(value = "采购员本地机构Id")
    private String enterpriseId;

    @ApiModelProperty(value = "供应商组织机构Id(远程)")
    private String supplierOrgId;

    @ApiModelProperty(value = "采购员本地机构名称")
    private String enterpriseName;

    @ApiModelProperty(value = "发货供应商企业id")
    private String shipEnterpriseId;

    @ApiModelProperty(value = "发货供应商企业名称")
    private String shipEnterpriseName;

    @ApiModelProperty(value = "收料人员Id")
    private String receiveId;

    @ApiModelProperty(value = "收料人员姓名")
    private String receiveName;

    @ApiModelProperty(value = "计划编号")
    private String sourceNo;

    @ApiModelProperty(value = "收料人员姓名")
    private String sourceId;

    @ApiModelProperty(value = "收料人员单位id")
    private String receiveOrgId;

    @ApiModelProperty(value = "收料单位名称")
    private String receiveOrgName;

    @ApiModelProperty(value = "总金额（不含税）")
    private BigDecimal noRateAmount;

    @ApiModelProperty(value = "总金额（含税）")
    private BigDecimal rateAmount;

    @ApiModelProperty(value = "二级总金额（不含税）")
    private BigDecimal otherNoRateAmount;

    @ApiModelProperty(value = "二级总金额（含税）")
    private BigDecimal otherRateAmount;

    @ApiModelProperty(value = "二级总金额（含税）")
    private BigDecimal otherTotalPrice;

    @ApiModelProperty(value = "发货单状态（0未发货。1发货中，2已收货，3.已退货）")
    private Integer type;

    @ApiModelProperty(value = "收货地址")
    private String shipAddress;

    @ApiModelProperty(value = "物流单号")
    private String deliveryFlowId;

    @ApiModelProperty(value = "物流公司")
    private String logisticsCompany;

    @ApiModelProperty(value = "收 料人员确认时间")
    private Date confirmTime;

    @ApiModelProperty(value = "外部收料单id")
    private String outBillId;

    @ApiModelProperty(value = "外部收料单id")
    private String idStr;

    @ApiModelProperty(value = "二级订单id")
    private String otherOrderId;

    @ApiModelProperty(value = "二级订单编号")
    private String otherOrderSn;

    @ApiModelProperty(value = "乐观锁")
    private String version;

    @ApiModelProperty(value = "10 零星采购发货单  12 大宗材料 13大宗临购 14 周转材料")
    private Integer productType;

    @ApiModelProperty(value = "是否待推送pcwp（1是0已推送null不需要推送）")
    private Integer isNotPush;

    @ApiModelProperty(value = "来源类型   1大宗月供合同  2 零星采购计划，6 大宗临购 7 周转材料 ")
    private Integer sourceType;

    @ApiModelProperty(value = "外部收料单编号")
    private String outBillNo;

    @ApiModelProperty(value = "附件")
    @TableField(exist = false)
    private List<File> files;

    @ApiModelProperty(value = "发货单项")
    @TableField(exist = false)
    List<OrderShipDtl> dtls;

    @ApiModelProperty(value = "推送日志id")
    private String outKeyId;

    @ApiModelProperty(value = "PCWP现场收料状态")
    private Integer receiveStatus;

    @ApiModelProperty(value = "收料编号")
    private String receiveNo;

    @ApiModelProperty(value = "收料审核人")
    private String receiveAuditName;

    @ApiModelProperty(value = "是否已审核")
    private Integer receiveAuditIs;

    @ApiModelProperty(value = "运输联系人")
    private String contactUser;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "签收人")
    private String recipient;
}
