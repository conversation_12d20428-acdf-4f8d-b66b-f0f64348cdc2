package scrbg.meplat.mall.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import scrbg.meplat.mall.dto.plan.*;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlan;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.vo.order.GetMaterialContractUseMonthPlanQtyVO;
import scrbg.meplat.mall.vo.user.userCenter.GetPlanChangeDtlInfoByPlanNoVO;
import scrbg.meplat.mall.vo.user.userCenter.GetPlanDtlInfoByPlanNoVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @描述：计划表 服务类
 * @作者: ye
 * @日期: 2023-06-27
 */
public interface MaterialMonthSupplyPlanService extends IService<MaterialMonthSupplyPlan> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMonthSupplyPlan> queryWrapper);

    void create(MaterialMonthSupplyPlan materialMonthSupplyPlan);

    void update(MaterialMonthSupplyPlan materialMonthSupplyPlan);

    MaterialMonthSupplyPlan getById(String id);

    void delete(String id);


    /**
     * 新增计划并且新增计划明细
     *
     * @param dto
     * @return
     */
    void createPlanAndPlanDtl(MaterialMonthSupplyPlanDTO dto);



    /**
     * 检查总数返回已消耗数量
     *
     * @param dtos
     * @return
     */
    List<Map> checkTotalNum(List<Map> dtos);

    /**
     * 批量提交审核
     *
     * @param planIds
     */
    void batchSubmitPlan(List<String> planIds);

    /**
     * 批量删除
     *
     * @param planIds
     */
    void batchDeletePlan(List<String> planIds);

    /**
     * 根据计划编号获取计划明细
     *
     * @param planNo
     * @return
     */
    GetPlanDtlInfoByPlanNoVO getPlanDtlInfoByPlanNo(String planNo);

    /**
     * 修改月供计划
     *
     * @param dto
     */
    void updatePlanDtlByPlanId(UpdatePlanDtlByPlanIdDTO dto);

    /**
     * 批量作废
     *
     * @param planIds
     */
    void cancellationPlan(List<String> planIds);

    /**
     * 审核计划
     *
     * @param dto
     * @param idStr
     * @param farArg
     * @param rD
     */
    void auditPlan(AuditPlanDTO dto, String idStr, StringBuilder farArg, MaterialMonthSupplyPlan rD);

    /**
     * 新增变更计划
     * @param dto
     */
    void createChangePlanAndPlanDtl(CreateChangePlanAndPlanDtlDTO dto);

    /**
     * 根据变更计划编号获取变更数据
     * @param planChangeNo
     * @return
     */
    GetPlanChangeDtlInfoByPlanNoVO getPlanChangeDtlInfoByPlanNo(String planChangeNo);

    /**
     * 修改变更月供计划
     * @param dto
     */
    void updatePlanChangeDtlByPlanId(UpdatePlanChangeDtlByPlanIdDTO dto);

    /**
     * 审核变更计划
     *
     * @param dto
     * @param idStr
     * @param farArg
     * @param rD
     */
    void auditChangePlan(AuditChangePlanDTO dto, String idStr, StringBuilder farArg, MaterialMonthSupplyPlan rD);

    /**
     * 批量作废变更计划
     * @param planChangeIds
     */
    void cancellationChangePlan(List<String> planChangeIds);

    /**
     * 根据合同明细ids获取物资采购合同明细已生成月供计划数量
     * @param dtlIds
     * @return
     */
    List<GetMaterialContractUseMonthPlanQtyVO> getMaterialContractUseMonthPlanQty(List<String> dtlIds);


    /**
     *  供应商查看已审核的的月供应计划
     * @param jsonObject
     * @param materialMonthSupplyPlanLambdaQueryWrapper
     * @return
     */
    PageUtils shopMangeListByEntity(JSONObject jsonObject, LambdaQueryWrapper<MaterialMonthSupplyPlan> materialMonthSupplyPlanLambdaQueryWrapper);

    PageUtils secondLevelPlanMonthList(JSONObject jsonObject, QueryWrapper<MaterialMonthSupplyPlan> materialMonthSupplyPlanQueryWrapper);

    void    planExport(String id, HttpServletResponse response);

    void planSecondLevelExport(String id, HttpServletResponse response);

    GetPlanDtlInfoByPlanNoVO getSecondLevelPlanDtlInfoByPlanNo(String planNo);


    /**
     * 根据合同id和版本获取合同明细数据
     * @param contactId
     * @param version
     * @return BillId DtlId ResidueQty
     */
    List<Map> getContactResidueQtyByContactIdAndVersion(String contactId,Integer version);

    /**
     * 删除计划
     *
     * @param planId
     */
    void deletePlanInfo(String planId);

    /**
     * 根据计划编号完结计划
     * @param planNo
     */
    void closeMaterialMonthSupplyPlanByPlanNo(String planNo, String idStr, StringBuilder farArg);
}
