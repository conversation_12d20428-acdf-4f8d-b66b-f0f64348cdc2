package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ContentCatalogue;

/**
 * @描述：内容目录表 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
public interface ContentCatalogueService extends IService<ContentCatalogue> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ContentCatalogue> queryWrapper);

    void create(ContentCatalogue contentCatalogue);

    void update(ContentCatalogue contentCatalogue);

    ContentCatalogue getById(String id);

    void delete(String id);
}
