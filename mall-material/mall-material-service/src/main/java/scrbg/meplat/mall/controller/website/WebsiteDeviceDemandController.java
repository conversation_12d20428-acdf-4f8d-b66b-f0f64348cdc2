package scrbg.meplat.mall.controller.website;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.DeviceDemand;
import scrbg.meplat.mall.service.DeviceDemandService;
import scrbg.meplat.mall.vo.product.website.demand.DeviceDemandHomeVO;
import scrbg.meplat.mall.vo.product.website.demand.DeviceDemandInfoVO;

/**
 * @描述：设备需求控制类
 * @作者: y
 * @日期: 2022-11-21
 */
@RestController
@RequestMapping("/w/deviceDemand")
@ApiSort(value = 100)
@Api(tags = "需求（前台）")
public class WebsiteDeviceDemandController {

    @Autowired
    public DeviceDemandService deviceDemandService;

    @PostMapping("/usedPageList")
    @ApiOperation(value = "获取需求列表")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（需求名称，发起方名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "brandId", value = "品牌id", dataTypeClass = String.class),
            @DynamicParameter(name = "city", value = "市", dataTypeClass = String.class),
            @DynamicParameter(name = "demandType", value = "需求类型（1租赁设备2二手设备）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（预算价格）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（预算价格）", dataTypeClass = String.class),
//            @DynamicParameter(name = "releaseType", value = "发布类型（0店铺1用户2平台）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按排序值排序1:按价格2:按发布时间)", dataTypeClass = String.class),
    })
    public PageR<DeviceDemandHomeVO> usedPageList(@RequestBody JSONObject jsonObject) {
        PageUtils page = deviceDemandService.usedPageList(jsonObject, Wrappers.lambdaQuery(DeviceDemand.class));
        return PageR.success(page);
    }


    @GetMapping("/demandInfo")
    @ApiOperation(value = "需求详细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "demandId", value = "需求id", required = true,
                    dataType = "String", paramType = "query"),
    })
    public R demandInfo(String demandId) {
        DeviceDemandInfoVO vo = deviceDemandService.demandInfo(demandId);
        return R.success(vo);
    }

}
