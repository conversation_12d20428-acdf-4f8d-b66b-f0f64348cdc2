package scrbg.meplat.mall.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.AuditRecords;
import scrbg.meplat.mall.entity.ProcessInstance;
import scrbg.meplat.mall.entity.ProcessRole;
import scrbg.meplat.mall.mapper.ProcessNodeOperationMapper;
import scrbg.meplat.mall.service.AuditService;
import scrbg.meplat.mall.service.ProcessInstanceService;
import scrbg.meplat.mall.service.ProcessNodeService;
import scrbg.meplat.mall.service.ProcessRoleService;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AuditServiceImpl implements AuditService {

    @Autowired
    ProcessNodeService processNodeService;

    @Autowired
    ProcessInstanceService processInstanceService;

    @Autowired
    ProcessNodeOperationMapper processNodeMapper;

    @Autowired
    ProcessRoleService processRoleService;

    @Override
    public List<AuditRecords> findRecordsBy(String processInstanceId) {
        // 校验流程实例是否存在
        ProcessInstance instance = processInstanceService.getById(processInstanceId);
        if (instance == null) {
            log.warn("流程实例ID:{} 不存在", processInstanceId);
            return Collections.emptyList();
        }

        // 查询该流程实例的所有审核记录
        List<AuditRecords> auditRecords = processNodeMapper.findListByInstanceId(processInstanceId);
        if (auditRecords.isEmpty()) {
            log.info("流程实例ID:{} 暂无审核记录", processInstanceId);
            return Collections.emptyList();
        }

        // 按时间排序确保记录顺序正确（假设按创建时间排序）
        auditRecords.sort(Comparator.comparing(AuditRecords::getAuditTime));

        AuditRecords previousRecord = null;
        for (AuditRecords currentRecord : auditRecords) {
            // 查询当前节点对应的角色信息
            List<ProcessRole> processRoles = processRoleService.lambdaQuery()
                    .eq(ProcessRole::getProcessNodeId, currentRecord.getNodeId())
                    .list();

            if (!processRoles.isEmpty()) {
                ProcessRole processRole = processRoles.get(0);
                currentRecord.setReviewSecondary(processRole.getRoleName());

                // 处理时间逻辑：使用上一条记录的审核时间作为当前记录的通知时间
                if (currentRecord.getAuditType() == 0) {
                    currentRecord.setNoticeTime(null);
                } else {
                    // 如果有上一条记录，使用上一条的审核时间作为当前的通知时间
                    if (previousRecord != null) {
                        currentRecord.setNoticeTime(previousRecord.getAuditTime());
                    } else {
                        // 第一条记录没有上一条，使用自身审核时间或留空
                        currentRecord.setNoticeTime(currentRecord.getAuditTime());
                    }
                }
            }

            // 更新上一条记录为当前记录，用于下一次循环
            previousRecord = currentRecord;
        }

        return auditRecords;
    }
}
