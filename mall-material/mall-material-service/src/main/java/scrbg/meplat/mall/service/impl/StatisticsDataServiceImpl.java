package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.StatisticsData;
import scrbg.meplat.mall.mapper.StatisticsDataMapper;
import scrbg.meplat.mall.service.StatisticsDataService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-07-07
 */
@Service
public class StatisticsDataServiceImpl extends ServiceImpl<StatisticsDataMapper, StatisticsData> implements StatisticsDataService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<StatisticsData> queryWrapper) {
        IPage<StatisticsData> page = this.page(
        new Query<StatisticsData>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(StatisticsData statisticsData) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(statisticsData);
    }

    @Override
    public void update(StatisticsData statisticsData) {
        super.updateById(statisticsData);
    }


    @Override
    public StatisticsData getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
