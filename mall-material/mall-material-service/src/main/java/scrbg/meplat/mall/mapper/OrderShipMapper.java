package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.OrderReturnItem;
import scrbg.meplat.mall.entity.OrderShip;
import scrbg.meplat.mall.entity.OrderShipDtl;
import scrbg.meplat.mall.vo.ship.OrderReturnItemPCWPVo;
import scrbg.meplat.mall.vo.ship.OrderShipInfoVo;

import java.util.ArrayList;
import java.util.List;

/**
 * @描述： Mapper 接口
 * @作者: ye
 * @日期: 2023-05-22
 */
@Mapper
@Repository
public interface OrderShipMapper extends BaseMapper<OrderShip> {

    OrderShipInfoVo exportDataByBillId(@Param("billId") String billId);

    List<OrderReturnItemPCWPVo> selectOtherItemsByOrderId(@Param("dtlIdList") List<String> dtlIdList);
}
