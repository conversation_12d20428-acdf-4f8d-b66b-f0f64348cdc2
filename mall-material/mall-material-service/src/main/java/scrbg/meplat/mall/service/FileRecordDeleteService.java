package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.FileRecordDelete;
/**
 * @描述： 服务类
 * @作者: y
 * @日期: 2022-11-28
 */
public interface FileRecordDeleteService extends IService<FileRecordDelete> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<FileRecordDelete> queryWrapper);

        void create(FileRecordDelete fileRecordDelete);

        void update(FileRecordDelete fileRecordDelete);

        FileRecordDelete getById(String id);

        void delete(String id);
}
