package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.ProductRulesClass;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ProductRulesClass;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.List;

/**
 * @描述：商品预警规则和分类关联表 服务类
 * @作者: ye
 * @日期: 2024-03-20
 */
public interface ProductRulesClassService extends IService<ProductRulesClass> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductRulesClass> queryWrapper);

    void create(ProductRulesClass productRulesClass);

    void update(ProductRulesClass productRulesClass);

    ProductRulesClass getById(String id);

    void delete(String id);

    void deleteBatch(List<String> ids);

    List<ProductRulesClass> findListByRulesId(String warningRuleId);

    /**
     * 根据分类id和物料id查询启用的预警规则
     * @param  classId 分类id
     * @param relevanceId 物料id
     * @return
     */
    List<ProductRulesClass> selcetDataByClassIdAndMaterialId(String classId, String relevanceId);
}
