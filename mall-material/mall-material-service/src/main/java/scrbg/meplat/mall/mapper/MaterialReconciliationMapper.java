package scrbg.meplat.mall.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;
import scrbg.meplat.mall.vo.platform.ReconciliationLedgerListVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @描述：物资验收 Mapper 接口
 * @作者: ye
 * @日期: 2023-07-26
 */
@Mapper
@Repository
public interface MaterialReconciliationMapper extends BaseMapper<MaterialReconciliation> {

    // 台账相关查询
    int listLedgerCount(@Param("dto") Map<String, Object> dto);

    List<ReconciliationLedgerListVo> ledgerList(Page<ReconciliationLedgerListVo> pages, @Param("dto") Map<String, Object> dto);

    BigDecimal selCountAmount(@Param("dto") Map<String, Object> dto);

    BigDecimal selCountNoRateAmount(@Param("dto") Map<String, Object> dto);
}