package scrbg.meplat.mall.controller.website.userCenter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.controller.UserController;
import scrbg.meplat.mall.dto.order.CreateMaterialOrderByCartIdsDTO;
import scrbg.meplat.mall.dto.order.ProductBuyInfoDTO;
import scrbg.meplat.mall.dto.order.UpdateOrderItemQtyDTO;
import scrbg.meplat.mall.dto.plan.SubmitMonthPlanOrderDTO;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrdersService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.MaterialLockUtils;
import scrbg.meplat.mall.util.RestTemplateUtils;
import scrbg.meplat.mall.vo.GetSynthesizeTemporaryPlanDetailVO;
import scrbg.meplat.mall.vo.product.order.OrderCreateResultVO;
import scrbg.meplat.mall.vo.product.order.OrderItemVo;
import scrbg.meplat.mall.vo.product.restsServe.SettleRepairVO;
import scrbg.meplat.mall.vo.user.userCenter.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/userCenter/orders")
@ApiSort(value = 200)
@Api(tags = "订单（个人中心）")
public class UserCenterOrderController {

    @Autowired
    public OrdersService ordersService;

    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @Autowired
    public OrderItemService orderItemService;

    @Autowired
    RestTemplateUtils restTemplateUtils;

    @Autowired
    MallConfig mallConfig;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @PostMapping("/listByParameters")
    @ApiOperation(value = "根据参数分页查询订单信息")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称，订单号，店铺名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "订单状态：0草稿 1已提交 2待确认 3已确认 4待签订合同 5已签合同 6已完成 7已关闭", dataTypeClass = Integer.class),
    })
    public PageR<Orders> listUserCenterOrdersByParameters(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.listUserCenterOrdersByParameters(jsonObject, Wrappers.lambdaQuery(Orders.class));
        return PageR.success(page);
    }

//    @PostMapping("/product/mergeOrder/orderSn")
//    @ApiOperation(value = "合并订单")
//    public R mergeOrder(@Valid @RequestBody MergeOrderDTO dto) {
//        ordersService.mergeOrder(dto);
//        return R.success();
//    }


//    @PostMapping("/createRepairOrder")
//    @ApiOperation(value = "新增维修服务订单")
//    public R createRepairOrder(@Valid @RequestBody CreateRepairOrderDTO dto) {
//        ordersService.createRepairOrder(dto);
//        return R.success();
//    }

//    @PostMapping("/createDeviceOrder")
//    @ApiOperation(value = "新增装备订单")
//    public R createDeviceOrder(@Valid @RequestBody CreateMaterialOrderByCartIdsDTO dto) {
//        Object vo = ordersService.createDeviceOrder(dto);
//        return R.success(vo);
//    }

    @GetMapping("/settleAccountProduct")
    @ApiOperation(value = "结算商品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "商品ID", required = true,
                    dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "buyNum", value = "购买数量", required = true,
                    dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "leaseNum", value = "租赁时长",
                    dataType = "double", paramType = "query"),
    })
    public R settleAccountProduct(String productId, String buyNum, BigDecimal leaseNum) {
        SettleAccountProductVO vo = ordersService.settleAccountProduct(productId, buyNum, leaseNum);
        return R.success(vo);
    }

    @GetMapping("/settleRepair")
    @ApiOperation(value = "结算其他服务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "商品ID", required = true,
                    dataType = "String", paramType = "query"),
    })
    public R settleRepair(String productId) {
        SettleRepairVO vo = ordersService.settleRepair(productId);
        return R.success(vo);
    }

    @PostMapping("/settleAccountProductCart")
    @ApiOperation(value = "结算购物车")
    public R settleAccountProductCart(@RequestBody List<String> cartIds) {
        List<SettleAccountProductCartVO> vos = ordersService.settleAccountProductCart(cartIds);
        return R.success(vos);
    }

    @PostMapping("/getUserOrderPageList")
    @ApiOperation(value = "查询我的订单")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称,商品编号,订单号）", dataTypeClass = String.class),
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "订单状态：0草稿 1已提交 2待确认 3已确认 4待签订合同 5已签合同 6已完成 7已关闭", dataTypeClass = Integer.class),
            @DynamicParameter(name = "isComment", value = "是否评价（1是0否）", dataTypeClass = Integer.class),
    })
    public PageR<UserOrderPageListVO> getUserOrderPageList(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.getUserOrderPageList(jsonObject);
        return PageR.success(page);
    }

    @PostMapping("/getUserOrderComment")
    @ApiOperation(value = "查询我的订单评价")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "isComment", value = "是否评价（1是0否）", dataTypeClass = Integer.class),
    })
    public PageR<UserOrderPageListVO> getUserOrderComment(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.getUserOrderComment(jsonObject);
        return PageR.success(page);
    }

    @PostMapping("/getUserLogisticsOrder")
    @ApiOperation(value = "查询我的物流订单")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
    })
    public PageR<LogisticsOrderVO> getUserLogisticsOrder(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.getUserLogisticsOrder(jsonObject);
        return PageR.success(page);
    }

    @GetMapping("/getOrderDetail")
    @ApiOperation(value = "获取订单详情")
    public R<OrderDetailVO> getOrderDetail(String orderSn) {
        OrderDetailVO vo = ordersService.getOrderDetail(orderSn);
        return R.success(vo);
    }

    @GetMapping("/getOrderPlanDetail")
    @ApiOperation(value = "获取操作合同订单详情")
    public R<List<OrderPlanDetail>> getOrderPlanDetail(String orderSn) {
        List<OrderPlanDetail> vos = ordersService.getOrderPlanDetail(orderSn);
        return R.success(vos);
    }

    @GetMapping("/getOrderById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R getOrderById(String id) {
        Orders orders = ordersService.getById(id);
        return R.success(orders);
    }

    @PostMapping("/batchUpdateOrderItem")
    @ApiOperation(value = "批量修改订单项")
    @NotResubmit
    public R batchUpdateOrderItem(@RequestBody List<OrderItem> OrderItems) {
        orderItemService.batchUpdateOrderItem(OrderItems);
        return R.success();
    }

    @PostMapping("/updateOrderInfo")
    @ApiOperation(value = "修改订单")
    @NotResubmit
    public R updateOrderInfo(@RequestBody Orders orders) {
        ordersService.update(orders);
        return R.success();
    }

    @PostMapping("/orderItemListByOrderId")
    @ApiOperation(value = "查询订单中的订单项")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderItem> orderItemListByOrderId(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderItemService.orderItemListByOrderId(jsonObject, new QueryWrapper<OrderItemVo>());
        return PageR.success(page);
    }

    @GetMapping("/confirmReceipt")
    @ApiOperation(value = "个人中心确认收货")
    @NotResubmit
    public R confirmReceipt(String orderId) {
        ordersService.confirmReceipt(orderId);
        return R.success();
    }

//    @PostMapping("/submitPlanAndOrder")
//    @ApiOperation(value = "提交计划，生成零星订单")
//    public R submitPlanAndOrder(@RequestBody List<ProductBuyInfoDTO> dtos) {
//        List<Map> maps = null;
//        try {
//            maps = ordersService.submitPlanAndOrder(dtos);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        if (CollectionUtils.isEmpty(maps)) {
//            return R.success();
//        } else {
//            return R.success(maps);
//        }
//    }


    @PostMapping("/submitPlanAndOrder")
    @ApiOperation(value = "生成零星采购订单")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R PCWP2submitPlanAndOrder(@RequestBody List<ProductBuyInfoDTO> dtos) {
        List<Map> maps = null;
        maps = ordersService.PCWP2submitPlanAndOrder(dtos);
        if (CollectionUtils.isEmpty(maps)) {
            return R.success();
        } else {
            return R.success(maps);
        }
    }

    @PostMapping("/createMaterialOrder")
    @ApiOperation(value = "生成普通订单")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R createMaterialOrder(@Valid @RequestBody CreateMaterialOrderByCartIdsDTO dto) {
        OrderCreateResultVO vo = ordersService.createMaterialOrder(dto);
        return R.success(vo);
    }

    @PostMapping("/submitMonthPlanOrder")
    @ApiOperation(value = "生成大宗月供订单")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R submitMonthPlanOrder(@Valid @RequestBody SubmitMonthPlanOrderDTO dto) {
        ordersService.submitMonthPlanOrder(dto);
        return R.success();
    }

    @PostMapping("/submitSynthesizeTemporaryOrder")
    @ApiOperation(value = "生成大宗临购订单")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R submitSynthesizeTemporaryOrder(@RequestBody GetSynthesizeTemporaryPlanDetailVO vo) {
        String idStr = IdWorker.getIdStr();
        StringBuilder stringBuilder = new StringBuilder();
        try {
            String clientId = UUID.randomUUID().toString();
            Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(MaterialLockUtils.PUSH_LX_PIAN_LOCK + ":" + vo.getBillNo(), clientId, 2, TimeUnit.MINUTES);
            if (b) {
                ordersService.submitSynthesizeTemporaryOrder(vo, idStr, stringBuilder);
            } else {
                throw new RuntimeException("请勿重复推送大宗零购订单，请返回上一页，查看计划数量，等待2分钟后重试");
            }
        } catch (Exception e) {
            LogUtil.writeErrorLog2(idStr, "submitSynthesizeTemporaryOrder", vo, stringBuilder.toString(), null, e.getMessage(), UserCenterOrderController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(UserCenterOrderController.class.getName());
            iLog.setMethodName("submitSynthesizeTemporaryOrder");
            iLog.setLocalArguments(JSON.toJSONString(vo));
            iLog.setFarArguments(stringBuilder.toString());
            iLog.setIsSuccess(0);
            iLog.setLogType(1);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            R r = null;
            try {
                r = restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + "/thirdapi/bulkRetailPlan/updateBulkRetailPlanDtl?keyId=" + idStr);
                if (r == null || r.getCode() != 200) {
                    throw new BusinessException("远程异常：回滚反写零星采购计划商城订单数量方法调用失败" + r);
                }
                InterfaceLogs iLog2 = new InterfaceLogs();
                iLog2.setSecretKey(idStr);
                iLog2.setClassPackage(UserCenterOrderController.class.getName());
                iLog2.setMethodName("submitSynthesizeTemporaryOrder(回滚)");
                iLog2.setLocalArguments(JSON.toJSONString(vo));
                iLog2.setFarArguments(stringBuilder.toString());
                iLog2.setIsSuccess(1);
                iLog2.setLogType(1);
                iLog2.setErrorInfo(e.getMessage());
                interfaceLogsService.create(iLog2);
            } catch (Exception e1) {
                InterfaceLogs iLog2 = new InterfaceLogs();
                iLog2.setSecretKey(idStr);
                iLog2.setClassPackage(UserCenterOrderController.class.getName());
                iLog2.setMethodName("submitSynthesizeTemporaryOrder(回滚)");
                iLog2.setLocalArguments(JSON.toJSONString(vo));
                iLog2.setFarArguments(stringBuilder.toString());
                iLog2.setIsSuccess(0);
                iLog2.setErrorInfo("远程异常：远程反写零星采购计划商城订单数量方法接口出错");
                iLog2.setLogType(1);
                iLog2.setErrorInfo(e.getMessage());
                interfaceLogsService.create(iLog2);
                throw new BusinessException(e.getMessage());
            }
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("/submitSynthesizeTemporaryInterOrder")
    @ApiOperation(value = "生成大宗临购订单（清单竞价）")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R submitInventoryOrder(@RequestBody GetSynthesizeTemporaryPlanDetailVO vo) {
        String idStr = IdWorker.getIdStr();
        StringBuilder stringBuilder = new StringBuilder();
        try {
            ordersService.submitInventoryOrder(vo, idStr, stringBuilder);
        } catch (Exception e) {
            LogUtil.writeErrorLog2(idStr, "submitSynthesizeTemporaryInterOrder", vo, stringBuilder.toString(), null, e.getMessage(), UserCenterOrderController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(UserCenterOrderController.class.getName());
            iLog.setMethodName("submitSynthesizeTemporaryInterOrder");
            iLog.setLocalArguments(JSON.toJSONString(vo));
            iLog.setFarArguments(stringBuilder.toString());
            iLog.setIsSuccess(0);
            iLog.setLogType(1);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("/history/orderItem")
    @ApiOperation(value = "根据商品条件查询订单项历史")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "13 大宗临购 12 大宗月供  10零星采购", dataTypeClass = Integer.class)
    })
    public PageR historyOrderItem(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderItemService.findAllHistoryOrderItem(jsonObject);
        return PageR.success(page);
    }

    @GetMapping("/deleteLGOrderByOrderId")
    @ApiOperation(value = "删除大宗临购订单")
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R deleteLGOrderByOrderId(String orderId) {
        String idStr = IdWorker.getIdStr();
        StringBuilder stringBuilder = new StringBuilder();
//        try {
        ordersService.deleteLGOrderByOrderId(orderId, idStr, stringBuilder);
//        } catch (Exception e) {
//            LogUtil.writeErrorLog2(idStr,"deleteLGOrderByOrderId",orderId,stringBuilder.toString(),null,e.getMessage(), UserCenterOrderController.class);
//            InterfaceLogs iLog = new InterfaceLogs();
//            iLog.setSecretKey(idStr);
//            iLog.setClassPackage(UserCenterOrderController.class.getName());
//            iLog.setMethodName("deleteLGOrderByOrderId");
//            iLog.setLocalArguments(orderId);
//            iLog.setFarArguments(stringBuilder.toString());
//            iLog.setIsSuccess(0);
//            iLog.setLogType(1);
//            iLog.setErrorInfo(e.getMessage());
//            interfaceLogsService.create(iLog);
//            throw new BusinessException(e.getMessage());
//        }
        return R.success();
    }

    @GetMapping("/deleteYGOrderByOrderId")
    @ApiOperation(value = "删除大宗月供订单")
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R deleteYGOrderByOrderId(String orderId) {
        ordersService.deleteYGOrderByOrderId(orderId);
        return R.success();
    }

    @PostMapping("/updateDZYGOrderItemQty")
    @ApiOperation(value = "修改大宗月供订单数量")
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R deleteYGOrderByOrderId(@RequestBody List<UpdateOrderItemQtyDTO> dtos) {
        orderItemService.updateDZYGOrderItemQty(dtos);
        return R.success();
    }

    @PostMapping("/updateDZLGOrderItemQty")
    @ApiOperation(value = "修改大宗临购订单数量")
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R updateDZLGOrderItemQty(@RequestBody List<UpdateOrderItemQtyDTO> dtos) {
        String idStr = IdWorker.getIdStr();
        StringBuilder stringBuilder = new StringBuilder();
//        try {
        orderItemService.updateDZLGOrderItemQty(dtos, idStr, stringBuilder);
//        } catch (Exception e) {
//            LogUtil.writeErrorLog2(idStr,"updateDZLGOrderItemQty",dtos,stringBuilder.toString(),null,e.getMessage(), UserCenterOrderController.class);
//            InterfaceLogs iLog = new InterfaceLogs();
//            iLog.setSecretKey(idStr);
//            iLog.setClassPackage(UserCenterOrderController.class.getName());
//            iLog.setMethodName("updateDZLGOrderItemQty");
//            iLog.setLocalArguments(JSON.toJSONString(dtos));
//            iLog.setFarArguments(stringBuilder.toString());
//            iLog.setIsSuccess(0);
//            iLog.setLogType(1);
//            iLog.setErrorInfo(e.getMessage());
//            interfaceLogsService.create(iLog);
//            throw new BusinessException(e.getMessage());
//        }
        return R.success();
    }
}
