package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：平台年费明细
 * @作者: ye
 * @日期: 2024-01-24
 */
@ApiModel(value="平台年费明细（废弃）")
@Data
@TableName("platform_year_fee_dtl")
public class PlatformYearFeeDtl extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "平台年费明细id")
    private String platformYearFeeDtlId;


    @ApiModelProperty(value = "服务类型（1店铺年费2招标年费）")

    private String serveType;


    @ApiModelProperty(value = "缴费记录id")
    private String paymentRecordId;

    @ApiModelProperty(value = "缴费记录编号")

    private String paymentRecordUn;


    @ApiModelProperty(value = "企业id")

    private String enterpriseId;


    @ApiModelProperty(value = "企业名称")

    private String enterpriseName;


    @ApiModelProperty(value = "服务金额")

    private BigDecimal serveAmount;


    @ApiModelProperty(value = "服务开始时间")

    private Date serveStartTime;


    @ApiModelProperty(value = "服务截止时间")

    private Date serveEndTime;


    @ApiModelProperty(value = "服务时长")

    private Integer paymentDuration;


    @ApiModelProperty(value = "服务时长类型（单位）（1天2周3月4年）")

    private Integer paymentDurationType;


    @ApiModelProperty(value = "乐观锁")
    @Version
    private Integer version;



}