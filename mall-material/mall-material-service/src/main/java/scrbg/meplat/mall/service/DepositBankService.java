package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.DepositBank;

/**
 * @描述：开户银行信息表 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
public interface DepositBankService extends IService<DepositBank> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<DepositBank> queryWrapper);

    void create(DepositBank depositBank);

    void update(DepositBank depositBank);

    DepositBank getById(String id);

    void delete(String id);
}
