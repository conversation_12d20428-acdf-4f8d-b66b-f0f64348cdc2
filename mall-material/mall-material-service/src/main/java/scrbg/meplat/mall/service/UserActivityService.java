package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.UserActivity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.UserActivity;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：用户行为表 服务类
 * @作者: ye
 * @日期: 2023-09-04
 */
public interface UserActivityService extends IService<UserActivity> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<UserActivity> queryWrapper);

        void create(UserActivity userActivity);
        void update(UserActivity userActivity);
        UserActivity getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
