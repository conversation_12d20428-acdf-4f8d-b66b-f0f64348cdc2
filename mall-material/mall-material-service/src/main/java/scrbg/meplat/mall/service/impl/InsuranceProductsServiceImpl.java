package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.InsuranceProducts;
import scrbg.meplat.mall.mapper.InsuranceProductsMapper;
import scrbg.meplat.mall.service.InsuranceProductsService;

import java.util.List;

/**
 * @描述：保险产品 服务类
 * @作者: sund
 * @日期: 2022-11-15
 */
@Service
public class InsuranceProductsServiceImpl extends ServiceImpl<InsuranceProductsMapper, InsuranceProducts> implements InsuranceProductsService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<InsuranceProducts> queryWrapper) {
        IPage<InsuranceProducts> page = this.page(
        new Query<InsuranceProducts>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(InsuranceProducts insuranceProducts) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(insuranceProducts);
    }

    @Override
    public void update(InsuranceProducts insuranceProducts) {
        super.updateById(insuranceProducts);
    }


    @Override
    public InsuranceProducts getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void updateByPublish(List<String> ids, String type) {
        List<InsuranceProducts> resutls = listByIds(ids);
        for (InsuranceProducts insuranceProducts : resutls) {
            if ("1".equals(type)) {
                insuranceProducts.setState(1);
            } else {
                insuranceProducts.setState(0);
            }
        }
        super.saveOrUpdateBatch(resutls);

    }

}
