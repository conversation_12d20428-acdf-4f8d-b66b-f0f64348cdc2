package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanChange;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanChange;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
/**
 * @描述：计划变更表 服务类
 * @作者: ye
 * @日期: 2023-06-27
 */
public interface MaterialMonthSupplyPlanChangeService extends IService<MaterialMonthSupplyPlanChange> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMonthSupplyPlanChange> queryWrapper);

        void create(MaterialMonthSupplyPlanChange materialMonthSupplyPlanChange);

        void update(MaterialMonthSupplyPlanChange materialMonthSupplyPlanChange);

        MaterialMonthSupplyPlanChange getById(String id);

        void delete(String id);
}