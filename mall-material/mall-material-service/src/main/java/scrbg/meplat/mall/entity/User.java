package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @描述：用户
 * @作者:
 * @日期: 2022-11-28
 */
@ApiModel(value = "用户")
@Data
@TableName("user")
public class User extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "内部id")
    private String interiorId;

    @ApiModelProperty(value = "用户编号")
    private String userNumber;

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "用户密码")
    private String password;

    @ApiModelProperty(value = "手机号码")
    private String userMobile;

    @ApiModelProperty(value = "手机验证码")
    @TableField(exist = false)
    private String verificationCode;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "头像(图片地址)")
    private String userImg;

    @ApiModelProperty(value = "图片记录id")
    private String userImgId;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "性别 1:男 0: 女")
    private Integer gender;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区县")
    private String county;

    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    @ApiModelProperty(value = "生日")
    private Date birthday;

    @ApiModelProperty(value = "用户状态 0：禁用 1：启用  ")
    private Integer state;

    @ApiModelProperty(value = "是否为管理员  1：是  0：不是")
    private Integer isAdmin;

    @ApiModelProperty(value = "上次登录时间")
    private Date gmtLogin;

    @ApiModelProperty(value = "是否为物资商城注册   1：是   0：否")
    private Integer isMaterial;

    @ApiModelProperty(value = "是否为设备商城注册   1：是   0：否")
    private Integer isDevice;

    @ApiModelProperty(value = "企业附加信息id")
    private String enterpriseId;

    @ApiModelProperty(value = "是否为内部用户   1：是   0：否（默认：0）")
    private Integer isInternalUser;

    @ApiModelProperty(value = "是否为平台管理员  1：是  0：不是（默认：0）")
    private Integer platformAdmin;

    @ApiModelProperty(value = "图标题（保存在数组第一个）")
    @TableField(exist = false)
    List<String> labelTitle;

    @ApiModelProperty(value = "图数量（保存在数组第一个）")
    @TableField(exist = false)
    List<Integer> count;

    @ApiModelProperty(value = "装备状态（0停用1启用）")
    private Integer deviceState;

    @ApiModelProperty(value = "物资状态（0停用1启用）")
    private Integer materialState;

    @ApiModelProperty(value = "微信的openId")
    private String wxOpenId;

    @ApiModelProperty(value = "登陆次数")
    private Integer loginCount;
    @ApiModelProperty(value = "是否首次登录 1 是 0 否")
    private Integer firstLogin;
    /**
     * 外部用户需月修改密码
     */
    @ApiModelProperty(value = "锁定状态(0正常 1锁定)")
    private Integer lockedState;

    @ApiModelProperty(value = "密码错误次数")
    private Integer passwordErrTimes;

    @ApiModelProperty(value = "锁定原因")
    private String lockCause;

    @ApiModelProperty(value = "上次密码修改时间")
    private Date pwdChangeDate;

    @ApiModelProperty(value = "上次密码错误时间")
    private Date lastFailedLoginTime;

    @ApiModelProperty(value = "图形验证码")
    @TableField(exist = false)
    private String verifyInput;

    @ApiModelProperty(value = "图形验证码id")
    @TableField(exist = false)
    private String verifyId;

    @ApiModelProperty(value = "备用1（目前是重置密码）")
    private Integer attrOne;

    @ApiModelProperty(value = "是否提示电子招标服务")
    private Integer isShowBid;
}



