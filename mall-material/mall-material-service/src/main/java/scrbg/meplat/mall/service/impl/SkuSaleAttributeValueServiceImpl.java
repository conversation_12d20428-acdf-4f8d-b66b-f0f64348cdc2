package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.SkuSaleAttributeValue;
import scrbg.meplat.mall.mapper.SkuSaleAttributeValueMapper;
import scrbg.meplat.mall.service.SkuSaleAttributeValueService;

/**
 * @描述：sku销售属性&值 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class SkuSaleAttributeValueServiceImpl extends ServiceImpl<SkuSaleAttributeValueMapper, SkuSaleAttributeValue> implements SkuSaleAttributeValueService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SkuSaleAttributeValue> queryWrapper) {
        IPage<SkuSaleAttributeValue> page = this.page(
                new Query<SkuSaleAttributeValue>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(SkuSaleAttributeValue skuSaleAttributeValue) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(skuSaleAttributeValue);
    }

    @Override
    public void update(SkuSaleAttributeValue skuSaleAttributeValue) {
        super.updateById(skuSaleAttributeValue);
    }


    @Override
    public SkuSaleAttributeValue getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
