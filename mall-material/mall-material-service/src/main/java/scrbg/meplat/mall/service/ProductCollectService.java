package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.ProductCollect;

import java.util.List;

/**
 * @描述：个人商品收藏 服务类
 * @作者: sund
 * @日期: 2022-11-10
 */
public interface ProductCollectService extends IService<ProductCollect> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductCollect> queryWrapper);

    /**
     * Description:  根据userId进行新增
     * date: 2022/11/10 9:22
     */
    void create(ProductCollect productCollect);

    void update(ProductCollect productCollect);

    ProductCollect getById(String id);

    void delete(String id);

    int selectCountByUserId(String userId, int countType);

    List<Object> selectByUserId(String userId, int type);

    R addProductCollect(ProductCollect productCollect);


    List<Product> selUserColloctNum(JSONObject jsonObject);

    ProductCollect selectByProductId(String productId);

    /**s
     * 判断用户是否收藏商品
     * @param productCollect
     * @return
     */
    Integer isCollect(ProductCollect productCollect);

    /**
     * 获得用户收藏商品数量
     * @param productCollect
     * @return
     */
    Integer getCollectNum( ProductCollect productCollect);

    /**
     * 根据商品id查询当前登陆用户是否收藏
     * @param productId
     * @return
     */
    boolean getIsCollectByProductId(String productId);

    /**
     * 根据商品id集合批量插入收藏数据
     * @param ids
     */
    void addBatch(List<String> ids);
}
