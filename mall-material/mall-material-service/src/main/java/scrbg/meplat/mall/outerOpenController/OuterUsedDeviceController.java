package scrbg.meplat.mall.outerOpenController;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.common.redis.RedisKey;
import scrbg.meplat.mall.dto.outer.CreateShopDeviceDTO;
import scrbg.meplat.mall.dto.outer.GetOuterUseShopIdDTO;
import scrbg.meplat.mall.dto.outer.UpdateBatchUsedDeviceStateDTO;
import scrbg.meplat.mall.dto.outer.UpdateUsedDeviceDTO;
import scrbg.meplat.mall.entity.Brand;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.enums.product.ProductCategoryEnum;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.vo.product.device.DeviseInfoVO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @描述：店铺商品信息控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/outer/usedDevice")
@Api(tags = "二手设备接口")
public class OuterUsedDeviceController {

    @Autowired
    private ProductService productService;
    @Autowired
    private ShopService shopService;

    @Autowired
    private BrandService brandService;
    @Autowired
    public ProductCategoryService productCategoryService;

    @Autowired
    private SystemParamService systemParamService;

    /**
     * 周材计划，
     * 获取店铺id：内部通过orgId，外部通过手机号
     * 查询二手设备上架的分类
     * 新增二手设备，查询店铺是否存在和停用
     * 修改二手设备 以上
     * 上下架二手设备
     * 根据设备id查询订单（交易记录），查询商品
     * 所有接口都要给店铺id
     */

    @PostMapping("/getOuterUseShopId")
    @ApiOperation(value = "获取店铺id")
    public R<Map> getOuterUseShopId(@Valid @RequestBody GetOuterUseShopIdDTO dto) {
        Map<String,String> map = shopService.getOuterUseShopId(dto);
        return R.success(map);
    }
    @GetMapping("/listUsedDeviceCategoryTree")
    @ApiOperation(value = "获取二手设备分类树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productType", value = "分类类型： 4二手设备",
                    dataType = "Integer", paramType = "query", required = true),
            @ApiImplicitParam(name = "mallType", value = "商城类型：1设备商城 ",
                    dataType = "Integer", paramType = "query", required = true),
    })
    @Cacheable(value = {RedisKey.CATEGORY}, key = "#productType + '_' + #mallType + '_getTree'")
    public scrbg.meplat.mall.util.R<List<ProductCategory>> getTree(@RequestParam(defaultValue = "4") Integer productType,@RequestParam(defaultValue = "1") Integer mallType) {
        List<ProductCategory> entities = productCategoryService.listWithTree(productType, null, ProductCategoryEnum.TYPE_OPEN.getCode(), null,0);
        return scrbg.meplat.mall.util.R.success(entities);
    }


    @PostMapping("/getBrandPageList")
    @ApiOperation(value = "获取品牌列表")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "name", value = "品牌名", dataTypeClass = String.class),
    })
    public PageR<Brand> getBrandPageList(@RequestBody JSONObject jsonObject) {
        PageUtils page = brandService.getBrandPageList(jsonObject, Wrappers.lambdaQuery(Brand.class));
        return PageR.success(page);
    }

    @GetMapping("/getDeviceUnit")
    @ApiOperation(value = "获取设备单位")
    public R<List<SystemParam>> getDeviceUnit() {
        List<SystemParam> systemParam = systemParamService.listByCode("deviceUnit",null);
        return R.success(systemParam);
    }
    @GetMapping("/getDeviceQuality")
    @ApiOperation(value = "获取成色")
    public R<List<SystemParam>> getDeviceQuality() {
        List<SystemParam> systemParam = systemParamService.listByCode("deviceQuality",null);
        return R.success(systemParam);
    }


    @PostMapping("/getDetailDealRecord")
    @ApiOperation(value = "获取商品交易记录")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productId", value = "商品id", dataTypeClass = Integer.class),
    })
    public R<PageUtils> getProductDetailDealRecord(@RequestBody JSONObject jsonObject) {
        PageUtils vo = productService.getProductDetailDealRecord(jsonObject);
        return R.success(vo);
    }
}

