package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.OrderPaymentInfo;
import scrbg.meplat.mall.mapper.OrderPaymentInfoMapper;
import scrbg.meplat.mall.service.OrderPaymentInfoService;

/**
 * @描述：支付信息表 服务类
 * @作者: y
 * @日期: 2022-11-08
 */
@Service
public class OrderPaymentInfoServiceImpl extends ServiceImpl<OrderPaymentInfoMapper, OrderPaymentInfo> implements OrderPaymentInfoService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderPaymentInfo> queryWrapper) {
        IPage<OrderPaymentInfo> page = this.page(
                new Query<OrderPaymentInfo>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(OrderPaymentInfo orderPaymentInfo) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(orderPaymentInfo);
    }

    @Override
    public void update(OrderPaymentInfo orderPaymentInfo) {
        super.updateById(orderPaymentInfo);
    }


    @Override
    public OrderPaymentInfo getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
