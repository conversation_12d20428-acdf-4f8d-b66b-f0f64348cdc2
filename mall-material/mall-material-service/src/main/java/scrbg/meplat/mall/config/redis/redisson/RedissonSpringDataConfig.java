package scrbg.meplat.mall.config.redis.redisson;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.redisson.config.SentinelServersConfig;
import org.redisson.config.SingleServerConfig;
import org.redisson.spring.data.connection.RedissonConnectionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 *  Redisson配置类 - 基于Spring Redis的配置
 */
@Configuration
@ConfigurationProperties(prefix = "spring.redis")
public class RedissonSpringDataConfig {

    private static final Logger log = LoggerFactory.getLogger(RedissonSpringDataConfig.class);

    private String host;
    private int port;
    private String password;
    private int database;
    private Sentinel sentinel;

    @Bean
    public RedissonConnectionFactory redissonConnectionFactory(RedissonClient redisson) {
        return new RedissonConnectionFactory(redisson);
    }

    @Bean(destroyMethod = "shutdown")
    public RedissonClient redisson() {
        Config config = new Config();

        // 检查是否配置了Sentinel
        if (sentinel != null && StringUtils.hasText(sentinel.getMaster())
                && sentinel.getNodes() != null && !sentinel.getNodes().isEmpty()) {
            log.info("配置Redisson使用Sentinel模式，masterName: {}", sentinel.getMaster());
            configureSentinel(config);
        } else {
            log.info("配置Redisson使用单机模式，host: {}, port: {}", host, port);
            configureSingleServer(config);
        }

        return Redisson.create(config);
    }

    /**
     * 配置单机模式
     */
    private void configureSingleServer(Config config) {
        SingleServerConfig serverConfig = config.useSingleServer()
                .setAddress("redis://" + host + ":" + port)
                .setDatabase(database);

        if (StringUtils.hasText(password)) {
            serverConfig.setPassword(password);
        }

        log.debug("已配置Redisson单机模式，地址: {}", serverConfig.getAddress());
    }

    /**
     * 配置Sentinel模式
     */
    private void configureSentinel(Config config) {
        SentinelServersConfig serverConfig = config.useSentinelServers()
                .setMasterName(sentinel.getMaster())
                .setDatabase(database)
                .setReadMode(ReadMode.SLAVE);

        // 添加Sentinel节点
        for (String node : sentinel.getNodes()) {
            serverConfig.addSentinelAddress("redis://" + node);
        }

        // 设置Redis密码
        if (StringUtils.hasText(password)) {
            serverConfig.setPassword(password);
        }

        // 设置Sentinel密码
        if (StringUtils.hasText(sentinel.getPassword())) {
            serverConfig.setSentinelPassword(sentinel.getPassword());
        }

        log.debug("已配置Redisson Sentinel模式，master: {}, nodes: {}",
                sentinel.getMaster(), sentinel.getNodes());
    }

    // Getters and Setters
    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getDatabase() {
        return database;
    }

    public void setDatabase(int database) {
        this.database = database;
    }

    public Sentinel getSentinel() {
        return sentinel;
    }

    public void setSentinel(Sentinel sentinel) {
        this.sentinel = sentinel;
    }

    // Sentinel内部类
    public static class Sentinel {
        private String master;
        private List<String> nodes;
        private String password;

        public String getMaster() {
            return master;
        }

        public void setMaster(String master) {
            this.master = master;
        }

        public List<String> getNodes() {
            return nodes;
        }

        public void setNodes(List<String> nodes) {
            this.nodes = nodes;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }
}