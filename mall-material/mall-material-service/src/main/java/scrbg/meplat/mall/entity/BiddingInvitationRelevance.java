package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：邀请竞价关联表
 * @作者: ye
 * @日期: 2023-09-27
 */
@ApiModel(value="邀请竞价关联表")
@Data
@TableName("bidding_invitation_relevance")
public class BiddingInvitationRelevance extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

















    @ApiModelProperty(value = "竞价邀请ID")
    @TableId(type = IdType.ASSIGN_ID)
    private String bidInvitationId;


    @ApiModelProperty(value = "竞价采购编号")

    private String biddingSn;


    @ApiModelProperty(value = "供应商id")

    private String supplierId;


    @ApiModelProperty(value = "供应商名称")

    private String supplierName;



}