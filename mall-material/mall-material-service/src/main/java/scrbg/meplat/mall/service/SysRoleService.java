package scrbg.meplat.mall.service;

import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.entity.SysMenu;
import scrbg.meplat.mall.entity.SysRole;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SysRole;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：角色表 服务类
 * @作者: ye
 * @日期: 2023-12-21
 */
public interface SysRoleService extends IService<SysRole> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysRole> queryWrapper);

        void create(SysRole sysRole);
        void update(SysRole sysRole);
        SysRole getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);



        /**
         * 根据pcwp角色列表查询角色以及对应
         * @param roleNames
         * @return
         */
        List<MallRole> getRoleMenuListByRoleNames(List<String> roleNames);


        /**
         * 根据角色名称和所属平台获取菜单列表树形
         * @param roleNames
         * @return
         */
        List<SysMenu> getMenuListByRoleNames(List<String> roleNames, Integer categoryType);
        // 根据角色名称查询角色

        /**
         * 根据角色名称查询角色信息
         * @param roleName
         * @return
         */
        SysRole getRoleByName(String roleName);
}
