package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.OrderContact;
import scrbg.meplat.mall.mapper.OrderContactMapper;
import scrbg.meplat.mall.service.OrderContactService;

/**
 * @描述：订单合同关联信息 服务类
 * @作者: ye
 * @日期: 2023-03-15
 */
@Service
public class OrderContactServiceImpl extends ServiceImpl<OrderContactMapper, OrderContact> implements OrderContactService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderContact> queryWrapper) {
        IPage<OrderContact> page = this.page(
        new Query<OrderContact>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(OrderContact orderContact) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(orderContact);
    }

    @Override
    public void update(OrderContact orderContact) {
        super.updateById(orderContact);
    }


    @Override
    public OrderContact getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}