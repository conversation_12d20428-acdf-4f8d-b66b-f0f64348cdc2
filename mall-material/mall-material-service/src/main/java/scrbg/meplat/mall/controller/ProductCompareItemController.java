package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.ProductCompareItemService;
import scrbg.meplat.mall.entity.ProductCompareItem;

import java.util.List;

/**
 * @描述：控制类
 * @作者: ye
 * @日期: 2023-12-12
 */
@RestController
@RequestMapping("/productCompareItem")
@Api(tags = "比价明细控制类")
public class ProductCompareItemController {

    @Autowired
    public ProductCompareItemService productCompareItemService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<ProductCompareItem> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = productCompareItemService.queryPage(jsonObject, new LambdaQueryWrapper<ProductCompareItem>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<ProductCompareItem> findById(String id) {
        ProductCompareItem productCompareItem = productCompareItemService.getById(id);
        return R.success(productCompareItem);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody ProductCompareItem productCompareItem) {
        productCompareItemService.create(productCompareItem);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody ProductCompareItem productCompareItem) {
        productCompareItemService.update(productCompareItem);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        productCompareItemService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        productCompareItemService.removeByIds(ids);
        return R.success();
    }
    @PostMapping("/createByProductIds")
    @ApiOperation(value = "根据商品ID批量新增比价记录")
    public R createByProductIds(@RequestBody  List<String> productIds){
        List<ProductCompareItem> products = productCompareItemService.createByProductIds(productIds,null);
        return R.success(products);
    }

    // 重新比价
    @PostMapping("/reCompare")
    @ApiOperation(value = "重新比价，商品ID和主表ID必传")

    public R reCompare(@RequestBody  List<String> productIds,String id){
        List<ProductCompareItem> products = productCompareItemService.reCompare(id,productIds);
        return R.success(products);
    }

}

