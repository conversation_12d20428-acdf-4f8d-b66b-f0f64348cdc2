package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.ProductCompareItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ProductCompareItem;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import javax.annotation.Resource;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-12-12
 */
public interface ProductCompareItemService extends IService<ProductCompareItem> {

        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductCompareItem> queryWrapper);

        void create(ProductCompareItem productCompareItem);
        void update(ProductCompareItem productCompareItem);
        ProductCompareItem getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

        /**
         * 根据商品ID新增比价明细
         * @param productIds
         * @param compareId 比价主表ID，更新用
         * @return 比价明细列表
         */
        List<ProductCompareItem> createByProductIds(List<String> productIds,String compareId);

        /**
         * 根据比价主表ID和商品ID重新比价
         * @param id
         * @param productIds
         * @return
         */

        List<ProductCompareItem> reCompare(String id, List<String> productIds);
}
