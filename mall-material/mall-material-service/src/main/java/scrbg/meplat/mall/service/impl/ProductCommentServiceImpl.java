package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.file.FileEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ProductCommentMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.pageUtils;
import scrbg.meplat.mall.vo.product.ProductDetailCommentPageListVO;
import scrbg.meplat.mall.vo.user.userCenter.UserOrderPageListVO;

import java.util.*;

/**
 * @描述：商品评价 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class ProductCommentServiceImpl extends ServiceImpl<ProductCommentMapper, ProductComment> implements ProductCommentService {

    @Autowired
    private OrderItemService orderItemService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private OrdersService ordersService;
    @Autowired
    private MallConfig mallConfig;
    @Autowired
    private UserService userService;
    @Autowired
    private ProductCommentService productCommentService;
    @Autowired
    private ProductCommentMapper productCommentMapper;
    @Autowired
    FileService fileService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductComment> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String productId = (String) innerMap.get("productId");
//        int sort = (int) innerMap.get("sort");
        q.eq(ProductComment::getProductId, productId);
        q.eq(ProductComment::getIsShow,1);
        q.orderByDesc(ProductComment::getSort);
        q.orderByDesc(ProductComment::getGmtCreate);
        IPage<ProductComment> page = this.page(
                new Query<ProductComment>().getPage(jsonObject),
                q
        );
        List<ProductDetailCommentPageListVO> vos = new ArrayList<>();
        List<ProductComment> productComments = page.getRecords();
        if (CollectionUtils.isEmpty(productComments)) {
            return new PageUtils(page);
        } else {
            for (ProductComment productComment : productComments) {
                ProductDetailCommentPageListVO vo = new ProductDetailCommentPageListVO();
                String userId = productComment.getUserId();
                if (mallConfig.mallType == 1) {
                    User user = userService.lambdaQuery().eq(User::getUserId, userId)
                            .select(User::getUserId, User::getNickName, User::getGender, User::getUserImg).one();
                    if (user != null) {
                        vo.setNickName(user.getNickName());
                        vo.setGender(user.getGender());
                        vo.setUserImg(user.getUserImg());
                    }
                }
                vo.setUserId(userId);
                vo.setCommentId(productComment.getCommentId());
                vo.setCommentContent(productComment.getCommentContent());
                vo.setCommentImgs(productComment.getCommentImgs());
                vos.add(vo);
            }
            if (mallConfig.mallType == 0) {
                // 发送请求查询用户信息
                List<ProductDetailCommentPageListVO> vosData = productCommentService.getCommentListUserInfo(vos);
                if (!CollectionUtils.isEmpty(vosData)) {
                    PageUtils pageUtils = new PageUtils(page);
                    pageUtils.setList(vosData);
                    return pageUtils;
                }
            }
        }
        PageUtils pageUtils = new PageUtils(page);
        pageUtils.setList(vos);
        return pageUtils;
    }

    @Override
    public PageUtils commentManageList(JSONObject jsonObject, LambdaQueryWrapper<ProductComment> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer productType = jsonObject.getInteger("productType");
        List<Integer> productTypes = null;
        if (productType != null) {
            // 根据productType值转换为对应的productTypes数组
            if (productType == 10) {
                productTypes = Arrays.asList(0, 10);
            } else if(productType == 13){
                productTypes = Arrays.asList(1, 13);
                // 如果是其他值，可以设置默认处理或者直接使用原值
            }else{
                productTypes = Arrays.asList(productType);
            }
        }
        innerMap.put("productTypes", productTypes);
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        innerMap.put("shopId", user.getShopId());
        int count = baseMapper.commentManageListCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);
        Page<ProductComment> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ProductComment> vos = baseMapper.commentManageList(pages, innerMap);
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProductComment productComment) {
        List<String> orderIds = productComment.getOrderIds();
        Map<String,String> pratentIds = new HashMap<>();
        List<ProductComment> proclist = new ArrayList<>();
        // 判断订单id数组是否为空
        if (!CollectionUtils.isEmpty(orderIds)) {
            for (String orderId : orderIds) {
                //保存订单评价
                ProductComment proc = new ProductComment();
                String CommentImgs = getCommentImgsJson(productComment, orderId);
                proc.setCommentImgs(CommentImgs);
                proc.setUserId(ThreadLocalUtil.getCurrentUser().getUserId());
                proc.setOrderId(orderId);
                proc.setCommentLevel(productComment.getCommentLevel());
                proc.setCommentSupply(productComment.getCommentSupply());
                proc.setCommentIntegrity(productComment.getCommentIntegrity());
                proc.setCommentService(productComment.getCommentService());
                proc.setCommentContent(productComment.getCommentContent());
                proc.setEvaluateTime(new Date());
                proc.setIsShow(1);
                proc.setIsReply(0);
                proc.setIsAnonymous(0);
                proc.setSort(0);
                proc.setProductType(productComment.getProductType());
                boolean save = super.save(proc);
                if (save) {
                    proclist.add(proc);
                    String pratentId = proc.getCommentId();
                    pratentIds.put(orderId, pratentId);
                    ordersService.lambdaUpdate()
                            .eq(Orders::getOrderId, orderId)
                            .set(Orders::getIsComment, PublicEnum.IS_YES.getCode())
                            .set(Orders::getGmtModified, new Date()).update();
                }
            }
            //保存商品评价
            List<OrderItem> childComments = productComment.getOrderItemComments();
            if(!CollectionUtils.isEmpty(childComments)){
                for (OrderItem orderItem : childComments) {
                    ProductComment childC = new ProductComment();
                    childC.setCommentFile(orderItem.getCommentFile());
                    String CommentImgs = getCommentImgsJson(childC, orderItem.getOrderItemId());
                    childC.setCommentImgs(CommentImgs);
                    childC.setProductId(orderItem.getProductId());
                    childC.setUserId(ThreadLocalUtil.getCurrentUser().getUserId());
                    String parentId = pratentIds.get(orderItem.getOrderId());
                    childC.setParentId(parentId);
                    childC.setOrderId(orderItem.getOrderId());
                    childC.setOrderItemId(orderItem.getOrderItemId());
                    childC.setProductName(orderItem.getProductName());
                    childC.setCommentContent(orderItem.getCommentContent());
                    childC.setEvaluateTime(new Date());
                    childC.setIsShow(1);
                    childC.setIsReply(0);
                    childC.setIsAnonymous(0);
                    childC.setProductType(orderItem.getProductType());
                    childC.setSort(0);
                    boolean save = super.save(childC);
                    if (save) {
                        orderItemService.lambdaUpdate()
                                .eq(OrderItem::getOrderItemId, orderItem.getOrderItemId())
                                .set(OrderItem::getIsComment, PublicEnum.IS_YES.getCode())
                                .set(OrderItem::getGmtModified, new Date()).update();
                    }
                }
            }
        }else{
            productComment.setUserId(ThreadLocalUtil.getCurrentUser().getUserId());
            productComment.setEvaluateTime(new Date());
            boolean save = super.save(productComment);
            // 如果评价成功修改订单状态
            if (save) {
                orderItemService.lambdaUpdate()
                        .eq(OrderItem::getOrderItemId, productComment.getOrderItemId())
                        .set(OrderItem::getIsComment, PublicEnum.IS_YES.getCode())
                        .set(OrderItem::getGmtModified, new Date()).update();
                // TODO 状态可能不需要修改
//            ordersService.lambdaUpdate()
//                    .eq(Orders::getOrderId,productComment.getOrderId())
//                    .set(Orders::getState,7)
//                    .set(Orders::getFlishTime,new Date())
//                    .set(Orders::getGmtModified,new Date()).update();
            }
        }
    }

    private String getCommentImgsJson(ProductComment productComment,String relevanceId){
        if (!CollectionUtils.isEmpty(productComment.getCommentFile())) {
            // 保存评价图
            List<File> commentFiles = productComment.getCommentFile();
            Map<String, String> commentImags = new HashMap<>();
            String imagName = "img";
            int i= 1;
            fileService.lambdaUpdate()
                    .eq(File::getRelevanceType, "8")
                    .eq(File::getRelevanceId,relevanceId)
                    .set(File::getIsDelete, PublicEnum.IS_DELETE_YES.getCode()).update();
            for(File commentFile : commentFiles){
                commentFile.setFileId(null);
                commentFile.setRelevanceId(relevanceId);
                boolean save2 = fileService.save(commentFile);
                if (!save2) {
                    throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "图片修改失败！");
                } else {
                    commentImags.put(imagName+i,commentFile.getUrl());
                    i++;
                }
            }
            return JSONObject.toJSONString(commentImags);
        }
        return null;
    }

    @Override
    public void update(ProductComment productComment) {
        String[] commentIds = productComment.getCommentIds();
        for(String commentId :commentIds){
            List<ProductComment> chirdComments = lambdaQuery().eq(ProductComment::getParentId,commentId).list();
            if(productComment.getSort()!=null){
                if (productComment.getSort() == 1) {
                    for(ProductComment chirdComment: chirdComments){
                        ProductComment commentSortFront = lambdaQuery().eq(ProductComment::getProductId, chirdComment.getProductId())
                                .orderByDesc(ProductComment::getSort).last("limit 1").one();
                        ProductComment chirdComnentUpdate = new ProductComment();
                        chirdComnentUpdate.setCommentId(chirdComment.getCommentId());
                        chirdComnentUpdate.setSort(commentSortFront.getSort()+1);
                        boolean sortUpdate = super.updateById(chirdComnentUpdate);
                    }
                }else if(productComment.getSort() == 0){
                    for(ProductComment chirdComment: chirdComments){
                        ProductComment chirdComnentUpdate = new ProductComment();
                        chirdComnentUpdate.setCommentId(chirdComment.getCommentId());
                        chirdComnentUpdate.setSort(productComment.getSort());
                        boolean sortUpdate = super.updateById(chirdComnentUpdate);
                    }
                }
            }
            if(productComment.getIsShow()!=null){
                for(ProductComment chirdComment: chirdComments){
                    ProductComment chirdComnentUpdate = new ProductComment();
                    chirdComnentUpdate.setCommentId(chirdComment.getCommentId());
                    chirdComnentUpdate.setIsShow(productComment.getIsShow());
                    boolean showUpdate = super.updateById(chirdComnentUpdate);
                }
            }
            productComment.setCommentId(commentId);
            boolean orderCommentSortUpdate = super.updateById(productComment);
        }
    }


    @Override
    public ProductComment getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 删除评价
     *
     * @param }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteComment(String orderId) {
        baseMapper.deleteCommentById(orderId);
        ordersService.lambdaUpdate()
                .eq(Orders::getOrderId, orderId)
                .set(Orders::getIsComment, PublicEnum.IS_NO.getCode())
                .set(Orders::getGmtModified, new Date()).update();
        orderItemService.lambdaUpdate()
                .eq(OrderItem::getOrderId, orderId)
                .set(OrderItem::getIsComment, PublicEnum.IS_NO.getCode())
                .set(OrderItem::getGmtModified, new Date()).update();
    }

    /**
     * 修改评价
     *
     * @param productComment
     */
    @Override
    public void updateComment(ProductComment productComment) {
        Map<String,String> pratentIds = new HashMap<>();
        List<ProductComment> proclist = new ArrayList<>();
        //保存订单评价
        ProductComment proc = new ProductComment();
        String CommentImgs = getCommentImgsJson(productComment, productComment.getOrderId());
        proc.setCommentImgs(CommentImgs);
        proc.setCommentLevel(productComment.getCommentLevel());
        proc.setCommentSupply(productComment.getCommentSupply());
        proc.setCommentIntegrity(productComment.getCommentIntegrity());
        proc.setCommentService(productComment.getCommentService());
        proc.setCommentContent(productComment.getCommentContent());
        proc.setCommentId(productComment.getCommentId());
        boolean orderCommentUpdate = super.updateById(proc);
        if(orderCommentUpdate){
            //保存商品评价
            List<OrderItem> childComments = productComment.getOrderItemComments();
            if(!CollectionUtils.isEmpty(childComments)){
                for (OrderItem orderItem : childComments) {
                    if (!CollectionUtils.isEmpty(orderItem.getCommentFile()) || StringUtils.isNotBlank(orderItem.getCommentContent())) {
                        ProductComment childC = new ProductComment();
                        childC.setCommentFile(orderItem.getCommentFile());
                        String CommentChildImgs = getCommentImgsJson(childC, orderItem.getOrderItemId());
                        childC.setCommentImgs(CommentChildImgs);
                        childC.setCommentContent(orderItem.getCommentContent());
                        childC.setCommentId(orderItem.getCommentId());
                        boolean productCommentUpdate = super.updateById(childC);
                    }

                }
            }
        }
    }


    /**
     * 获取评价根据订单项id
     *
     * @param orderItemId
     * @return
     */
    @Override
    public ProductComment getCommentByOrderItemId(String orderItemId) {
        ProductComment one = lambdaQuery().eq(ProductComment::getOrderItemId, orderItemId).one();
        return one;
    }

    /**
     * 获取评价根据订单id
     *
     * @param orderId
     * @return
     */
    @Override
    public ProductComment getCommentByOrderId(String orderId) {
        ProductComment one = lambdaQuery().eq(ProductComment::getOrderId, orderId).isNull(ProductComment::getProductId).one();
        List<File> productCommentFile = fileService.listFileByParameters(one.getOrderId(),
                FileEnum.RELEVANCE_TYPE_COMMENT.getCode(),
                FileEnum.IS_MIN_NO.getCode(),
                FileEnum.TYPE_IMG.getCode(),
                FileEnum.IMG_TYPE_GENERAL.getCode()
        );
        one.setCommentFile(productCommentFile);
        List<String> orderIds = Arrays.asList(one.getOrderId());
        List<OrderItem> orderItems = orderItemService.getOrderItemByOrderIds(orderIds);
        for(OrderItem orderItem: orderItems){
            List<File> files = fileService.listFileByParameters(orderItem.getOrderItemId(),
                    FileEnum.RELEVANCE_TYPE_COMMENT.getCode(),
                    FileEnum.IS_MIN_NO.getCode(),
                    FileEnum.TYPE_IMG.getCode(),
                    FileEnum.IMG_TYPE_GENERAL.getCode()
            );
            orderItem.setCommentFile(files);
            ProductComment productComment = lambdaQuery().eq(ProductComment::getOrderId, orderId)
                    .eq(ProductComment::getProductId,orderItem.getProductId()).one();
            orderItem.setCommentId(productComment.getCommentId());
            orderItem.setCommentContent(productComment.getCommentContent());
        }
        one.setOrderItemComments(orderItems);
        return one;
    }

    /**
     * 补充评价信息列表
     *
     * @param dtos
     * @return
     */
    @Override
    public List<ProductDetailCommentPageListVO> getCommentListUserInfo(List<ProductDetailCommentPageListVO> dtos) {
        for (ProductDetailCommentPageListVO dto : dtos) {
            String userId = dto.getUserId();
            User user = userService.lambdaQuery().eq(User::getUserId, userId)
                    .select(User::getUserId, User::getNickName, User::getGender, User::getUserImg).one();
            if (user != null) {
                dto.setNickName(user.getNickName());
                dto.setGender(user.getGender());
                dto.setUserImg(user.getUserImg());
            }
        }
        return dtos;
    }

    /**
     * 通过店铺id,评价时间查询订单评价
     */
    @Override
    public List<ProductComment> getProductCommentByShopId(String shopId, Date startDate, Date endDate) {
        return baseMapper.getProductCommentByShopId(shopId,startDate,endDate);
    }
}