package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.SysMenu;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SysMenu;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：菜单表 服务类
 * @作者: ye
 * @日期: 2023-12-21
 */
public interface SysMenuService extends IService<SysMenu> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysMenu> queryWrapper);

        void create(SysMenu sysMenu);
        void update(SysMenu sysMenu);
        SysMenu getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
