package scrbg.meplat.mall.adapter.configAutoBean;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MyConfig {
    @Bean
//    @ConditionalOnProperty(value = "spring.profiles.active", havingValue = "dev")
    public SystemRequestLogAspect systemRequestLogAspect() {
        return new SystemRequestLogAspect();
    }
}