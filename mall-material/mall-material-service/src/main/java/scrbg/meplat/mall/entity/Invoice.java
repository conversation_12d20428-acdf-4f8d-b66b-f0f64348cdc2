package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：发票
 * @作者: ye
 * @日期: 2023-11-13+
 */
@ApiModel(value="发票")
@Data
@TableName("invoice")
public class Invoice extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "发票id")
    private String invoiceId;

    @ApiModelProperty(value = "项目部Id")

    private String enterpriseId;


    @ApiModelProperty(value = "项目部名称")

    private String enterpriseName;


    @ApiModelProperty(value = "供应商id")

    private String supplierId;


    @ApiModelProperty(value = "供应商名称")

    private String supplierName;


    @ApiModelProperty(value = "1:已申请 2:已开票，3被拒 4采购员申请作废 5供应商申请作废,6作废 7.作废被拒 8.采购员申请红字 9.供应商申请红字 10已申请通过红字发票 11.申请红字被拒 12.红字已开票")

    private Integer invoiceState;


    @ApiModelProperty(value = "0：增值税专用发票、1增值税普通发票")

    private Integer invoiceType;
    @ApiModelProperty(value = "0：普通发票、红字发票")

    private Integer invoiceCategory;


    @ApiModelProperty(value = "0:个人 1:单位")

    private Integer riseType;


    @ApiModelProperty(value = "收票人姓名")

    private String userName;


    @ApiModelProperty(value = "收票人地址")

    private String userAddress;


    @ApiModelProperty(value = "收票人联系电话")

    private String userPhone;


    @ApiModelProperty(value = "收票人邮箱")

    private String email;


    @ApiModelProperty(value = "单位名称")

    private String company;


    @ApiModelProperty(value = "单位税号")

    private String dutyParagraph;


    @ApiModelProperty(value = "注册地址")

    private String registerAddress;


    @ApiModelProperty(value = "注册电话")

    private String registerPhone;


    @ApiModelProperty(value = "开户银行")

    private String bank;


    @ApiModelProperty(value = "银行账号")

    private String bankAccount;



    @ApiModelProperty(value = "发票项集合")
    @TableField(exist = false)
    private List<InvoiceDtl> dtls;

    @ApiModelProperty(value = "文件")
    @TableField(exist = false)
    private List<File> files;
    @ApiModelProperty(value = "审核历史")
    @TableField(exist = false)
    private List<AuditRecord> auditList;










    @ApiModelProperty(value = "通过时间")

    private Date gmtApply;


    @ApiModelProperty(value = "申请时间")

    private Date gmtAdopt;


    @ApiModelProperty(value = "发票单号")

    private String invoiceNo;

    @ApiModelProperty(value = "状态")

    private Integer state;

    @ApiModelProperty(value = "对账单类型  1 对账单  2二级对账单")

    private Integer invoiceClass;




    @ApiModelProperty(value = "详细地址")

    private String detailAddr;


    @ApiModelProperty(value = "发票抬头")

    private String invoiceTitle;


    @ApiModelProperty(value = "含税金额")

    private BigDecimal rateAmount;


    @ApiModelProperty(value = "不含税金额")

    private BigDecimal noRateAmount;

    @ApiModelProperty(value = "审核失败原因")
    private String failReason;


    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;

    @ApiModelProperty(value = "对账单id")
     @TableField(exist = false)
    private List<String> reconciliationIds;



    @ApiModelProperty(value = "关联发票id（用于管理红单发票id）")
    private String relevanceId;

    @ApiModelProperty(value = "省")
    @TableField(exist = false)
    private String province;

    @ApiModelProperty(value = "市")
    @TableField(exist = false)
    private String city;

    @ApiModelProperty(value = "县、区")
    @TableField(exist = false)
    private String county;


    @ApiModelProperty(value = "省")
    @TableField(exist = false)
    private String userProvince;

    @ApiModelProperty(value = "市")
    @TableField(exist = false)
    private String userCity;

    @ApiModelProperty(value = "县、区")
    @TableField(exist = false)
    private String userCounty;


}
