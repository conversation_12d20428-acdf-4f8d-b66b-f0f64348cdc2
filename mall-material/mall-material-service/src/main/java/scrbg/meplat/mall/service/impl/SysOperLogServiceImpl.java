package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.SysOperLog;
import scrbg.meplat.mall.mapper.SysOperLogMapper;
import scrbg.meplat.mall.service.SysOperLogService;

import java.util.List;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2024-11-04
 */
@Service
public class SysOperLogServiceImpl extends ServiceImpl<SysOperLogMapper, SysOperLog> implements SysOperLogService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysOperLog> queryWrapper) {
        IPage<SysOperLog> page = this.page(
                new Query<SysOperLog>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(SysOperLog sysOperLog) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(sysOperLog);
    }

    @Override
    public void update(SysOperLog sysOperLog) {
        super.updateById(sysOperLog);
    }


    @Override
    public SysOperLog getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

}
