package scrbg.meplat.mall.entity;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: maill_api
 * @description: 竞价函Excel对象
 * @author: 代文翰
 * @create: 2023-07-21 10:20
 **/
@Data
public class BidLetter implements Serializable {
    @ExcelProperty(value = "序号")
    private String biddingProductId;

    @ExcelProperty(value = "物资名称")
    private String productName;
    @ExcelProperty(value = "规格型号")
    private String spec;
    @ExcelProperty(value = "物资材质")

    private String productTexture;
    @ExcelProperty(value = "计量单位")
    private String unit;
    @ExcelProperty("预估数量")
    private BigDecimal num;

    @ExcelProperty("供货地址")
    private String deliveryAddress;
    @ExcelProperty("供货时间")

    private Date deliveryDate;
    @ExcelProperty("不含税到场单价")

    private BigDecimal bidPrice;
    @ExcelProperty("税率")

    private BigDecimal taxRate;
    @ExcelProperty("含税到场单价")

    private BigDecimal bidRatePrice;
    @ExcelProperty("含税总金额")

    private BigDecimal bidRateAmount;
    @ExcelProperty("不含税总金额")
    private BigDecimal bidAmount;
    @ExcelProperty("备注")

    private String remarks;

}
