package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.OrderShipDtl;
import scrbg.meplat.mall.vo.platform.ListShipByAffirmListVO;
import scrbg.meplat.mall.vo.platform.ShopListShipByAffirmListVO;
import scrbg.meplat.mall.vo.ship.MaterialShipDtlVo;
import scrbg.meplat.mall.vo.ship.ReconcilableMaterialVO;
import scrbg.meplat.mall.dto.ship.ReconcilableMaterialDTO;
import scrbg.meplat.mall.dto.ship.ReconcilableMaterialDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @描述： Mapper 接口
 * @作者: ye
 * @日期: 2023-05-22
 */
@Mapper
@Repository
public interface OrderShipDtlMapper extends BaseMapper<OrderShipDtl> {

    /**
     * 查询物资交易量信息
     * @param dto
     * @return
     */
    int listByAffirmListCount(@Param("dto") Map<String, Object> dto);

    /**
     * 查询物资交易量信息
     *
     * @param pages
     * @param dto
     * @return
     */
    List<ListShipByAffirmListVO> listByAffirmList(Page<ListShipByAffirmListVO> pages, @Param("dto") Map<String, Object> dto);

    /**
     * 查询物资交易量信息、导出
     * @param dto
     * @return
     */
    List<ListShipByAffirmListVO> platformOutputExcel(@Param("dto") Map<String, Object> dto);

    /**
     * 查询物资交易量信息（供应商）
     * @param dto
     * @return
     */
    int shopListByAffirmListCount(@Param("dto") Map<String, Object> dto);

    /**
     * 查询物资交易量信息（供应商）
     *
     * @param pages
     * @param dto
     * @return
     */
    List<ShopListShipByAffirmListVO> shopListByAffirmList(Page<ShopListShipByAffirmListVO> pages, @Param("dto") Map<String, Object> dto);

    /**
     * 导出数据店铺
     * @param dto
     * @return
     */
    List<ShopListShipByAffirmListVO> shopManageOutputExcel(@Param("dto") Map<String, Object> dto);

    List<OrderShipDtl> getTwoOrderShipByBillid(@Param("billId")String billId);

    BigDecimal getCountByOrderItemIdAndType(@Param("orderItemId")String orderItemId, @Param("type")int type);

    BigDecimal selectSumShipNum(@Param("ew") QueryWrapper<OrderShipDtl> q);

    BigDecimal selectSumShipCount( @Param("ew")QueryWrapper<OrderShipDtl> q);


    List<MaterialShipDtlVo> findAllMaterialShipDtlByOrderShipId(@Param("billId") String billId);

    BigDecimal selCountAmount(@Param("dto") Map<String, Object> dto);

    BigDecimal selCountNoRateAmount(@Param("dto") Map<String, Object> dto);

    List<MaterialShipDtlVo> getMaterialShipDzDtlsByOrderShipId(String billId);

    /**
     * 查询一级大宗临购可可对账商品物资列表
     * @param dto 查询参数
     * @return 可对账物资列表
     */
    List<ReconcilableMaterialVO> getReconcilableMaterialList(@Param("dto") ReconcilableMaterialDTO dto);

    /**
     * 查询一级大宗临购可对账商品物资列表
     * @param dto 查询参数
     * @return 总数
     */
    int getReconcilableMaterialListCount(@Param("dto") ReconcilableMaterialDTO dto);
}
