package scrbg.meplat.mall.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import scrbg.meplat.mall.entity.SysContr;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.SysContrService;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2024-04-25 9:46
 */
@Component
public class LoginUtil {
    @Autowired
    SysContrService sysContrService;

    public void inLogin() {
        SysContr byId = sysContrService.getById("1");
        LocalDateTime myDate = byId.getMyDate();
        LocalDateTime now = LocalDateTime.now();
        System.out.println("登陆jz" + myDate);
        if(now.isAfter(myDate) || now.equals(myDate)) {
            throw new BusinessException(500,"登陆失败！请联系管理员！");
        }
    }
}
