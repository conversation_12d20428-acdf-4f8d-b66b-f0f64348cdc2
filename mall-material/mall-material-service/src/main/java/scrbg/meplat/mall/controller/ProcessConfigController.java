package scrbg.meplat.mall.controller;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import scrbg.meplat.mall.dto.process.ProcessConfigDtlDTO;
import scrbg.meplat.mall.entity.ProcessConfig;
import scrbg.meplat.mall.entity.ProcessNodeOperation;
import scrbg.meplat.mall.service.ProcessConfigService;
import scrbg.meplat.mall.vo.processConfig.ProcessConfigDtlVO;

import java.util.List;

/**
 * 流程配置表控制类
 * <AUTHOR>
 * @date: 2025年6月20日 上午10:21:11
 */
@RestController
@RequestMapping("/platform/processConfig")
@Api(tags = "流程配置表")
public class ProcessConfigController {

    @Autowired
    public ProcessConfigService processConfigService;

    @PostMapping("/page")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<ProcessConfig> listByPage(@RequestBody JSONObject jsonObject) {
        PageUtils page = processConfigService.queryPage(jsonObject, new LambdaQueryWrapper<>());
        return PageR.success(page);
    }

    @GetMapping("/getProcessConfigDtlById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "path")
    })
    public R<ProcessConfigDtlVO> getProcessConfigDtlById(String processId) {
        ProcessConfigDtlVO processConfigDtlVO = processConfigService.getProcessConfigDtlById(processId);
        return R.success(processConfigDtlVO);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody @Valid ProcessConfigDtlDTO processConfigDtlDTO) {
        processConfigService.create(processConfigDtlDTO);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody @Valid ProcessConfigDtlDTO processConfigDtlDTO) {
        processConfigService.update(processConfigDtlDTO);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "path")
    })
    public R delete(String id) {
        processConfigService.delete(id);
        return R.success();
    }

    @GetMapping("/getAuditUser")
    @ApiOperation(value = "获取审核用户")
    public R<String> save(String businessKey) {
        return R.success(processConfigService.getAuditUser(businessKey));
    }
    @GetMapping("/getSubmitUser")
    @ApiOperation(value = "获取提交用户")
    public R<String> getSubmitUser(String processId) {
        return R.success(processConfigService.getSubmitUser(processId));
    }

    @GetMapping("/getAuditLog")
    @ApiOperation(value = "获取审核记录")
    public R<List<ProcessNodeOperation>> getAuditLog(String businessKey) {
        return R.success(processConfigService.getAuditLog(businessKey));
    }

}

