package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：
 * @作者: ye
 * @日期: 2023-07-07
 */
@ApiModel(value="")
@Data
@TableName("interface_logs")
public class InterfaceLogs implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "日志id")
    private String logId;

    @ApiModelProperty(value = "秘钥唯一key")
    private String secretKey;

    @ApiModelProperty(value = "业务标识（当多业务相同key，此字段用于区分开业务）(情况少)")
    private String businessFlag;

    @ApiModelProperty(value = "日志类型（1请求远程2请求远程回滚3请求本地4请求本地回滚）")
    private Integer logType;

    @ApiModelProperty(value = "请求方法")
    private String methodName;

    @ApiModelProperty(value = "类包信息")
    private String classPackage;

    @ApiModelProperty(value = "修改前的数据（json）")
    private String updateBefore;

    @ApiModelProperty(value = "本地方法请求参数（json）")
    private String localArguments;

    @ApiModelProperty(value = "请求远程接口参数（json）")
    private String farArguments;

    @ApiModelProperty(value = "是否成功（0否1是）")
    private Integer isSuccess;

    @ApiModelProperty(value = "返回结果（可选）")
    private String result;

    @ApiModelProperty(value = "错误信息（可选）")
    private String errorInfo;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "创建人名称")
    private String founderName;

    @ApiModelProperty(value = "创建人Id")
    private String founderId;

    @ApiModelProperty(value = "备注（记录描述信息）")
    private String remarks;

    @ApiModelProperty(value = "执行时间")
    private String executeTime;

}