package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.PlatformYearFeeDtl;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.PlatformYearFeeDtl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：平台年费明细 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
public interface PlatformYearFeeDtlService extends IService<PlatformYearFeeDtl> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformYearFeeDtl> queryWrapper);

        void create(PlatformYearFeeDtl platformYearFeeDtl);
        void update(PlatformYearFeeDtl platformYearFeeDtl);
        PlatformYearFeeDtl getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
