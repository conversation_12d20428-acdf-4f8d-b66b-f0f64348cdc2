package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.PrivateKeySupplier;
import scrbg.meplat.mall.mapper.PrivateKeySupplierMapper;
import scrbg.meplat.mall.service.PrivateKeySupplierService;

/**
 * @描述：供应商秘钥 服务类
 * @作者: ye
 * @日期: 2023-03-27
 */
@Service
public class PrivateKeySupplierServiceImpl extends ServiceImpl<PrivateKeySupplierMapper, PrivateKeySupplier> implements PrivateKeySupplierService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PrivateKeySupplier> queryWrapper) {
        IPage<PrivateKeySupplier> page = this.page(
        new Query<PrivateKeySupplier>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(PrivateKeySupplier privateKeySupplier) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(privateKeySupplier);
    }

    @Override
    public void update(PrivateKeySupplier privateKeySupplier) {
        super.updateById(privateKeySupplier);
    }


    @Override
    public PrivateKeySupplier getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}