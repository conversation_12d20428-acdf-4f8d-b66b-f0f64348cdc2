package scrbg.meplat.mall.util;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.security.NoSuchAlgorithmException;
import java.util.Scanner;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
/**
 * 前端注册的时候使用的这个加密方法对密码进程对称加密
 */
public class AESUtil {

    /**
     * 默认算法
     */
    private static final String KEY = "hnbWVJUbruSQhcqMFy2rWlSmbW1n8kea"; // 长度必须是 16
//    private static final String KEY = "LxqZ6LFOpiXFI8HM"; // 长度必须是 16
    //private static final String KEY = "********************************";
    private static String iv = "VWz35S5kltEAJ1Q1";//偏移量字符串必须是16位 当模式是CBC的时候必须设置偏移量
    private static String Algorithm = "AES";
    private static String AlgorithmProvider = "AES/CBC/PKCS5Padding"; //算法/模式/补码方式

    public static byte[] generatorKey() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(Algorithm);
        keyGenerator.init(256);//默认128，获得无政策权限后可为192或256
        SecretKey secretKey = keyGenerator.generateKey();
        return secretKey.getEncoded();
    }

    public static IvParameterSpec getIv() throws UnsupportedEncodingException {
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes("utf-8"));
//        System.out.println("偏移量：" + byteToHexString(ivParameterSpec.getIV()));
        return ivParameterSpec;
    }

    public static String encrypt(String src) {
        try {
            byte[] key = KEY.getBytes("utf-8");
            SecretKey secretKey = new SecretKeySpec(key, Algorithm);
            IvParameterSpec ivParameterSpec = getIv();
            Cipher cipher = Cipher.getInstance(AlgorithmProvider);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivParameterSpec);
            byte[] cipherBytes = cipher.doFinal(src.getBytes(Charset.forName("utf-8")));
            return byteToHexString(cipherBytes);
        } catch (Exception ex) {

            return null;
        }
    }

    public static String decrypt(String src) {

        try {
            byte[] key = KEY.getBytes("utf-8");
            SecretKey secretKey = new SecretKeySpec(key, Algorithm);
            IvParameterSpec ivParameterSpec = getIv();
            Cipher cipher = Cipher.getInstance(AlgorithmProvider);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameterSpec);
            byte[] hexBytes = hexStringToBytes(src);
            byte[] plainBytes = cipher.doFinal(hexBytes);
            return new String(plainBytes);
        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * 将byte转换为16进制字符串
     *
     * @param src
     * @return
     */
    public static String byteToHexString(byte[] src) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xff;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                sb.append("0");
            }
            sb.append(hv);
        }
        return sb.toString();
    }

    /**
     * 将16进制字符串装换为byte数组
     *
     * @param hexString
     * @return
     */
    public static byte[] hexStringToBytes(String hexString) {
        hexString = hexString.toUpperCase();
        int length = hexString.length() / 2;
        char[] hexChars = hexString.toCharArray();
        byte[] b = new byte[length];
        for (int i = 0; i < length; i++) {
            int pos = i * 2;
            b[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
        }
        return b;
    }

    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }


    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        // 提示用户选择加密或解密
        System.out.print("请选择操作 (加密: 0, 解密: 1)：");
        int choice = scanner.nextInt();
        scanner.nextLine();  // 读取换行符

        // 提示输入字符串
        System.out.print("请输入字符串：");
        String input = scanner.nextLine();

        // 处理选择
        String result;
        if (choice == 0) {
            result = encrypt(input);
            System.out.println("加密结果： " + result);
        } else if (choice == 1) {
            result = decrypt(input);
            System.out.println("解密结果： " + result);
        } else {
            System.out.println("无效选择，请输入0或1。");
        }

        scanner.close();
    }
}