package scrbg.meplat.mall.entity.parent;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
public class MallBaseEntity {

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    @ApiModelProperty(value = "创建人名称")
    @TableField(value = "founder_name", fill = FieldFill.INSERT)
    private String founderName;

    @ApiModelProperty(value = "创建人Id")
    @TableField(value = "founder_id", fill = FieldFill.INSERT)
    private String founderId;

    @ApiModelProperty(value = "修改人名称")
    @TableField(value = "modify_name", fill = FieldFill.INSERT_UPDATE)
    private String modifyName;

    @ApiModelProperty(value = "修改人Id")
    @TableField(value = "modify_id", fill = FieldFill.INSERT_UPDATE)
    private String modifyId;

    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")
    @TableField(value = "is_delete", fill = FieldFill.INSERT)
    private Integer isDelete;
}
