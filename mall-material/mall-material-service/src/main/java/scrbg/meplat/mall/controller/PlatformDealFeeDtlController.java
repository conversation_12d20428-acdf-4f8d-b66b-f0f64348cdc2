package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.PlatformDealFeeRecordDtl;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.PlatformDealFeeDtlService;
import scrbg.meplat.mall.entity.PlatformDealFeeDtl;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @描述：平台交易费明细控制类
 * @作者: ye
 * @日期: 2024-01-24
 */
@RestController
@RequestMapping("/")
@Api(tags = "平台交易费明细")
public class PlatformDealFeeDtlController {

    @Autowired
    public PlatformDealFeeDtlService platformDealFeeDtlService;

    @Autowired
    MallConfig mallConfig;

    @PostMapping("supplier/platformDealFeeDtl/notPayDealFeeDtlList")
    @ApiOperation(value = "供应商查询待缴费的明细")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {@DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class), @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class), @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class), @DynamicParameter(name = "serveType", value = "服务类型（1店铺交易服务费2合同履约服务费用）", dataTypeClass = List.class),})
    public PageR<PlatformDealFeeDtl> notPayDealFeeDtlList(@RequestBody JSONObject jsonObject) {
        PageUtils page = platformDealFeeDtlService.notPayDealFeeDtlList(jsonObject, new LambdaQueryWrapper<PlatformDealFeeDtl>());
        return PageR.success(page);
    }

    @PostMapping("platform/platformDealFeeRecordDtl/listByEntity")
    @ApiOperation(value = "平台查询缴费的明细")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {@DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class), @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)})
    public PageR<PlatformDealFeeDtl> platformListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = platformDealFeeDtlService.ListByEntity(jsonObject, new LambdaQueryWrapper<PlatformDealFeeDtl>());
        return PageR.success(page);
    }

    @PostMapping("supplier/platformDealFeeRecordDtl/listByEntity")
    @ApiOperation(value = "供应商查询缴费的明细")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {@DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class), @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)})
    public PageR<PlatformDealFeeDtl> supplierListByEntity(@RequestBody JSONObject jsonObject) {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        if (StringUtils.isBlank(enterpriseId)) throw new BusinessException(500, "登陆过期");
        jsonObject.put("enterpriseId", enterpriseId);
        PageUtils page = platformDealFeeDtlService.ListByEntity(jsonObject, new LambdaQueryWrapper<PlatformDealFeeDtl>());
        return PageR.success(page);
    }

    @PostMapping("platform/platformDealFeeRecordDtl/exportExcelFreeDtl")
    @ApiOperation(value = "平台导出供应商交易费用明细")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {@DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class), @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)})
    public void platformExportExcelFreeDtl(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        ArrayList ids = (ArrayList) jsonObject.get("ids");
        if (CollectionUtils.isEmpty(ids)) {
            jsonObject.put("page", 1);
            jsonObject.put("limit", 4000);
        }
        PageUtils<PlatformDealFeeDtl> page = platformDealFeeDtlService.ListByEntity(jsonObject, new LambdaQueryWrapper<PlatformDealFeeDtl>());
        HashMap<String, Object> dataMap = new HashMap<>();
        List<PlatformDealFeeDtl> list = page.getList();
        dataMap.put("dataList", list);
        if (CollectionUtils.isNotEmpty(list)) {
            dataMap.put("totalFree", list.get(0).getTotalFree());
            dataMap.put("paymentAmount", list.get(0).getPaymentAmount());
        }
        String src = mallConfig.templateFormUrl;
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "供应商管理交易费用明细模板.xlsx", src, "供应商管理交易费用明细.xlsx");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("supplier/platformDealFeeRecordDtl/exportExcelFreeDtl")
    @ApiOperation(value = "供应商导出供应商交易费用明细")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {@DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class), @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)})
    public void supplierExportExcelFreeDtl(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        ArrayList ids = (ArrayList) jsonObject.get("ids");
        if (CollectionUtils.isEmpty(ids)) {
            jsonObject.put("page", 1);
            jsonObject.put("limit", 4000);
        }
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        if (StringUtils.isBlank(enterpriseId)) throw new BusinessException(500, "登陆过期");
        jsonObject.put("enterpriseId", enterpriseId);
        PageUtils page = platformDealFeeDtlService.ListByEntity(jsonObject, new LambdaQueryWrapper<PlatformDealFeeDtl>());
        HashMap<String, Object> dataMap = new HashMap<>();
        List<PlatformDealFeeDtl> list = page.getList();
        dataMap.put("dataList", list);
        if (CollectionUtils.isNotEmpty(list)) {
            dataMap.put("totalFree", list.get(0).getTotalFree());
            dataMap.put("paymentAmount", list.get(0).getPaymentAmount());
        }
        String src = mallConfig.templateFormUrl;
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "供应商管理交易费用明细模板.xlsx", src, "供应商管理交易费用明细.xlsx");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
//
//
//    @GetMapping("/findById")
//    @ApiOperation(value = "根据主键查询")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R<PlatformDealFeeDtl> findById(String id) {
//        PlatformDealFeeDtl platformDealFeeDtl = platformDealFeeDtlService.getById(id);
//        return R.success(platformDealFeeDtl);
//    }
//
//    @PostMapping("/create")
//    @ApiOperation(value = "新增")
//    public R save(@RequestBody PlatformDealFeeDtl platformDealFeeDtl) {
//        platformDealFeeDtlService.create(platformDealFeeDtl);
//        return R.success();
//    }
//
//    @PostMapping("/update")
//    @ApiOperation(value = "修改")
//    public R update(@RequestBody PlatformDealFeeDtl platformDealFeeDtl) {
//        platformDealFeeDtlService.update(platformDealFeeDtl);
//        return R.success();
//    }
//
//    @GetMapping("/delete")
//    @ApiOperation(value = "根据主键删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R delete(String id) {
//        platformDealFeeDtlService.delete(id);
//        return R.success();
//    }
//
//
//    @PostMapping("/deleteBatch")
//    @ApiOperation(value = "根据主键批量删除")
//    public R deleteBatch(@RequestBody List<String> ids) {
//        platformDealFeeDtlService.removeByIds(ids);
//        return R.success();
//    }
}

