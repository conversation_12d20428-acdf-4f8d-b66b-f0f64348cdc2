package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.OperateLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.OperateLog;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2024-01-04
 */
public interface OperateLogService extends IService<OperateLog> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OperateLog> queryWrapper);

        void create(OperateLog operateLog);
        void update(OperateLog operateLog);
        OperateLog getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
