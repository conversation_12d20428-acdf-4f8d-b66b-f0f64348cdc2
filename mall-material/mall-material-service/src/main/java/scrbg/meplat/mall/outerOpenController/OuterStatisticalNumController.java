package scrbg.meplat.mall.outerOpenController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: maill_api
 * @description: 统计对外接口
 * @author: 代文翰
 * @create: 2024-05-06 09:54
 **/
@RestController
@RequestMapping("/w/systemStatistics")
@Api(tags = "统计接口")
public class OuterStatisticalNumController {

    @Autowired
    public MallConfig mallConfig;
    @Autowired
    public EnterpriseInfoService enterpriseInfoService;
    @Autowired
    public ShopService shopService;

    @Autowired
    public OrdersService ordersService;

    @Autowired
    public UserService userService;

    @Autowired
    public OrderShipService orderShipService;

    @Autowired
    public ProductService productService;
    @Autowired
    public StatisticalNumService statisticalNumService;

    @Autowired
    MaterialReconciliationService materialReconciliationService;


    @Autowired
    DealOrderInfoService dealOrderInfoService;
    @Resource
    OrderShipDtlService orderShipDtlService;



    @GetMapping("/getStatics")
    public Map<String,Object> getSystemStatisticalData(String key){
        if (key==null || !key.equalsIgnoreCase("materialStaticsMetaData")){
            throw new BusinessException("非安全访问!");
        }
        Map<String,Object> dataMap = new LinkedHashMap<>();
        dataMap.put("统计时间", LocalDateTime.now());

        int supplierNum = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getIsPcwp, 1).isNotNull(EnterpriseInfo::getSocialCreditCode));
        dataMap.put("外部供应商数", supplierNum);

        int shopCoounts = shopService.count(new LambdaQueryWrapper<Shop>().ne(Shop::getAuditStatus, 3));
        dataMap.put("店铺数", shopCoounts);

        int inUserCounts = userService.count(new LambdaQueryWrapper<User>().eq(User::getIsInternalUser, 1));
        dataMap.put("使用过平台路桥用户数", inUserCounts);

        int inEnterPrise = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>().isNotNull(EnterpriseInfo::getInteriorId));
        dataMap.put("使用过平台路桥企业数据", inEnterPrise);

        int upProductNum = productService.count(new LambdaQueryWrapper<Product>().eq(Product::getState, 1));
        dataMap.put("上架商品总数", upProductNum);


        int productNum = productService.count();
        dataMap.put("商品总数", productNum);


        List<Orders> ordersList = ordersService.list(new LambdaQueryWrapper<Orders>().in(Orders::getOrderClass, 1, 2).eq(Orders::getProductType, 10));
        BigDecimal sumAmount = new BigDecimal(0);
        for (Orders orders : ordersList) {
            sumAmount = sumAmount.add(orders.getActualAmount());
        }
        dataMap.put("零星采购订单数", ordersList.size());
        dataMap.put("零星采购订单金额", sumAmount);


        List<OrderShip> orderShips = orderShipService.list(new LambdaQueryWrapper<OrderShip>()
                .eq(OrderShip::getProductType, 10)
                .in(OrderShip::getType, 2, 1));
        BigDecimal orderShipAmount = new BigDecimal(0);
        //for (OrderShip orderShip : orderShips) {
        //orderShipAmount = orderShipAmount.add(orderShip.getRateAmount());
        for (OrderShip orderShip : orderShips) {
            List<OrderShipDtl> itemList = orderShipDtlService.lambdaQuery().eq(OrderShipDtl::getBillId, orderShip.getBillId()).list();
            for (OrderShipDtl orderShipDtl : itemList) {
                // 已退货
                BigDecimal returnCounts = orderShipDtl.getReturnCounts();
                // 已收货数量
                BigDecimal shipNum = orderShipDtl.getShipNum();
                BigDecimal productPrice = orderShipDtl.getProductPrice();
                BigDecimal realAmount = shipNum.subtract(returnCounts).multiply(productPrice).setScale(2, BigDecimal.ROUND_UP);
                orderShipAmount = orderShipAmount.add(realAmount);
            }
        }
        //}
        dataMap.put("零星采购发货金额", orderShipAmount);

        List<Orders> contractPlanLists = ordersService.list(new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrderClass, 1, 2)
                .eq(Orders::getProductType, 12));
        dataMap.put("大宗订单数", contractPlanLists.size());


        List<MaterialReconciliation> list = materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getBusinessType, 1)
                .eq(MaterialReconciliation::getState, 3).list();
        BigDecimal toavbleA = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list) {
            toavbleA = toavbleA.add(materialReconciliation.getReconciliationAmount());
        }
        dataMap.put("大宗月供订单对账总金额", toavbleA);


        List<Orders> clgOrderLists = ordersService.list(new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrderClass, 1, 2)
                .eq(Orders::getProductType, 13));
        dataMap.put("大宗临购订单数", clgOrderLists.size());


        BigDecimal sumAmount2 = new BigDecimal(0);
        for (Orders orders : clgOrderLists) {
            sumAmount2 = sumAmount2.add(orders.getActualAmount());
        }
        dataMap.put("大宗临购订单金额", sumAmount2);

        List<MaterialReconciliation> list2 = materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getBusinessType, 2)
                .eq(MaterialReconciliation::getState, 3).list();
        BigDecimal toavbleA2 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list2) {
            toavbleA2 = toavbleA2.add(materialReconciliation.getReconciliationAmount());
        }
        dataMap.put("零星采购对账金额", toavbleA2);

        List<DealOrderInfo> dealOrderInfos = dealOrderInfoService.lambdaQuery()
                .list();
        BigDecimal bigDecimal = new BigDecimal(0);
        for (DealOrderInfo dealOrderInfo : dealOrderInfos) {
            bigDecimal = bigDecimal.add(dealOrderInfo.getAmount());
        }
        dataMap.put("结算总金额", bigDecimal);
        return dataMap;
    }




}
