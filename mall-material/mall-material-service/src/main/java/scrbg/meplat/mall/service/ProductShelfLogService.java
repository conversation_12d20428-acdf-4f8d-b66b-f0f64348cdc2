package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ProductShelfLog;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ProductShelfLogService extends IService<ProductShelfLog> {
    void deleteByLogId(String logId);

    PageUtils queryByProductId(JSONObject jsonObject);

    void saveProductShelfLog(ProductShelfLog productShelfLog);

    void exportShelfLogExcelFile(List<String> id, HttpServletResponse response);
}
