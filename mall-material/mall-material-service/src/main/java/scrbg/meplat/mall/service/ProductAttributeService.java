package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ProductAttribute;

/**
 * @描述：商品属性 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface ProductAttributeService extends IService<ProductAttribute> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductAttribute> queryWrapper);

    void create(ProductAttribute productAttribute);

    void update(ProductAttribute productAttribute);

    ProductAttribute getById(String id);

    void delete(String id);
}
