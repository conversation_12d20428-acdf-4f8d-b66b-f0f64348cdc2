package scrbg.meplat.mall.mapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.AskAnswer;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.vo.user.userCenter.OutBoxVo;

import java.util.List;
import java.util.Map;

/**
 * @描述：发件详情——企业供应商和对应店铺 Mapper 接口
 *
 */
@Mapper
@Repository
public interface OutBoxMapper extends BaseMapper<EnterpriseInfo> {
    List<OutBoxVo> getSupplyShop(IPage<OutBoxVo> pages, QueryWrapper<OutBoxVo> q, @Param("dto") Map<String, Object> innerMap);

}

