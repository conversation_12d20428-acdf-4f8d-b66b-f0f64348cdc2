package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.MaterialMasterPlan;
import scrbg.meplat.mall.mapper.MaterialMasterPlanMapper;
import scrbg.meplat.mall.service.MaterialMasterPlanService;

/**
 * @描述：物资总计划 服务类
 * @作者: y
 * @日期: 2022-11-10
 */
@Service
public class MaterialMasterPlanServiceImpl extends ServiceImpl<MaterialMasterPlanMapper, MaterialMasterPlan> implements MaterialMasterPlanService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMasterPlan> queryWrapper) {
        IPage<MaterialMasterPlan> page = this.page(
                new Query<MaterialMasterPlan>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(MaterialMasterPlan materialMasterPlan) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(materialMasterPlan);
    }

    @Override
    public void update(MaterialMasterPlan materialMasterPlan) {
        super.updateById(materialMasterPlan);
    }


    @Override
    public MaterialMasterPlan getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
