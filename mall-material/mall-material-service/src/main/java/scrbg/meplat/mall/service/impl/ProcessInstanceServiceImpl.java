package scrbg.meplat.mall.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import scrbg.meplat.mall.entity.ProcessInstance;
import scrbg.meplat.mall.mapper.ProcessInstanceMapper;
import scrbg.meplat.mall.service.ProcessInstanceService;

/**
 * 流程实例表 服务类
 * <AUTHOR>
 * @date: 2025年6月20日 上午10:06:41
 */
@Service
public class ProcessInstanceServiceImpl extends ServiceImpl<ProcessInstanceMapper, ProcessInstance> implements ProcessInstanceService {

    @Autowired
    ProcessInstanceService processInstanceService;
    @Autowired
    ProcessInstanceMapper processInstanceMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProcessInstance processInstance) {
        super.save(processInstance);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProcessInstance processInstance) {
        super.updateById(processInstance);
    }

    @Override
    public ProcessInstance getById(String id) {
        return super.getById(id);
    }
    
    @Override
    public ProcessInstance getProcessInstanceByKey(String businessKey) {
        ProcessInstance processInstance =  processInstanceMapper.getProcessInstanceByKey(businessKey);
        return processInstance;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        super.removeById(id);
    }

}
