package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.OrderPaymentInfo;

/**
 * @描述：支付信息表 服务类
 * @作者: y
 * @日期: 2022-11-08
 */
public interface OrderPaymentInfoService extends IService<OrderPaymentInfo> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderPaymentInfo> queryWrapper);

    void create(OrderPaymentInfo orderPaymentInfo);

    void update(OrderPaymentInfo orderPaymentInfo);

    OrderPaymentInfo getById(String id);

    void delete(String id);
}
