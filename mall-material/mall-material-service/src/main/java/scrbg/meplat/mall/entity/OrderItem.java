package scrbg.meplat.mall.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：订单项
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "订单项")
@Data
@TableName("order_item")
public class OrderItem extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "订单项id")
    private String orderItemId;

    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "订单号")
    private String orderSn;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品编号")
    private String productSn;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品图片")
    private String productImg;

    @ApiModelProperty(value = "skuid")
    private String skuId;

    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "商品成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "总成本价")
    private BigDecimal costAmount;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "购买数量")
    private BigDecimal buyCounts;

    @ApiModelProperty(value = "确认收货数量")
    private BigDecimal confirmCounts;

    @ApiModelProperty(value = "商品总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "评论状态： 0 未评价  1 已评价")
    private Integer isComment;

    @ApiModelProperty(value = "购买时间")
    private Date buyTime;

    @ApiModelProperty(value = "订单明细状态（0默认不使用1已选择供方2待分配3待分配竞价4已生成竞价）")
    private Integer state;

    @ApiModelProperty(value = "商品类型：0 低值易耗品 1大宗临购 2、周转材料")
    private Integer productType;

    @ApiModelProperty(value = "租赁时长")
    private BigDecimal leaseNum;

    @ApiModelProperty(value = "租赁单位（天月年）")
    private String leaseUnit;

    @ApiModelProperty(value = "sku单位")
    private String unit;

    @Version
    private Integer version;//乐观锁

    @ApiModelProperty(value = "关联计划id（逗号分隔）废弃")
    private String billId;

    @ApiModelProperty(value = "关联计划明细id（逗号分割）废弃")
    private String dtlId;

    @ApiModelProperty(value = "关联名称（用于关联外部的名称唯一不修改）废弃")
    private String relevanceName;

    @ApiModelProperty(value = "关联编号")
    private String relevanceNo;

    @ApiModelProperty(value = "关联id")
    private String relevanceId;

    @ApiModelProperty(value = "  退货状态 1:审核中 2:审核成功 3:审核失败 4:退货完成）")
    private Integer returnState;

    @ApiModelProperty(value = "发票状态  0：未申请  1：已申请")
    private String invoiceState;

    @ApiModelProperty(value = "利润")
    private BigDecimal profitPrice;

    @ApiModelProperty(value = "发货数量")
    private BigDecimal shipCounts;

    @ApiModelProperty(value = "供应商id（本地机构id）主要用户拆单分组生成订单用（都是取商品实际的供应商id）")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称）")
    @TableField(exist = false)
    private String supplierName;
    @ApiModelProperty(value = "客户）")
    @TableField(exist = false)
    private String enterpriseName;

    @ApiModelProperty(value = "店铺名称）")
    @TableField(exist = false)
    private String shopName;

    @ApiModelProperty(value = "完成时间）")
    @TableField(exist = false)
    private String flishTime;

    @ApiModelProperty(value = "父订单明细id（是拆单订单这里才有值）")
    private String parentOrderItemId;

    @ApiModelProperty(value = "商城退货数量")
    private BigDecimal returnCounts;

    @ApiModelProperty(value = "分类路径名称（xxx/xxx/xxx）")
    private String classPathName;

    @ApiModelProperty(value = "分类路径id（xxx/xxx/xxx）")
    private String classPathId;

    @ApiModelProperty(value = "分类id")
    private String classId;

    @ApiModelProperty(value = "品牌id")
    private String brandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "材质")
    private String texture;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal noRatePrice;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal noRateAmount;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "网价（浮动价格使用）")
    private BigDecimal netPrice;

    @ApiModelProperty(value = "固定费用（浮动价格使用）")
    private BigDecimal fixationPrice;

    @ApiModelProperty(value = "出厂价（固定价格使用）")
    private BigDecimal outFactoryPrice;

    @ApiModelProperty(value = "运杂费（固定价格使用）")
    private BigDecimal transportPrice;

    @ApiModelProperty(value = "是否有二级单位")

    private Integer isTwoUnit;
    @ApiModelProperty(value = "二级单位")

    private String twoUnit;
    @ApiModelProperty(value = "二级单位购买数量")

    private BigDecimal twoUnitNum;
    @ApiModelProperty(value = "临购副单位对应主单位数量系数")

    private BigDecimal secondUnitNum;
    @ApiModelProperty(value = "pcwp退货数量")

    private BigDecimal pcwpReturn;

    @ApiModelProperty(value = "账期，下单时选择")
    private Integer paymentPeriod;

    @ApiModelProperty(value = "总商品成本价")
    @TableField(exist = false)
    private BigDecimal costPriceSum;

    @ApiModelProperty(value = "总原价")
    @TableField(exist = false)
    private BigDecimal originalPriceSum;

    @ApiModelProperty(value = "最大发货数量（最新版）")
    @TableField(exist = false)
    private BigDecimal maxSendQty;

    @ApiModelProperty(value = "超量（废弃）")
    @TableField(exist = false)
    private BigDecimal aboveQty;

    @ApiModelProperty(value = "最高价")
    @TableField(exist = false)
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "图标题（保存在数组第一个）")
    @TableField(exist = false)
    List<String> labelTitle;

    @ApiModelProperty(value = "图数量（保存在数组第一个）")
    @TableField(exist = false)
    List<Integer> count;

    @ApiModelProperty(value = "图总利润（保存在数组第一个）")
    @TableField(exist = false)
    List<BigDecimal> profitPriceTotals;

    @ApiModelProperty(value = "手动关闭订单，pcwp退货打开订单记录最大发货数量")
    private BigDecimal closeMaxQty;

    @ApiModelProperty(value = "二级出厂价")
    @TableField(exist = false)
    private BigDecimal twoOutFactoryPrice;

    @ApiModelProperty(value = "二级运杂费")
    @TableField(exist = false)
    private BigDecimal twoTransportPrice;

    @ApiModelProperty(value = "二级网价")
    @TableField(exist = false)
    private BigDecimal twoNetPrice;

    @ApiModelProperty(value = "二级固定费用")
    @TableField(exist = false)
    private BigDecimal twoFixationPrice;

    @ApiModelProperty(value = "评价照片")
    @TableField(exist = false)
    List<File> commentFile;

    @ApiModelProperty(value = "评价内容")
    @TableField(exist = false)
    private String commentContent;

    @ApiModelProperty(value = "评价图片")
    @TableField(exist = false)
    private String CommentImgs;

    @ApiModelProperty(value = "评价id")
    @TableField(exist = false)
    private String CommentId;
}
