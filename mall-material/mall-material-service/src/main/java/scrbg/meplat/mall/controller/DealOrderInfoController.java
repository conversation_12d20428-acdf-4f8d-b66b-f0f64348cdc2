package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.DealOrderInfoService;
import scrbg.meplat.mall.entity.DealOrderInfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @描述：结算订单明细控制类
 * @作者: ye
 * @日期: 2023-06-16
 */
@RestController
@RequestMapping("/")
@Api(tags = "结算订单明细")
public class DealOrderInfoController {

    @Autowired
    public DealOrderInfoService dealOrderInfoService;

    @PostMapping("shopManage/dealOrderInfo/listByEntity")
    @ApiOperation(value = "店铺查询结算信息")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<DealOrderInfo> shopListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = dealOrderInfoService.shopListByEntity(jsonObject, new LambdaQueryWrapper<DealOrderInfo>());
        return PageR.success(page);
    }


    @PostMapping("shopManage/dealOrderInfo/outputExcel")
    @ApiOperation(value = "导出数据")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "ids", value = "导出的id", dataTypeClass = List.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public R outputExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
         dealOrderInfoService.outputExcel(jsonObject, new QueryWrapper<DealOrderInfo>(),response);
        return R.success();
    }



    @PostMapping("platform/dealOrderInfo/listByEntity")
    @ApiOperation(value = "平台查询结算信息")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "dealType", value = "结算类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "bugOrgName", value = "客户", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<DealOrderInfo> platformListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = dealOrderInfoService.platformListByEntity(jsonObject, new LambdaQueryWrapper<DealOrderInfo>());
        return PageR.success(page);
    }



    @PostMapping("platform/dealOrderInfo/outputExcel1")
    @ApiOperation(value = "导出数据平台(旧平台结算)")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
//            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "ids", value = "导出的id", dataTypeClass = List.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public R platformOutputExcel1(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        dealOrderInfoService.platformOutputExcel(jsonObject, new QueryWrapper<DealOrderInfo>(),response);
        return R.success();
    }
    @PostMapping("platform/dealOrderInfo/outputExcel")
    @ApiOperation(value = "导出数据平台")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
//            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "ids", value = "导出的id", dataTypeClass = List.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public R platformOutputExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        dealOrderInfoService.platformOutputExcel2(jsonObject, new QueryWrapper<DealOrderInfo>(),response);
        return R.success();
    }


    @PostMapping("shopManage/dealOrderInfo/vendorlistByEntity")
    @ApiOperation(value = "供应商平台查询结算信息")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class)
    })
    public PageR<DealOrderInfo> vendorListByEntity(@RequestBody JSONObject vendorjsonObject )  {
        PageUtils page = dealOrderInfoService.vendorListByEntity(vendorjsonObject, new LambdaQueryWrapper<DealOrderInfo>());
        return PageR.success(page);
    }

    @PostMapping("platform/dealOrderInfo/supplyOutputExcel")
    @ApiOperation(value = "导出供应数据平台")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "startFinishDate", value = "完成开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endFinishDate", value = "完成结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（交易金额）", dataTypeClass = String.class),
            @DynamicParameter(name = "ids", value = "导出的id", dataTypeClass = List.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public R supplyPlatformOutputExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        dealOrderInfoService.supplyPlatformOutputExcel(jsonObject, new QueryWrapper<DealOrderInfo>(),response);
        return R.success();
    }

//    @PostMapping("/listByEntity")
//    @ApiOperation(value = "根据实体属性分页查询")
//    @DynamicParameters(name = "根据实体属性分页查询", properties = {
//            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
//            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
//    })
//    public PageR<DealOrderInfo> listByEntity(@RequestBody JSONObject jsonObject) {
//        PageUtils page = dealOrderInfoService.queryPage(jsonObject, new LambdaQueryWrapper<DealOrderInfo>());
//        return PageR.success(page);
//    }

    @GetMapping("dealOrderInfo/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<DealOrderInfo> findById(String id) {
        DealOrderInfo dealOrderInfo = dealOrderInfoService.getById(id);
        return R.success(dealOrderInfo);
    }

    @PostMapping("dealOrderInfo/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody DealOrderInfo dealOrderInfo) {
        dealOrderInfoService.create(dealOrderInfo);
        return R.success();
    }

    @PostMapping("dealOrderInfo/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody DealOrderInfo dealOrderInfo) {
        dealOrderInfoService.update(dealOrderInfo);
        return R.success();
    }

    @GetMapping("dealOrderInfo/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        dealOrderInfoService.delete(id);
        return R.success();
    }


    @PostMapping("dealOrderInfo/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        dealOrderInfoService.removeByIds(ids);
        return R.success();
    }
}

