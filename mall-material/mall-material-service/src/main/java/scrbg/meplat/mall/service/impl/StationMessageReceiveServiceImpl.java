package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.StationMessageReceive;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.mapper.OutBoxMapper;
import scrbg.meplat.mall.mapper.StationMessageReceiveMapper;
import scrbg.meplat.mall.service.OutBoxService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.StationMessageReceiveService;
import scrbg.meplat.mall.service.UserService;
import scrbg.meplat.mall.util.R;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.message.RemindMessageVo;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;
import scrbg.meplat.mall.vo.product.material.StationMessageReceiveVo;
import scrbg.meplat.mall.vo.user.userCenter.OutBoxVo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @描述：站点接收消息 服务类
 * @作者: y
 * @日期: 2022-11-24
 */
@Service
public class StationMessageReceiveServiceImpl extends ServiceImpl<StationMessageReceiveMapper, StationMessageReceive> implements StationMessageReceiveService {

    @Autowired
    UserService userService;
    @Autowired
    private MallConfig mallConfig;
    @Autowired
    private StationMessageReceiveMapper stationMessageReceiveMapper;
    @Autowired
    public ShopService shopService;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    public  OutBoxService outBoxService;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, QueryWrapper<StationMessageReceiveVo> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        wrapper.eq("r.mall_type", mallConfig.mallType);
        String title = (String) innerMap.get("title");
        if (!StringUtils.isEmpty(title)) {
            wrapper.eq("s.title", title);
        }

        Integer allRead = (Integer) innerMap.get("allRead");
        if (!StringUtils.isEmpty(allRead)) {
            wrapper.eq("s.all_read", allRead);
        }
        String startDate = (String) innerMap.get("startDate");
        if (!StringUtils.isEmpty(startDate)) {
            wrapper.gt("s.send_date", startDate);
        }
        String endDate = (String) innerMap.get("endDate");
        if (!StringUtils.isEmpty(endDate)) {
            wrapper.lt("s.send_date", endDate);
        }
        String keywords = (String) innerMap.get("keywords");
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.like("s.title", keywords).or()
                    .eq("s.send_date", keywords);
        }
        wrapper.orderByDesc("s.send_date");
        wrapper.eq("r.is_delete", 0);
        IPage<StationMessageReceiveVo> pages = new Query<StationMessageReceiveVo>().getPage(jsonObject);
        List<StationMessageReceiveVo> list = stationMessageReceiveMapper.findByCondition(pages, wrapper);
        pages.setRecords(list);
        return new PageUtils(pages);
    }

    @Override
    public void create(StationMessageReceive stationMessageReceive) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(stationMessageReceive);
    }

    @Override
    public void update(StationMessageReceive stationMessageReceive) {
        super.updateById(stationMessageReceive);
    }


    @Override
    public StationMessageReceive getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public List<StationMessageReceive> getMessageNum(Integer receiveType) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        LambdaQueryWrapper<StationMessageReceive> wrapper = new LambdaQueryWrapper<>();
        if (receiveType == 0) {
            wrapper.eq(StationMessageReceive::getReceiveId, user.getShopId());
        } else if (receiveType == 1) {
            wrapper.eq(StationMessageReceive::getReceiveId, user.getUserId());
        }

        wrapper.eq(StationMessageReceive::getReceiveType, receiveType)
                .eq(StationMessageReceive::getIsRead, "0")
                .eq(StationMessageReceive::getMallType, mallConfig.mallType);
        List<StationMessageReceive> list = super.list(wrapper);
        return list;
    }

    @Override
    public void addBath(StationMessageReceiveVO vo) {
        //保存收件人信息
        ArrayList<String> receiveList = vo.getReceiveList();
        if (receiveList != null && receiveList.size() > 0) {
            if (mallConfig.mallType == 1) {
                List<User> userList = userService.listByIds(receiveList);
                ArrayList<StationMessageReceive> receiveArrayList = new ArrayList<>();
                if (userList != null && userList.size() > 0) {
                    for (User userInfo : userList) {
                        StationMessageReceive receive = new StationMessageReceive();
                        receive.setStationMessageId(vo.getStationMessageId());
                        receive.setReceiveCode(userInfo.getUserNumber());
                        receive.setReceiveId(userInfo.getUserId());
                        if (userInfo.getRealName() != null) {
                            receive.setReceiveName(userInfo.getRealName());
                        }
                        receive.setReceiveType(1);
                        receive.setIsRead(0);
                        receiveArrayList.add(receive);
                    }
                    saveBatch(receiveArrayList);
                }
            } else {
                List<User> list= userService.getUserListByids(receiveList);
                if (!CollectionUtils.isEmpty(list)) {
                    ArrayList<StationMessageReceive> userlist = new ArrayList<>();
                    for (User user : list) {
                        StationMessageReceive receive = new StationMessageReceive();
                        receive.setStationMessageId(vo.getStationMessageId());
                        receive.setReceiveCode(user.getUserNumber());
                        receive.setReceiveId(user.getUserId());
                        if(user.getRealName() != null) {
                            receive.setReceiveName(user.getRealName());
                        }
                        receive.setReceiveType(1);
                        receive.setIsRead(0);
                        userlist.add(receive);
                    }
                    saveBatch(userlist);
                }
            }

        }
        ArrayList<String> enterpriseIdList = vo.getEnterpriseIdList();
        if (enterpriseIdList != null && enterpriseIdList.size() > 0) {
            ArrayList<StationMessageReceive> suppliersList = new ArrayList<>();
            List<EnterpriseInfo> enterprises = outBoxService.listByIds(enterpriseIdList);

            for (EnterpriseInfo outBoxVo : enterprises) {
                StationMessageReceive receive = new StationMessageReceive();
                receive.setStationMessageId(vo.getStationMessageId());
                receive.setReceiveId(outBoxVo.getEnterpriseId());
                receive.setReceiveName(outBoxVo.getEnterpriseName());
                receive.setReceiveType(3);
                receive.setIsRead(0);

//                取消了账号不是null
                receive.setReceiveCode(null);
                suppliersList.add(receive);
            }
            saveBatch(suppliersList);
        }
    }

    @Override
    public void changStateById(String id) {
        StationMessageReceive messageReceive = getById(id);
        if (messageReceive.getIsRead() == 0) {
            messageReceive.setIsRead(1);
            updateById(messageReceive);
        }

    }

    @Override
    public PageUtils receivePageList(JSONObject jsonObject, LambdaQueryWrapper<StationMessageReceive> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        wrapper.eq(StationMessageReceive::getMallType, mallConfig.mallType);
        String stationMessageId = (String) innerMap.get("stationMessageId");
        if (!StringUtils.isEmpty(stationMessageId)) {
            wrapper.eq(StationMessageReceive::getStationMessageId, stationMessageId);
        }
        String gmtCreate = (String) innerMap.get("gmtCreate");
        if (!StringUtils.isEmpty(gmtCreate)) {
            wrapper.eq(StationMessageReceive::getGmtCreate, gmtCreate);
        }
        String receiveCode = (String) innerMap.get("receiveCode");
        if (!StringUtils.isEmpty(receiveCode)) {
            wrapper.eq(StationMessageReceive::getReceiveCode, receiveCode);
        }
        Integer isRead = (Integer) innerMap.get("isRead");
        if (!StringUtils.isEmpty(isRead)) {
            wrapper.eq(StationMessageReceive::getIsRead, isRead);
        }
        String keywords = (String) innerMap.get("keywords");
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.and(i -> i.like(StationMessageReceive::getReceiveName, keywords)
                    .or().like(StationMessageReceive::getReceiveCode, keywords));
        }
        wrapper.orderByDesc(StationMessageReceive::getGmtCreate);
        IPage<StationMessageReceive> page = this.page(
                new Query<StationMessageReceive>().getPage(jsonObject),
                wrapper
        );
        return new PageUtils(page);

    }

    @Override
    public void updateBathState(List receiceId) {
        List<StationMessageReceive> receives = listByIds(receiceId);
        if (receives.size() > 0) {
            for (StationMessageReceive receive : receives) {
                receive.setIsRead(1);
            }
            updateBatchById(receives);
        }

    }
    @Override
    public Map<String,Object> getRemindMessage() {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String userId = user.getUserId();
        String shopId = user.getShopId();
        Map<String,Object> map = new HashMap<>(2);
        // 查询消息条数
        Integer count = baseMapper.getRemindMessageCount(userId,shopId);
        map.put("count",count);
        // 查询消息列表
        if (count>0){
            List<RemindMessageVo> list = baseMapper.getRemindMessage(userId,shopId);
            map.put("list",list);
        }
        return map;
    }
}
