package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.SupplierReconciliation;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SupplierReconciliation;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.entity.SupplierReconciliationDtl;
import scrbg.meplat.mall.vo.supplier.SupplierReconciliationVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
/**
 * @描述：物资验收 服务类
 * @作者: ye
 * @日期: 2023-08-15
 */
public interface SupplierReconciliationService extends IService<SupplierReconciliation> {
        /**
         * 查询二级对账单列表
         * @param jsonObject
         * @param queryWrapper
         * @return
         */
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SupplierReconciliation> queryWrapper);

        /**
         * 新增二级对账单
         * @param supplierReconciliation
         */
        void create(SupplierReconciliation supplierReconciliation);

        /**
         * 修改二级对账单
         * @param supplierReconciliation
         */
        void update(SupplierReconciliation supplierReconciliation);
        SupplierReconciliation getById(String id);

        /**
         * 删除二级对账单
         * @param id
         */
        void delete(String id);

        void deleteBatch( List<String> ids);

        /**
         *    根据供应商id生成二级对账单编号
         * @param enterpriseId
         * @return
         */
        String createMaterialPlanNo(String enterpriseId);

        /**
         * 主动推送对账单到PCWP
         * @param billId 对账单ID
         */
        void supplierReconciliationPushAcceptance(String billId);

        /**
         * 根据编号获取明细
         * @param billNo
         * @return
         */
        SupplierReconciliation getByNo(String billNo);
        /**
         * 批量提交
         * @param ids
         */
        void reconciliationSubmit(List<String> ids);
        /**
         * 供应商确认单据
         * @param billId
         */
        void twoSupplierReconciliationSupplierAffirm(String billId);
        /**
         * 供应商新增对账
         * @param dto
         */
        void twoSupplierReconciliationSupplierCreate(SupplierReconciliation dto);

        /**
         * 改变对账单状态
         * @param billId
         * @param state
         */
        void updateState(String billId, int state);

        /**
         * 供应商确认单据
         * @param billId
         */
        void supplierReconciliationSupplierAffirm(String billId);

        /**
         * 供应商修改对账单
         * @param dto
         */
        void saveAndUpdate(SupplierReconciliation dto);

        /**
         * 导出二级对账单
         * @param billId
         * @param response
         */
        void outputExcel(String billId, HttpServletResponse response);
}


