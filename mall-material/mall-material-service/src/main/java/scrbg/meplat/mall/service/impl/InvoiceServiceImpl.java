package scrbg.meplat.mall.service.impl;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.InvoiceMapper;
import scrbg.meplat.mall.service.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.TaxCalculator;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.invoice.AuditRecordVo;
import scrbg.meplat.mall.vo.invoice.DataListPassVo;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @描述：发票 服务类
 * @作者: ye
 * @日期: 2023-11-13
 */
@Service
public class InvoiceServiceImpl extends ServiceImpl<InvoiceMapper, Invoice> implements InvoiceService {


    @Autowired
    InvoiceDtlService invoiceDtlService;


    @Autowired
    InvoiceRecordService invoiceRecordService;
    @Autowired
    MaterialReconciliationService materialReconciliationService;
    @Autowired
    MaterialReconciliationDtlService materialReconciliationDtlService;

    @Autowired
    SupplierReconciliationService supplierReconciliationService;
    @Autowired
    SupplierReconciliationDtlService supplierReconciliationDtlService;


    @Autowired
    FileService fileService;
    @Autowired
    EnterpriseInfoService enterpriseInfoService;
    @Autowired
    AuditRecordService auditRecordService;
    @Resource
    private MallConfig mallConfig;


    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Invoice> queryWrapper) {
        // 履约平台发票数据权限控制
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        String enterpriseId =currentUser.getEnterpriseId();
        if (mallConfig.profilesActive.equals("dev")){
            Integer dataSelect = (Integer)jsonObject.get("dataSelect");
            List dataScopes = (List) jsonObject.get("dataScope");
            // PCWP的机构ID转换本地机构ID进行查询
            String roleName = RoleEnum.ROLE_5.getName();
            List<MallRole> mallRoles = currentUser.getMallRoles();
            if (CollectionUtils.isEmpty(mallRoles) || mallRoles.stream().noneMatch(t -> t.getName().equals(roleName))){
                throw new BusinessException(500, "当前用户无物资采购平台履约系统权限！");
            }
            MallRole role = mallRoles.stream().filter(t -> t.getName().equals(roleName)).findFirst().get();

            List<String> OrgIds = currentUser.getOrgAndSon().stream().map(t -> t.getOrgId()).collect(Collectors.toList());
            List<String> localOrgIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(OrgIds);
            // 根据dataSelect进行查询
            if(dataSelect != null && dataSelect == 1 && role.getOrgSearch() == 1) {
                // 全部
                queryWrapper.in(Invoice::getEnterpriseId, localOrgIds);
            }else if (dataSelect!= null && dataSelect == 2){
                // 自己
                queryWrapper.in(Invoice::getEnterpriseId, enterpriseId);
            } else if (dataSelect!= null && dataSelect == 3 && role.getOrgSearch() == 1){
                // 指定
                if (!CollectionUtils.isEmpty(dataScopes)){
                    List<String> localEnterpriseByOrgIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(dataScopes);
                    queryWrapper.in(!CollectionUtils.isEmpty(localEnterpriseByOrgIds),Invoice::getEnterpriseId, localEnterpriseByOrgIds);
                    queryWrapper.in(CollectionUtils.isEmpty(localEnterpriseByOrgIds),Invoice::getEnterpriseId, enterpriseId);
                }else {
                    // 查询除自己之外的其他机构数据
                    List<String> orgList = OrgIds.stream().filter(t -> !t.equalsIgnoreCase(currentUser.getOrgId())).collect(Collectors.toList());
                    List<String> localEnterpriseByOrgIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(orgList);
                    queryWrapper.in(!CollectionUtils.isEmpty(localEnterpriseByOrgIds),Invoice::getEnterpriseId, localEnterpriseByOrgIds);
                    queryWrapper.in(CollectionUtils.isEmpty(localEnterpriseByOrgIds),Invoice::getEnterpriseId, enterpriseId);
                }
            }
            // 查自己的
            else {
                queryWrapper.eq(Invoice::getEnterpriseId, enterpriseId);
            }

        }else {
            queryWrapper.eq(Invoice::getEnterpriseId, enterpriseId);
        }
        queryWrapper(jsonObject, queryWrapper);

        IPage<Invoice> page = this.page(
                new Query<Invoice>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    private void queryWrapper(JSONObject jsonObject, LambdaQueryWrapper<Invoice> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String bankAccount = (String) innerMap.get("bankAccount");
        String bank = (String) innerMap.get("bank");
        Integer invoiceType = (Integer) innerMap.get("invoiceType");
        Integer riseType = (Integer) innerMap.get("riseType");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Integer invoiceCategory = (Integer) innerMap.get("invoiceCategory");
        String state = (String) innerMap.get("state");
        String userName = (String) innerMap.get("belowPrice");
        String userAddress = (String) innerMap.get("userAddress");
        String userPhone = (String) innerMap.get("userPhone");
        ArrayList<Integer> invoiceState = (ArrayList<Integer>) innerMap.get("invoiceState");
        String company = (String) innerMap.get("company");
        String dutyParagraph = (String) innerMap.get("dutyParagraph");
        queryWrapper.eq(StringUtils.isNotEmpty(state), Invoice::getState, state);
        queryWrapper.in(invoiceState != null, Invoice::getInvoiceState, invoiceState);
        queryWrapper.eq(riseType != null, Invoice::getRiseType, riseType);
        queryWrapper.eq(invoiceType != null, Invoice::getInvoiceType, invoiceType);
        queryWrapper.eq(invoiceCategory != null, Invoice::getInvoiceCategory, invoiceCategory);

        queryWrapper.like(StringUtils.isNotEmpty(bankAccount), Invoice::getBankAccount, bankAccount);
        queryWrapper.like(StringUtils.isNotEmpty(bank), Invoice::getBank, bank);
        queryWrapper.like(StringUtils.isNotEmpty(userName), Invoice::getUserName, userName);
        queryWrapper.like(StringUtils.isNotEmpty(userAddress), Invoice::getUserAddress, userAddress);
        queryWrapper.like(StringUtils.isNotEmpty(userPhone), Invoice::getUserPhone, userPhone);
        queryWrapper.like(StringUtils.isNotEmpty(company), Invoice::getCompany, company);
        queryWrapper.like(StringUtils.isNotEmpty(dutyParagraph), Invoice::getDutyParagraph, dutyParagraph);
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(Invoice::getUserName, keywords)
                        .or()
                        .like(Invoice::getBank, keywords)
                        .or()
                        .like(Invoice::getUserPhone, keywords)
                        .or()
                        .like(Invoice::getUserAddress, keywords)
                        .or()
                        .like(Invoice::getCompany, keywords)
                        .or()
                        .like(Invoice::getDutyParagraph, keywords)
                        .or()
                        .like(Invoice::getBankAccount, keywords);

            });
        }
        queryWrapper.orderByDesc(Invoice::getState);
        if (orderBy != null) {
            if (orderBy == 1) {
                queryWrapper.orderByDesc(Invoice::getGmtCreate);
            }
            if (orderBy == 2) {
                queryWrapper.orderByDesc(Invoice::getGmtModified);
            }

        } else {
            queryWrapper.orderByDesc(Invoice::getGmtCreate);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(Invoice invoice) {

        List<InvoiceDtl> dtls = invoice.getDtls();
        MaterialReconciliation byId = materialReconciliationService.getById(dtls.get(0).getReconciliationId());
//        if (byId != null) {
//            if (StringUtils.isNotBlank(byId.getOrgShort())) {
//                EnterpriseInfo one = enterpriseInfoService.lambdaQuery()
//                        .eq(EnterpriseInfo::getShortCode, byId.getOrgShort())
//                        .one();
//                invoice.setSupplierId(one.getEnterpriseId());
//                invoice.setSupplierName(one.getEnterpriseName());
//
//            } else {
//                EnterpriseInfo one = enterpriseInfoService.lambdaQuery()
//                        .eq(EnterpriseInfo::getSocialCreditCode, byId.getCreditCode())
//                        .one();
//                invoice.setSupplierName(one.getEnterpriseName());
//                invoice.setSupplierId(one.getEnterpriseId());
//            }
//        }
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(invoice);
        ArrayList<MaterialReconciliation> reconciliationIdList = new ArrayList<>();
        ArrayList<InvoiceDtl> dtlsList = new ArrayList<>();
        if (dtls != null && dtls.size() > 0) {
            Map<String, List<InvoiceDtl>> reconciliation = dtls.stream().collect(Collectors.groupingBy(InvoiceDtl::getReconciliationId));
            reconciliation.forEach((reconciliationId, list) -> {
                MaterialReconciliation info = materialReconciliationService.getById(reconciliationId);
                if (byId.getTaxRate().compareTo(info.getTaxRate()) == 0
                        && byId.getSupplierId().equals(info.getSupplierId())) {
                    if (info.getInvoiceState() == 2 || info.getInvoiceState() == 1) {
                        throw new BusinessException(500, "对账单号为"
                                + info.getReconciliationNo() + "的数据已申请或已通过。请避免重复开票");
                    }
                } else {
                    throw new BusinessException(500, "税率和公司不匹配，请清除对账单号为"
                            + info.getReconciliationNo() + "的数据");
                }
                info.setInvoiceState(invoice.getInvoiceState());
                reconciliationIdList.add(info);
                for (InvoiceDtl invoiceDtl : list) {
                    invoiceDtl.setInvoiceId(invoice.getInvoiceId());
                    invoiceDtl.setInvoiceNo(info.getReconciliationNo());
                    invoiceDtl.setTaxRate(info.getTaxRate());
                    dtlsList.add(invoiceDtl);
                }
            });
        }

        BigDecimal rateAmount = BigDecimal.valueOf(0);
        BigDecimal noRateAmount = BigDecimal.valueOf(0);
        for (MaterialReconciliation materialReconciliation : reconciliationIdList) {
            rateAmount = rateAmount.add(materialReconciliation.getReconciliationAmount());
            noRateAmount = noRateAmount.add(materialReconciliation.getReconciliationNoRateAmount());
        }
        BigDecimal notTarRateAmount=TaxCalculator.noTarRateAmount(noRateAmount,rateAmount, invoice.getTaxRate());
        invoice.setRateAmount(rateAmount);
        invoice.setNoRateAmount(notTarRateAmount);
        invoice.setInvoiceCategory(0);
        materialReconciliationService.updateBatchById(reconciliationIdList);
        invoiceDtlService.saveBatch(dtlsList);
        update(invoice);
    }

    @Override
    public void update(Invoice invoice) {
        super.updateById(invoice);
    }


    @Override
    public Invoice getById(String id) {
        Invoice byId = super.getById(id);
        List<InvoiceDtl> list = invoiceDtlService.lambdaQuery().in(InvoiceDtl::getInvoiceId, id).list();
        byId.setDtls(list);
        List<File> files = fileService.listReIdAndTypeAndPKey(id, 16, null);
        byId.setFiles(files);
        List<AuditRecord> auditRecordList = auditRecordService.lambdaQuery().eq(AuditRecord::getRelevanceId, id).eq(AuditRecord::getRelevanceType, 8).list();
        if (auditRecordList != null) {
            byId.setAuditList(auditRecordList);
        }
        return byId;
    }

    @Override
    @NotResubmit
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        super.removeById(id);
        invoiceDtlService.deleteInvoiceId(id);
        fileService.deleteBatchFileByRelevanceIdAndType(id, 16);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    @Override
    public PageUtils platformQueryPage(JSONObject jsonObject, LambdaQueryWrapper<Invoice> q) {
        queryWrapper(jsonObject, q);
        IPage<Invoice> page = this.page(
                new Query<Invoice>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Override
    public PageUtils shopManageQueryPage(JSONObject jsonObject, LambdaQueryWrapper<Invoice> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        q.eq(Invoice::getSupplierId, user.getEnterpriseId());
        queryWrapper(jsonObject, q);

        IPage<Invoice> page = this.page(
                new Query<Invoice>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Override
    @NotResubmit
    @Transactional(rollbackFor = Exception.class)
    public void updateInvoiceState(Invoice invoice) {
        Invoice byId = getById(invoice.getInvoiceId());
        List<InvoiceDtl> list = invoiceDtlService.lambdaQuery().eq(InvoiceDtl::getInvoiceId, byId.getInvoiceId()).list();
        List<String> reconciliationIds = list.stream().map(InvoiceDtl::getReconciliationId).distinct().collect(Collectors.toList());
//        List<MaterialReconciliation> reconciliationList = materialReconciliationService.lambdaQuery()
//                .in(MaterialReconciliation::getReconciliationId, reconciliationIds).list();
        byId.setInvoiceState(invoice.getInvoiceState());
        materialReconciliationService.lambdaUpdate()
                .in(MaterialReconciliation::getReconciliationId, reconciliationIds)
                .set(MaterialReconciliation::getInvoiceState, invoice.getInvoiceState()).update();
        if (invoice.getInvoiceState() == 3) {
            byId.setFailReason(invoice.getFailReason());
            createAuditRecord(invoice.getInvoiceId(), 2, 9, "【拒绝】" + invoice.getFailReason());
        } else if (invoice.getInvoiceState() == 2) {
            createAuditRecord(invoice.getInvoiceId(), 1, 9, "【同意】");
        }
        fileService.deleteBatchFileByRelevanceIdAndType(invoice.getInvoiceId(), 16);
        if (invoice.getFiles() != null && invoice.getFiles().size() > 0) {
            List<File> files = invoice.getFiles();
            for (File file : files) {
                file.setRelevanceId(invoice.getInvoiceId());
                file.setRelevanceType(16);
            }
            fileService.saveBatch(files);
        }
        update(byId);


    }

    @Override
    public void updateBathIds(DataListPassVo dataListPassVos) {
        List<String> ids = dataListPassVos.getIds();
        List<InvoiceDtl> dtllist = invoiceDtlService.lambdaQuery().in(InvoiceDtl::getInvoiceId, ids).list();
        List<String> reconciliationIds = dtllist.stream().map(InvoiceDtl::getReconciliationId).distinct().collect(Collectors.toList());
        if (dataListPassVos.getState() == 2) {
            lambdaUpdate().in(Invoice::getInvoiceId, ids).set(Invoice::getInvoiceState, dataListPassVos.getState()).update();
            for (String id : ids) {
                createAuditRecord(id, 1, 9, "【同意】");
            }
        } else {
            lambdaUpdate().in(Invoice::getInvoiceId, ids).set(Invoice::getInvoiceState, dataListPassVos.getState()).set(Invoice::getFailReason, dataListPassVos.getFailReason()).update();
            for (String id : ids) {
                createAuditRecord(id, 1, 9, "【拒绝】" + dataListPassVos.getFailReason());
            }
        }
        materialReconciliationService.lambdaUpdate().in(MaterialReconciliation::getReconciliationId, reconciliationIds).
                set(MaterialReconciliation::getInvoiceState, dataListPassVos.getState()).update();
    }


    @Override
    public void changInvoiceState(AuditRecordVo auditRecordVo) {
        Invoice invoice = getById(auditRecordVo.getId());
        switch (auditRecordVo.getState()) {
            //采购员提出作废
            case 4:
                invoice.setInvoiceState(4);
                break;
            //供应商提出作废
            case 5:
                invoice.setInvoiceState(5);
                break;
            //作废通过
            case 6:
                invoice.setInvoiceState(6);
                createAuditRecord(invoice.getInvoiceId(), 1, 7, "【同意】");
                closeMaterialReconcilation(invoice);

                break;
            //作废不通过
            case 7:
                invoice.setInvoiceState(7);
                createAuditRecord(invoice.getInvoiceId(), 2, 7, "【拒绝】" + auditRecordVo.getFailReason());
                break;

            //采购员申请红字
            case 8:
                invoice.setInvoiceState(8);

                break;
            //供应商提出申请红字
            case 9:
                invoice.setInvoiceState(9);
                break;
            //红字通过
            case 10:
                invoice.setInvoiceState(10);
                createAuditRecord(invoice.getInvoiceId(), 1, 8, "【同意】");
                String redInvoice = createRedInvoice(invoice);
                invoice.setRelevanceId(redInvoice);
                closeMaterialReconcilation(invoice);

                break;
            //红字不通过
            case 11:
                invoice.setInvoiceState(11);
                createAuditRecord(invoice.getInvoiceId(), 2, 8, "【拒绝】" + auditRecordVo.getFailReason());
                break;

        }
        update(invoice);


    }

    private void closeMaterialReconcilation(Invoice invoice) {
        List<InvoiceDtl> dtllist = invoiceDtlService.getListByInvoiceId(invoice.getInvoiceId());
        List<String> reconciliationIds = dtllist.stream().map(InvoiceDtl::getReconciliationId).distinct().collect(Collectors.toList());
        if (invoice.getInvoiceClass()==1){
            materialReconciliationService.lambdaUpdate().in(MaterialReconciliation::getReconciliationId, reconciliationIds).
                    set(MaterialReconciliation::getInvoiceState, 0).update();
        }else {
            supplierReconciliationService.lambdaUpdate().in(SupplierReconciliation::getBillId, reconciliationIds).
                    set(SupplierReconciliation::getInvoiceState, 0).update();
        }

    }


    @Override
    public void saveFiles(Invoice invoice) {
        fileService.deleteBatchFileByRelevanceIdAndType(invoice.getInvoiceId(), 16);
        if (invoice.getFiles() != null && invoice.getFiles().size() > 0) {
            List<File> files = invoice.getFiles();
            for (File file : files) {
                file.setRelevanceId(invoice.getInvoiceId());
                file.setRelevanceType(16);
            }
            fileService.saveBatch(files);
        }
    }


    private void createAuditRecord(String id, Integer resultType, Integer auditType, String auditResult) {
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setRelevanceId(id);
        auditRecord.setRelevanceType(8);
        auditRecord.setAuditType(auditType);
        auditRecord.setResultType(resultType);
        auditRecord.setAuditResult(auditResult);
        auditRecordService.save(auditRecord);
    }

    private String createRedInvoice(Invoice invoice) {
        Invoice newInvoice = new Invoice();
        BeanUtils.copyProperties(invoice, newInvoice);
        newInvoice.setRelevanceId(invoice.getInvoiceId());
        newInvoice.setInvoiceId(null);
        newInvoice.setInvoiceCategory(1);
        newInvoice.setRateAmount(invoice.getRateAmount().negate());
        newInvoice.setNoRateAmount(invoice.getNoRateAmount().negate());
        newInvoice.setInvoiceState(12);
        save(newInvoice);
        ArrayList<InvoiceDtl> invoiceDtls = new ArrayList<>();
        List<InvoiceDtl> dtls = invoiceDtlService.getListByInvoiceId(invoice.getInvoiceId());
        for (InvoiceDtl dtl : dtls) {
            InvoiceDtl newDtl = new InvoiceDtl();
            BeanUtils.copyProperties(dtl, newDtl);
            newDtl.setInvoiceId(newInvoice.getInvoiceId());
            newDtl.setInvoiceDtlId(null);
            newDtl.setQuantity(dtl.getQuantity().negate());
//            newDtl.setPrice(dtl.getPrice().negate());

//            newDtl.setNoPrice(dtl.getNoPrice().negate());

//            newDtl.setNoRateAmount(dtl.getNoRateAmount().negate());
            newDtl.setAcceptanceAmount(dtl.getAcceptanceAmount().negate());
            invoiceDtls.add(newDtl);
        }
        invoiceDtlService.saveBatch(invoiceDtls);

        return newInvoice.getInvoiceId();
    }


    @Override
    public Invoice ListByBillIds(JSONObject jsonObject, LambdaQueryWrapper<Invoice> invoiceLambdaQueryWrapper) {

        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        List<String> billIds = (List<String>) innerMap.get("billIds");
        Integer userType = (Integer) innerMap.get("userType");
        Integer businessType = (Integer) innerMap.get("businessType");
        //二级对账单ids。
        // 供应商id,
        // 发票状态， 0
        // 业务类型 业务类型（1合同2计划3调拨4甲供5暂估6大宗临购
        List<SupplierReconciliation> list=new ArrayList<>();
        if (userType==2){
            list = supplierReconciliationService.lambdaQuery().in(SupplierReconciliation::getBillId, billIds)
                    .eq(businessType != null, SupplierReconciliation::getBusinessType, businessType)
                    .eq(SupplierReconciliation::getInvoiceState, 0)
                    .eq(SupplierReconciliation::getTwoSupplierOrgId, currentUser.getEnterpriseId()).list();
        }else {
             list = supplierReconciliationService.lambdaQuery().in(SupplierReconciliation::getBillId, billIds)
                    .eq(businessType != null, SupplierReconciliation::getBusinessType, businessType)
                    .eq(SupplierReconciliation::getInvoiceState, 0)
                    .eq(SupplierReconciliation::getSupplierOrgId, currentUser.getEnterpriseId()).list();
        }


        //比较二级对账单税率和二级供应商是否一致
        SupplierReconciliation info = list.get(0);
        boolean b = list.stream().allMatch(item -> item.getTaxRate().compareTo(info.getTaxRate()) == 0
                && item.getTwoSupplierOrgId().equals(info.getTwoSupplierOrgId()));
        if (b) {
            //创建发票
            Invoice invoice = new Invoice();
            InvoiceRecord one = invoiceRecordService.lambdaQuery().eq(InvoiceRecord::getEnterpriseId, currentUser.getEnterpriseId())
                    .eq(InvoiceRecord::getState, 1)
                    .eq(InvoiceRecord::getUserType, 1).one();
            //设置发票抬头
            if (one != null) {
                BeanUtils.copyProperties(one, invoice);
//                invoice.setInvoiceType(one.getInvoiceType());
//                invoice.setRiseType(one.getRiseType());
//                invoice.setUserName(one.getUserName());
//                invoice.setUserAddress(one.getUserAddress());
//                invoice.setUserPhone(one.getUserPhone());
//                invoice.setEmail(one.getEmail());
//                invoice.setCompany(one.getCompany());
//                invoice.setDutyParagraph(one.getDutyParagraph());
//                invoice.setRegisterAddress(one.getRegisterAddress());
//                invoice.setRegisterPhone(one.getRegisterPhone());
//                invoice.setBank(one.getBank());
//                invoice.setBankAccount(one.getBankAccount());
            }
//            invoice.setEnterpriseId(currentUser.getEnterpriseId());
//            invoice.setEnterpriseName(currentUser.getEnterpriseId());
            invoice.setInvoiceClass(2);
            //计算总含税金额和总不含税金额
            BigDecimal rateAmount = BigDecimal.valueOf(0);
            BigDecimal noRateAmount = BigDecimal.valueOf(0);
            for (SupplierReconciliation supplierReconciliation : list) {
                rateAmount = rateAmount.add(supplierReconciliation.getRateAmount());
                noRateAmount = noRateAmount.add(supplierReconciliation.getNoRateAmount());
            }
            invoice.setRateAmount(rateAmount);
            invoice.setNoRateAmount(noRateAmount);
            invoice.setEnterpriseId(info.getSupplierOrgId());
            invoice.setEnterpriseName(info.getSupplierName());
            invoice.setSupplierName(info.getTwoSupplierName());
            invoice.setSupplierId(info.getTwoSupplierOrgId());
            invoice.setTaxRate(info.getTaxRate());
            List<String> ids = list.stream().map(SupplierReconciliation::getBillId).collect(Collectors.toList());
            List<SupplierReconciliationDtl> dtls = supplierReconciliationDtlService.lambdaQuery().in(SupplierReconciliationDtl::getBillId, ids).list();
            ArrayList<InvoiceDtl> invoiceDtls = new ArrayList<>();
            for (SupplierReconciliationDtl dtl : dtls) {
                InvoiceDtl invoiceDtl = new InvoiceDtl();
                invoiceDtl.setQuantity(dtl.getQuantity());
                invoiceDtl.setMaterialName(dtl.getMaterialName());
                invoiceDtl.setUnit(dtl.getUnit());
                invoiceDtl.setReconciliationId(dtl.getBillId());
                invoiceDtl.setReconciliationNo(dtl.getBillNo());
                invoiceDtl.setReconciliationDtlId(dtl.getDtlId());
                invoiceDtl.setAcceptanceAmount(dtl.getRateAmount());
                invoiceDtl.setNoRateAmount(dtl.getNoRateAmount());
                invoiceDtl.setPrice(dtl.getPrice());
                invoiceDtl.setNoPrice(dtl.getNoRatePrice());
                invoiceDtl.setSpec(dtl.getSpec());
                invoiceDtl.setTexture(dtl.getTexture());
                invoiceDtl.setTaxRate(info.getTaxRate());
                invoiceDtls.add(invoiceDtl);
            }
            invoice.setDtls(invoiceDtls);
            return invoice;
        } else {
            throw new BusinessException(500, "二级供应商后者税率不统一。不能生成对账单");
        }

    }


    @Override
    public void twoEnterpriseCreate(Invoice invoice) {
        invoice.setInvoiceCategory(0);
        invoice.setInvoiceClass(2);
        save(invoice);
        List<InvoiceDtl> dtls = invoice.getDtls();
//        Map<String, List<InvoiceDtl>> collect = dtls.stream().collect(Collectors.groupingBy(InvoiceDtl::getReconciliationId));
//      collect.forEach((key, value)->{
//          SupplierReconciliation byId = supplierReconciliationService.getById(key);
//
//      });
        ArrayList<InvoiceDtl> dtlsList = new ArrayList<>();
        Set<String> ids = dtls.stream().map(InvoiceDtl::getReconciliationId).collect(Collectors.toSet());
        //cha
        List<SupplierReconciliation> reconciliations = supplierReconciliationService.listByIds(ids);
        BigDecimal rateAmount = BigDecimal.valueOf(0);
        BigDecimal noRateAmount = BigDecimal.valueOf(0);
        if (reconciliations != null) {
            for (SupplierReconciliation reconciliation : reconciliations) {
                if (reconciliation.getTaxRate().compareTo(invoice.getTaxRate()) == 0
                        && reconciliation.getTwoSupplierOrgId().equals(invoice.getSupplierId())) {
                    //0 未开票  10已通过红字发票  发票被拒后对账单发票状态为0
                    if (reconciliation.getInvoiceState() != 0 ) {
                        throw new BusinessException(500, "对账单号为"
                                + reconciliation.getBillNo() + "的数据已申请或已通过。请避免重复开票");
                    }
                } else {
                    throw new BusinessException(500, "税率和公司不匹配，请清除对账单号为"
                            + reconciliation.getBillNo() + "的数据");
                }
                reconciliation.setInvoiceState(1);
                List<InvoiceDtl> dtl = dtls.stream().filter(item -> item.getReconciliationId()
                                .equals(reconciliation.getBillId()))
                        .collect(Collectors.toList());
                for (InvoiceDtl invoiceDtl : dtl) {
                    rateAmount=rateAmount.add(invoiceDtl.getAcceptanceAmount());
                    noRateAmount=noRateAmount.add(invoiceDtl.getNoRateAmount());
                    invoiceDtl.setInvoiceId(invoice.getInvoiceId());
                    invoiceDtl.setReconciliationNo(reconciliation.getBillNo());
                    invoiceDtl.setTaxRate(reconciliation.getTaxRate());
                    dtlsList.add(invoiceDtl);
                }
            }
        }
        if (rateAmount.compareTo(invoice.getRateAmount()) != 0) {
            throw new BusinessException("含税总金额计算不一致！");
        }
        BigDecimal notTarRateAmount= TaxCalculator.noTarRateAmount(noRateAmount,rateAmount, invoice.getTaxRate());
        invoice.setRateAmount(rateAmount);
        invoice.setNoRateAmount(notTarRateAmount);
        supplierReconciliationService.updateBatchById(reconciliations);
        invoiceDtlService.saveBatch(dtlsList);
        invoice.setInvoiceCategory(0);
        update(invoice);

    }
    @Override
    public void twoSupplierCreate(Invoice invoice) {
        invoice.setInvoiceCategory(0);
        invoice.setInvoiceClass(2);
        save(invoice);
        List<InvoiceDtl> dtls = invoice.getDtls();
//        Map<String, List<InvoiceDtl>> collect = dtls.stream().collect(Collectors.groupingBy(InvoiceDtl::getReconciliationId));
//      collect.forEach((key, value)->{
//          SupplierReconciliation byId = supplierReconciliationService.getById(key);
//
//      });
        ArrayList<InvoiceDtl> dtlsList = new ArrayList<>();
        Set<String> ids = dtls.stream().map(InvoiceDtl::getReconciliationId).collect(Collectors.toSet());
        //cha
        List<SupplierReconciliation> reconciliations = supplierReconciliationService.listByIds(ids);
        BigDecimal rateAmount = BigDecimal.valueOf(0);
        BigDecimal noRateAmount = BigDecimal.valueOf(0);
        if (reconciliations != null) {
            for (SupplierReconciliation reconciliation : reconciliations) {
                if (reconciliation.getTaxRate().compareTo(invoice.getTaxRate()) == 0
                        && reconciliation.getSupplierOrgId().equals(invoice.getEnterpriseId())) {
                    //0 未开票  10已通过红字发票  发票被拒后对账单发票状态为0
                    if (reconciliation.getInvoiceState() == 0 | reconciliation.getInvoiceState() == 10) {
                        reconciliation.setInvoiceState(invoice.getState());
                    }else {
                        throw new BusinessException(500, "对账单号为"
                                + reconciliation.getBillNo() + "的数据已申请或已通过。请避免重复开票");
                    }
                } else {
                    throw new BusinessException(500, "税率和公司不匹配，请清除对账单号为"
                            + reconciliation.getBillNo() + "的数据");
                }

                List<InvoiceDtl> dtl = dtls.stream().filter(item -> item.getReconciliationId()
                                .equals(reconciliation.getBillId()))
                        .collect(Collectors.toList());
                for (InvoiceDtl invoiceDtl : dtl) {
                    rateAmount=rateAmount.add(invoiceDtl.getAcceptanceAmount());
                    noRateAmount=noRateAmount.add(invoiceDtl.getNoRateAmount());
                    invoiceDtl.setInvoiceId(invoice.getInvoiceId());
                    invoiceDtl.setReconciliationNo(reconciliation.getBillNo());
                    invoiceDtl.setTaxRate(reconciliation.getTaxRate());
                    dtlsList.add(invoiceDtl);
                }
            }
        }
        if (rateAmount.compareTo(invoice.getRateAmount()) != 0) {
            throw new BusinessException("含税总金额计算不一致！");
        }
        BigDecimal notTarRateAmount= TaxCalculator.noTarRateAmount(noRateAmount,rateAmount, invoice.getTaxRate());
        invoice.setRateAmount(rateAmount);
        invoice.setRateAmount(rateAmount);
        invoice.setNoRateAmount(notTarRateAmount);
        supplierReconciliationService.updateBatchById(reconciliations);
        invoiceDtlService.saveBatch(dtlsList);
        invoice.setInvoiceCategory(0);
        update(invoice);

    }
}
