package scrbg.meplat.mall.service.impl;

import com.alibaba.excel.util.StringUtils;
import scrbg.meplat.mall.entity.ProductRulesClass;
import scrbg.meplat.mall.mapper.ProductRulesClassMapper;
import scrbg.meplat.mall.service.ProductRulesClassService;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
import java.util.Map;

/**
 * @描述：商品预警规则和分类关联表 服务类
 * @作者: ye
 * @日期: 2024-03-20
 */
@Service
public class ProductRulesClassServiceImpl extends ServiceImpl<ProductRulesClassMapper, ProductRulesClass> implements ProductRulesClassService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductRulesClass> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String classPathName = (String) innerMap.get("classPathName");
        String materialName = (String) innerMap.get("materialName");
        String skuName = (String) innerMap.get("skuName");
        String brandName = (String) innerMap.get("brandName");
                if (StringUtils.isNotBlank(classPathName)) {
            queryWrapper.like(ProductRulesClass::getClassPathName, classPathName);
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(ProductRulesClass::getClassPathName, keywords)
                        .or()
                        .like(ProductRulesClass::getBrandName, keywords)
                        .or()
                        .like(ProductRulesClass::getSkuName, keywords)
                        .or()
                        .like(ProductRulesClass::getMaterialName, keywords);
            });
        }
        if (StringUtils.isNotBlank(materialName)) {
            queryWrapper.like(ProductRulesClass::getMaterialName, materialName);
        }
                if (StringUtils.isNotBlank(skuName)) {
            queryWrapper.like(ProductRulesClass::getSkuName, skuName);
        }
        if (StringUtils.isNotBlank(brandName)) {
            queryWrapper.like(ProductRulesClass::getBrandName, brandName);
        }
        IPage<ProductRulesClass> page = this.page(
        new Query<ProductRulesClass>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(ProductRulesClass productRulesClass) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(productRulesClass);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(ProductRulesClass productRulesClass) {
        super.updateById(productRulesClass);
    }


    @Override
    public ProductRulesClass getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     @Transactional(rollbackFor = Exception.class)
     @NotResubmit
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
         }

    @Override
    public List<ProductRulesClass> findListByRulesId(String warningRuleId) {
        return lambdaQuery().eq(ProductRulesClass::getWarningRuleId,warningRuleId).list();
    }


    @Override
    public List<ProductRulesClass> selcetDataByClassIdAndMaterialId(String classId, String relevanceId) {
        List<ProductRulesClass> list= baseMapper.selcetDataByClassIdAndMaterialId(classId,relevanceId);
        return list;
    }
}
