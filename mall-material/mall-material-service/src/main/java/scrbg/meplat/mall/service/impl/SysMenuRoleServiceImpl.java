package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.SysMenuRole;
import scrbg.meplat.mall.mapper.SysMenuRoleMapper;
import scrbg.meplat.mall.service.SysMenuRoleService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：菜单角色表 服务类
 * @作者: ye
 * @日期: 2023-12-21
 */
@Service
public class SysMenuRoleServiceImpl extends ServiceImpl<SysMenuRoleMapper, SysMenuRole> implements SysMenuRoleService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysMenuRole> queryWrapper) {
        IPage<SysMenuRole> page = this.page(
        new Query<SysMenuRole>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(SysMenuRole sysMenuRole) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(sysMenuRole);
    }

    @Override
    public void update(SysMenuRole sysMenuRole) {
        super.updateById(sysMenuRole);
    }


    @Override
    public SysMenuRole getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
