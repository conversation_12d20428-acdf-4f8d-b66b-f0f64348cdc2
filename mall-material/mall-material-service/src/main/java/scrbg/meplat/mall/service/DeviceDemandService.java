package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.dto.DeviceDemand.UpdateDemandCheckStateDTO;
import scrbg.meplat.mall.entity.DeviceDemand;
import scrbg.meplat.mall.vo.product.website.demand.DeviceDemandInfoVO;

/**
 * @描述：设备需求 服务类
 * @作者: y
 * @日期: 2022-11-21
 */
public interface DeviceDemandService extends IService<DeviceDemand> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<DeviceDemand> queryWrapper);

    void create(DeviceDemand deviceDemand);

    void update(DeviceDemand deviceDemand);

    DeviceDemand getById(String id);

    void delete(String id);

    /**
     * 批量修改审核状态
     * @param dto
     */
    void updateBatchCheckState(UpdateDemandCheckStateDTO dto);

    /**
     * 获取需求列表
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils usedPageList(JSONObject jsonObject, LambdaQueryWrapper<DeviceDemand> lambdaQuery);

    /**
     * 需求详细
     * @param demandId
     * @return
     */
    DeviceDemandInfoVO demandInfo(String demandId);

    /**
     * 查询我发布的需求
     * @param jsonObject
     * @param deviceDemandLambdaQueryWrapper
     * @return
     */
    PageUtils userDeviceDemand(JSONObject jsonObject, LambdaQueryWrapper<DeviceDemand> deviceDemandLambdaQueryWrapper);


    PageUtils attendList(JSONObject jsonObject, QueryWrapper<DeviceDemand> deviceDemandQueryWrapper);
}
