package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：
 * @作者: ye
 * @日期: 2024-01-29
 */
@ApiModel(value="")
@Data
@TableName("shudao_enterprise")
public class ShudaoEnterprise extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "蜀道企业ID")
    private String shudaoEnterpriseId;

    @ApiModelProperty(value = "隶属企业")

    private String affiliationEnterprise;


    @ApiModelProperty(value = "公司名称")

    private String enterpriseName;


    @ApiModelProperty(value = "企业类别(1:一类 2:二类 3:三类)")

    private Integer enterpriseCategory;


    @ApiModelProperty(value = "调整")

    private String adjust;


    @ApiModelProperty(value = "企业性质")

    private String enterpriseNature;














    @ApiModelProperty(value = "状态")

    private Integer state;







}