package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * @描述：反馈中心
 * @作者: y
 * @日期: 2022-11-25
 */
@ApiModel(value = "反馈中心")
@Data
@TableName("message_informations")
public class MessageInformations extends MustBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "留言信息id")

    private String messageInformationsId;

    @ApiModelProperty(value = "留言人id")

    private String messageId;

    @ApiModelProperty(value = "留言人名称")

    private String messageName;
    @ApiModelProperty(value = "留言人所属企业名称")
    private String enterpriseName;
    @ApiModelProperty(value = "留言人所属店铺名称")
    private String shopName;
    @ApiModelProperty(value = "留言人是否是内部用户（0否1是）")
    private Integer isInterior;

    @ApiModelProperty(value = "回复人id")

    private String respondId;

    @ApiModelProperty(value = "回复人名称")

    private String respondName;


    @ApiModelProperty(value = " 留言标题(总结你的反馈）")

    private String  messagTitle;

    @ApiModelProperty(value = "留言内容(详细描述)")

    private String messagContent;

    @ApiModelProperty(value = "留言类型（1：问题，2：建议）")

    private String messagType;

    @ApiModelProperty(value = "留言是否有附件(0否1是)")

    private String isFile;

    @ApiModelProperty(value = "留言发送时间")

    private Date messagDate;

    @ApiModelProperty(value = "回复标题")

    private String respondTitle;

    @ApiModelProperty(value = "回复内容")

    private String respondContent;

    @ApiModelProperty(value = "回复时间")

    private Date respondDate;

    @ApiModelProperty(value = "处理状态（0：未处理 1：已处理）")

    private Integer state;


    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除（只能平台删除）")

    private Integer isDelete;



    @ApiModelProperty(value = "公开展示留言（0：不展示 1：展示）")

    private Integer publicDisplay;

}
