package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.ProductAttributeValue;
import scrbg.meplat.mall.mapper.ProductAttributeValueMapper;
import scrbg.meplat.mall.service.ProductAttributeValueService;

/**
 * @描述：商品属性值 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class ProductAttributeValueServiceImpl extends ServiceImpl<ProductAttributeValueMapper, ProductAttributeValue> implements ProductAttributeValueService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductAttributeValue> queryWrapper) {
        IPage<ProductAttributeValue> page = this.page(
                new Query<ProductAttributeValue>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ProductAttributeValue productAttributeValue) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(productAttributeValue);
    }

    @Override
    public void update(ProductAttributeValue productAttributeValue) {
        super.updateById(productAttributeValue);
    }


    @Override
    public ProductAttributeValue getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
