package scrbg.meplat.mall.mapper;

import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.BiddingPurchase;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;
import scrbg.meplat.mall.vo.bidding.HitBidVo;

import java.util.List;

/**
 * @描述：竞价采购表 Mapper 接口
 * @作者: ye
 * @日期: 2023-07-11
 */
@Mapper
@Repository
public interface BiddingPurchaseMapper extends BaseMapper<BiddingPurchase> {
    // 根据清单编号查询已中标的竞价数据
    List<HitBidVo> selectByBiddingSn(@Param("biddingSn") String biddingSn);

}