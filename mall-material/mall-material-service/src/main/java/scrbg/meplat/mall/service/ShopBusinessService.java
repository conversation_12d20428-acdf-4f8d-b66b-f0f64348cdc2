package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.ShopBusiness;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ShopBusiness;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.ArrayList;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2025-01-16
 */
public interface ShopBusinessService extends IService<ShopBusiness> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShopBusiness> queryWrapper);

        void create(ShopBusiness shopBusiness);
        void update(ShopBusiness shopBusiness);
        ShopBusiness getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

        /**
         * 根据企业id创建自营店铺
         * @param enterpriseId
         */
    void createDataByEnterpriseId(String enterpriseId);

    void updateState(ShopBusiness shopBusiness);


        //查询已经自营店铺id
      List<String> getBusinessOrgList();

    ArrayList<String> getStopBusiness();

    void updateBatchSortById(List<ShopBusiness> list);
}
