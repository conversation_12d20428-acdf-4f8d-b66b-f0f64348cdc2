package scrbg.meplat.mall.service;

import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.entity.ReceiptPerson;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ReceiptPerson;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.List;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-07-13
 */
public interface ReceiptPersonService extends IService<ReceiptPerson> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ReceiptPerson> queryWrapper);

    void create(ReceiptPerson receiptPerson);

    void update(ReceiptPerson receiptPerson);

    ReceiptPerson getById(String id);

    void delete(String id);
    /*
     *启/停用状态批量更新
     * */

    void updateByPublish(List<String> ids, String type);
}