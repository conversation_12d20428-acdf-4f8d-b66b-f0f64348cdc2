package scrbg.meplat.mall.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import scrbg.meplat.mall.entity.DealOrderInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @描述：结算订单明细 Mapper 接口
 * @作者: ye
 * @日期: 2023-06-16
 */
@Mapper
@Repository
public interface DealOrderInfoMapper extends BaseMapper<DealOrderInfo> {

    /**
     * 修改结算单为0，不删除
     * @param settleAccountsId
     */
    @Update("update deal_order_info set is_delete = 0 where settle_accounts_id = #{settleAccountsId}")
    void updateTo0BySettleAccountsId(String settleAccountsId);

    IPage<DealOrderInfo> platformList(IPage<DealOrderInfo> page,@Param("params")Map<String, Object> params);

    BigDecimal selCountAmount(@Param("params") Map<String, Object> params);

    BigDecimal selNoRateCountAmount(@Param("params") Map<String, Object> params);
}