package scrbg.meplat.mall.config.auth;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.util.List;

/**
 * <p>
 * 防重复提交注解的实现，使用AOP。
 * </p>
 *
 */
@Aspect
@Component
public class IncludeAuthAOP {

   /**
    * 判断权限
    */
   @Around("@annotation(scrbg.meplat.mall.config.auth.IsRole)")
   public Object includeAuthM(ProceedingJoinPoint pjp) throws Throwable {
      MethodSignature signature = (MethodSignature) pjp.getSignature();
      IsRole isAuth = signature.getMethod().getAnnotation(IsRole.class);
      List<String> roles = ThreadLocalUtil.getCurrentUser().getRoles();
      String name = isAuth.roleName().getName();
      boolean isCheckAuth = roles.contains(isAuth.roleName().getName());
      if (!isCheckAuth) {
         throw new BusinessException("没有权限，请联系管理员！");
      }
      return pjp.proceed();
   }

//   @Around("@annotation(scrbg.meplat.mall.config.auth.IsRoles)")
//   public Object includeAuthsM(ProceedingJoinPoint pjp) throws Throwable {
//      MethodSignature signature = (MethodSignature) pjp.getSignature();
//      IsRoles IsRoles = signature.getMethod().getAnnotation(IsRoles.class);
//      List<String> roles = ThreadLocalUtil.getCurrentUser().getRoles();
//      RoleEnum[] roleEnums = IsRoles.roleNames();
//      for (RoleEnum roleEnum : roleEnums) {
//         boolean isCheckAuth = roles.contains(roleEnum.getName());
//         if (!isCheckAuth) {
//            throw new BusinessException("没有权限，请联系管理员！");
//         }
//      }
//      return pjp.proceed();
//   }

   @Around("@annotation(scrbg.meplat.mall.config.auth.IsLogin)")
   public Object isLoginM(ProceedingJoinPoint pjp) throws Throwable {
      MethodSignature signature = (MethodSignature) pjp.getSignature();
      IsLogin isLogin = signature.getMethod().getAnnotation(IsLogin.class);
      int login = isLogin.isLogin();
      if(login == 1) {
         UserLogin user = ThreadLocalUtil.getCurrentUser();
         if(user == null) {
            throw new BusinessException("请登陆后访问！");

         }
      }
      return pjp.proceed();
   }

}
