//package scrbg.meplat.mall.config.redis;
//
//import com.fasterxml.jackson.annotation.JsonAutoDetect;
//import com.fasterxml.jackson.annotation.JsonTypeInfo;
//import com.fasterxml.jackson.annotation.PropertyAccessor;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.redis.connection.RedisConnectionFactory;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
//import org.springframework.data.redis.serializer.StringRedisSerializer;
//
///**
// * <p>
// *  redis配置类
// * </p>
// *
// * <AUTHOR>
// * @since 2022-02-08
// */
//@Configuration
//public class RedisConfig1 {
//
//   @Bean
//   public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
//      StringRedisTemplate redisTemplate = new StringRedisTemplate();
//      redisTemplate.setConnectionFactory(redisConnectionFactory);
//      return redisTemplate;
//   }
//
//   @Bean
//   public RedisTemplate redisTemplate(RedisConnectionFactory factory) {
//      RedisTemplate<String, Object> template = new RedisTemplate<>();
//      template.setConnectionFactory(factory);
//
//      Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
//      ObjectMapper objectMapper = new ObjectMapper();
//      objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
//      objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
//      jackson2JsonRedisSerializer.setObjectMapper(objectMapper);
//
//      StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
//      // key采用String的序列化方式
//      template.setKeySerializer(stringRedisSerializer);
//      // hash的key也采用String的序列化方式
//      template.setHashKeySerializer(stringRedisSerializer);
//      // value序列化方式采用jackson
//      template.setValueSerializer(jackson2JsonRedisSerializer);
//      // hash的value序列化方式采用jackson
//      template.setHashValueSerializer(jackson2JsonRedisSerializer);
//      template.afterPropertiesSet();
//      return template;
//   }
//}
