package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.PlatformDealFeeDtlMapper;
import scrbg.meplat.mall.mapper.PlatformDealFeeMapper;
import scrbg.meplat.mall.service.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @描述：平台交易费 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
@Service
public class PlatformDealFeeServiceImpl extends ServiceImpl<PlatformDealFeeMapper, PlatformDealFee> implements PlatformDealFeeService {


    @Autowired
    PlatformDealFeeDtlService platformDealFeeDtlService;

    @Autowired
    PlatformDealFeeRecordService platformDealFeeRecordService;

    @Autowired
    PlatformBalanceOperateService platformBalanceOperateService;

    @Autowired
    PlatformDealFeeDtlMapper platformDealFeeDtlMapper;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    PlatformDealFeeService platformDealFeeService;

    @Autowired
    SystemParamService systemParamService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFee> queryWrapper) {
        IPage<PlatformDealFee> page = this.page(
                new Query<PlatformDealFee>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(PlatformDealFee platformDealFee) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(platformDealFee);
    }

    @Override
    public void update(PlatformDealFee platformDealFee) {
        super.updateById(platformDealFee);
    }


    @Override
    public PlatformDealFee getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    /**
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFee> q) {
        Integer serveType = (Integer) jsonObject.get("serveType");
        String startGmtCreate = (String) jsonObject.get("startGmtCreate");
        String platformDealFeeNu = (String) jsonObject.get("platformDealFeeNu");
        String endGmtCreate = (String) jsonObject.get("endGmtCreate");
        String startGmtModified = (String) jsonObject.get("startGmtModified");
        String endGmtModified = (String) jsonObject.get("endGmtModified");
        String startArrearageDateTime = (String) jsonObject.get("startArrearageDateTime");
        String endArrearageDateTime = (String) jsonObject.get("endArrearageDateTime");
        String keywords = (String) jsonObject.get("keywords");
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        q.eq(serveType != null, PlatformDealFee::getServeType, serveType);
        q.like(platformDealFeeNu != null, PlatformDealFee::getPlatformDealFeeNu, platformDealFeeNu);
        q.orderByDesc(orderBy == 0, PlatformDealFee::getGmtCreate);
        q.orderByDesc(orderBy == 1, PlatformDealFee::getGmtModified);
        q.orderByDesc(orderBy == 2, PlatformDealFee::getArrearageDateTime);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(PlatformDealFee::getPlatformDealFeeNu, keywords)
                        .or()
                        .like(PlatformDealFee::getEnterpriseName, keywords);
            });
        }
        q.between(StringUtils.isNotEmpty(startGmtCreate) && StringUtils.isNotEmpty(endGmtCreate), PlatformDealFee::getGmtCreate, startGmtCreate, endGmtCreate);
        q.between(StringUtils.isNotEmpty(startGmtModified) && StringUtils.isNotEmpty(endGmtModified), PlatformDealFee::getGmtModified, startGmtModified, endGmtModified);
        q.between(StringUtils.isNotEmpty(startArrearageDateTime) && StringUtils.isNotEmpty(endArrearageDateTime), PlatformDealFee::getArrearageDateTime, startArrearageDateTime, endArrearageDateTime);

        IPage<PlatformDealFee> page = this.page(
                new Query<PlatformDealFee>().getPage(jsonObject),
                q
        );
        for (PlatformDealFee record : page.getRecords()) {
            QueryWrapper<PlatformDealFeeDtl> q1 = new QueryWrapper<>();
            q1.eq("platform_deal_fee_id", record.getPlatformDealFeeId())
                    .eq("serve_type", record.getServeType())
                    .eq("finish_pay_fee", 0)
                    .eq("state", 0) // 排除作废
                    .select("sum(residue_pay_fee) as residuePayFee");
            Map<String, Object> map1 = platformDealFeeDtlMapper.selectMaps(q1).get(0);
            BigDecimal residuePayFee = new BigDecimal(0);
            if (map1 != null && map1.get("residuePayFee") != null) {
                residuePayFee = new BigDecimal(map1.get("residuePayFee").toString());
            }
            record.setResiduePayFee(residuePayFee);

            record.setStopServe(0);
            // 查询是否停止服务
            QueryWrapper<PlatformDealFeeDtl> isStopQ = new QueryWrapper<>();
            isStopQ.eq("enterprise_id", record.getEnterpriseId())
                    .eq("serve_type", record.getServeType())
                    .eq("finish_pay_fee", 0)
                    .eq("state", 0)
                    .select("sum(residue_pay_fee) as totalAmount");
            List<Map<String, Object>> maps = platformDealFeeDtlMapper.selectMaps(isStopQ);
            BigDecimal newTotalAmount = null;
            if (maps.get(0) == null) {
                newTotalAmount = new BigDecimal(0);
            } else {
                newTotalAmount = new BigDecimal(maps.get(0).get("totalAmount").toString());
            }

            EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, record.getEnterpriseId())
                    .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getArrearage,
                            EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getArrearageDateNum, EnterpriseInfo::getArrearageDateType).one();
            boolean b = checkOutTime(supplier, record, newTotalAmount);
            if (b) {
                record.setStopServe(1);
            }
        }
        return new PageUtils(page);
    }

    /**
     * 每天定时校验欠费是否过期
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkOutArrearageDate() {
        // TODO 主要防止超过了额度，本来未欠费，但是很久没有交易超过了欠费时间，主要统计使用
        List<PlatformDealFee> list = lambdaQuery().isNull(PlatformDealFee::getArrearageDateTime).list();
        for (PlatformDealFee platformDealFee : list) {
            // 处理：stop_serve
        }
    }

    @Override
    public PlatformDealFee findBySn(String sn) {
        PlatformDealFee one = lambdaQuery().eq(PlatformDealFee::getPlatformDealFeeNu, sn).one();
        if (one != null) {
            List<PlatformDealFeeDtl> feeDtlList = platformDealFeeDtlService.lambdaQuery()
                    .eq(PlatformDealFeeDtl::getPlatformDealFeeId, one.getPlatformDealFeeId())
                    .orderByDesc(PlatformDealFeeDtl::getGmtCreate)
                    .list();
            if (feeDtlList != null && feeDtlList.size() > 0) {
                one.setFeeDtls(feeDtlList);
            }

            // 待缴费金额（不包含作废的）
            QueryWrapper<PlatformDealFeeDtl> q1 = new QueryWrapper<>();
            q1.eq("platform_deal_fee_id", one.getPlatformDealFeeId())
                    .eq("serve_type", one.getServeType())
                    .eq("finish_pay_fee", 0)
                    .eq("state", 0) // 排除作废
                    .select("sum(residue_pay_fee) as residuePayFee");
            Map<String, Object> map1 = platformDealFeeDtlMapper.selectMaps(q1).get(0);
            BigDecimal residuePayFee = new BigDecimal(0);
            if (map1 != null && map1.get("residuePayFee") != null) {
                residuePayFee = new BigDecimal(map1.get("residuePayFee").toString());
            }
            one.setResiduePayFee(residuePayFee);

            // 总缴费金额(不包含作废)
            QueryWrapper<PlatformDealFeeDtl> q2 = new QueryWrapper<>();
            q2.eq("platform_deal_fee_id", one.getPlatformDealFeeId())
                    .eq("serve_type", one.getServeType())
                    .eq("pay_type", 1)
//                    .eq("finish_pay_fee", 0)
                    .eq("state", 0)
                    .select("sum(pay_fee) as paymentAmount");
            Map<String, Object> map2 = platformDealFeeDtlMapper.selectMaps(q2).get(0);
            BigDecimal paymentAmount = new BigDecimal(0);
            if (map2 != null) {
                paymentAmount = new BigDecimal(map2.get("paymentAmount").toString());
            }
            one.setPaymentAmount(paymentAmount);

            // 总服务费（不包含作废）
            QueryWrapper<PlatformDealFeeDtl> q3 = new QueryWrapper<>();
            q3.eq("platform_deal_fee_id", one.getPlatformDealFeeId())
                    .eq("serve_type", one.getServeType())
                    .eq("pay_type", 1)
//                    .eq("finish_pay_fee", 0)
                    .eq("state", 0)
                    .select("sum(serve_fee) as dealAmount");
            Map<String, Object> map3 = platformDealFeeDtlMapper.selectMaps(q3).get(0);
            BigDecimal dealAmount = new BigDecimal(0);
            if (map3 != null) {
                dealAmount = new BigDecimal(map3.get("dealAmount").toString());
            }
            one.setDealAmount(dealAmount);


            // 总交易金额（不包含作废）
            QueryWrapper<PlatformDealFeeDtl> q4 = new QueryWrapper<>();
            q4.eq("platform_deal_fee_id", one.getPlatformDealFeeId())
                    .eq("serve_type", one.getServeType())
//                    .eq("finish_pay_fee", 0)
                    .eq("state", 0)
                    .select("sum(deal_amount) as totalDeal");
            Map<String, Object> map4 = platformDealFeeDtlMapper.selectMaps(q4).get(0);
            BigDecimal totalDeal = new BigDecimal(0);
            if (map4 != null) {
                totalDeal = new BigDecimal(map4.get("totalDeal").toString());
            }
            one.setTotalDeal(totalDeal);


//            QueryWrapper<PlatformDealFeeDtl> q5 = new QueryWrapper<>();
//            q5.eq("platform_deal_fee_id", one.getPlatformDealFeeId())
//                    .eq("serve_type", one.getServeType())
////                    .eq("finish_pay_fee", 0)
//                    .eq("pay_type", 2)
////                    .eq("state", 0)
//                    .select("sum(pay_fee) as totalMPay");
//            Map<String, Object> map5 = platformDealFeeDtlMapper.selectMaps(q5).get(0);
//            BigDecimal totalMPay = new BigDecimal(0);
//            if (map5 != null) {
//                totalMPay = new BigDecimal(map5.get("totalMPay").toString());
//            }
//            one.setTotalMPay(totalMPay);


            // 先拿到直接是免费金额总金额
//            QueryWrapper<PlatformDealFeeDtl> t1q = new QueryWrapper<>();
//            t1q.eq("platform_deal_fee_id", one.getPlatformDealFeeId())
//                    .eq("serve_type", one.getServeType())
//                    .eq("pay_type", 2)
//                    .eq("state", 0)
//                    .select("sum(deal_amount) as totalAmount");
//            List<Map<String, Object>> maps2 = platformDealFeeDtlMapper.selectMaps(t1q);
//            BigDecimal t1 = new BigDecimal(0);
//            if (maps2.get(0) != null) {
//                t1 = new BigDecimal(maps2.get(0).get("totalAmount").toString());
//            }


            // 拿到占用免费金额部分
            QueryWrapper<PlatformDealFeeDtl> ep = new QueryWrapper<>();
            ep.eq("platform_deal_fee_id", one.getPlatformDealFeeId())
                    .eq("serve_type", one.getServeType())
                    .eq("state", 0)
                    .select("sum(use_exempt_free) as useExemptFree");
            List<Map<String, Object>> epMap = platformDealFeeDtlMapper.selectMaps(ep);
            BigDecimal t2 = new BigDecimal(0);
            if (epMap.get(0) != null) {
                t2 = new BigDecimal(epMap.get(0).get("useExemptFree").toString());
            }

//            one.setThisTotalExempt(t1.add(t2));
            one.setThisTotalExempt(t2);

            String queryKey = one.getServeType() == 1 ? PublicEnum.PLATFORM_DEAL_MAX_QUOTA_DP.getRemark() : PublicEnum.PLATFORM_DEAL_MAX_QUOTA_LY.getRemark();
            SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode, queryKey).one();
            BigDecimal totalEd = new BigDecimal(systemParam.getKeyValue());
            one.setExemptTotalAmount(totalEd);

            EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, one.getEnterpriseId())
                    .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getArrearage,
                            EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getArrearageDateNum, EnterpriseInfo::getArrearageDateType).one();
            one.setArrearage(supplier.getArrearage());
            one.setArrearageDateNum(supplier.getArrearageDateNum());
            one.setArrearageDateType(supplier.getArrearageDateType());


            one.setStopServe(0);
            // 查询是否停止服务
            QueryWrapper<PlatformDealFeeDtl> isStopQ = new QueryWrapper<>();
            isStopQ.eq("enterprise_id", supplier.getEnterpriseId())
                    .eq("serve_type", one.getServeType())
                    .eq("finish_pay_fee", 0)
                    .eq("state", 0)
                    .select("sum(residue_pay_fee) as totalAmount");
            List<Map<String, Object>> maps = platformDealFeeDtlMapper.selectMaps(isStopQ);
            BigDecimal newTotalAmount = null;
            if (maps.get(0) == null) {
                newTotalAmount = new BigDecimal(0);
            } else {
                newTotalAmount = new BigDecimal(maps.get(0).get("totalAmount").toString());
            }

            boolean b = checkOutTime(supplier, one, newTotalAmount);
            if (b) {
                one.setStopServe(1);
            }

            // 查询余额变动
            List<PlatformBalanceOperate> list = platformBalanceOperateService.lambdaQuery()
                    .eq(PlatformBalanceOperate::getRelevanceId, one.getPlatformDealFeeId())
                    .eq(PlatformBalanceOperate::getRelevanceType, 1)
                    .orderByDesc(PlatformBalanceOperate::getGmtCreate)
                    .list();
            one.setBalanceODtls(list);
        }
        return one;
    }

    /**
     * 设置欠费时间
     *
     * @param platformDealFee
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void setServerStopDate(PlatformDealFee platformDealFee) {
        PlatformDealFee byId = getById(platformDealFee);
        if (byId.getArrearageDateTime() == null) {
            byId.setArrearageDateTime(new Date());
            platformDealFeeService.update(byId);
        }

    }


    /**
     * 校验是否欠费，返回布尔，用于查看详情是否停止服务
     * freem7
     *
     * @param supplier
     * @param platformDealFee
     * @param thisFree
     * @return
     */
    public boolean checkOutTime(EnterpriseInfo supplier, PlatformDealFee platformDealFee, BigDecimal thisFree) {
        if (thisFree.compareTo(BigDecimal.ZERO) == 0) {
            return false;
        }
        Integer serveType = platformDealFee.getServeType();
//        QueryWrapper<PlatformDealFeeDtl> q2 = new QueryWrapper<>();
//        q2.eq("enterprise_id", supplier.getEnterpriseId())
//                .eq("serve_type", serveType)
//                .eq("pay_type", 2)
//                .eq("state", 0)
//                .select("sum(deal_amount) as totalAmount");
//        List<Map<String, Object>> maps2 = platformDealFeeDtlMapper.selectMaps(q2);
//        BigDecimal dealAmount = new BigDecimal(0);
//        if (maps2.get(0) != null) {
//            dealAmount = new BigDecimal(maps2.get(0).get("totalAmount").toString());
//        }


        // 拿到占用免费金额部分
        QueryWrapper<PlatformDealFeeDtl> ep = new QueryWrapper<>();
        ep.eq("enterprise_id", supplier.getEnterpriseId())
                .eq("serve_type", serveType)
                .eq("state", 0)
                .select("sum(use_exempt_free) as useExemptFree");
        List<Map<String, Object>> epMap = platformDealFeeDtlMapper.selectMaps(ep);
        BigDecimal useExemptFree = new BigDecimal(0);
        if (epMap.get(0) != null) {
            useExemptFree = new BigDecimal(epMap.get(0).get("useExemptFree").toString());
        }
        // 拿到最大免费额度
        String queryKey = serveType == 1 ? PublicEnum.PLATFORM_DEAL_MAX_QUOTA_DP.getRemark() : PublicEnum.PLATFORM_DEAL_MAX_QUOTA_LY.getRemark();
        SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode, queryKey).one();
        BigDecimal totalEd = new BigDecimal(systemParam.getKeyValue());

        boolean flag = false;
//        BigDecimal addEFree = useExemptFree.add(dealAmount);
        BigDecimal addEFree = useExemptFree;
        // 免费金额
        if (addEFree.compareTo(totalEd) == -1) {
            return flag;
        }else {
            BigDecimal arrearage = supplier.getArrearage();
            // 判断他审核的本次的金额会不会导致欠费，只是为了处理欠费时间
            if (arrearage.compareTo(BigDecimal.ZERO) == 0) {
                flag = true;
            } else {
                if (thisFree.compareTo(arrearage) == 1) {
                    flag = true;
                } else {
                    // 未超过欠费额度
                    Integer arrearageDateNum = supplier.getArrearageDateNum();
                    Integer arrearageDateType = supplier.getArrearageDateType();
                    if (arrearageDateNum != null && platformDealFee.getArrearageDateTime() != null) {
                        // 说明设置了欠费时间，并且已经欠费
                        Date arrearageDateTime = platformDealFee.getArrearageDateTime();
                        int day = 0;
                        if (arrearageDateType == 1) {
                            day = arrearageDateNum;
                        } else if (arrearageDateType == 2) {
                            day = arrearageDateNum * 30;
                        } else if (arrearageDateType == 3) {
                            day = arrearageDateNum * 365;
                        } else {
                            throw new BusinessException("欠费时长时间类型错误！");
                        }
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(arrearageDateTime);
                        calendar.add(Calendar.DATE, day);
                        Date newDate = calendar.getTime();

                        Date currentDate = new Date();  // 获取当前日期
                        if (currentDate.after(newDate)) { // 当前时间在欠费截止时间之后
                            flag = true;
                        } else if (currentDate.before(newDate)) {
                        } else {
                            flag = true;
                        }
                    }
                }
            }
        }
        return flag;
    }
}
