package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ShopRoleAssociation;

/**
 * @描述：店铺-角色类型关联表 服务类
 * @作者: y
 * @日期: 2022-11-07
 */
public interface ShopRoleAssociationService extends IService<ShopRoleAssociation> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShopRoleAssociation> queryWrapper);

    void create(ShopRoleAssociation shopRoleAssociation);

    void update(ShopRoleAssociation shopRoleAssociation);

    ShopRoleAssociation getById(String id);

    void delete(String id);
}
