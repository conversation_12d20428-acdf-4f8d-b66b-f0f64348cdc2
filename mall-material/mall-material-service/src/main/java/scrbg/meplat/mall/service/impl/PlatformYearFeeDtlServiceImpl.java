package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.PlatformYearFeeDtl;
import scrbg.meplat.mall.mapper.PlatformYearFeeDtlMapper;
import scrbg.meplat.mall.service.PlatformYearFeeDtlService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：平台年费明细 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
@Service
public class PlatformYearFeeDtlServiceImpl extends ServiceImpl<PlatformYearFeeDtlMapper, PlatformYearFeeDtl> implements PlatformYearFeeDtlService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformYearFeeDtl> queryWrapper) {
        IPage<PlatformYearFeeDtl> page = this.page(
        new Query<PlatformYearFeeDtl>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(PlatformYearFeeDtl platformYearFeeDtl) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(platformYearFeeDtl);
    }

    @Override
    public void update(PlatformYearFeeDtl platformYearFeeDtl) {
        super.updateById(platformYearFeeDtl);
    }


    @Override
    public PlatformYearFeeDtl getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
