package scrbg.meplat.mall.util;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.Session;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * java 远程执行linux命令
 */
public class LinuxOperUtl {

    /**
     * 远程执行 linux 命令
     */
    public static String RemoteSubmitCommand(String command,String host,String userName,String password) {
        // 设置主机IP地址
        Connection c = new Connection(host);
        StringBuilder buffer = new StringBuilder();
        try {
            c.connect();
            // 设置登录名称和登录密码
            boolean flag = c.authenticateWithPassword(userName, password);
            System.out.println(flag);
            Session session = c.openSession();
            session.execCommand(command);

            BufferedReader br = new BufferedReader(new InputStreamReader(session.getStdout(), "UTF-8"));

            String line = null;
            while ((line = br.readLine()) != null) {
                System.out.println("行:" + line);
                buffer.append(line).append("\n");
            }

            System.out.println("end");
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return buffer.toString();
    }

    /**
     * 本地执行 shell 命令 包括java shell python
     */
    public static Boolean submitModelDataSync(String command) throws IOException {
        boolean flag = true;
        try {
            final Process process = Runtime.getRuntime().exec(command);
            BufferedReader br1 = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            long startTime = System.currentTimeMillis() / 1000;
            while (true) {
                long endTime = System.currentTimeMillis() / 1000;
                if (endTime - startTime > 10000000) {
                    return false;
                }
                String line = br1.readLine();
                if (line.contains("success")) {
                    flag = true;
                    break;
                }
                if (line.contains("Error") || line.contains("error")) {
                    flag = false;
                    break;
                }
            }
            br1.close();
        } catch (Exception e) {
        }
        return flag;
    }
}

