package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：友情链接
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "友情链接")
@Data
@TableName("links")
public class Links extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "链接id")

    private String linkId;

    @ApiModelProperty(value = "链接名")

    private String name;


    @ApiModelProperty(value = "链接url")

    private String url;


    @ApiModelProperty(value = "状态 1:发布 0:未发布")

    private Integer state;

    @ApiModelProperty(value = "链接显示 0:首页显示 1:首页未显示")

    private Integer display;

}
