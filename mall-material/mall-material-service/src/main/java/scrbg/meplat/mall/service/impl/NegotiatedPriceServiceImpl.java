package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.NegotiatedPrice;
import scrbg.meplat.mall.mapper.NegotiatedPriceMapper;
import scrbg.meplat.mall.service.NegotiatedPriceService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.user.userCenter.NegotiatedPriceVo;

import java.util.List;
import java.util.Map;

/**
 * @描述：报价 服务类
 * @作者: y
 * @日期: 2022-11-22
 */
@Service
public class NegotiatedPriceServiceImpl extends ServiceImpl<NegotiatedPriceMapper, NegotiatedPrice> implements NegotiatedPriceService{
    @Autowired
    MallConfig mallConfig;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<NegotiatedPrice> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String relevanceId = (String) innerMap.get("relevanceId");
        Integer type = (Integer) innerMap.get("type");
        if(StringUtils.isNotBlank(keywords)){
            q.and((t) -> {
                t.like(NegotiatedPrice::getEnquiryAmount,keywords)
                        .or()
                        .like(NegotiatedPrice::getIllustrate,keywords);

            });
        }
        q.eq(type != null,NegotiatedPrice::getType,type);
        q.eq(StringUtils.isNotBlank(relevanceId),NegotiatedPrice::getRelevanceId,relevanceId);
        q.orderByDesc(NegotiatedPrice::getGmtModified);
        q.eq(NegotiatedPrice::getMallType, mallConfig.mallType);
        IPage<NegotiatedPrice> page = this.page(
        new Query<NegotiatedPrice>().getPage(jsonObject),
        q
        );
        return new PageUtils(page);
    }

    @Override
    public void create(NegotiatedPrice negotiatedPrice) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(negotiatedPrice);
    }

    @Override
    public void update(NegotiatedPrice negotiatedPrice) {
        super.updateById(negotiatedPrice);
    }


    @Override
    public NegotiatedPrice getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public PageUtils listByUser(JSONObject jsonObject, QueryWrapper<NegotiatedPriceVo> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String userId = user.getUserId();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String relevanceId = (String) innerMap.get("relevanceId");
        Integer type = (Integer) innerMap.get("type");
        if (type!=null){
            if (type==2){
                q.eq("n.type", 0);
            }else {
                q.eq("n.type", type);
            }
        }
        q.eq(userId != null,"n.founder_id",userId);
        q.eq(StringUtils.isNotBlank(relevanceId),"n.relevance_id",relevanceId);
        q.orderByDesc("n.gmt_create");
        q.eq("n.mall_type", mallConfig.mallType);
        IPage<NegotiatedPriceVo> pages = new Query<NegotiatedPriceVo>().getPage(jsonObject);
        List<NegotiatedPriceVo>  list=baseMapper.listByUser(pages,q);
        pages.setRecords(list);
        return  new PageUtils(pages);
    }
}
