package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrdersService;

import java.util.List;

/**
 * @描述：订单项控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/")
@Api(tags = "订单项")
public class OrderItemController {

    @Autowired
    public OrderItemService orderItemService;


    @Autowired
    public OrdersService ordersService;

    @PostMapping("orderItem/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderItem> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderItemService.queryPage(jsonObject, new LambdaQueryWrapper<OrderItem>());
        return PageR.success(page);
    }

    @GetMapping("orderItem/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<OrderItem> findById(String id) {
        OrderItem orderItem = orderItemService.getById(id);
        return R.success(orderItem);
    }

    @PostMapping("orderItem/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody OrderItem orderItem) {
        orderItemService.create(orderItem);
        return R.success();
    }

    @PostMapping("orderItem/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody OrderItem orderItem) {
        orderItemService.update(orderItem);
        return R.success();
    }

    @GetMapping("orderItem/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        orderItemService.delete(id);
        return R.success();
    }


    @PostMapping("orderItem/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        orderItemService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("shopManage/orders/listBidingOrderItemsList")
    @ApiOperation(value = "查询可以选择的竞价采购的零星采购多供方订单明细")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
    })
    public PageR<OrderItem> listBidingOrderItemsList(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderItemService.listBidingOrderItemsList(jsonObject);
        return PageR.success(page);
    }

    @PostMapping("shopManage/orders/listBidingOrderList")
    @ApiOperation(value = "查询可以选择的竞价采购订单列表")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "billType", value = "清单类型（1浮动价格2固定价格）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
    })
    public PageR<Orders> listBidingOrderList(@RequestBody JSONObject jsonObject) {
        PageUtils page = ordersService.listBidingOrderList(jsonObject,new LambdaQueryWrapper<Orders>());
        return PageR.success(page);
    }

    @PostMapping("shopManage/orders/listBidingOrderListIds")
    @ApiOperation(value = "查询可以选择的竞价采购订单号分页")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "billType", value = "清单类型（1浮动价格2固定价格）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
    })
    public PageR<OrderItem> listBidingOrderListIds(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderItemService.listBidingOrderListIds(jsonObject,new LambdaQueryWrapper<OrderItem>());
        return PageR.success(page);
    }
}

