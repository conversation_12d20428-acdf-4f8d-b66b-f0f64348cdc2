package scrbg.meplat.mall.util.excel.productCompareExcel;

import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.AbstractCellWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * @program: maill_api
 * @description: 针对样式（（单个文件生成））
 * @author: 代文翰
 * @create: 2023-12-14 16:42
 **/
public class Sheet<PERSON>and<PERSON> extends AbstractCellWriteHandler {
    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        //super.afterCellDispose(context);

        // 这是当前单元格的对象
        //单独设置样式
        context.getRow().setHeight((short) 1300);
        Integer columnIndex = context.getColumnIndex();
        context.getRow().getCell(columnIndex).getSheet().setColumnWidth(columnIndex, 5000);
        // 针对图片做处理
        CellType cellType = context.getCell().getCellType();
        if (cellType == CellType.BLANK) {
            // 图片
            Workbook workbook = context.getCell().getSheet().getWorkbook();

            Sheet sheet = context.getCell().getSheet();
            //int columnIndex = context.getColumnIndex();
            int rowIndex = context.getRowIndex();

            Row row = sheet.getRow(rowIndex);
            Cell cell = row.getCell(columnIndex);
            // 暂时不使用缩放图片
            if (cell.getCellType() == CellType.BLANK && false) {
                List<WriteCellData<?>> cellDataList = context.getCellDataList();
                if (cellDataList == null || cellDataList.size() == 0) {
                    return;
                }
                List<ImageData> imageDataList = cellDataList.get(0).getImageDataList();
                if (imageDataList == null || imageDataList.size() == 0) {
                    return;
                }

                // 获取原图片的行号和列号
                int originalRowIndex = context.getRowIndex();
                int originalColumnIndex = columnIndex;
                // 删除原图片
                removeImage(sheet, originalRowIndex, originalColumnIndex);
                byte[] bytes = imageDataList.get(0).getImage();
                //context.getCellDataList().get(0).getImageDataList().get(0).getImage()
                if (bytes != null && bytes.length > 0) {
                    // 调整图片大小
                    int columnWidth = sheet.getColumnWidth(columnIndex);
                    int rowHeight = row.getHeight();

                    double ratio = 0.9;
                    try {
                        ImageSize imageSize = ImageUtils.getImageSize(bytes);
                        int pictureWidth = imageSize.getWidth();
                        int pictureHeight = imageSize.getHeight();
                        if (pictureWidth > columnWidth) {
                            ratio = (double) columnWidth / pictureWidth * 0.9;
                        }

                        if (pictureHeight * ratio > rowHeight) {
                            ratio = (double) rowHeight / pictureHeight * 0.9;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    XSSFClientAnchor anchor = new XSSFClientAnchor(
                            0, 0, 0, 0, columnIndex, rowIndex, columnIndex + 1, rowIndex + 1
                    );
                    anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_DONT_RESIZE);
                    Drawing<?> patriarch = sheet.createDrawingPatriarch();
                    // 移除当前坐标的图片

                    //patriarch.createPicture()
                    ImageData.ImageType imageType = imageDataList.get(0).getImageType();
                    XSSFPicture picture = (XSSFPicture) patriarch.createPicture(anchor, workbook.addPicture(bytes, Workbook.PICTURE_TYPE_PNG));
                    picture.resize(ratio);
                }
            }
        }
    }

    // 新增方法：删除原图片
// 新增方法：删除原图片
// 新增方法：删除原图片
// 新增方法：删除原图片
    private void removeImage(Sheet sheet, int rowIndex, int columnIndex) {
        // 移除指定坐标图片


    }



}
