package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.PageInfo;
/**
 * @描述： 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
public interface PageInfoService extends IService<PageInfo> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PageInfo> queryWrapper);

        void create(PageInfo pageInfo);

        void update(PageInfo pageInfo);

        PageInfo getById(String id);

        void delete(String id);
}
