package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.ProductCollect;

import java.util.List;

/**
 * @描述：个人商品收藏 Mapper 接口
 * @作者: sund
 * @日期: 2022-11-10
 */
@Mapper
@Repository
public interface ProductCollectMapper extends BaseMapper<ProductCollect> {

    void deleteBycollectId(@Param("collectId")String collectId);
    @Delete("<script>" +
            "DELETE FROM product_collect WHERE product_id in " +
            "<foreach collection='ids' open='(' item='id_' separator=',' close=')'> #{id_}" +
            "</foreach>" +
            "</script>")
    void deleBath(@Param("ids")List<String> ids);

//    void delCollectByProductIds(@Param("params") HashMap<String, Object> hashMap,@Param("ids") List<String> productIdList);

    void delCollectByProductIds(@Param("userId")String userId,
                                @Param("mallType")Integer mallType,
                                @Param("collectType")Integer collectType,
                                @Param("ids") List<String> productIdList);
}
