package scrbg.meplat.mall.entity;

import lombok.Data;

import java.util.Date;

@Data
public class AuditRecords {

    private String id;

    private String nodeId;

    /**
     * 审核类型；需自行转换
     */
    private String auditTypeStr;

    /**
     * 0:提交 1:审核 2: 审核不通过
     */
    private Integer auditType;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 审核次级
     */
    private String reviewSecondary;

    /**
     * 通知时间
     */
    private Date noticeTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核意见
     */
    private String auditRemark;

}
