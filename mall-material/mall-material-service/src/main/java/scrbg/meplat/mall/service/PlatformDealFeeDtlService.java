package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.PlatformDealFeeDtl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @描述：平台交易费明细 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
public interface PlatformDealFeeDtlService extends IService<PlatformDealFeeDtl> {

    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeDtl> queryWrapper);

    void create(PlatformDealFeeDtl platformDealFeeDtl);

    void update(PlatformDealFeeDtl platformDealFeeDtl);

    PlatformDealFeeDtl getById(String id);

    void delete(String id);

    void deleteBatch(List<String> ids);

    /**
     * 供应商查询待缴费的明细
     *
     * @param jsonObject
     * @param q
     * @return
     */
    PageUtils notPayDealFeeDtlList(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeDtl> q);


    /**
     * 查询待缴费的明细
     *
     * @return
     */
    List<PlatformDealFeeDtl> getNotPayDealDtlList(String enterpriseId, Integer serveType);

    /**
     * 校验本次交易是否欠费
     */
    void checkDealAmount(MaterialReconciliation mr, int operateType);


    /**
     * 保存交易明细（审核对账单时收取）废弃
     *
     * @param mr
     */
    void addDealFeeDtl(MaterialReconciliation mr);


    /**
     * 对账单作废审核通过，作废交易明细
     *
     * @param mr
     */
    void dealFeeCancellation(MaterialReconciliation mr);

    /**
     * 对账单作废审核通过，新增一条负数记录，并冲消该条对账单对应服务费记录
     *
     * @param reconciliation 对账单
     */
    void addNegativeFeeCancellation(MaterialReconciliation reconciliation);

    /**
     * 二级供应商对账单作废审核通过，新增一条负数记录，并冲消该条对账单对应服务费记录
     *
     * @param reconciliation 对账单
     */
    void addTwoNegativeFeeCancellation(SupplierReconciliation reconciliation);


    PageUtils<PlatformDealFeeDtl> ListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeDtl> q);

    /**
     * 查询季度待缴费的明细
     *
     * @return
     */
    List<PlatformDealFeeDtl> getNotPayQuarterlyDealDtlList(String enterpriseId,
                                                           Integer serveType,
                                                           LocalDate quarterStartDate,
                                                           LocalDate quarterEndDate);

    /**
     * 校验本次交易是否欠-二级供应商对账单费供应商对账单     * @param mr
     */
    void checkTwoDealAmount(SupplierReconciliation sr);
    /**
     * 根据企业id查询交易明细
     *
     */
    List<PlatformDealFeeDtl> getByEnterpriseId(String enterpriseId);
}
