package scrbg.meplat.mall.util.countExcel;

import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import scrbg.meplat.mall.util.poi.exp.PoiExporter;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Map;

public class ExcelForWebUtil {


    /** 传入的map里面有list的话必须是voList，不能是mapList
     * @param response
     * @param beanParams     数据
     * @param templementFile 模板名称
     * @param dir            模板路径
     * @param fileName       文件名称
     * @throws Exception
     */
    public static void exportExcel(HttpServletResponse response, Map<String, Object> beanParams, String templementFile,
                                   String dir, String fileName) throws Exception {
        String url = dir + File.separator + templementFile;
        FileInputStream fin = new FileInputStream(url);
        response.setHeader("Content-Disposition",
                "attachment; filename=\"" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + "\"");
        PoiExporter.export2Destination(fin, beanParams, response.getOutputStream());
        fin.close();
        fin = null;
    }
    public static void exportExcelQrCode(HttpServletResponse response, Map<String, Object> beanParams, String templementFile,
                                   String dir, String fileName,String text) throws Exception {
        String url = dir + File.separator + templementFile;
        FileInputStream fin = new FileInputStream(url);
        response.setHeader("Content-Disposition",
                "attachment; filename=\"" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + "\"");
        PoiExporter.export2DestinationQrCode(text,fin, beanParams, response.getOutputStream());
        fin.close();
        fin = null;
    }

    /**
     * 生成Excel返回流
     * @param response 文件流返回
     * @param beanParams 参数列表
     * @param templementFile
     * @param dir
     * @param fileName
     * @throws Exception
     */
    public static OutputStream exportExcelWithStream(HttpServletResponse response, Map<String, Object> beanParams, String templementFile,
                                   String dir, String fileName) throws Exception {
        // 文件输出流
        OutputStream outputStream = null;
        String url = dir + File.separator + templementFile;
        FileInputStream fin = new FileInputStream(url);
        response.setHeader("Content-Disposition",
                "attachment; filename=\"" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + "\"");
        PoiExporter.export2Destination(fin, beanParams, outputStream);
        fin.close();
        fin = null;
        return outputStream;
    }

    /**
     * 传入的map里面有list的话必须是voList，不能是mapList
     *
     * @param savefileName
     * @param beanParams
     * @param templementFile
     * @throws Exception
     */
    public static void saveExcel( String savefileName, Map<String, Object> beanParams,
                                 String templementFile) throws Exception {
        FileInputStream fin = new FileInputStream(templementFile);
        File f = new File(savefileName);
        if (f.isFile()) {
            f.createNewFile();
        }
        FileOutputStream fos = new FileOutputStream(f);
        PoiExporter.export2Destination(fin, beanParams, fos);
        fos.close();
        fin.close();
        fos = null;
        fin = null;
    }


//    InputStream is = ImportGenericTest.class.getClassLoader().getResourceAsStream("excel/order.xlsx");
//    PoiGenericSheetVo<OrderImportVo> genericSheetVo = PoiImporter.importFirstSheetFrom(is, OrderImportVo.class);
//		System.out.println("===>" + JSON.toJSONString(genericSheetVo.getHead()));
//		System.out.println("===>" + JSON.toJSONString(genericSheetVo.getBody()));
}
