package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import lombok.Data;

import java.io.Serializable;

/**
 * 公告操作日志对象 content_log
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
public class ContentLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 内容id
     */
    private String contentId;

    /**
     * 操作类型
     */
    private Integer type;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH-mm-ss")
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH-mm-ss")
    private Date gmtModified;

    /**
     * 创建人Id
     */
    private String founderId;

    /**
     * 创建人名称
     */
    private String founderName;

    /**
     * 修改人名称
     */
    private String modifyName;

    /**
     * 修改人id
     */
    private String modifyId;

}
