package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.SysUserOrg;
import scrbg.meplat.mall.mapper.SysUserOrgMapper;
import scrbg.meplat.mall.service.SysUserOrgService;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2024-04-30
 */
@Service
public class SysUserOrgServiceImpl extends ServiceImpl<SysUserOrgMapper, SysUserOrg> implements SysUserOrgService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysUserOrg> queryWrapper) {
        IPage<SysUserOrg> page = this.page(
        new Query<SysUserOrg>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(SysUserOrg sysUserOrg) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(sysUserOrg);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(SysUserOrg sysUserOrg) {
        super.updateById(sysUserOrg);
    }


    @Override
    public SysUserOrg getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     @Transactional(rollbackFor = Exception.class)
     @NotResubmit
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
         }
}
