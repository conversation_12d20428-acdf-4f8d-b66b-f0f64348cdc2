package scrbg.meplat.mall.enums.shop.deviceDemand;

public enum DeviceDemandEnum {

    RESULT_CODE_500401(500401,"该需求已被删除！"),
    RESULT_CODE_500402(500402,"该需求已失效！"),

    STATE_INIT(0,"初始化"),
    STATE_YES(1,"已审核"),
//    STATE_INIT(2,"报价中"),
//    STATE_INIT(3,"报价中"),

    CHECK_STATE_INIT(0,"待审核"),
    CHECK_STATE_YES(1,"通过"),
    CHECK_STATE_NO(2,"不通过");

    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    DeviceDemandEnum() {
    }

    DeviceDemandEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    }

