package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.FloorGoods;

import java.util.List;

/**
 * @描述：楼层显示的商品 服务类
 * @作者: 胡原武
 * @日期: 2022-11-10
 */
public interface FloorGoodsService extends IService<FloorGoods> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<FloorGoods> queryWrapper);

    void create(FloorGoods floorGoods);

    void update(FloorGoods floorGoods);

    FloorGoods getById(String id);

    void delete(String id);

    PageUtils queryFlooerGoodsPage(JSONObject jsonObject, LambdaQueryWrapper<FloorGoods> floorGoodsLambdaQueryWrapper);

    /**
     * 批量新增
     * @param floorGoods
     */
    void batchCreate(List<FloorGoods> floorGoods);

    void updateByPublish(List<String> ids, String s);
}
