package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：
 * @作者: ye
 * @日期: 2025-01-16
 */
@ApiModel(value="")
@Data
@TableName("shop_business")
public class ShopBusiness extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺名称")

    private String shopName;


    @ApiModelProperty(value = "店铺状态 1:启用 0停用")

    private Integer state;


    @ApiModelProperty(value = "企业名称")

    private String enterpriseName;


    @ApiModelProperty(value = "远程机构id")

    private String interiorId;


    @ApiModelProperty(value = "企业id")

    private String enterpriseId;


















    @ApiModelProperty(value = "修改人名称")

    private String modifyName;


    @ApiModelProperty(value = "修改人id")

    private String modifyId;



}