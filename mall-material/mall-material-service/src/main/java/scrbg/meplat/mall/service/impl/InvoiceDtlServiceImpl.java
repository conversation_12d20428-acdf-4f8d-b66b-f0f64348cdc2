package scrbg.meplat.mall.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import scrbg.meplat.mall.entity.InvoiceDtl;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.mapper.InvoiceDtlMapper;
import scrbg.meplat.mall.service.InvoiceDtlService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.service.MaterialReconciliationService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-11-13
 */
@Service
public class InvoiceDtlServiceImpl extends ServiceImpl<InvoiceDtlMapper, InvoiceDtl> implements InvoiceDtlService {
    @Autowired
    MaterialReconciliationService materialReconciliationService;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<InvoiceDtl> queryWrapper) {
        IPage<InvoiceDtl> page = this.page(
                new Query<InvoiceDtl>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(InvoiceDtl invoiceDtl) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(invoiceDtl);
    }

    @Override
    public void update(InvoiceDtl invoiceDtl) {
        super.updateById(invoiceDtl);
    }


    @Override
    public InvoiceDtl getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    @Override
    public void deleteInvoiceId(String invoiceId) {
        List<InvoiceDtl> list = lambdaQuery().eq(InvoiceDtl::getInvoiceId, invoiceId).list();
        HashSet<String> hashSet = new HashSet<>();
        for (InvoiceDtl invoiceDtl : list) {
            hashSet.add(invoiceDtl.getReconciliationId());
        }
        List<String> collect = list.stream().map(InvoiceDtl::getInvoiceDtlId).collect(Collectors.toList());
        deleteBatch(collect);
        ArrayList<String> reList = new ArrayList<>();
        for (String s : hashSet) {
            reList.add(s);
        }
        materialReconciliationService.lambdaUpdate()
                .in(MaterialReconciliation::getReconciliationId,reList)
                .set(MaterialReconciliation::getInvoiceState,0).update();

    }


    @Override
    public List<InvoiceDtl> getListByInvoiceId(String invoiceId) {
        return lambdaQuery().eq(InvoiceDtl::getInvoiceId,invoiceId).list();
    }
}
