package scrbg.meplat.mall.openInterface.pcwp2;

import com.alibaba.fastjson.JSON;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.MaterialReconciliationService;
import scrbg.meplat.mall.service.OrderShipService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.vo.shopManage.ReturnGoodsVo;

/**
 * <AUTHOR>
 * @create 2023-07-26 9:49
 */
@RestController
@RequestMapping("/w/thirdApi/orderShipReturn")
@Api(tags = "发货单退货相关（对接）")
public class OrderShipReturnController {

    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @Autowired
    private OrderShipService orderShipService;

    @PostMapping("/create")
    @ApiOperation(value = "收料单退货")
    public R createPCWPOrderRetuen(@RequestBody ReturnGoodsVo dto) {
        String idStr = dto.getKeyId();
        try {
            orderShipService.createPCWPOrderRetuen2(dto);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.writeErrorLog(idStr, "createPCWPOrderReturn", dto, null, null, e.getMessage(), OrderShipReturnController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(OrderShipReturnController.class.getName());
            iLog.setMethodName("createPCWPOrderRetuen");
            iLog.setLocalArguments(JSON.toJSONString(dto));
            iLog.setIsSuccess(0);
            iLog.setLogType(3);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @GetMapping("/rollBackCreate")
    @ApiOperation(value = "回滚收料单退货")
    public R rollBackReconciliationCreate(String keyId) {
        orderShipService.rollBackReconciliationCreate(keyId);
        return R.success();
    }

}
