package scrbg.meplat.mall.exception;

import com.mysql.cj.jdbc.exceptions.CommunicationsException;
import com.scrbg.common.exception.RRException;
import com.scrbg.common.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.util.HashMap;
import java.util.Map;

@RestControllerAdvice
@Slf4j
public class RRExceptionHandler {

    /**
     * 方法参数校验
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public R<Map> handleVaildException(MethodArgumentNotValidException e) {
        log.error("数据校验出现问题{}, 异常类型:{}", e.getMessage(), e.getClass());
        BindingResult bindingResult = e.getBindingResult();
        HashMap<String, String> errorMap = new HashMap<>();
        bindingResult.getFieldErrors().forEach(fieldError -> {
            errorMap.put(fieldError.getField(), fieldError.getDefaultMessage());
        });
        return R.validateFailed(errorMap);
    }

    /**
     * 处理自定义异常
     *
     * @param e
     * @return
     */


//    @ExceptionHandler(HttpMessageNotReadableException.class)
//    public R handleRRException(HttpMessageNotReadableException e) {
//        log.error(e.getMessage(), e);
//        return R.failed(400, "未携带数据获取数据类型错误！");
//    }

    @ExceptionHandler(RRException.class)
    public R handleRRException(RRException e) {
        log.error(e.getMessage(), e);
        return R.failed(e.getCode(),e.getMessage());
    }

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public R handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        return R.failed(500, "对象大小超出限制, 请压缩或降低对象质量! ");
    }

    @ExceptionHandler(BusinessException.class)
    public R handleBusinessException(BusinessException e) {
        log.error(e.getMessage(), e);
        return R.failed(e.getCode(), e.getMessage());
    }

//    @ExceptionHandler(CommunicationsException.class)
//    public R handleBusinessException(CommunicationsException e) {
//        log.error(e.getMessage(), e);
//        return R.failed(500, "请刷新！");
//    }

    @ExceptionHandler(Exception.class)
    public R handleException(Exception e) {
        log.error(e.getMessage(), e);
        return R.failed(500, "系统错误或系统繁忙，请稍后再试！");
    }


}
