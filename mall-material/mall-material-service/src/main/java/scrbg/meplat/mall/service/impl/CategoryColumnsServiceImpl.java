package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.CategoryColumns;
import scrbg.meplat.mall.entity.Floor;
import scrbg.meplat.mall.entity.FloorGoods;
import scrbg.meplat.mall.entity.Links;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.enums.CodeEnum;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.CategoryColumnsMapper;
import scrbg.meplat.mall.mapper.FloorGoodsMapper;
import scrbg.meplat.mall.mapper.FloorMapper;
import scrbg.meplat.mall.service.CategoryColumnsService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.util.CodeUtil;
import scrbg.meplat.mall.vo.floor.website.WColumnFloorVO;
import scrbg.meplat.mall.vo.floor.website.WFloorGoodsVO;
import scrbg.meplat.mall.vo.floor.website.WFloorVO;
import scrbg.meplat.mall.vo.product.website.IndexMaterialVO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: y
 * @日期: 2022-12-07
 */
@Service
public class CategoryColumnsServiceImpl extends ServiceImpl<CategoryColumnsMapper, CategoryColumns> implements CategoryColumnsService {

    @Autowired
    private FloorMapper floorMapper;

    @Autowired
    private FloorGoodsMapper floorGoodsMapper;

    @Autowired
    private MallConfig mallConfig;

    @Autowired
    private ProductService productService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<CategoryColumns> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String columnName = (String) innerMap.get("columnName");
        String remarks = (String) innerMap.get("remarks");
        Integer state = (Integer) innerMap.get("state");
        Integer mallType = (Integer) innerMap.get("mallType");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String programaKey = (String) innerMap.get("programaKey");

        if (!StringUtils.isEmpty(columnName)) {
            queryWrapper.like(CategoryColumns::getColumnName, columnName);
        }
        if (!StringUtils.isEmpty(remarks)) {
            queryWrapper.like(CategoryColumns::getRemarks, remarks);
        }
        if (!StringUtils.isEmpty(keywords)) {
            queryWrapper.like(CategoryColumns::getColumnName, keywords).or().like(CategoryColumns::getRemarks, keywords);
        }
        if (mallType != null) {
            queryWrapper.eq(CategoryColumns::getMallType, mallType);
        }
        if (state != null) {
            queryWrapper.eq(CategoryColumns::getState, state);
        }

        //排序方式(排序值升序)
        if (orderBy == null) {
        } else if (orderBy == PublicEnum.ORDER_BY_SORT.getCode()) {
            queryWrapper.orderByDesc(CategoryColumns::getSort).orderByDesc(CategoryColumns::getGmtCreate);
            //排序方式(修改时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(CategoryColumns::getGmtModified);
            //排序方式(发布时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_RELEASE.getCode()) {
//            queryWrapper.orderByDesc(Column::getGmtRelease);
        }

        IPage<CategoryColumns> page = this.page(
                new Query<CategoryColumns>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(CategoryColumns categoryColumns) {
        CategoryColumns one = lambdaQuery().eq(CategoryColumns::getColumnName, categoryColumns.getColumnName()).one();
        if (one != null) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "名称重复！");
        }
        categoryColumns.setColumnNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_LM.getRemark()));
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(categoryColumns);
    }

    @Override
    public void update(CategoryColumns categoryColumns) {
        CategoryColumns one = lambdaQuery().eq(CategoryColumns::getColumnName, categoryColumns.getColumnName()).one();
        if (one != null) {
            if (one.getColumnId().equals(categoryColumns.getColumnId())) {
                super.updateById(categoryColumns);
            } else {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "名称重复！");
            }
        } else {
            super.updateById(categoryColumns);
        }
        super.updateById(categoryColumns);
    }


    @Override
    public CategoryColumns getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void updateByPublish(List<String> ids, String type) {
        List<CategoryColumns> resutls = listByIds(ids);
        for (CategoryColumns categoryColumns : resutls) {
            if ("1".equals(type)) {
                categoryColumns.setGmtRelease(new Date());
                categoryColumns.setState(1);
            } else {
                categoryColumns.setState(0);
                categoryColumns.setGmtRelease(null);
            }
        }
        super.saveOrUpdateBatch(resutls);
    }

    /**
     * 首页栏目
     *
     * @param size
     * @return
     */
    public List<WColumnFloorVO> listBySize(Integer size) {
        QueryWrapper<WColumnFloorVO> vosWrapper = new QueryWrapper<>();
        List<WColumnFloorVO> vos= baseMapper.wColumnFloorVOsList(vosWrapper);
        if (CollectionUtils.isEmpty(vos)) return vos;
        for (WColumnFloorVO vo2 : vos) {
            List<WFloorVO> floorVOS = vo2.getFloorVOS();
            if (!floorVOS.isEmpty()){
                for (WFloorVO floorVO : floorVOS) {
                    QueryWrapper<WFloorGoodsVO> goodsWrapper = new QueryWrapper<>();
                    goodsWrapper.eq("f.floor_id",floorVO.getFloorId())
                            .eq("f.state",PublicEnum.IS_YES.getCode())
                            .eq("f.is_delete",0)
                            .orderByDesc("f.sort","f.gmt_create")
                            .last(size != null, "limit " + size);
                    List<WFloorGoodsVO> wFloorGoodsVOlist= floorGoodsMapper.wFloorGoodsVOByList(goodsWrapper);
                    if (wFloorGoodsVOlist!=null&&wFloorGoodsVOlist.size()>0){
                        for (WFloorGoodsVO wFloorGoodsVO : wFloorGoodsVOlist) {
                            wFloorGoodsVO.setFloorName(floorVO.getFloorName());
                            wFloorGoodsVO.setFloorNameText(floorVO.getFloorNameText());
                        }
                        floorVO.setGoodsVOS(wFloorGoodsVOlist);
                    }
                }
            }
        }
//        List<WColumnFloorVO> vos = new ArrayList<>();
//        List<CategoryColumns> list = lambdaQuery().eq(CategoryColumns::getState, PublicEnum.IS_YES.getCode())
//                .orderByDesc(CategoryColumns::getSort)
//                .eq(CategoryColumns::getIsFixed,0)
//                .list();
//        if (CollectionUtils.isEmpty(list)) return vos;
//        for (CategoryColumns c : list) {
//            WColumnFloorVO vo = new WColumnFloorVO();
//            BeanUtils.copyProperties(c, vo);
//            LambdaQueryWrapper<Floor> q = Wrappers.lambdaQuery(Floor.class);
//            q.eq(Floor::getState,PublicEnum.IS_YES.getCode())
//                    .eq(Floor::getColumnId,c.getColumnId())
//                    .eq(Floor::getMallType,mallConfig.mallType)
//                    .orderByDesc(Floor::getSort);
//            List<Floor> floors = floorMapper.selectList(q);
//            List<WFloorVO> floorVOS = new ArrayList<>();
//            // 如果楼层不是空
//            if(!CollectionUtils.isEmpty(floors)){
//                for (Floor floor : floors) {
//                    WFloorVO wFloorVO = new WFloorVO();
//                    BeanUtils.copyProperties(floor,wFloorVO);
//                    List<WFloorGoodsVO> floorGoodsVOS = new ArrayList<>();
//                    LambdaQueryWrapper<FloorGoods> q2 = Wrappers.lambdaQuery(FloorGoods.class);
//                    q2.eq(FloorGoods::getFloorId,floor.getFloorId())
//                            .eq(FloorGoods::getState,PublicEnum.IS_YES.getCode())
//                            .orderByDesc(FloorGoods::getSort).last(size != null, "limit " + size);
//                    List<FloorGoods> floorGoods = floorGoodsMapper.selectList(q2);
//                    if(!CollectionUtils.isEmpty(floorGoods)){
//                        for (FloorGoods floorGood : floorGoods) {
//                            WFloorGoodsVO wFloorGoodsVO = new WFloorGoodsVO();
//                            String goodsId = floorGood.getGoodsId();
//                            IndexMaterialVO indexMaterialVO = productService.getIndexMaterialVO(goodsId);
//                            if(indexMaterialVO != null && indexMaterialVO.getProductId() != null){
//                                BeanUtils.copyProperties(indexMaterialVO,wFloorGoodsVO);
//                                floorGoodsVOS.add(wFloorGoodsVO);
//                            }
//                        }
//                    }
//                    wFloorVO.setGoodsVOS(floorGoodsVOS);
//                    floorVOS.add(wFloorVO);
//                }
//            }
//            vo.setFloorVOS(floorVOS);
//            vos.add(vo);
//        }
        return vos;
    }


    @Override
    public List<WColumnFloorVO> fixedListBySize(Integer size, Integer isFixed) {
        List<WColumnFloorVO> vos = new ArrayList<>();
        List<CategoryColumns> list = lambdaQuery().eq(CategoryColumns::getState, PublicEnum.IS_YES.getCode())
                .orderByAsc(CategoryColumns::getSort).orderByDesc(CategoryColumns::getGmtCreate)
                .eq(CategoryColumns::getIsFixed,isFixed)
                .list();
        if (CollectionUtils.isEmpty(list)) return vos;
        for (CategoryColumns c : list) {
            WColumnFloorVO vo = new WColumnFloorVO();
            BeanUtils.copyProperties(c, vo);
            LambdaQueryWrapper<Floor> q = Wrappers.lambdaQuery(Floor.class);
            q.eq(Floor::getState,PublicEnum.IS_YES.getCode())
                    .eq(Floor::getColumnId,c.getColumnId())
                    .eq(Floor::getMallType,mallConfig.mallType)
                    .orderByDesc(Floor::getSort).orderByDesc(Floor::getGmtCreate);
            List<Floor> floors = floorMapper.selectList(q);
            List<WFloorVO> floorVOS = new ArrayList<>();
            // 如果楼层不是空
            if(!CollectionUtils.isEmpty(floors)){
                for (Floor floor : floors) {
                    WFloorVO wFloorVO = new WFloorVO();
                    BeanUtils.copyProperties(floor,wFloorVO);
                    List<WFloorGoodsVO> floorGoodsVOS = new ArrayList<>();
                    LambdaQueryWrapper<FloorGoods> q2 = Wrappers.lambdaQuery(FloorGoods.class);
                    q2.eq(FloorGoods::getFloorId,floor.getFloorId())
                            .eq(FloorGoods::getState,PublicEnum.IS_YES.getCode())
                            .orderByDesc(FloorGoods::getSort, MustBaseEntity::getGmtCreate).last(size != null, "limit " + size);
                    List<FloorGoods> floorGoods = floorGoodsMapper.selectList(q2);
                    if(!CollectionUtils.isEmpty(floorGoods)){
                        for (FloorGoods floorGood : floorGoods) {
                            WFloorGoodsVO wFloorGoodsVO = new WFloorGoodsVO();
                            String goodsId = floorGood.getGoodsId();
                            IndexMaterialVO indexMaterialVO = productService.getIndexMaterialVO(goodsId);
                            if(indexMaterialVO != null && indexMaterialVO.getProductId() != null){
                                BeanUtils.copyProperties(indexMaterialVO,wFloorGoodsVO);
                                floorGoodsVOS.add(wFloorGoodsVO);
                            }
                        }
                    }
                    wFloorVO.setGoodsVOS(floorGoodsVOS);
                    floorVOS.add(wFloorVO);
                }
            }
            vo.setFloorVOS(floorVOS);
            vos.add(vo);
        }
        return vos;
    }


    @Override
    public List<WColumnFloorVO> revolvingMaterials(Integer size, Integer isFixed) {
        List<WColumnFloorVO> vos = new ArrayList<>();
        List<CategoryColumns> list = lambdaQuery().eq(CategoryColumns::getState, PublicEnum.IS_YES.getCode())
                .orderByDesc(CategoryColumns::getSort).orderByDesc(CategoryColumns::getGmtCreate)
                .eq(CategoryColumns::getIsFixed,isFixed)
                .eq(CategoryColumns::getColumnName,"周转材料")
                .list();
        if (CollectionUtils.isEmpty(list)) return vos;
        for (CategoryColumns c : list) {
            WColumnFloorVO vo = new WColumnFloorVO();
            BeanUtils.copyProperties(c, vo);
            LambdaQueryWrapper<Floor> q = Wrappers.lambdaQuery(Floor.class);
            q.eq(Floor::getState,PublicEnum.IS_YES.getCode())
                    .eq(Floor::getColumnId,c.getColumnId())
//                    .eq(Floor::getMallType,mallConfig.mallType)
                    .orderByDesc(Floor::getSort).orderByDesc(Floor::getGmtCreate);
            List<Floor> floors = floorMapper.selectList(q);
            List<WFloorVO> floorVOS = new ArrayList<>();
            // 如果楼层不是空
            if(!CollectionUtils.isEmpty(floors)){
                for (Floor floor : floors) {
                    WFloorVO wFloorVO = new WFloorVO();
                    BeanUtils.copyProperties(floor,wFloorVO);
                    List<WFloorGoodsVO> floorGoodsVOS = new ArrayList<>();
                    LambdaQueryWrapper<FloorGoods> q2 = Wrappers.lambdaQuery(FloorGoods.class);
                    q2.eq(FloorGoods::getFloorId,floor.getFloorId())
                            .eq(FloorGoods::getState,PublicEnum.IS_YES.getCode())
                            .orderByDesc(FloorGoods::getSort, MustBaseEntity::getGmtCreate).last(size != null, "limit " + size);
                    List<FloorGoods> floorGoods = floorGoodsMapper.selectList(q2);
                    if(!CollectionUtils.isEmpty(floorGoods)){
                        for (FloorGoods floorGood : floorGoods) {
                            WFloorGoodsVO wFloorGoodsVO = new WFloorGoodsVO();
                            String goodsId = floorGood.getGoodsId();
                            IndexMaterialVO indexMaterialVO = productService.getIndexMaterialVO(goodsId);
                            if(indexMaterialVO != null && indexMaterialVO.getProductId() != null){
                                BeanUtils.copyProperties(indexMaterialVO,wFloorGoodsVO);
                                floorGoodsVOS.add(wFloorGoodsVO);
                            }
                        }
                    }
                    wFloorVO.setGoodsVOS(floorGoodsVOS);
                    floorVOS.add(wFloorVO);
                }
            }
            vo.setFloorVOS(floorVOS);
            vos.add(vo);
        }
        return vos;
    }
}
