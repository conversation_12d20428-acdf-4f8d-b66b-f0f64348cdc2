package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.PlatformDealFeeRecord;
import scrbg.meplat.mall.entity.PlatformDealFeeRecordDtl;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.PlatformDealFeeRecordDtl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.List;

/**
 * @描述：平台交易费缴费记录明细 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
public interface PlatformDealFeeRecordDtlService extends IService<PlatformDealFeeRecordDtl> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeRecordDtl> queryWrapper);

    void create(PlatformDealFeeRecordDtl platformDealFeeRecordDtl);

    void update(PlatformDealFeeRecordDtl platformDealFeeRecordDtl);

    PlatformDealFeeRecordDtl getById(String id);

    void delete(String id);

    void deleteBatch(List<String> ids);

    /**
     * 批量新增明细
     *
     * @param dtos
     */
    void batchCreate(List<PlatformDealFeeRecordDtl> dtos);

    /**
     * 批量删除明细
     * @param ids
     */
    void batchDelete(List<String> ids);

}
