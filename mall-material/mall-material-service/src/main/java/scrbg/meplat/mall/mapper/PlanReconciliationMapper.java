package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.scrbg.common.utils.PageUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.dto.reconciliation.SupplierPlanReconciliationQueryDTO;
import scrbg.meplat.mall.vo.reconciliation.PlanReconciliationVO;

import java.util.List;

/**
 * @描述：计划对账 Mapper 接口
 * @作者: tanfei
 * @日期: 2025-06-19
 */
@Mapper
@Repository
public interface PlanReconciliationMapper {

    /**
     * 供应商方-查询可对账的计划列表总数
     * @param dto 查询参数
     * @return 总数
     */
    int getReconciliablePlansBySupplierCount(@Param("dto") SupplierPlanReconciliationQueryDTO dto);

    /**
     * 供应商方-查询可对账的计划列表
     * @param dto 查询参数
     * @return 计划列表
     */
    List<PlanReconciliationVO> getReconciliablePlansBySupplier(@Param("dto") SupplierPlanReconciliationQueryDTO dto);

    /**
     * 供应商方-查询可对账的项目部总数
     * @param dto 查询参数
     * @return 总数
     */
    int getReconciliableEnterprisePageListCount(@Param("dto") SupplierPlanReconciliationQueryDTO dto);

    /**
     * 供应商方-查询可对账的项目部列表
     * @param dto 查询参数
     * @return 项目部列表
     */
    List<PlanReconciliationVO> getReconciliableEnterprisePageList(@Param("dto") SupplierPlanReconciliationQueryDTO dto);


    /**
     * 采购方-获取可对账的供应商数量
     * @param dto 查询参数
     * @return 总数
     */
    int getReconciliableSupplierByEnterpriseCount(@Param("dto") SupplierPlanReconciliationQueryDTO dto);

    /**
     * 采购方-获取可对账的供应商列表
     * @param dto 查询参数
     * @return 供应商列表
     */
    List<PlanReconciliationVO> getReconciliableSupplierByEnterprise(@Param("dto") SupplierPlanReconciliationQueryDTO dto);

    /**
     * 采购方-获取可对账的计划订单数量
     * @param dto 查询参数
     * @return 总数
     */
    int getReconciliablePlansByEnterpriseCount(@Param("dto") SupplierPlanReconciliationQueryDTO dto);

    /**
     * 采购方-获取可对账的计划订单列表
     * @param dto 查询参数
     * @return 计划列表
     */
    List<PlanReconciliationVO> getReconciliablePlansByEnterprise(@Param("dto") SupplierPlanReconciliationQueryDTO dto);

}
