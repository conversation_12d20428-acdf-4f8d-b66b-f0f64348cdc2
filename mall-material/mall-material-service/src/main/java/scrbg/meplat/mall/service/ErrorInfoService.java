package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.ErrorInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ErrorInfo;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：异常信息记录表 服务类
 * @作者: ye
 * @日期: 2023-10-25
 */
public interface ErrorInfoService extends IService<ErrorInfo> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ErrorInfo> queryWrapper);

        void create(ErrorInfo errorInfo);
        void update(ErrorInfo errorInfo);
        ErrorInfo getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
