package scrbg.meplat.mall.service.impl;

import com.alibaba.excel.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.OrderShip;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.ShopBusiness;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ShopBusinessMapper;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.ShopBusinessService;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.service.ShopService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2025-01-16
 */
@Service
public class ShopBusinessServiceImpl extends ServiceImpl<ShopBusinessMapper, ShopBusiness> implements ShopBusinessService{
   @Autowired
    EnterpriseInfoService enterpriseInfoService;
   @Autowired
    ShopService shopService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShopBusiness> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        if (innerMap.get("state") != null){
            queryWrapper.eq(ShopBusiness::getState,innerMap.get("state"));
        }
        if (innerMap.get("shopName") != null){
            queryWrapper.like(ShopBusiness::getShopName,innerMap.get("shopName"));
        }
        if (innerMap.get("enterpriseName") != null){
            queryWrapper.like(ShopBusiness::getEnterpriseName,innerMap.get("enterpriseName"));
        }
        if (innerMap.get("orderBy") != null){
            if (innerMap.get("orderBy").equals("1")){
                queryWrapper.orderByDesc(ShopBusiness::getSort);
            }else if (innerMap.get("orderBy").equals("2")){
                queryWrapper.orderByAsc(ShopBusiness::getGmtModified);
            }

        }else {
            queryWrapper.orderByDesc(ShopBusiness::getGmtCreate);
        }
        String keywords = (String) innerMap.get("keywords");
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and(i -> i.like(ShopBusiness::getShopName, keywords)
                    .or().like(ShopBusiness::getEnterpriseName, keywords));
        }
        IPage<ShopBusiness> page = this.page(
        new Query<ShopBusiness>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(ShopBusiness shopBusiness) {
        Shop byId = shopService.getById(shopBusiness.getShopId());
        byId.setIsBusiness(1);
        shopService.update(byId);
        super.save(shopBusiness);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(ShopBusiness shopBusiness) {
        super.updateById(shopBusiness);
    }


    @Override
    public ShopBusiness getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     @Transactional(rollbackFor = Exception.class)
     @NotResubmit
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
         }


    @Override
    @Transactional
    public void updateState(ShopBusiness shopBusiness) {
        if (shopBusiness.getState().equals("1")){
            Shop byId = shopService.getById(shopBusiness.getShopId());
            byId.setIsBusiness(1);
            shopService.update(byId);
        }
        EnterpriseInfo byId = enterpriseInfoService.getById(shopBusiness.getEnterpriseId());
        if (byId.getInteriorId().equals("1838284925812342784")){
            throw new BusinessException(500,"该自营店暂时没有权限修改,无法关闭自营店，请联系管理员");
        }
        ShopBusiness info = getById(shopBusiness.getShopId());
        info.setState(shopBusiness.getState());
        updateById(info);
        List<ShopBusiness> list = lambdaQuery().eq(ShopBusiness::getState, 1).list();
        List<String> shopIds = list.stream().map(ShopBusiness::getShopId).collect(Collectors.toList());
        List<String> interiorIds = list.stream().map(ShopBusiness::getInteriorId).collect(Collectors.toList());
        stringRedisTemplate.opsForValue().set("isBusinessShopId", String.join(",", shopIds));
        stringRedisTemplate.opsForValue().set("isBusinessOrg", String.join(",", interiorIds));
    }

    @Override
    @Transactional
    public void createDataByEnterpriseId(String enterpriseId) {
        ShopBusiness info = lambdaQuery().eq(ShopBusiness::getEnterpriseId,enterpriseId).one();
        if (info!=null){
            throw new BusinessException("该企业成为自营店，不许要重复添加");
        }

        EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .isNotNull(EnterpriseInfo::getInteriorId).one();
         if (one==null){
             throw new BusinessException("该企业没有内部企业");
         }
        if (one != null){
            if (one.getInteriorId().equals("1838284925812342784")||one.getInteriorId().equals("aa5422473bfb-aeea-9146-8ce7-04ff3c5e")){
                Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, enterpriseId).eq(Shop::getState, 1).one();
                if (shop!=null){
                    ShopBusiness shopBusiness = new ShopBusiness();
                    shopBusiness.setEnterpriseId(enterpriseId);
                    shopBusiness.setEnterpriseName(one.getEnterpriseName());
                    shopBusiness.setShopId(shop.getShopId());
                    shopBusiness.setShopName(shop.getShopName());
                    shopBusiness.setInteriorId(one.getInteriorId());
                    shopBusiness.setState(1);
                    save(shopBusiness);
                }else {
                    throw new BusinessException("该企业未开店，请开店后才设置成为自营店");
                }
            }else {
                throw new BusinessException("暂时只有物资分公司和子公司能成为自营店");
            }


        }
        List<ShopBusiness> list = lambdaQuery().eq(ShopBusiness::getState, 1).list();
        List<String> shopIds = list.stream().map(ShopBusiness::getShopId).collect(Collectors.toList());
        List<String> interiorIds = list.stream().map(ShopBusiness::getInteriorId).collect(Collectors.toList());
        stringRedisTemplate.opsForValue().set("isBusinessShopId", String.join(",", shopIds));
        stringRedisTemplate.opsForValue().set("isBusinessOrg", String.join(",", interiorIds));
    }


    @Override
    public List<String> getBusinessOrgList() {
        String isBusinessShopId= stringRedisTemplate.opsForValue().get("isBusinessShopId");
        if (isBusinessShopId==null){
            List<ShopBusiness> list = lambdaQuery().eq(ShopBusiness::getState, 1).list();
            List<String> collect = list.stream().map(ShopBusiness::getShopId).collect(Collectors.toList());
            stringRedisTemplate.opsForValue().set("isBusinessShopId", String.join(",", collect));
            return collect;
        }
        return new ArrayList<>();
    }

    @Override
    public ArrayList<String> getStopBusiness() {
        List<ShopBusiness> list = lambdaQuery().eq(ShopBusiness::getState, 0).list();
        if (list.size()==0){
            return new ArrayList<>();
        }
        List<String> collect = list.stream().map(ShopBusiness::getShopId).collect(Collectors.toList());
        return (ArrayList<String>) collect;
    }


    @Override
    public void updateBatchSortById(List<ShopBusiness> list) {
        ArrayList<ShopBusiness> updateSortList = new ArrayList<>();
        for (ShopBusiness shopBusiness : list) {
            ShopBusiness byId = getById(shopBusiness.getShopId());
            byId.setSort(shopBusiness.getSort());
            updateSortList.add(byId);
        }
        updateBatchById(updateSortList);
    }
}
