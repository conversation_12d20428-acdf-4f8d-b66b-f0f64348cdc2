package scrbg.meplat.mall.service.stockManage.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.SecondarySupplierRecord;
import scrbg.meplat.mall.mapper.stockManage.SecondarySupplierLogMapper;
import scrbg.meplat.mall.service.stockManage.SecondarySupplierLogService;

import java.util.List;
import java.util.Map;


@Service
public class SecondarySupplierLogServiceImpl extends ServiceImpl<SecondarySupplierLogMapper, SecondarySupplierRecord> implements SecondarySupplierLogService {

    private SecondarySupplierLogMapper secondarySupplierLogMapper;


    @Autowired
    public void setSecondarySupplierLogMapper(SecondarySupplierLogMapper selfOperatedLogMapper) {
        this.secondarySupplierLogMapper = selfOperatedLogMapper;
    }

    @Override
    public void delete(String id) {
        secondarySupplierLogMapper.deleteById(id);
    }

    @Override
    public void saveLogs(List<SecondarySupplierRecord> logs) {
        super.saveBatch(logs);
    }

    @Override
    public PageUtils<SecondarySupplierRecord> queryPage(JSONObject jsonObject, LambdaQueryWrapper<SecondarySupplierRecord> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String orderSn = (String) innerMap.get("orderSn");
        String productName = (String) innerMap.get("productName");
        String belowPrice = (String) innerMap.get("belowPrice");
        String abovePrice = (String) innerMap.get("abovePrice");
        String purchasingAgencyName = (String) innerMap.get("purchasingAgencyName");
        Integer recordType = (Integer) innerMap.get("recordType");
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(SecondarySupplierRecord::getProductName, keywords)
                        .or()
                        .like(SecondarySupplierRecord::getOrderSn, keywords);

            });
        }
        if (!StringUtils.isEmpty(productName)) {
            queryWrapper.eq(SecondarySupplierRecord::getProductName, productName);
        }
        if (!StringUtils.isEmpty(orderSn)) {
            queryWrapper.like(SecondarySupplierRecord::getOrderSn, orderSn);
        }
        if (!StringUtils.isEmpty(purchasingAgencyName)) {
            queryWrapper.like(SecondarySupplierRecord::getPurchasingAgencyName, purchasingAgencyName);
        }
        if(null != recordType){
            queryWrapper.eq(SecondarySupplierRecord::getRecordType, recordType);
        }
        queryWrapper.ge(StringUtils.isNotBlank(abovePrice), SecondarySupplierRecord::getBidRateAmount, abovePrice);
        queryWrapper.le(StringUtils.isNotBlank(belowPrice), SecondarySupplierRecord::getBidRateAmount, belowPrice);
        queryWrapper.orderByDesc(SecondarySupplierRecord::getGmtCreate);
        IPage<SecondarySupplierRecord> page = this.page(
                new Query<SecondarySupplierRecord>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils<SecondarySupplierRecord>(page);
    }
}
