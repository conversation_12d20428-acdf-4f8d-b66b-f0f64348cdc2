package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.sun.org.apache.bcel.internal.generic.NEW;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.Brand;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.BrandMapper;
import scrbg.meplat.mall.mapper.ProductMapper;
import scrbg.meplat.mall.service.BrandService;
import scrbg.meplat.mall.service.ProductCategoryService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.product.material.CategoryClassIdAndClassNameVO;
import scrbg.meplat.mall.vo.product.website.WBrandVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @描述：品牌 服务类
 * @作者: y
 * @日期: 2022-11-10
 */
@Service
public class BrandServiceImpl extends ServiceImpl<BrandMapper, Brand> implements BrandService {

    @Autowired
    MallConfig mallConfig;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private BrandService brandService;
    @Autowired
    private ProductCategoryService productCategoryService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Brand> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String name = (String) innerMap.get("name");
        String classId = (String) innerMap.get("classId");
        String className = (String) innerMap.get("className");
        String classNamePath = (String) innerMap.get("classNamePath");
        Integer mallType = (Integer) innerMap.get("mallType");
        Integer productType = (Integer) innerMap.get("productType");
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        if (productType != null) {
            q.eq(Brand::getProductType, productType);
        }
        if (className != null) {
            q.eq(Brand::getClassName, className);
        }
        q.eq(Brand::getMallType, mallType);
        if (StringUtils.isNotEmpty(name)) {
            q.like(Brand::getName, name);
        }
        if (StringUtils.isNotEmpty(classNamePath)) {
            ProductCategory one = productCategoryService.lambdaQuery().eq(ProductCategory::getClassPath, classNamePath)
                    .eq(ProductCategory::getState, 1).one();
            if (one == null) {
                throw new BusinessException("分类：【" + classNamePath + "】不存在或已停用！");
            }
            List<String> categoryParentIdList = productCategoryService.getCategoryParentIdList(one.getClassId());
            // 处理分类路径
            String cp = "";
            for (String s : categoryParentIdList) {
                cp += s + "/";
            }
            cp = cp.substring(0, cp.length() - 1);
            q.eq(Brand::getClassPath,cp);
        }

        if (StringUtils.isNotEmpty(classId)) {
            q.like(Brand::getClassPath, classId);
        }
        IPage<Brand> page = this.page(
                new Query<Brand>().getPage(jsonObject),
                q
        );
        List<Brand> records = page.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            for (Brand record : records) {
                List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(record.getClassId());
                if (!CollectionUtils.isEmpty(categoryParentPath)) {
                    ArrayList<String> strings = new ArrayList<>();
                    String cp = "";
                    for (CategoryClassIdAndClassNameVO s : categoryParentPath) {
                        cp += s.getClassName() + "/";
                        strings.add(s.getClassName());
                    }
                    record.setClassNamePath(strings);
                    record.setClassNamePathStr(cp.substring(0, cp.length() - 1));
                }
            }
        }
        return new PageUtils(page);
    }

    @Override
    public void create(Brand brand) {
        //调用父类方法即可
        //也可以baseMapper.insert
        QueryWrapper<Brand> wrapper = new QueryWrapper();
        wrapper.eq("name", brand.getName());
        wrapper.eq("mall_type", mallConfig.mallType);
        wrapper.eq("class_id", brand.getClassId());
        List<Brand> results = brandService.list(wrapper);
        if (results.size() > 0) {
            if (StringUtils.isNotEmpty(brand.getBrandId()) && !results.get(0).getBrandId().equals(brand.getBrandId())) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "标题重复");
            }
            if (StringUtils.isEmpty(brand.getBrandId())) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "标题重复");
            }

        }
        ProductCategory category = productCategoryService.getById(brand.getClassId());
        brand.setClassName(category.getClassName());
        List<String> classPath = brand.getClassNamePath();
        if (CollectionUtils.isEmpty(classPath)) {
            throw new BusinessException(400, "分类不能为空！");
        }
        String cp = "";
        if (!CollectionUtils.isEmpty(classPath)) {
            for (String s : classPath) {
                cp += s + "/";
            }
        }
        brand.setClassPath(cp.substring(0, cp.length() - 1));
        super.save(brand);
    }

    @Override
    public void update(Brand brand) {
        //调用父类方法即可
        //也可以baseMapper.insert
        QueryWrapper<Brand> wrapper = new QueryWrapper();
        wrapper.eq("name", brand.getName());
        wrapper.eq("mall_type", mallConfig.mallType);
        List<Brand> results = brandService.list(wrapper);
        if (results.size() > 0) {

            if (StringUtils.isNotEmpty(brand.getBrandId()) && !results.get(0).getBrandId().equals(brand.getBrandId())) {
                throw new BusinessException("标题重复");
            }
            if (StringUtils.isEmpty(brand.getBrandId())) {
                throw new BusinessException("标题重复");
            }

        }
        List<String> classPath = brand.getClassNamePath();
        String cp = "";
        if (!CollectionUtils.isEmpty(classPath)) {
            for (String s : classPath) {
                cp += s + "/";
            }
        }
        brand.setClassPath(cp.substring(0, cp.length() - 1));
        super.updateById(brand);
    }


    @Override
    public Brand getById(String id) {
        Brand byId = super.getById(id);
        if (byId != null) {
            if (StringUtils.isNotBlank(byId.getClassId())) {
                List<String> classPath = productCategoryService.getCategoryParentIdList(byId.getClassId());
                byId.setClassNamePath(classPath);
            }
            return byId;
        } else {
            return null;
        }
    }

    @Override
    public void delete(String id) {
        LambdaQueryWrapper<Product> q = Wrappers.lambdaQuery(Product.class);
        q.eq(Product::getBrandId, id);
        Integer count = productMapper.selectCount(q);
        if (count > 0) {
            throw new BusinessException("已有商品在使用该品牌logo,不能删除");
        } else {
            super.removeById(id);
        }
//        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
//        LambdaQueryWrapper<Product> eq = queryWrapper.eq(Product::getBrandId, id);
//        if (eq.nonEmptyOfEntity()){
//            throw new RuntimeException("已有商品在使用该品牌logo,不能删除");
//        }else {
//            super.removeById(id);
//        }
    }

    /**
     * 根据分类id修改分类名称
     *
     * @param classId
     * @param className
     */
    @Override
    public boolean updateClassNameByClassId(String classId, String className) {
        return lambdaUpdate().eq(Brand::getClassId, classId).set(Brand::getClassName, className).update();
    }

    @Override
    public PageUtils queryBrandLogo(JSONObject jsonObject, LambdaQueryWrapper<Brand> queryWrapper) {

        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer state = (Integer) innerMap.get("state");
        Integer mallType = Integer.valueOf(mallConfig.mallType);
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String classId = (String) innerMap.get("classId");
        String name = (String) innerMap.get("name");
        Integer productType = (Integer) innerMap.get("productType");

        if (name != null) {
            queryWrapper.like(Brand::getName, name);
        }
        if (StringUtils.isNotBlank(classId)) {
            queryWrapper.like(Brand::getClassPath, classId);
        }
        if (productType != null) {
            queryWrapper.eq(Brand::getProductType, productType);
        }
        queryWrapper.eq(Brand::getMallType, mallType);
        if (state != null) {
            queryWrapper.eq(Brand::getState, state);
        }
        if (StringUtils.isNotEmpty(keywords)) {
            queryWrapper.like(Brand::getName, keywords);
            queryWrapper.and(t -> {
                t.like(Brand::getRemarks, keywords.trim())
                        .or()
                        .like(Brand::getName, keywords.trim())
                        .or()
                        .like(Brand::getDescript, keywords.trim());

            });
        }

        //排序方式(排序值升序)
        if (orderBy == null) {

        } else if (orderBy == PublicEnum.ORDER_BY_SORT.getCode()) {
            queryWrapper.orderByDesc(Brand::getSort, Brand::getGmtCreate);
            //排序方式(修改时间降序)

        } else if (orderBy == PublicEnum.ORDER_BY_GMT_CREATE.getCode()) {
            queryWrapper.orderByDesc(Brand::getGmtModified);

            //排序方式(创建时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(Brand::getGmtCreate);
        }

        IPage<Brand> page = this.page(
                new Query<Brand>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByPublish(List<String> ids, String type) {
        List<Brand> resutls = listByIds(ids);
        for (Brand brand : resutls) {
            if ("1".equals(type)) {
                brand.setState(1);
            } else {
                brand.setState(0);
            }
        }
        super.saveOrUpdateBatch(resutls);
    }

    /**
     * 获取品牌分页列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils pageList(JSONObject jsonObject, QueryWrapper<Brand> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer productType = (Integer) innerMap.get("productType");
        String classId = (String) innerMap.get("classId");
        if (productType != null) {
            q.eq("product_type", productType);
        }
        if (StringUtils.isNotEmpty(classId)) {
            q.like("class_path", classId);
        }
        q.select("DISTINCT  name, sort");
        q.eq("mall_type", mallConfig.mallType)

                .eq("state", PublicEnum.IS_YES.getCode())
                .orderByDesc("sort");
        IPage<Brand> page = this.page(
                new Query<Brand>().getPage(jsonObject),
                q
        );

        List<Brand> records = page.getRecords();
        if (records.size() == 0) return new PageUtils(page);
        List<WBrandVO> vos = new ArrayList<>();
        for (Brand brand : records) {
            WBrandVO vo = new WBrandVO();
            BeanUtils.copyProperties(brand, vo);
            vos.add(vo);
        }
        page.setRecords(null);
        PageUtils pageUtils = new PageUtils(page);
        pageUtils.setList(vos);
        return pageUtils;
    }

    /**
     * 根据主键批量删除
     *
     * @param ids
     */
    @Override
    public void deleteBatch(List<String> ids) {
        LambdaQueryWrapper<Product> q = Wrappers.lambdaQuery(Product.class);
        q.in(Product::getBrandId, ids);
        Integer count = productMapper.selectCount(q);
        if (count > 0) {
            throw new BusinessException("已有商品在使用该品牌logo,不能删除");
        } else {
            brandService.removeByIds(ids);
        }
    }

    /**
     * 获取品牌列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils getBrandPageList(JSONObject jsonObject, LambdaQueryWrapper<Brand> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String name = (String) innerMap.get("name");
        String classId = (String) innerMap.get("classId");
        q.eq(Brand::getMallType, mallConfig.mallType);
        if (StringUtils.isNotEmpty(name)) {
            q.like(Brand::getName, name);
        }
        if (StringUtils.isNotEmpty(classId)) {
            q.like(Brand::getClassPath, classId);
        }
        IPage<Brand> page = this.page(
                new Query<Brand>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Override
    public Brand getBrandByBrandName(String BrandName) {
        Brand brand = lambdaQuery().eq(Brand::getName, BrandName)
                .eq(Brand::getMallType, mallConfig.mallType)
                .eq(Brand::getState, PublicEnum.IS_YES.getCode()).one();
        return brand;
    }

    @Override
    public List<Brand> selectAllByClassId(String classId) {
        List<Brand> list = lambdaQuery().like(Brand::getClassPath, classId)
                .eq(Brand::getState, 1).list();
        return list;
    }

    @Override
    public Brand findByClassIdAndBrandName(String classId, String brandName) {
        Brand brand = lambdaQuery().eq(Brand::getName, brandName)
                .eq(Brand::getClassId, classId).one();
        return brand;

    }
}
