package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.ImportSuppler;
import scrbg.meplat.mall.mapper.ImportSupplerMapper;
import scrbg.meplat.mall.service.ImportSupplerService;

/**
 * @描述：导入供应商失败的结果 服务类
 * @作者: ye
 * @日期: 2023-05-09
 */
@Service
public class ImportSupplerServiceImpl extends ServiceImpl<ImportSupplerMapper, ImportSuppler> implements ImportSupplerService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ImportSuppler> queryWrapper) {
        IPage<ImportSuppler> page = this.page(
        new Query<ImportSuppler>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ImportSuppler importSuppler) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(importSuppler);
    }

    @Override
    public void update(ImportSuppler importSuppler) {
        super.updateById(importSuppler);
    }


    @Override
    public ImportSuppler getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}