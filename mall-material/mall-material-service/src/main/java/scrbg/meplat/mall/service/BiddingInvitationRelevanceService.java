package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.BiddingInvitationRelevance;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.BiddingInvitationRelevance;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：邀请竞价关联表 服务类
 * @作者: ye
 * @日期: 2023-09-27
 */
public interface BiddingInvitationRelevanceService extends IService<BiddingInvitationRelevance> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingInvitationRelevance> queryWrapper);

        void create(BiddingInvitationRelevance biddingInvitationRelevance);
        void update(BiddingInvitationRelevance biddingInvitationRelevance);
        BiddingInvitationRelevance getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
