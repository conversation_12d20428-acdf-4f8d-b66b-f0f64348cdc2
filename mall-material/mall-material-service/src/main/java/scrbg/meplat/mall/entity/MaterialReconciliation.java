package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：物资验收
 * @作者: ye
 * @日期: 2023-07-26
 */
@ApiModel(value = "物资验收（本地相关id只是商城可能会填充）")
@Data
@TableName("material_reconciliation")
public class MaterialReconciliation extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "对账单ID")
    private String reconciliationId;

    @ApiModelProperty(value = "对账单编号")
    private String reconciliationNo;

    @ApiModelProperty(value = "对账标题（暂时废弃）")
    private String title;

    @ApiModelProperty(value = "对账价格类型（1浮动价格对账单2固定价格对账单）")
    private Integer type;

    @ApiModelProperty(value = "业务类型（1合同2计划3调拨4甲供5暂估6大宗临购）")
    private Integer businessType;

    @ApiModelProperty(value = "对账结算类型（ 10零星采购（低值易耗品）11办公用品 12大宗月供订单 13大宗临购")
    private Integer reconciliationProductType;

    @ApiModelProperty(value = "源单ID")
    private String sourceBillId;

    @ApiModelProperty(value = "源单编号")
    private String sourceBillNo;

    @ApiModelProperty(value = "源单名称")
    private String sourceBillName;

    @ApiModelProperty(value = "供应商id（pcwp）")
    private String supplierId;

    @ApiModelProperty(value = "供应商本地机构id（废弃）")
    private String supplierEnterpriseId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "采购人员ID")
    private String purchaserId;

    @ApiModelProperty(value = "采购人员本地id（废弃）")
    private String purchaserLocalId;

    @ApiModelProperty(value = "采购人员名称")
    private String purchaserName;

    @ApiModelProperty(value = "采购单位ID")
    private String purchasingOrgId;

    @ApiModelProperty(value = "采购单位本地id（废弃）")
    private String purchasingLocalOrgId;

    @ApiModelProperty(value = "采购单位名称")
    private String purchasingOrgName;

    @ApiModelProperty(value = "对账创建远程人员ID")
    private String acceptanceId;

    @ApiModelProperty(value = "对账创建远程人员名称")
    private String acceptanceName;

    @ApiModelProperty(value = "对账总金额（含税）")
    private BigDecimal reconciliationAmount;

    @ApiModelProperty(value = "税额（含税）")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "对账总金额（不含税）")
    private BigDecimal reconciliationNoRateAmount;

    @ApiModelProperty(value = "已结算金额")
    private BigDecimal settleAmount;

    @ApiModelProperty(value = "对账开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty(value = "对账结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty(value = "新增来源（1采购员新增2供应商新增3pcwp新增）")
    private Integer createType;

    @ApiModelProperty(value = "采购是否确认（0否1是）")
    private Integer purchaseIsAffirm;

    @ApiModelProperty(value = "采购员确认时间")
    private Date purchaseAffirmTime;

    @ApiModelProperty(value = "供应商是否确认（0否1是）")
    private Integer supplierIsAffirm;

    @ApiModelProperty(value = "供应商确认时间")
    private Date supplierAffirmTime;

    @ApiModelProperty(value = "是否推送（商城以后定时器使用）")
    private Integer isPush;

    @ApiModelProperty(value = "运输单位费用(含税,元)（废弃）")
    private BigDecimal freight;

    @ApiModelProperty(value = "税率(%)")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "作废原因")
    private String nullifyReason;

    @ApiModelProperty(value = "作废人本地id")
    private String nullifyCreatorLocalId;

    @ApiModelProperty(value = "作废人id")
    private String nullifyCreatorId;

    @ApiModelProperty(value = "作废人")
    private String nullifyCreator;

    @ApiModelProperty(value = "作废时间")
    private Date nullifyCreated;

    @ApiModelProperty(value = "状态（0草稿1已提交2待审核3审核通过4审核失败7已作废）0和1对于采购员来说都是草稿，供应商默认提交")
    private Integer state;

    @ApiModelProperty(value = "创建机构名称（未使用，放入采购方机构id）")
    private String orgName;

    @ApiModelProperty(value = "创建机构id（未使用，放入采购方机构id）")
    private String orgId;

    @ApiModelProperty(value = "创建本地机构id（废弃）")
    private String enterpriseId;

    @ApiModelProperty(value = "关联id（对应pcwp验收单id）")
    private String relevanceId;

    @ApiModelProperty(value = "关联名称（对应pcwp验收单编号）")
    private String relevanceSn;

    @ApiModelProperty(value = "供应商信用代码（外部供应商使用）")
    private String creditCode;

    @ApiModelProperty(value = "供应商机构简码（内部供应商使用）")
    private String orgShort;

    @ApiModelProperty(value = "供应商机构id（废弃）")
    private String supplierOrgId;

    @ApiModelProperty(value = "是否待推送pcwp（1是0已推送null不需要推送）")
    private Integer isNotPush;

    @ApiModelProperty(value = "货款支付周期（单位月）")
    private Integer paymentWeek;

    @ApiModelProperty(value = "是否开发票（0 未开，1申请中、2已开票、3申请被拒）")
    private Integer invoiceState;

    @ApiModelProperty(value = "超期垫资利息（%）")
    private BigDecimal outPhaseInterest;

    @ApiModelProperty(value = "结算时间")
    @TableField(exist = false)
    private Integer settlementTime;

    //乐观锁
    @Version
    private Integer version;

    @ApiModelProperty(value = "对账明细")
    @TableField(exist = false)
    private List<MaterialReconciliationDtl> dtl;

    @ApiModelProperty(value = "keyId")
    @TableField(exist = false)
    private String keyId;

    @ApiModelProperty(value = "审核历史（新增请忽略）")
    @TableField(exist = false)
    private List<AuditRecord> auditRecords;

    @ApiModelProperty(value = "是否提交")
    @TableField(exist = false)
    private int isSubmit;
}
