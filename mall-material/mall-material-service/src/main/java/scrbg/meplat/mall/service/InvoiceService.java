package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.Invoice;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.Invoice;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.vo.invoice.AuditRecordVo;
import scrbg.meplat.mall.vo.invoice.DataListPassVo;

import java.util.List;
/**
 * @描述：发票 服务类
 * @作者: ye
 * @日期: 2023-11-13
 */
public interface InvoiceService extends IService<Invoice> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Invoice> queryWrapper);

        void create(Invoice invoice);
        void update(Invoice invoice);
        Invoice getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

        PageUtils platformQueryPage(JSONObject jsonObject, LambdaQueryWrapper<Invoice> invoiceLambdaQueryWrapper);

        PageUtils shopManageQueryPage(JSONObject jsonObject, LambdaQueryWrapper<Invoice> invoiceLambdaQueryWrapper);

        void updateInvoiceState(Invoice invoice);

        void updateBathIds(DataListPassVo dataListPassVos);

        void changInvoiceState(AuditRecordVo auditRecordVo);

        void saveFiles(Invoice invoice);

        /**
         * 根据二级对账单id查询可生成发票数据
         * @param jsonObject
         * @param invoiceLambdaQueryWrapper
         * @return
         */
        Invoice ListByBillIds(JSONObject jsonObject, LambdaQueryWrapper<Invoice> invoiceLambdaQueryWrapper);


        /**
         * 二级对账单发票新增（自营店
         * @param invoice
         */
        void twoEnterpriseCreate(Invoice invoice);



        /**
         * 二级对账单发票新增（二级供应商
         * @param invoice
         */
        void twoSupplierCreate(Invoice invoice);
}
