package scrbg.meplat.mall.entity.plan;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MallBaseEntity;

/**
 * @描述：采购计划主表
 * @作者: tanfei
 * @日期: 2025-05-27
 */
@ApiModel(value = "采购计划主表")
@Data
@TableName("plan")
public class Plan extends MallBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "计划ID")
    private String billId;

    @ApiModelProperty(value = "计划编号")
    private String billNo;

    @ApiModelProperty(value = "PCWP计划ID")
    private String pBillId;

    @ApiModelProperty(value = "PCWP计划编号")
    private String pBillNo;

    @ApiModelProperty(value = "计划状态(-1已作废0待提交（已撤回）1待审核2已审核3已完成4审核不通过)")
    private String state;

    @ApiModelProperty(value = "计划日期")
    private Date billDate;

    @ApiModelProperty(value = "计划类型(0零星采购 1大宗临购 2周转材料)")
    private Integer type;

    @ApiModelProperty(value = "采购人员所属组织机构ID")
    private String orgId;

    @ApiModelProperty(value = "采购人员所属组织机构名称")
    private String orgName;

    @ApiModelProperty(value = "采购人员所属组织机构简称")
    private String orgShort;

    @ApiModelProperty(value = "计划金额(不含税)")
    private BigDecimal planAmount;

    @ApiModelProperty(value = "计划金额（含税）")
    private BigDecimal taxPlanAmount;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "乐观锁版本号")
    private Integer version;

    @ApiModelProperty(value = "计划明细信息")
    @TableField(exist = false)
    List<PlanDetail> details;
}