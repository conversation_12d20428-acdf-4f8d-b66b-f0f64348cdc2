package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @描述：供应商秘钥
 * @作者: ye
 * @日期: 2023-03-27
 */
@ApiModel(value="供应商秘钥")
@Data
@TableName("private_key_supplier")
public class PrivateKeySupplier implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "私有id")
    private String privateId;

    @ApiModelProperty(value = "供应商id")

    private String supplierId;


    @ApiModelProperty(value = "供应商名称")

    private String supplierName;


    @ApiModelProperty(value = "供应商类型")

    private Integer supplierType;


    @ApiModelProperty(value = "秘钥key")

    private String privateKey;


    @ApiModelProperty(value = "创建时间")

    private Date createTime;

    @ApiModelProperty(value = "店铺id")

    private String shopId;
    @ApiModelProperty(value = "备注")

    private String remarks;

    @ApiModelProperty(value = "失效时间")

    private Date invalidTime;
    @ApiModelProperty(value = "ip地址")

    private String ipAuth;

}