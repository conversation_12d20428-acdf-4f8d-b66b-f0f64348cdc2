package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.CategoryColumns;
import scrbg.meplat.mall.vo.floor.website.WColumnFloorVO;

import java.util.List;

/**
 * @描述： 服务类
 * @作者: y
 * @日期: 2022-12-07
 */
public interface CategoryColumnsService extends IService<CategoryColumns> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<CategoryColumns> queryWrapper);

    void create(CategoryColumns categoryColumns);

    void update(CategoryColumns categoryColumns);

    CategoryColumns getById(String id);

    void delete(String id);

    void updateByPublish(List<String> ids, String s);

    /**
     * 首页栏目
     * @param size
     * @return
     */
    List<WColumnFloorVO> listBySize(Integer size);

    List<WColumnFloorVO> fixedListBySize(Integer size, Integer isFixed);

    List<WColumnFloorVO> revolvingMaterials(Integer size, Integer isFixed);

}
