package scrbg.meplat.mall.service.impl;


import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.dto.user.OrgAndSon;
import scrbg.meplat.mall.entity.Brand;
import scrbg.meplat.mall.entity.ReceiptPerson;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ReceiptPersonMapper;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.ReceiptPersonService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-07-13
 */
@Service
public class ReceiptPersonServiceImpl extends ServiceImpl<ReceiptPersonMapper, ReceiptPerson> implements ReceiptPersonService {
    @Autowired
    private ReceiptPersonService receiptPersonService;
    @Resource
    private EnterpriseInfoService enterpriseInfoService;

    @Resource
    private MallConfig mallConfig;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ReceiptPerson> queryWrapper) {

        String keywords = (String) jsonObject.get("keywords");
        String name = (String) jsonObject.get("name");
        String phone = (String) jsonObject.get("phone");
        if (StringUtils.isNotBlank(keywords)){
            queryWrapper.and(t->{
                t.like(ReceiptPerson::getName,keywords)
                        .or()
                        .like(ReceiptPerson::getPhone,keywords);
            });
        }
        Integer state = (Integer) jsonObject.get("state");
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like(ReceiptPerson::getName, name);
        }
        if (StringUtils.isNotBlank(phone)) {
            queryWrapper.like(ReceiptPerson::getPhone, phone);
        }
        if (state != null) {
            queryWrapper.eq(ReceiptPerson::getState, state);
        }
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (mallConfig.profilesActive.equals("dev")) {
            String roleName = RoleEnum.ROLE_5.getName();
            List<MallRole> mallRoles = user.getMallRoles();
            if (CollectionUtils.isEmpty(mallRoles) || mallRoles.stream().noneMatch(t -> t.getName().equals(roleName))) {
                throw new BusinessException(500, "当前用户无物资采购平台履约系统权限！");
            }
            // 机构数据查看权限（1本机及子级2只看本级3指定）
            MallRole role = mallRoles.stream().filter(t -> t.getName().equals(roleName)).findFirst().get();
            Integer dataSelect = (Integer) jsonObject.get("dataSelect");
            List dataScopes = (List) jsonObject.get("dataScope");
            if (dataScopes != null &&dataSelect == 3 && role.getOrgSearch() == 1) {
                // 指定
                if (dataScopes != null && !CollectionUtils.isEmpty(dataScopes)) {
                    List<String> orgIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(dataScopes);
                    if (org.springframework.util.CollectionUtils.isEmpty(orgIds)){
                        throw new BusinessException("机构查询失败");
                    }
                    queryWrapper.in(ReceiptPerson::getLocalOrgId, orgIds);
                }
                // 为空则查询除自己之外
                else {
                    List<String> ids = user.getOrgAndSon().stream().map(OrgAndSon::getOrgId).collect(Collectors.toList());
                    List<String> id = ids.stream().filter(t -> !t.equals(user.getEnterpriseId())).collect(Collectors.toList());
                    List<String> orgIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(id);
                    queryWrapper.in(!CollectionUtils.isEmpty(orgIds),ReceiptPerson::getLocalOrgId, orgIds);
                }
            }
            if (dataScopes != null &&dataSelect == 2) {
                // 自己
                // 本地机构id
                String enterpriseId = user.getEnterpriseId();
                queryWrapper.eq(ReceiptPerson::getLocalOrgId, enterpriseId);
            }
            if (dataScopes != null &&dataSelect == 1 && role.getOrgSearch() == 1) {
                //  全部
                List<String> ids = user.getOrgAndSon().stream().map(OrgAndSon::getOrgId).collect(Collectors.toList());
                List<String> orgIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(ids);
                queryWrapper.in(!CollectionUtils.isEmpty(orgIds),ReceiptPerson::getLocalOrgId, orgIds);
            }
        }
        else {
            String enterpriseId = user.getEnterpriseId();
            queryWrapper.eq(ReceiptPerson::getLocalOrgId, enterpriseId);
        }

        IPage<ReceiptPerson> page = this.page(
                new Query<ReceiptPerson>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ReceiptPerson receiptPerson) {
        Integer count = lambdaQuery().eq(ReceiptPerson::getPhone, receiptPerson.getPhone()).count();
        if (count > 0) {
            throw new BusinessException("手机号重复");
        }
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        receiptPerson.setLocalOrgId(currentUser.getEnterpriseId());
        receiptPerson.setLocalOrgName(currentUser.getEnterpriseName());
        super.save(receiptPerson);
    }

    @Override
    public void update(ReceiptPerson receiptPerson) {
        Integer count = lambdaQuery().eq(ReceiptPerson::getPhone, receiptPerson.getPhone())
                .and(t ->{
                    t.ne(ReceiptPerson::getReceiptPersonId,receiptPerson.getReceiptPersonId());
                })
                .count();
        if (count > 0){
            throw new BusinessException("手机号重复");
        }
        super.updateById(receiptPerson);
    }


    @Override
    public ReceiptPerson getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateByPublish(List<String> ids, String type) {
        List<ReceiptPerson> result = listByIds(ids);
        for (ReceiptPerson person : result) {
            if ("1".equals(type)) {
                person.setState(1);
            } else {
                person.setState(0);
            }
        }
        super.saveOrUpdateBatch(result);
    }
}