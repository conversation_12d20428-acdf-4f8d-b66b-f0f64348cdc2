package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.FileRecordDelete;
import scrbg.meplat.mall.mapper.FileRecordDeleteMapper;
import scrbg.meplat.mall.service.FileRecordDeleteService;

/**
 * @描述： 服务类
 * @作者: y
 * @日期: 2022-11-28
 */
@Service
public class FileRecordDeleteServiceImpl extends ServiceImpl<FileRecordDeleteMapper, FileRecordDelete> implements FileRecordDeleteService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<FileRecordDelete> queryWrapper) {
        IPage<FileRecordDelete> page = this.page(
        new Query<FileRecordDelete>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(FileRecordDelete fileRecordDelete) {
        if(StringUtils.isBlank(fileRecordDelete.getRecordId())){
            return;
        }else {
            Integer count = lambdaQuery().eq(FileRecordDelete::getRecordId, fileRecordDelete.getRecordId()).count();
            if(count > 0){
                return;
            }else {
                super.save(fileRecordDelete);
            }
        }


    }

    @Override
    public void update(FileRecordDelete fileRecordDelete) {
        super.updateById(fileRecordDelete);
    }


    @Override
    public FileRecordDelete getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
