package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.Brand;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.mapper.OutBoxMapper;
import scrbg.meplat.mall.service.OutBoxService;
import scrbg.meplat.mall.vo.user.userCenter.OutBoxVo;

import java.util.List;
import java.util.Map;

@Service
public class OutBoxServiceImpl extends ServiceImpl<OutBoxMapper, EnterpriseInfo> implements OutBoxService {
@Autowired
MallConfig mallConfig;
    @Autowired
    OutBoxMapper outBoxMapper;
    @Override
    public PageUtils queryPageSupplierShops(JSONObject jsonObject, QueryWrapper<OutBoxVo> q) {
        IPage<OutBoxVo> pages = new Query<OutBoxVo>().getPage(jsonObject);
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        List<OutBoxVo> list=baseMapper.getSupplyShop(pages,q,innerMap);


        pages.setRecords(list);
        return new PageUtils(pages);
    }


}

