package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.SynthesizeTemporaryDtlService;
import scrbg.meplat.mall.entity.SynthesizeTemporaryDtl;

import java.util.List;

/**
 * @描述：控制类
 * @作者: ye
 * @日期: 2023-10-07
 */
@RestController
@RequestMapping("/synthesizeTemporaryDtl")
@Api(tags = "大宗临购单明细")
public class SynthesizeTemporaryDtlController {

    @Autowired
    public SynthesizeTemporaryDtlService synthesizeTemporaryDtlService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<SynthesizeTemporaryDtl> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = synthesizeTemporaryDtlService.queryPage(jsonObject, new LambdaQueryWrapper<SynthesizeTemporaryDtl>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<SynthesizeTemporaryDtl> findById(String id) {
        SynthesizeTemporaryDtl synthesizeTemporaryDtl = synthesizeTemporaryDtlService.getById(id);
        return R.success(synthesizeTemporaryDtl);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody SynthesizeTemporaryDtl synthesizeTemporaryDtl) {
        synthesizeTemporaryDtlService.create(synthesizeTemporaryDtl);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody SynthesizeTemporaryDtl synthesizeTemporaryDtl) {
        synthesizeTemporaryDtlService.update(synthesizeTemporaryDtl);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        synthesizeTemporaryDtlService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        synthesizeTemporaryDtlService.removeByIds(ids);
        return R.success();
    }
}

