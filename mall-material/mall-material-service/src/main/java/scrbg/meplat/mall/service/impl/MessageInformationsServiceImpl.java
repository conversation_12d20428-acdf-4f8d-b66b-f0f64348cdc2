package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.MessageInformations;
import scrbg.meplat.mall.mapper.MessageInformationsMapper;
import scrbg.meplat.mall.service.MessageInformationsService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @描述：反馈中心 服务类
 * @作者: y
 * @日期: 2022-11-22
 */
@Service
public class MessageInformationsServiceImpl extends ServiceImpl<MessageInformationsMapper, MessageInformations> implements MessageInformationsService {
    @Autowired
    public MessageInformationsMapper messageInformationsMapper;
    /**
     * 新增新留言
     * @param
     * @return
     */
    @Override
    public void createMessage(MessageInformations messageInfor) {
        MessageInformations messageInformations = new MessageInformations();
        UserLogin userLogin = ThreadLocalUtil.getCurrentUser();
        messageInformations.setMessageId(userLogin.getUserId());
        messageInformations.setMessageName(userLogin.getUserName());

        messageInformations.setMessagTitle(messageInfor.getMessagTitle());
        messageInformations.setMessagContent(messageInfor.getMessagContent());
        messageInformations.setMessagDate(new Date());
        messageInformations.setState(0);
        messageInformations.setPublicDisplay(0);

        String enterpriseName =  ThreadLocalUtil.getCurrentUser().getEnterpriseName();
        if (!StringUtils.isEmpty(enterpriseName)) {
            messageInformations.setEnterpriseName(userLogin.getEnterpriseName());
        }
        String shopName =  ThreadLocalUtil.getCurrentUser().getShopName();
        if (!StringUtils.isEmpty(shopName)) {
            messageInformations.setShopName(userLogin.getShopName());
        }
        Integer isInterior = ThreadLocalUtil.getCurrentUser().getIsInterior();
        if (!StringUtils.isEmpty(isInterior)) {
            messageInformations.setIsInterior(userLogin.getIsInterior());
        }
//保存留言信息
        super.save(messageInformations);
    }
    /**
     * 用户查询留言
     * @param
     * @return
     */
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MessageInformations> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        wrapper.eq(MessageInformations::getMessageId,user.getUserId());
        Integer messageType = (Integer) innerMap.get("messageType");
        if (messageType!=null){
        }
        String title = (String) innerMap.get("title");
        if (!StringUtils.isEmpty(title)) {
            wrapper.like(MessageInformations::getMessagTitle,title);
        }
 
        String keywords = (String) innerMap.get("keywords");
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.like(MessageInformations::getMessagContent,keywords);
        }
        String startDate = (String) innerMap.get("startDate");
        if (!StringUtils.isEmpty(startDate)) {
            wrapper.gt(MessageInformations::getMessagDate,startDate);
        }
        String endDate = (String) innerMap.get("endDate");
        if (!StringUtils.isEmpty(endDate)) {
            wrapper.lt(MessageInformations::getMessagDate,endDate);
        }
        wrapper.orderByDesc(MessageInformations::getMessagDate);
        IPage<MessageInformations> page = this.page(
                new Query<MessageInformations>().getPage(jsonObject),
                wrapper
        );
        return new PageUtils(page);
    }
    /**
     * 用户查看公开留言
     * @param
     * @return
     */
    @Override
    public PageUtils queryShowPage(JSONObject jsonObject, LambdaQueryWrapper<MessageInformations> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        Integer messageType = (Integer) innerMap.get("messageType");
        if (messageType!=null){
        }
        String title = (String) innerMap.get("title");
        if (!StringUtils.isEmpty(title)) {
            wrapper.like(MessageInformations::getMessagTitle,title);
        }

        String keywords = (String) innerMap.get("keywords");
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.like(MessageInformations::getMessagContent,keywords);
        }
        String startDate = (String) innerMap.get("startDate");
        if (!StringUtils.isEmpty(startDate)) {
            wrapper.gt(MessageInformations::getMessagDate,startDate);
        }
        String endDate = (String) innerMap.get("endDate");
        if (!StringUtils.isEmpty(endDate)) {
            wrapper.lt(MessageInformations::getMessagDate,endDate);
        }
        wrapper.eq(MessageInformations::getPublicDisplay,"1");
        wrapper.orderByDesc(MessageInformations::getMessagDate);
        IPage<MessageInformations> page = this.page(
                new Query<MessageInformations>().getPage(jsonObject),
                wrapper
        );
        return new PageUtils(page);
    }
    /**
     * 管理员查询所有留言
     * @param
     * @return
     */
    @Override
    public PageUtils queryAllPage(JSONObject jsonObject, LambdaQueryWrapper<MessageInformations> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String title = (String) innerMap.get("title");
        if (!StringUtils.isEmpty(title)) {
            wrapper.like(MessageInformations::getMessagTitle,title);
        }
        Integer state = (Integer) innerMap.get("state");
        if (!StringUtils.isEmpty(state)) {
            wrapper.eq(MessageInformations::getState,state);
        }
        String keywords = (String) innerMap.get("keywords");
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.like(MessageInformations::getMessagContent,keywords);
        }
        String startDate = (String) innerMap.get("startDate");
        if (!StringUtils.isEmpty(startDate)) {
            wrapper.gt(MessageInformations::getMessagDate,startDate);
        }
        String endDate = (String) innerMap.get("endDate");
        if (!StringUtils.isEmpty(endDate)) {
            wrapper.lt(MessageInformations::getMessagDate,endDate);
        }
        String respondEndDate = (String) innerMap.get("respondEndDate");
        if (!StringUtils.isEmpty(respondEndDate)) {
            wrapper.lt(MessageInformations::getRespondDate,respondEndDate);
        }
        String respondStartDate = (String) innerMap.get("respondStartDate");
        if (!StringUtils.isEmpty(respondStartDate)) {
            wrapper.gt(MessageInformations::getRespondDate,respondStartDate);
        }

        wrapper.orderByDesc(MessageInformations::getMessagDate);
        IPage<MessageInformations> page = this.page(
                new Query<MessageInformations>().getPage(jsonObject),
                wrapper
        );
        return new PageUtils(page);
    }
    /**
     * 根据主键删除留言
     * @param
     * @return
     */
    @Override
    public void delete(String id) {
        super.removeById(id);
    }
    /**
     * 新增回复
     * @param
     * @return
     */
    @Override
    public void createRespond(MessageInformations messageInfor) {
        MessageInformations messageInformations = new MessageInformations();
        UserLogin userLogin = ThreadLocalUtil.getCurrentUser();
        messageInformations.setRespondId(userLogin.getUserId());
        messageInformations.setRespondName(userLogin.getUserName());

        messageInformations.setRespondTitle(messageInfor.getMessagTitle());
        messageInformations.setRespondContent(messageInfor.getMessagContent());
        messageInformations.setRespondDate(new Date());
        messageInformations.setMessageInformationsId(messageInfor.getMessageInformationsId());
        messageInformations.setState(1);
        //保存回复信息
        super.updateById(messageInformations);
    }
    /**
     * 批量取消展示或展示
     * @param
     * @return
     */
    @Override
    public void updateByPublish(List<String> ids, String type) {
        List<MessageInformations> resutls = listByIds(ids);
        for (MessageInformations messageInformations : resutls) {
            if (type == "1") {
                messageInformations.setPublicDisplay(1);
            } if (type == "0"){
                messageInformations.setPublicDisplay(0);
            }
        }
        super.saveOrUpdateBatch(resutls);
    }
}
