package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.PlatformBalanceOperate;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.PlatformBalanceOperate;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：平台交易余额操作记录 服务类
 * @作者: ye
 * @日期: 2024-01-31
 */
public interface PlatformBalanceOperateService extends IService<PlatformBalanceOperate> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformBalanceOperate> queryWrapper);

        void create(PlatformBalanceOperate platformBalanceOperate);
        void update(PlatformBalanceOperate platformBalanceOperate);
        PlatformBalanceOperate getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
