package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtlChange;
import scrbg.meplat.mall.mapper.MaterialMonthSupplyPlanDtlChangeMapper;
import scrbg.meplat.mall.service.MaterialMonthSupplyPlanDtlChangeService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

/**
 * @描述：计划明细 服务类
 * @作者: ye
 * @日期: 2023-06-27
 */
@Service
public class MaterialMonthSupplyPlanDtlChangeServiceImpl extends ServiceImpl<MaterialMonthSupplyPlanDtlChangeMapper, MaterialMonthSupplyPlanDtlChange> implements MaterialMonthSupplyPlanDtlChangeService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMonthSupplyPlanDtlChange> queryWrapper) {
        IPage<MaterialMonthSupplyPlanDtlChange> page = this.page(
        new Query<MaterialMonthSupplyPlanDtlChange>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(MaterialMonthSupplyPlanDtlChange materialMonthSupplyPlanDtlChange) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(materialMonthSupplyPlanDtlChange);
    }

    @Override
    public void update(MaterialMonthSupplyPlanDtlChange materialMonthSupplyPlanDtlChange) {
        super.updateById(materialMonthSupplyPlanDtlChange);
    }


    @Override
    public MaterialMonthSupplyPlanDtlChange getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}