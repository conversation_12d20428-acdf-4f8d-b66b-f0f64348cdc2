package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.BiddingProduct;
import scrbg.meplat.mall.mapper.BiddingProductMapper;
import scrbg.meplat.mall.service.BiddingProductService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

import java.util.List;

/**
 * @描述：竞价商品信息 服务类
 * @作者: ye
 * @日期: 2023-07-11
 */
@Service
public class BiddingProductServiceImpl extends ServiceImpl<BiddingProductMapper, BiddingProduct> implements BiddingProductService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingProduct> queryWrapper) {
        IPage<BiddingProduct> page = this.page(
        new Query<BiddingProduct>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(BiddingProduct biddingProduct) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingProduct);
    }

    @Override
    public void update(BiddingProduct biddingProduct) {
        super.updateById(biddingProduct);
    }


    @Override
    public BiddingProduct getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public List<BiddingProduct> selectBidingPurchaseByBidingId(String bidingId) {
        LambdaQueryWrapper<BiddingProduct> q = new LambdaQueryWrapper<>();
        q.eq(BiddingProduct::getBiddingId,bidingId);
        List<BiddingProduct> list = list(q);
        return list;

    }
}
