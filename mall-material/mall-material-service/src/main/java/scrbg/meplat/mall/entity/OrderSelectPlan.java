package scrbg.meplat.mall.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.entity.plan.Plan;

/**
 * @描述：选择计划清单
 * @作者: ye
 * @日期: 2023-02-28
 */
@ApiModel(value = "选择计划清单")
@Data
@TableName("order_select_plan")
public class OrderSelectPlan extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "订单已选择计划id")
    private String orderSelectPlanId;

    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "订单号")
    private String orderSn;

    @ApiModelProperty(value = "机构id(采购员的远程orgId)")
    private String orgId;

    @ApiModelProperty(value = "机构名称(采购员的机构名称)")
    private String orgName;

    @ApiModelProperty(value = "订单项id")
    private String orderItemId;

    @ApiModelProperty(value = "计划明细id")
    private String dtlId;

    @ApiModelProperty(value = "商品名称")
    private String equipmentName;

    @ApiModelProperty(value = "已选数量")
    private BigDecimal count;

    @ApiModelProperty(value = "计划编号")
    private String billNo;

    @ApiModelProperty(value = "计划id")
    private String billId;

    @ApiModelProperty(value = "状态")
    private Integer state;

    @ApiModelProperty(value = "商品类型：0物资 10零星采购，11办公用品, 12大宗月供订单")
    private Integer productType;

    @ApiModelProperty(value = "计划单价")
    private BigDecimal price;

    @ApiModelProperty(value = "计划总金额")
    private BigDecimal account;

    @ApiModelProperty(value = "供应商id（pcwp供应商id）")
    private String storageId;

    @ApiModelProperty(value = "供应商内部名称")
    private String storageName;

    @ApiModelProperty(value = "机构简码")
    private String shortCode;

    @ApiModelProperty(value = "供应商信用代码")
    private String creditCode;

    @ApiModelProperty(value = "供应商类型（1外部供应商2内部供应商）")
    private Integer supplierType;

    @ApiModelProperty(value = "供应商内部机构id（是机构id）")
    private String storageOrgId;

    @ApiModelProperty(value = "计划类型(0零星采购 1大宗临购 2周转材料)")
    private Integer planType;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "清单类型（1浮动价格2固定价格）（大宗临购使用）")
    private Integer billType;

    @ApiModelProperty(value = "大宗临购单明细id")
    private String synthesizeTemporaryDtlId;

    @ApiModelProperty(value = "源计划明细总数量（不能使用这个会变更变化）")
    private BigDecimal sourcePlanQty;

    @ApiModelProperty(value = "源合同明细总数量")
    private BigDecimal sourceContractQty;

    @TableField(exist = false)
    Plan plan;//计划
}
