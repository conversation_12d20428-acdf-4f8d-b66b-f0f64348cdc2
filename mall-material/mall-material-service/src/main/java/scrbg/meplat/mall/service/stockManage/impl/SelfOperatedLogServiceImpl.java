package scrbg.meplat.mall.service.stockManage.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.entity.SelfOperatedStoreRecord;
import scrbg.meplat.mall.enums.file.FileEnum;
import scrbg.meplat.mall.mapper.ProductMapper;
import scrbg.meplat.mall.mapper.stockManage.SelfOperatedLogMapper;
import scrbg.meplat.mall.service.FileService;
import scrbg.meplat.mall.service.stockManage.SelfOperatedLogService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.pageUtils;

import java.util.List;
import java.util.Map;


@Service
public class SelfOperatedLogServiceImpl extends ServiceImpl<SelfOperatedLogMapper, SelfOperatedStoreRecord> implements SelfOperatedLogService {

    private SelfOperatedLogMapper selfOperatedLogMapper;

    private ProductMapper productMapper;

    private FileService fileService;


    @Autowired
    public void setSelfOperatedLogMapper(SelfOperatedLogMapper selfOperatedLogMapper) {
        this.selfOperatedLogMapper = selfOperatedLogMapper;
    }

    @Autowired
    public void setProductMapper(ProductMapper productMapper) {
        this.productMapper = productMapper;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Override
    public void delete(String id) {
        selfOperatedLogMapper.deleteById(id);
    }

    @Override
    public SelfOperatedStoreRecord getSelfInfo(String id) {
        SelfOperatedStoreRecord record = getById(id);
        if(null  == record){
            return null;
        }
        // 获取主图
        List<File> files = fileService.listFileByParameters(record.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_YES.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_GENERAL.getCode());
        record.setAdminFile(files);
        // 获取小图
        List<File> files2 = fileService.listFileByParameters(record.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_NO.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_MIN.getCode());
        record.setMinFile(files2);
        // 获取商品图片
        List<File> files3 = fileService.listFileByParameters(record.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_NO.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_GENERAL.getCode());
        record.setProductFiles(files3);
        return record;
    }

    @Override
    public void saveLog(SelfOperatedStoreRecord log) {
        String userName = ThreadLocalUtil.getCurrentUser().getUserName();
        log.setOperationUser(userName);
        if (log.getCostPrice() != null) {
            log.setProfitPrice(log.getSellPrice().subtract(log.getCostPrice()));
        }
        super.save(log);
    }

    @Override
    public void saveLogs(List<SelfOperatedStoreRecord> logs) {
        super.saveBatch(logs);
    }

    @Override
    public PageUtils<SelfOperatedStoreRecord> queryPage(JSONObject jsonObject, LambdaQueryWrapper<SelfOperatedStoreRecord> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String serialNum = (String) innerMap.get("serialNum");
        String productName = (String) innerMap.get("productName");
        String className = (String) innerMap.get("className");
        String belowPrice = (String) innerMap.get("belowPrice");
        String abovePrice = (String) innerMap.get("abovePrice");
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(SelfOperatedStoreRecord::getProductName, keywords)
                        .or()
                        .like(SelfOperatedStoreRecord::getSerialNum, keywords);

            });
        }

        if (!StringUtils.isEmpty(productName)) {
            queryWrapper.like(SelfOperatedStoreRecord::getProductName, productName);
        }
        if (!StringUtils.isEmpty(serialNum)) {
            queryWrapper.like(SelfOperatedStoreRecord::getSerialNum, serialNum);
        }
        if (!StringUtils.isEmpty(className)) {
            queryWrapper.like(SelfOperatedStoreRecord::getClassPathName, className);
        }
        queryWrapper.between(StringUtils.isNotBlank(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), SelfOperatedStoreRecord::getGmtCreate, startCreateDate, endCreateDate);
        queryWrapper.ge(StringUtils.isNotBlank(abovePrice), SelfOperatedStoreRecord::getSellPrice, abovePrice);
        queryWrapper.le(StringUtils.isNotBlank(belowPrice), SelfOperatedStoreRecord::getSellPrice, belowPrice);
        queryWrapper.orderByDesc(SelfOperatedStoreRecord::getGmtCreate);
        IPage<SelfOperatedStoreRecord> page = this.page(
                new Query<SelfOperatedStoreRecord>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils<SelfOperatedStoreRecord>(page);
    }

    @Override
    public PageUtils<SelfOperatedStoreRecord> getProductInfo(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
        innerMap.put("shopId",shopId);
        int count = productMapper.listSelfMaterialPageCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);
        Page<SelfOperatedStoreRecord> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<SelfOperatedStoreRecord> selfOperatedStoreRecords = productMapper.listSelfMaterialPage(pages, innerMap);
        pages.setRecords(selfOperatedStoreRecords);
        return new PageUtils<SelfOperatedStoreRecord>(pages);
    }
}
