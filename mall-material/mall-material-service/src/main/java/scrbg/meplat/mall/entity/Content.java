package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @描述：内容
 * @作者: Fiver.hu
 * @日期: 2022-12-05
 */
@ApiModel(value = "内容")
@Data
@TableName("content")
public class Content extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "内容id")
    private String contentId;

    @ApiModelProperty(value = "文件名")

    private String fileName;


    @ApiModelProperty(value = "内容")

    private String content;


    @ApiModelProperty(value = "源码")

    private String source;


    @ApiModelProperty(value = "状态")

    private Integer state;


    @ApiModelProperty(value = "评论")

    private String comments;


    @ApiModelProperty(value = "默认图片路径")

    private String defaultPicturePath;


    @ApiModelProperty(value = "大图片路径")

    private String bigPicturePath;


    @ApiModelProperty(value = "小图片路径")

    private String smallPicturePath;


    @ApiModelProperty(value = "微小图片路径")

    private String tinyPicturePath;


    @ApiModelProperty(value = "是否主页显示: 1显示 2不显示")

    private String home;


    @ApiModelProperty(value = "是否顶部显示: 1显示 2:不显示")

    private String top;

    @ApiModelProperty(value = "重要通知: 1是 2:否")
    private Integer isImportantNotice;


    @ApiModelProperty(value = "标题")

    private String title;


    @ApiModelProperty(value = "信息类型")

    private Integer infoType;


    @ApiModelProperty(value = "副标题")

    private String subtitle;


    @ApiModelProperty(value = "作者")

    private String author;


    @ApiModelProperty(value = "文件路径")

    private String filePath;


    @ApiModelProperty(value = "主图片路径")

    private String homePicturePath;


    @ApiModelProperty(value = "浏览次数")

    private Integer viewCount;


    @ApiModelProperty(value = "栏目id")

    private String programaId;


    @ApiModelProperty(value = "栏目Key")

    private String programaKey;


    @ApiModelProperty(value = "发布时间")

    private Date gmtRelease;

    @ApiModelProperty(value = "概述")

    private String summary;
    @ApiModelProperty(value = "用户注册协议")

    private String agreementType;
    @ApiModelProperty(value = "附件列表")

    @TableField(exist = false)
    private List<File> files;

    @ApiModelProperty(value = "新闻主页图")

    private String bannerImg;


}
