package scrbg.meplat.mall.util.excel.productCompareExcel;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;

/**
 * @program: maill_api
 * @description: 图片大小读取
 * @author: 代文翰
 * @create: 2023-12-20 10:58
 **/
public class ImageUtils {
    public static ImageSize getImageSize(byte[] imageData) throws Exception {
        ByteArrayInputStream bis = new ByteArrayInputStream(imageData);
        BufferedImage bufferedImage = ImageIO.read(bis);
        int width = bufferedImage.getWidth();
        int height = bufferedImage.getHeight();
        return new ImageSize(width, height);
    }
}

class ImageSize {
    private int width;
    private int height;

    public ImageSize(int width, int height) {
        this.width = width;
        this.height = height;
    }

    // Getters and setters

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }
}
