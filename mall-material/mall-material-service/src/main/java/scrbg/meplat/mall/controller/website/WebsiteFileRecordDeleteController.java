package scrbg.meplat.mall.controller.website;

import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.entity.FileRecordDelete;
import scrbg.meplat.mall.service.FileRecordDeleteService;
import scrbg.meplat.mall.service.FileService;

/**
 * @描述：控制类
 * @作者: y
 * @日期: 2022-11-28
 */
@RestController
@RequestMapping("/w/fileRecordDelete")
@Api(tags = "附件删除表")
public class WebsiteFileRecordDeleteController {

    @Autowired
    public FileRecordDeleteService fileRecordDeleteService;
    @Autowired
    public FileService fileService;

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody FileRecordDelete fileRecordDelete) {
        fileRecordDeleteService.create(fileRecordDelete);
        fileService.deleteByfileFarId(fileRecordDelete.getRecordId());
        return R.success();
    }

}

