package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.entity.PlatformDealFeeRecord;
import scrbg.meplat.mall.service.PlatformDealFeeRecordDtlService;
import scrbg.meplat.mall.entity.PlatformDealFeeRecordDtl;

import java.util.List;

/**
 * @描述：平台交易费缴费记录明细控制类
 * @作者: ye
 * @日期: 2024-01-24
 */
@RestController
@RequestMapping("/")
@Api(tags = "平台交易费缴费记录明细")
public class PlatformDealFeeRecordDtlController {

    @Autowired
    public PlatformDealFeeRecordDtlService platformDealFeeRecordDtlService;

    @PostMapping("supplier/platformDealFeeRecordDtl/batchCreate")
    @ApiOperation(value = "批量新增明细")
    @NotResubmit
    public R batchCreate(@RequestBody List<PlatformDealFeeRecordDtl> dtos) {
        platformDealFeeRecordDtlService.batchCreate(dtos);
        return R.success();
    }

    @PostMapping("supplier/platformDealFeeRecordDtl/batchDelete")
    @ApiOperation(value = "批量删除明细")
    public R batchDelete(@RequestBody List<String> ids) {
        platformDealFeeRecordDtlService.batchDelete(ids);
        return R.success();
    }

//    @PostMapping("/listByEntity")
//    @ApiOperation(value = "根据实体属性分页查询")
//    @DynamicParameters(name = "根据实体属性分页查询", properties = {
//            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
//            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
//    })
//    public PageR<PlatformDealFeeRecordDtl> listByEntity(@RequestBody JSONObject jsonObject) {
//        PageUtils page = platformDealFeeRecordDtlService.queryPage(jsonObject, new LambdaQueryWrapper<PlatformDealFeeRecordDtl>());
//        return PageR.success(page);
//    }
//
//    @GetMapping("/findById")
//    @ApiOperation(value = "根据主键查询")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R<PlatformDealFeeRecordDtl> findById(String id) {
//        PlatformDealFeeRecordDtl platformDealFeeRecordDtl = platformDealFeeRecordDtlService.getById(id);
//        return R.success(platformDealFeeRecordDtl);
//    }
//
//    @PostMapping("/create")
//    @ApiOperation(value = "新增")
//    public R save(@RequestBody PlatformDealFeeRecordDtl platformDealFeeRecordDtl) {
//        platformDealFeeRecordDtlService.create(platformDealFeeRecordDtl);
//        return R.success();
//    }
//
//    @PostMapping("/update")
//    @ApiOperation(value = "修改")
//    public R update(@RequestBody PlatformDealFeeRecordDtl platformDealFeeRecordDtl) {
//        platformDealFeeRecordDtlService.update(platformDealFeeRecordDtl);
//        return R.success();
//    }
//
//    @GetMapping("/delete")
//    @ApiOperation(value = "根据主键删除")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "ID", required = true,
//                    dataType = "String", paramType = "query")
//    })
//    public R delete(String id) {
//        platformDealFeeRecordDtlService.delete(id);
//        return R.success();
//    }
//
//
//    @PostMapping("/deleteBatch")
//    @ApiOperation(value = "根据主键批量删除")
//    public R deleteBatch(@RequestBody List<String> ids) {
//        platformDealFeeRecordDtlService.removeByIds(ids);
//        return R.success();
//    }
}

