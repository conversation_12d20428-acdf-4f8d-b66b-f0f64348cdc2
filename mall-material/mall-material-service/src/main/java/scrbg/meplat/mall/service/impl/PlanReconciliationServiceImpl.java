package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.scrbg.common.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.dto.reconciliation.SupplierPlanReconciliationQueryDTO;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.mapper.PlanReconciliationMapper;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.OrderShipDtlService;
import scrbg.meplat.mall.service.OrdersService;
import scrbg.meplat.mall.service.PlanReconciliationService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.reconciliation.PlanReconciliationVO;

import java.util.ArrayList;
import java.util.List;

/**
 * @描述：计划对账 服务实现类
 * @作者: tanfei
 * @日期: 2025-06-19
 */
@Slf4j
@Service
public class PlanReconciliationServiceImpl implements PlanReconciliationService {

    @Autowired
    private PlanReconciliationMapper baseMapper;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    OrdersService ordersService;

    @Autowired
    OrderShipDtlService orderShipDtlService;

    /**
     * 供应商方-获取可对账的计划订单列表
     * 根据当前登录用户的供应商信息过滤可对账的计划
     *
     * @param dto 查询参数
     * @return 分页结果
     */
    @Override
    public PageUtils getReconciliablePlansBySupplierPageList(SupplierPlanReconciliationQueryDTO dto) {
        // 设置默认分页参数（如果前端没有传入）
        if (dto.getPage() == null || dto.getPage() <= 0) {
            dto.setPage(1);
        }
        if (dto.getLimit() == null || dto.getLimit() <= 0) {
            dto.setLimit(10);
        }
        // 获取当前登录用户信息
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();
        // 设置供应商过滤条件到DTO中
        dto.setSupplierId(enterpriseId);
        // 获取总数
        int count = baseMapper.getReconciliablePlansBySupplierCount(dto);
        // 如果总数为0，直接返回空结果
        if (count == 0) {
            Page<PlanReconciliationVO> emptyPages = new Page<>(dto.getPage(), dto.getLimit());
            emptyPages.setTotal(0);
            emptyPages.setRecords(new ArrayList<>());
            return new PageUtils(emptyPages);
        }
        // 分页查询
        List<PlanReconciliationVO> list = baseMapper.getReconciliablePlansBySupplier(dto);
        // 构建分页结果
        Page<PlanReconciliationVO> pages = new Page<>(dto.getPage(), dto.getLimit());
        pages.setRecords(list);
        pages.setTotal(count);
        return new PageUtils(pages);
    }

    /**
     * 采购方-获取可对账的计划订单列表
     * @param dto 查询参数
     * @return 分页结果
     */
    @Override
    public PageUtils getReconciliablePlansByEnterprisePageList(SupplierPlanReconciliationQueryDTO dto) {
        // 设置默认分页参数（如果前端没有传入）
        if (dto.getPage() == null || dto.getPage() <= 0) {
            dto.setPage(1);
        }
        if (dto.getLimit() == null || dto.getLimit() <= 0) {
            dto.setLimit(10);
        }
        dto.setSupplierId(dto.getSupplierId());
        // 获取总数
        int count = baseMapper.getReconciliablePlansByEnterpriseCount(dto);
        // 如果总数为0，直接返回空结果
        if (count == 0) {
            Page<PlanReconciliationVO> emptyPages = new Page<>(dto.getPage(), dto.getLimit());
            emptyPages.setTotal(0);
            emptyPages.setRecords(new ArrayList<>());
            return new PageUtils(emptyPages);
        }
        // 分页查询
        List<PlanReconciliationVO> list = baseMapper.getReconciliablePlansByEnterprise(dto);
        // 构建分页结果
        Page<PlanReconciliationVO> pages = new Page<>(dto.getPage(), dto.getLimit());
        pages.setRecords(list);
        pages.setTotal(count);
        return new PageUtils(pages);
    }

    /**
     * 供应商方-获取可对账的项目部
     * 参照OrderShipDtlServiceImpl.getReconciliableMaterialList方法优化
     * 根据当前登录用户的供应商信息过滤可对账的项目部
     *
     * @param dto 查询参数
     * @return 分页结果
     */
    @Override
    public PageUtils getReconciliableEnterpriseBySupplierPageList(SupplierPlanReconciliationQueryDTO dto) {
        // 设置默认分页参数（如果前端没有传入）
        if (dto.getPage() == null || dto.getPage() <= 0) {
            dto.setPage(1);
        }
        if (dto.getLimit() == null || dto.getLimit() <= 0) {
            dto.setLimit(10);
        }
        // 获取当前登录用户信息
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();
        dto.setSupplierId(enterpriseId);
        // 设置默认查询条件 - 大宗临采
        if (dto.getProductType() == null) {
            dto.setProductType(2); // 2 对应大宗临购 (2, 12)，但实际数据是 product_type = 13
        }
        // 获取总数
        int count = baseMapper.getReconciliableEnterprisePageListCount(dto);
        // 如果总数为0，直接返回空结果
        if (count == 0) {
            Page<PlanReconciliationVO> emptyPages = new Page<>(dto.getPage(), dto.getLimit());
            emptyPages.setTotal(0);
            emptyPages.setRecords(new ArrayList<>());
            return new PageUtils(emptyPages);
        }
        // 分页查询
        List<PlanReconciliationVO> list = baseMapper.getReconciliableEnterprisePageList(dto);
        // 构建分页结果
        Page<PlanReconciliationVO> pages = new Page<>(dto.getPage(), dto.getLimit());
        pages.setRecords(list);
        pages.setTotal(count);
        return new PageUtils(pages);
    }

    /**
     * 采购方-获取可对账的供应商列表
     * @param dto 查询参数
     * @return 分页结果
     */
    @Override
    public PageUtils getReconciliableSupplierByEnterprisePageList(SupplierPlanReconciliationQueryDTO dto) {
        // 设置默认分页参数（如果前端没有传入）
        if (dto.getPage() == null || dto.getPage() <= 0) {
            dto.setPage(1);
        }
        if (dto.getLimit() == null || dto.getLimit() <= 0) {
            dto.setLimit(10);
        }
        // 获取当前登录用户信息
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();
        dto.setSupplierId(enterpriseId);
        // 获取总数
        int count = baseMapper.getReconciliableSupplierByEnterpriseCount(dto);
        // 如果总数为0，直接返回空结果
        if (count == 0) {
            Page<PlanReconciliationVO> emptyPages = new Page<>(dto.getPage(), dto.getLimit());
            emptyPages.setTotal(0);
            emptyPages.setRecords(new ArrayList<>());
            return new PageUtils(emptyPages);
        }
        // 分页查询
        List<PlanReconciliationVO> list = baseMapper.getReconciliableSupplierByEnterprise(dto);
        // 构建分页结果
        Page<PlanReconciliationVO> pages = new Page<>(dto.getPage(), dto.getLimit());
        pages.setRecords(list);
        pages.setTotal(count);
        return new PageUtils(pages);
    }
}
