package scrbg.meplat.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;

import scrbg.meplat.mall.entity.ProcessInstance;
import scrbg.meplat.mall.vo.processConfig.ProcessConfigDtlVO;
/**
 * 流程实例表 服务类
 * <AUTHOR>
 * @date: 2025年6月20日 上午9:51:25
 */
public interface ProcessInstanceService extends IService<ProcessInstance> {

        void create(ProcessInstance processInstance);

        void update(ProcessInstance processInstance);

        ProcessInstance getById(String id);
        
        ProcessInstance getProcessInstanceByKey(String businessKey);

        void delete(String id);

}
