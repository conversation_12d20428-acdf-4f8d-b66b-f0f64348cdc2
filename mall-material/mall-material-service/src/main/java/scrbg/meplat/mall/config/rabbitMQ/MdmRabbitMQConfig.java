package scrbg.meplat.mall.config.rabbitMQ;

import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;

@Log4j2
@Configuration
@EnableRabbit
@ConfigurationProperties(prefix = "spring.rabbitmq.mdm")
@ConditionalOnProperty(name = "spring.rabbitmq.mdm.isSync", havingValue = "true", matchIfMissing = false)
@Data
public class MdmRabbitMQConfig {

    private String host;
    private int port;
    private String username;
    private String password;
    private String virtualHost;
    private String exchange;
    private boolean isSync;
    private List<QueueConfig> queues;

//    @Bean
//    public TopicExchange mdmExchange() {
//        return new TopicExchange(exchange, true, false);
//    }

    @Bean
    public DirectExchange mdmExchange() {
        return new DirectExchange(exchange, true, false);
    }

    @Bean
    public Declarables bindings() {
        Declarables declarables = new Declarables();
        queues.forEach(queue -> {
            Queue q = new Queue(queue.getName(), true);
            Binding binding = BindingBuilder.bind(q)
                    .to(mdmExchange())
                    .with(queue.getRoutingKey());
            declarables.getDeclarables().add(q);
            declarables.getDeclarables().add(binding);
        });
        return declarables;
    }

    // 连接工厂配置
    @Bean
    public ConnectionFactory connectionFactory() {
        CachingConnectionFactory factory = new CachingConnectionFactory();
        factory.setHost(host);
        factory.setPort(port);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        return factory;
    }

//    @Bean
//    public ThreadPoolTaskExecutor rabbitTaskExecutor() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//
//        // 获取机器核心数 (留出2个核心给系统)
//        int availableCores = Math.max(2, Runtime.getRuntime().availableProcessors() - 2);
//
//        // 动态计算线程数 (建议公式)
//        int corePoolSize = availableCores * 2;  // CPU密集型可减少，IO密集型可增加
//        int maxPoolSize = availableCores * 4;
//
//        executor.setCorePoolSize(corePoolSize);
//        executor.setMaxPoolSize(maxPoolSize);
//        executor.setQueueCapacity(corePoolSize * 10); // 根据内存调整
//        executor.setThreadNamePrefix("rabbit-dynamic-");
//        log.info("动态线程池配置: 核心%d/最大%d (基于%d可用核心)%n",
//                corePoolSize, maxPoolSize, availableCores);
//        return executor;
//    }

//    @Bean
//    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
//            ConnectionFactory connectionFactory) {
//        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
//        factory.setConnectionFactory(connectionFactory);
//        factory.setConcurrentConsumers(5); // 非活跃期最低消耗
//        factory.setMaxConcurrentConsumers(10);
//        factory.setIdleEventInterval(600000L); // 10分钟检测
//        factory.setMissingQueuesFatal(false);
//        return factory;
//    }

    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
            ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setConcurrentConsumers(1); // 非活跃期最低消耗
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(10);
        factory.setIdleEventInterval(600000L); // 10分钟检测
        factory.setMissingQueuesFatal(false);
        return factory;
    }

//    @Bean
//    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
//            ConnectionFactory connectionFactory,ThreadPoolTaskExecutor rabbitTaskExecutor) {
//
//        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
//        factory.setConnectionFactory(connectionFactory);
//        factory.setTaskExecutor(rabbitTaskExecutor);
//        // 动态设置并发消费者 (不超过线程池核心数的一半)
//        int recommendedConcurrency = rabbitTaskExecutor.getCorePoolSize() / 2;
//        factory.setConcurrentConsumers(recommendedConcurrency);
//        factory.setMaxConcurrentConsumers(rabbitTaskExecutor.getMaxPoolSize() / 2);
//
//        // 根据线程池调整prefetch
//        factory.setPrefetchCount(Math.max(5, recommendedConcurrency * 2));
//        // 关键性能参数
//        factory.setIdleEventInterval(30000L); // 30秒空闲检测
//        factory.setMissingQueuesFatal(false); // 队列不存在时不报错
//        return factory;
//    }
    // 添加这个关键方法
    @Bean
    public String[] mdmQueueNames() {
        return queues.stream()
                .map(QueueConfig::getName)
                .toArray(String[]::new);
    }

    // getters and setters
    @Data
    public static class QueueConfig {
        private String name;
        private String routingKey;
    }
}
