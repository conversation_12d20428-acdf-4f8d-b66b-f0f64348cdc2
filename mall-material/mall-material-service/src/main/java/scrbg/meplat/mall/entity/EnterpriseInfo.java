package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.xiaoymin.knife4j.annotations.Ignore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @描述：企业附加信息表
 * @作者: y
 * @日期: 2022-11-28
 */
@ApiModel(value = "企业附加信息表")
@Data
@TableName("enterprise_info")
public class EnterpriseInfo extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "企业id")
    private String enterpriseId;

    @ApiModelProperty(value = "企业编号")
    private String enterpriseNumber;

    @ApiModelProperty(value = "企业名称（公司名称、供应商公司名称 ）")
    private String enterpriseName;

    @ApiModelProperty(value = "统一社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty(value = "法定代表人")
    private String legalRepresentative;

    @ApiModelProperty(value = "企业邮箱")
    private String email;

    @ApiModelProperty(value = "经营者")
    private String operator;

    @ApiModelProperty(value = "注册日期")
    private Date creationTime;

    @ApiModelProperty(value = "经营场所")
    private String placeOfBusiness;

    @ApiModelProperty(value = "营业执照有效期")
    private Date licenseTerm;

    @ApiModelProperty(value = "是否为蜀道企业(1是，0否)")
    private Integer shuDaoFlag;

    @ApiModelProperty(value = "是否生产商(1是，0否)")
    private Integer isProduction;

    @ApiModelProperty(value = "是否为贸易商(1是，0否)")
    private Integer isTrader;

    @ApiModelProperty(value = "注册资本(万元)")
    private BigDecimal registeredCapital;

    @ApiModelProperty(value = "注册省份")
    private String provinces;

    @ApiModelProperty(value = "注册市级")
    private String city;

    @ApiModelProperty(value = "注册县、区")
    private String county;

    @ApiModelProperty(value = "注册详细地址")
    private String detailedAddress;

    @ApiModelProperty(value = "工作注册省份")
    private String workProvinces;

    @ApiModelProperty(value = "工作注册市级")
    private String workCity;

    @ApiModelProperty(value = "工作注册县、区")
    private String workCounty;

    @ApiModelProperty(value = "纳税人类别")
    private String taxpayerCategory;

    @ApiModelProperty(value = "工作注册详细地址")
    private String workAddress;

    @ApiModelProperty(value = "经营范围")
    private String mainBusiness;

    @ApiModelProperty(value = "营业执照（地址）")
    private String businessLicense;

    @ApiModelProperty(value = "营业执照（记录id）")
    private String businessLicenseId;

    @ApiModelProperty(value = "身份证人像面(记录id)")
    private String cardPortraitFaceId;

    @ApiModelProperty(value = "身份证人像面(存地址)")
    private String cardPortraitFace;

    @ApiModelProperty(value = "身份证国徽面（记录id）")
    private String cardPortraitNationalEmblemId;

    @ApiModelProperty(value = "身份证国徽面（存地址）")
    private String cardPortraitNationalEmblem;

    @ApiModelProperty(value = "管理员姓名")
    private String adminName;

    @ApiModelProperty(value = "管理员电话")
    private String adminPhone;

    @ApiModelProperty(value = "管理员密码")
    @TableField(exist = false)
    private String adminPassword;

    @ApiModelProperty(value = "验证码")
    @TableField(exist = false)
    private String verificationCode;

    @ApiModelProperty(value = "管理员身份证号")
    private String adminNumber;

    @ApiModelProperty(value = "来电时间")
    private Date incomeCallTime;

    @ApiModelProperty(value = "来电内容")
    private String incomeCallContent;

    @ApiModelProperty(value = "处理部门")
    private String processingDepartnment;

    @ApiModelProperty(value = "处理时间")
    private Date processingTime;

    @ApiModelProperty(value = "办理状态： 0:办理中 1:办理成功")
    private Integer handlingResult;

    @ApiModelProperty(value = "状态 1：启用  0:禁用 ")
    private Integer state;

    @ApiModelProperty(value = "是否为供应商：0 : 否 1 ：待定  2：是")
    private Integer isSupplier;

    @ApiModelProperty(value = "是否为pcwp内部供应商：0 : 否 1 是")
    private Integer isPcwp;

    @ApiModelProperty(value = "企业类型：0：个体户  1：企业  2：个人")
    private Integer enterpriseType;

    @ApiModelProperty(value = "企业营业状态 ：0： 停业 1：营业")
    private Integer enterpriseBusinessType;

    @ApiModelProperty(value = "内部企业id")
    private String interiorId;

    @ApiModelProperty(value = "是否是物资商城企业")
    private Integer isMaterialMall;

    @ApiModelProperty(value = "是否是装备商城企业")
    private Integer isDeviceMall;

    @ApiModelProperty(value = "是否未通过供应商审核")
    private Integer isNoSupplierAudit;

    @ApiModelProperty(value = "导入类型：1excel")
    private Integer importType;
    @ApiModelProperty(value = "审核失败原因")
    private String auditFailReason;

    @ApiModelProperty(value = "机构简码")
    private String shortCode;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "可欠费额度   元")
    private BigDecimal arrearage;

    @ApiModelProperty(value = "欠费过期时间时长为null就是永久")
    private Integer arrearageDateNum;

    @ApiModelProperty(value = "欠欠费过期时间类型（1天2月3年）")
    private Integer arrearageDateType;

    @ApiModelProperty(value = "附件列表")
    @TableField(exist = false)
    private List<File> files;

    @ApiModelProperty(value = "图形验证码")
    @TableField(exist = false)
    private String verifyInput;

    @ApiModelProperty(value = "图形验证码id")
    @TableField(exist = false)
    private String verifyId;

    @ApiModelProperty(value = "店铺id")
    @TableField(exist = false)
    private String shopId;

    @ApiModelProperty(value = "供方类型（1.生产商  2.贸易商）")
    private String supplierType;

    @ApiModelProperty(value = "纳税人类别")
    private String taxpayerType;

    @ApiModelProperty(value = "固定工作地址-详细地址")
    private String workXxdz;

    @ApiModelProperty(value = "承诺书（记录id）")
    private String letterCommitmentId;

    @ApiModelProperty(value = "承诺书（存地址）")
    private String letterCommitment;

    @ApiModelProperty(value = "完税证明（记录id）")
    private String taxPaymentCertificateId;

    @ApiModelProperty(value = "完税证明（存地址）")
    private String taxPaymentCertificate;

    @ApiModelProperty(value = "完税证明开始时间")
    private Date tpcStartTime;

    @ApiModelProperty(value = "完税证明结束时间")
    private Date tpcEndTime;

    @ApiModelProperty(value = "税务评级证明（记录id）")
    private String taxRatingCertificateId;

    @ApiModelProperty(value = "税务评级证明（存地址）")
    private String taxRatingCertificate;

    @ApiModelProperty(value = "税务评级证明开始时间")
    private Date trcStartTime;

    @ApiModelProperty(value = "税务评级证明结束时间")
    private Date trcEndTime;

    @ApiModelProperty(value = "信用中国报告（记录id）")
    private String creditChinaReportId;

    @ApiModelProperty(value = "信用中国报告（存地址）")
    private String creditChinaReport;

    @ApiModelProperty(value = "信用中国报告开始时间")
    private Date ccrStartTime;

    @ApiModelProperty(value = "信用中国报告结束时间")
    private Date ccrEndTime;

    @ApiModelProperty(value = "执行信息（记录id）")
    private String zxgkId;

    @ApiModelProperty(value = "执行信息（存地址）")
    private String zxgk;

    @ApiModelProperty(value = "执行信息开始时间")
    private Date zxgkStartTime;

    @ApiModelProperty(value = "执行信息结束时间")
    private Date zxgkEndTime;

    @ApiModelProperty(value = "资质证书（记录id）")
    private String qualificationCertificateId;

    @ApiModelProperty(value = "资质证书（存地址）")
    private String qualificationCertificate;

    @ApiModelProperty(value = "资质证书开始时间")
    private Date qcStartTime;

    @ApiModelProperty(value = "资质证书结束时间")
    private Date qcEndTime;

    @ApiModelProperty(value = "其他资料（记录id）")
    private String otherId;

    @ApiModelProperty(value = "其他资料（存地址）")
    private String other;

    @ApiModelProperty(value = "其他资料开始时间")
    private Date otherStartTime;

    @ApiModelProperty(value = "其他资料结束时间")
    private Date otherEndTime;

    @ApiModelProperty(value = "质量认证类型")
    private String certificateType;

    @ApiModelProperty(value = "其他质量认证")
    private String certificateOther;

    @ApiModelProperty(value = "企业概况")
    private String companyProfile;

    @ApiModelProperty(value = "财务情况")
    private String financialSituation;

    @ApiModelProperty(value = "诉讼情况")
    private String litigationSituation;

    @ApiModelProperty(value = "开户银行")
    private String bankName;

    @ApiModelProperty(value = "银行户名")
    private String accountName;

    @ApiModelProperty(value = "银行账号")
    private String bankAccount;

    @ApiModelProperty(value = "开票备注")
    private String invoiceRemark;

    @ApiModelProperty(value = "法人身份证人像面（记录id）")
    private String legalPersonFaceId;

    @ApiModelProperty(value = "法人身份证人像面（存地址）")
    private String legalPersonFace;

    @ApiModelProperty(value = "法人身份证国徽面（记录id）")
    private String legalPersonNationalId;

    @ApiModelProperty(value = "法人身份证国徽面（存地址）")
    private String legalPersonNational;

    @ApiModelProperty(value = "法人姓名")
    private String legalPersonName;

    @ApiModelProperty(value = "法人身份证号码")
    private String legalPersonNum;

    @ApiModelProperty(value = "法人身份证开始日期")
    private Date lpStartTime;

    @ApiModelProperty(value = "法人身份证结束日期")
    private Date lpEndTime;

    @ApiModelProperty(value = "法人身份证明（记录id）")
    private String lpIdentificationId;

    @ApiModelProperty(value = "法人身份证明（存地址）")
    private String lpIdentification;

    @ApiModelProperty(value = "管理员授权委托书（记录id）")
    private String adminAuthorizeId;

    @ApiModelProperty(value = "管理员授权委托书（存地址）")
    private String adminAuthorize;

    @ApiModelProperty(value = "固定工作地址省")
    private String provincesGd;

    @ApiModelProperty(value = "固定工作地址市")
    private String cityGd;

    @ApiModelProperty(value = "固定工作地址区县")
    private String countyGd;

    @ApiModelProperty(value = "企业业绩列表")
    @TableField(exist = false)
    private List<EnterprisePerformance> epLists;

    @TableField(exist = false)
    private List<String> certificate;

    @ApiModelProperty(value = "注册状态（0.注册 1.平台初审 2.申请开店 3.合同签约及缴费 4.平台复审 5.完成）")
    private String zcstate;

    @ApiModelProperty(value = "附件是否修改（0.未修改  1.已修改）")
    private Integer isFileModify;

    @ApiModelProperty(value = "附件修改时间")
    private Date fileModifyTime;

    @ApiModelProperty(value = "注册省份编码")
    private String provincesCode;

    @ApiModelProperty(value = "注册市级编码")
    private String cityCode;

    @ApiModelProperty(value = "注册县、区编码")
    private String countyCode;

    @ApiModelProperty(value = "固定工作地址省编码")
    private String provincesGdCode;

    @ApiModelProperty(value = "固定工作地址市编码")
    private String cityGdCode;

    @ApiModelProperty(value = "固定工作地址区县编码")
    private String countyGdCode;

    @ApiModelProperty(value = "管理员身份证开始日期")
    private Date adminPeriodStart;

    @ApiModelProperty(value = "管理员身份证结束日期")
    private Date adminPeriodEnd;
}
