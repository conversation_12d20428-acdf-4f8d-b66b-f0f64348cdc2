package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.BiddingType;
import scrbg.meplat.mall.mapper.BiddingTypeMapper;
import scrbg.meplat.mall.service.BiddingTypeService;

/**
 * @描述：招标类型表 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
@Service
public class BiddingTypeServiceImpl extends ServiceImpl<BiddingTypeMapper, BiddingType> implements BiddingTypeService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingType> queryWrapper) {
        IPage<BiddingType> page = this.page(
                new Query<BiddingType>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(BiddingType biddingType) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingType);
    }

    @Override
    public void update(BiddingType biddingType) {
        super.updateById(biddingType);
    }


    @Override
    public BiddingType getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
