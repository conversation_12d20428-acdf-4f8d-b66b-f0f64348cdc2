package scrbg.meplat.mall.controller.website;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.service.SystemParamService;
import scrbg.meplat.mall.util.LinuxOperUtl;

import java.util.List;

/**
 * @描述：系统参数表控制类
 * @作者: sund
 * @日期: 2022-12-05
 */
@RestController
@RequestMapping("/w/systemParam")
@ApiSort(value = 100)
@Api(tags = "系统参数表")
public class WebsiteSystemParamController {
    @Autowired
    public SystemParamService systemParamService;






    @GetMapping("/listByCode")
    @ApiOperation(value = "根据编码获取参数列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true,
                    dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "数量", required = true,
                    dataType = "Integer", paramType = "query")
    })
    public R<List<SystemParam>> listByCode(String code, Integer size) {
        List<SystemParam> systemParam = systemParamService.listByCode(code, size);
        return R.success(systemParam);
    }

//    @GetMapping("/getQuality")
//    public R getQuality() {
//        List<SystemParamsVO> qualityMap = DictionariesUtil.getQualityMap();
//        return R.success(qualityMap);
//    }

}

