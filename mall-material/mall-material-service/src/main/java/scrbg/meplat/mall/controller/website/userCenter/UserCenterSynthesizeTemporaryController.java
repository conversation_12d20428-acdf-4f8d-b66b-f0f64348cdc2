package scrbg.meplat.mall.controller.website.userCenter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.bidding.AuditBusinessDTO;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.SynthesizeTemporary;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.SynthesizeTemporaryService;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.vo.GetSynthesizeTemporaryPlanDetailVO;

/**
 * @描述：大宗临购单控制类
 * @作者: ye
 * @日期: 2023-10-07
 */
@RestController
@RequestMapping("/userCenter/synthesizeTemporary")
@Api(tags = "大宗临购单")
public class UserCenterSynthesizeTemporaryController {

    @Autowired
    public SynthesizeTemporaryService synthesizeTemporaryService;

    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @PostMapping("/getThisOrgList")
    @ApiOperation(value = "查询当前机构的数据")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = Integer.class),
            @DynamicParameter(name = "stType", value = "清单类型 0 大宗临购 1 周转材料 默认值0", dataTypeClass = Integer.class)
    })
    @IsRole(roleName = RoleEnum.ROLE_3)
    public PageR<SynthesizeTemporary> getThisOrgList(@RequestBody JSONObject jsonObject) {
        PageUtils page = synthesizeTemporaryService.getThisOrgList(jsonObject, new LambdaQueryWrapper<SynthesizeTemporary>());
        return PageR.success(page);
    }

    @GetMapping("/getBySn")
    @ApiOperation(value = "根据编号获取数据")
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R<SynthesizeTemporary> getBySn(String sn, String orgId) {
        SynthesizeTemporary data = synthesizeTemporaryService.getBySn(sn, orgId);
        return R.success(data);
    }

    @PostMapping("/updateInfo")
    @ApiOperation(value = "修改数据")
    @IsRole(roleName = RoleEnum.ROLE_3)
    @NotResubmit(delaySeconds = 2)
    public R getBySn(@RequestBody SynthesizeTemporary dto) {
        synthesizeTemporaryService.updateInfo(dto);
        return R.success();
    }

    @GetMapping("/deleteInfo")
    @ApiOperation(value = "删除数据")
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R deleteInfo(String id) {
        synthesizeTemporaryService.deleteInfo(id);
        return R.success();
    }

    @PostMapping("/createSynthesizeTemporary")
    @ApiOperation(value = "新增清单")
    @NotResubmit
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R createSynthesizeTemporary(@RequestBody SynthesizeTemporary vo) {
        synthesizeTemporaryService.createSynthesizeTemporary(vo);
        return R.success();
    }

    @GetMapping("/deleteInfoItem")
    @ApiOperation(value = "删除明细数据")
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R deleteInfoItem(String id) {
        synthesizeTemporaryService.deleteInfoItem(id);
        return R.success();
    }

    @GetMapping("/submitSynthesizeTemporaryPlan")
    @ApiOperation(value = "推送大宗临购计划到pcwp")
    @IsRole(roleName = RoleEnum.ROLE_3)
    @NotResubmit(delaySeconds = 3)
    public R submitSynthesizeTemporaryPlan(String id) {
        String idStr = IdWorker.getIdStr();
        StringBuilder farArg = new StringBuilder();
        try {
            synthesizeTemporaryService.submitSynthesizeTemporaryPlan(id, idStr, farArg);
        } catch (Exception e) {
            LogUtil.writeErrorLog2(idStr, "submitSynthesizeTemporaryPlan", id, farArg.toString(), null, e.getMessage(), UserCenterSynthesizeTemporaryController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(UserCenterSynthesizeTemporaryController.class.getName());
            iLog.setMethodName("submitSynthesizeTemporaryPlan");
            iLog.setLocalArguments(JSON.toJSONString(id));
            iLog.setFarArguments(farArg.toString());
            iLog.setIsSuccess(0);
            iLog.setLogType(1);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @GetMapping("/getSynthesizeTemporaryPlanDetail")
    @ApiOperation(value = "根据pcwp大宗临购计划id获取计划明细vo")
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R<GetSynthesizeTemporaryPlanDetailVO> getSynthesizeTemporaryPlanDetail(String id) {
        GetSynthesizeTemporaryPlanDetailVO vo = synthesizeTemporaryService.getSynthesizeTemporaryPlanDetail(id);
        return R.success(vo);
    }

    @PostMapping("/refuseBusiness")
    @ApiOperation(value = "供应商拒绝单据")
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R refuseBusiness(@RequestBody AuditBusinessDTO dto) {
        synthesizeTemporaryService.refuseBusiness(dto);
        return R.success();
    }
}

