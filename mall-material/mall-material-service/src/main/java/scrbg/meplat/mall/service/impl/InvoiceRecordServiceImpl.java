package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import org.apache.commons.lang.StringUtils;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.InvoiceRecordMapper;
import scrbg.meplat.mall.service.InvoiceRecordService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.vo.invoice.ChangInvoiceStateVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-11-16
 */
@Service
public class InvoiceRecordServiceImpl extends ServiceImpl<InvoiceRecordMapper, InvoiceRecord> implements InvoiceRecordService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<InvoiceRecord> queryWrapper) {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String bankAccount = (String) innerMap.get("bankAccount");
        String bank = (String) innerMap.get("bank");
        Integer invoiceType = (Integer) innerMap.get("invoiceType");
        Integer riseType = (Integer) innerMap.get("riseType");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Integer state = (Integer) innerMap.get("state");
        String userName = (String) innerMap.get("belowPrice");
        String userAddress = (String) innerMap.get("userAddress");
        String userPhone = (String) innerMap.get("userPhone");
        String company = (String) innerMap.get("company");
        String dutyParagraph = (String) innerMap.get("dutyParagraph");
        queryWrapper.eq(state != null, InvoiceRecord::getState, state);
        queryWrapper.eq(riseType != null, InvoiceRecord::getRiseType, riseType);
        queryWrapper.eq(invoiceType != null, InvoiceRecord::getInvoiceType, invoiceType);
        queryWrapper.eq( InvoiceRecord::getEnterpriseId, enterpriseId);
        queryWrapper.like(StringUtils.isNotEmpty(bankAccount), InvoiceRecord::getBankAccount, bankAccount);
        queryWrapper.like(StringUtils.isNotEmpty(bank), InvoiceRecord::getBank, bank);
        queryWrapper.like(StringUtils.isNotEmpty(userName), InvoiceRecord::getUserName, userName);
        queryWrapper.like(StringUtils.isNotEmpty(userAddress), InvoiceRecord::getUserAddress, userAddress);
        queryWrapper.like(StringUtils.isNotEmpty(userPhone), InvoiceRecord::getUserPhone, userPhone);
        queryWrapper.like(StringUtils.isNotEmpty(company), InvoiceRecord::getCompany, company);
        queryWrapper.like(StringUtils.isNotEmpty(dutyParagraph), InvoiceRecord::getDutyParagraph, dutyParagraph);
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(InvoiceRecord::getUserName, keywords)
                        .or()
                        .like(InvoiceRecord::getBank, keywords)
                        .or()
                        .like(InvoiceRecord::getUserPhone, keywords)
                        .or()
                        .like(InvoiceRecord::getUserAddress, keywords)
                        .or()
                        .like(InvoiceRecord::getCompany, keywords)
                        .or()
                        .like(InvoiceRecord::getDutyParagraph, keywords)
                        .or()
                        .like(InvoiceRecord::getBankAccount, keywords);

            });
        }
        queryWrapper.orderByDesc(InvoiceRecord::getState);
        if (orderBy != null) {
            if (orderBy == 1) {
                queryWrapper.orderByDesc(InvoiceRecord::getGmtCreate);
            }
            if (orderBy == 2) {
                queryWrapper.orderByDesc(InvoiceRecord::getGmtModified);
            }

        }

        IPage<InvoiceRecord> page = this.page(
                new Query<InvoiceRecord>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(InvoiceRecord invoiceRecord) {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        Integer count =0;
        if (invoiceRecord.getInvoiceRecordId()!=null){
             count = lambdaQuery().eq(InvoiceRecord::getEnterpriseId, enterpriseId).eq(InvoiceRecord::getCompany, invoiceRecord.getCompany())
                    .eq(InvoiceRecord::getDutyParagraph, invoiceRecord.getDutyParagraph()).ne(InvoiceRecord::getInvoiceRecordId,invoiceRecord.getInvoiceRecordId()).count();
        }else {
            count = lambdaQuery().eq(InvoiceRecord::getEnterpriseId, enterpriseId).eq(InvoiceRecord::getCompany, invoiceRecord.getCompany())
                    .eq(InvoiceRecord::getDutyParagraph, invoiceRecord.getDutyParagraph()).count();
        }
        if (count!=0){
            throw new BusinessException(500,"单位名称和单位税号不能重复");
        }
        if (invoiceRecord.getState()==1){
            lambdaUpdate().eq(InvoiceRecord::getEnterpriseId, enterpriseId).set(InvoiceRecord::getState, 0).update();
        }
        invoiceRecord.setEnterpriseId(enterpriseId);
        //调用父类方法即可
        //也可以baseMapper.insert
        super.saveOrUpdate(invoiceRecord);
    }

    @Override
    public void update(InvoiceRecord invoiceRecord) {
        if (invoiceRecord.getState()==1){
            String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
            lambdaUpdate().eq(InvoiceRecord::getEnterpriseId,enterpriseId).set(InvoiceRecord::getState,0);
        }
        super.updateById(invoiceRecord);
    }


    @Override
    public InvoiceRecord getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }


    @Override
    public void updateState(ChangInvoiceStateVo changInvoiceStateVo) {
        InvoiceRecord byId = getById(changInvoiceStateVo.getInvoiceRecordId());
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        ArrayList<InvoiceRecord> invoiceRecords = new ArrayList<>();
        if (changInvoiceStateVo.getState()==1){
            InvoiceRecord one = lambdaQuery().
                    eq(InvoiceRecord::getEnterpriseId, enterpriseId).
                    eq(InvoiceRecord::getState, 1).one();
            if (one!=null){
                one.setState(0);
                invoiceRecords.add(one);
            }
        }
        byId.setState(changInvoiceStateVo.getState());
        invoiceRecords.add(byId);
        updateBatchById(invoiceRecords);

    }

    @Override
    public InvoiceRecord getData() {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        InvoiceRecord one = lambdaQuery().
                eq(InvoiceRecord::getEnterpriseId, enterpriseId).
                eq(InvoiceRecord::getState, 1).one();
        return one;
    }

    @Override
    public InvoiceRecord getInvoiceRecord(String id) {
        InvoiceRecord one = lambdaQuery().eq(InvoiceRecord::getEnterpriseId, id)
                .eq(InvoiceRecord::getState, 1).one();
        if(null == one){
            return new InvoiceRecord();
        }
        return one;
    }
}
