package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.ProductCategory1;
import scrbg.meplat.mall.mapper.ProductCategory1Mapper;
import scrbg.meplat.mall.service.ProductCategory1Service;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：商品分类 服务类
 * @作者: ye
 * @日期: 2023-09-27
 */
@Service
public class ProductCategory1ServiceImpl extends ServiceImpl<ProductCategory1Mapper, ProductCategory1> implements ProductCategory1Service{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductCategory1> queryWrapper) {
        IPage<ProductCategory1> page = this.page(
        new Query<ProductCategory1>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ProductCategory1 productCategory1) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(productCategory1);
    }

    @Override
    public void update(ProductCategory1 productCategory1) {
        super.updateById(productCategory1);
    }


    @Override
    public ProductCategory1 getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
