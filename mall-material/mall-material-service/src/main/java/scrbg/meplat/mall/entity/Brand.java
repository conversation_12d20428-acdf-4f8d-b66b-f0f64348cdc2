package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.List;

/**
 * @描述：品牌
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "品牌")
@Data
@TableName("brand")
public class Brand extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "品牌id")

    private String brandId;

    @ApiModelProperty(value = "品牌名")

    private String name;

    @ApiModelProperty(value = "品牌logo地址")

    private String logo;

    @ApiModelProperty(value = "介绍")

    private String descript;

    @ApiModelProperty(value = "显示状态[0-不显示；1-显示]")

    private Integer state;

    @ApiModelProperty(value = "")

    private Integer productType;

    @ApiModelProperty(value = "分类id")
    private String classId;

    @ApiModelProperty(value = "分类名称")
    private String className;

    @ApiModelProperty(value = "分类id路径/分割")
    private String classPath;


    @ApiModelProperty(value = "分类路径")
    @TableField(exist = false)
    private List<String> classNamePath;

    @ApiModelProperty(value = "分类路径xx/xxx/xxx")
    @TableField(exist = false)
    private String classNamePathStr;
}
