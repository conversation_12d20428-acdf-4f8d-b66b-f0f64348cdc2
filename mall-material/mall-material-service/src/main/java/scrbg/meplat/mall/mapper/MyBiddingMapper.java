package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.vo.bidding.MyBiddingVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Mapper
@Repository
public interface MyBiddingMapper {
    List<MyBiddingVo> getMyBidList(Page<MyBiddingVo> pages, @Param("bid") MyBiddingVo myBiddingVo);

    int getMyBidListCount(@Param("bid") MyBiddingVo myBiddingVo);

}
