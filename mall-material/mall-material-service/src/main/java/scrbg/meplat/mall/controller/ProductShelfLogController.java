package scrbg.meplat.mall.controller;


import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.ProductShelfLog;
import scrbg.meplat.mall.service.ProductShelfLogService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Log4j2
@RestController
@RequestMapping("/")
@ApiSort(value = 500)
@Api(tags = "店铺商品上下架记录（后台）")
public class ProductShelfLogController {

    private ProductShelfLogService productShelfLogService;


    @Autowired
    public void setProductShelfLogService(ProductShelfLogService productShelfLogService) {
        this.productShelfLogService = productShelfLogService;
    }


    @PostMapping("productShelfLog/listPage")
    @ApiOperation(value = "根据商品id等参数查询上下架记录")
    @DynamicParameters(name = "根据商品id分页查询", properties = {
            @DynamicParameter(name = "productId", value = "商品id", required = true,
                    dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "商品类型", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
    })
    public PageR<ProductShelfLog> materialInfo(@RequestBody JSONObject jsonObject) {
        PageUtils page = productShelfLogService.queryByProductId(jsonObject);
        return PageR.success(page);
    }

    @GetMapping("productShelfLog/{logId}")
    @ApiOperation(value = "根据日志id删除上下架记录")
    public R materialInfo(@PathVariable String logId) {
        productShelfLogService.deleteByLogId(logId);
        return R.success();
    }
    @PostMapping("/productShelfLog/OutputExcel")
    @ApiOperation(value = "导出上架记录信息")
    public void exportShopPayExcelFile(@RequestBody List<String> ids,HttpServletResponse response) {
        productShelfLogService.exportShelfLogExcelFile(ids,response);
    }

}
