package scrbg.meplat.mall.outerOpenController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.controller.website.userCenter.UserCenterUserInfoController;
import scrbg.meplat.mall.entity.Brand;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.vo.product.ImportOuterProductVO;
import scrbg.meplat.mall.vo.w.ImportBatchLcMaterialDTO;
import scrbg.meplat.mall.vo.w.ImportBatchMaterialDTO;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@RestController
@Log4j2
@RequestMapping("/")
@Api(tags = "物资商品导入接口")
public class OuterOpenMaterialController {

    @Autowired
    private ProductService productService;
    @Autowired
    private BrandService brandService;
    @Autowired
    public ProductCategoryService productCategoryService;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private InterfaceLogsService interfaceLogsService;

//    @GetMapping("outer/open/material/getProductCategory")
//    @ApiOperation(value = "物资分类")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "className", value = "分类名称",
//                    dataType = "String", paramType = "query"),
//    })
//    public R<List<ProductCategory>> getProductCategory(String className) {
//        Integer state = 1;
//        ArrayList<Integer> productTypes = new ArrayList<>();
//        productTypes.add(0);
//        productTypes.add(10);
//        List<ProductCategory> productCategory = productCategoryService.listByClassName(className, null, state, null, 0);
//        return R.success(productCategory);
//    }


    @PostMapping("outer/open/material/importBatchMaterial")
    @ApiOperation(value = "商品导入（二级供应商导入）")
    public scrbg.meplat.mall.util.R<List<ImportOuterProductVO>> importBatchMaterial(@Valid @RequestBody ImportBatchMaterialDTO dto, HttpServletRequest request) {
        InterfaceLogs iLog = new InterfaceLogs();
        String idStr = IdWorker.getIdStr();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(OuterOpenMaterialController.class.getName());
        iLog.setMethodName("importBatchMaterial");
        iLog.setLocalArguments("中建推送商品接口");
        iLog.setFarArguments(String.valueOf(dto));
        iLog.setLogType(3);


        if(dto.getProductList().size() > 2000) {
            throw new BusinessException(500111,"数据条数过大！");
        }
        List<ImportOuterProductVO> vos = productService.outerImportBatchMaterial(dto.getProductList(), request);
        log.info("api商品导入返回：" + vos);

        iLog.setResult(vos.toString());
        iLog.setIsSuccess(1);
        interfaceLogsService.create(iLog);
        if (CollectionUtils.isNotEmpty(vos)) {
            return scrbg.meplat.mall.util.R.success(vos);
        } else {
            return scrbg.meplat.mall.util.R.success();
        }
    }

    @PostMapping("outer/open/material/importBatchMaterialShop")
    @ApiOperation(value = "商品导入（店铺导入）")
    public scrbg.meplat.mall.util.R<List<ImportOuterProductVO>> importBatchMaterialShop(@Valid @RequestBody ImportBatchMaterialDTO dto, HttpServletRequest request) {
        if(dto.getProductList().size() > 2000) {
            throw new BusinessException(500111,"数据条数过大！");
        }
        List<ImportOuterProductVO> vos = productService.importBatchMaterialShop(dto.getProductList(), request);
        log.info("api商品导入返回（店铺导入）：" + vos);
        if (CollectionUtils.isNotEmpty(vos)) {
            return scrbg.meplat.mall.util.R.success(vos);
        } else {
            return scrbg.meplat.mall.util.R.success();
        }
    }

    // 临购 商品Api导入
    @PostMapping("outer/open/material/importBatchLc Material")
    @ApiOperation(value = "临购商品导入（二级供应商导入）")
    public scrbg.meplat.mall.util.R<List<ImportOuterProductVO>> importBatchLcMaterial(@Valid @RequestBody ImportBatchLcMaterialDTO dto, HttpServletRequest request) {
        if(dto.getProductList().size() > 2000) {
            throw new BusinessException(500111,"数据条数过大！");
        }
        List<ImportOuterProductVO> vos = productService.outerImportBatchLcMaterial(dto.getProductList(), request);
        log.info("api商品导入返回：" + vos);
        if (CollectionUtils.isNotEmpty(vos)) {
            return scrbg.meplat.mall.util.R.success(vos);
        } else {
            return scrbg.meplat.mall.util.R.success();
        }
    }

    @PostMapping("outer/open/material/importBatchLcMaterialShop")
    @ApiOperation(value = "临购商品导入（店铺导入）")
    public scrbg.meplat.mall.util.R<List<ImportOuterProductVO>> importBatchLcMaterialShop(@Valid @RequestBody ImportBatchLcMaterialDTO dto, HttpServletRequest request) {
        if(dto.getProductList().size() > 2000) {
            throw new BusinessException(500111,"数据条数过大！");
        }
        List<ImportOuterProductVO> vos = productService.importBatchLcMaterialShop(dto.getProductList(), request);
        log.info("api临购商品导入返回（店铺导入）：" + vos);
        if (CollectionUtils.isNotEmpty(vos)) {
            return scrbg.meplat.mall.util.R.success(vos);
        } else {
            return scrbg.meplat.mall.util.R.success();
        }
    }


//    @PostMapping("outer/open/material/getBrandPageList")
//    @ApiOperation(value = "获取品牌列表")
//    @DynamicParameters(name = "根据实体属性分页查询", properties = {
//            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
//            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
//            @DynamicParameter(name = "name", value = "品牌名", dataTypeClass = String.class),
//            @DynamicParameter(name = "classNamePath", value = "分类名称路径（xxx/xx/xxxx）", dataTypeClass = String.class),
//    })
//    public PageR<Brand> getBrandPageList(@RequestBody JSONObject jsonObject) {
//        jsonObject.put("mallType", 0);
//        jsonObject.put("classId", "a927249b2810-a00f-1d43-ef73-78cbd5be");
//        PageUtils page = brandService.queryPage(jsonObject, Wrappers.lambdaQuery(Brand.class));
//        return PageR.success(page);
//    }

//    @GetMapping("outer/open/material/getMaterialUnitList")
//    @ApiOperation(value = "获取计量单位")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "size", value = "获取数量", required = false,
//                    dataType = "Integer", paramType = "query")
//    })
//    public R<List<String>> getMaterialUnitList(Integer size) {
//        if(size == null){
//            size = 1000;
//        }
//        List<SystemParam> systemParam = systemParamService.listByCode("materialUnit", size);
//        List<String> collect = systemParam.stream().map(SystemParam::getKeyValue2).collect(Collectors.toList());
//        return R.success(collect);
//    }


}

