package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.Contract;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.Contract;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：合同 服务类
 * @作者: ye
 * @日期: 2025-03-04
 */
public interface ContractService extends IService<Contract> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Contract> queryWrapper);

        void create(Contract contract);
        void update(Contract contract);
        Contract getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

        Contract createContractNo(String enterpriseId);

        Contract getByPartyBOrgId(String partyBOrgId);

}
