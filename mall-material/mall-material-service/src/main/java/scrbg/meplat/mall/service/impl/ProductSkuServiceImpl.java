package scrbg.meplat.mall.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

import scrbg.meplat.mall.entity.ProductSku;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ProductSkuMapper;
import scrbg.meplat.mall.service.ProductSkuService;

/**
 * @描述：商品sku 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class ProductSkuServiceImpl extends ServiceImpl<ProductSkuMapper, ProductSku> implements ProductSkuService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductSku> queryWrapper) {
        IPage<ProductSku> page = this.page(
                new Query<ProductSku>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ProductSku productSku) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(productSku);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProductSku productSku) {
        super.updateById(productSku);
    }


    @Override
    public ProductSku getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 根据商品id获取规格信息
     *
     * @param productId
     * @param state
     * @return
     */
    @Override
    public List<ProductSku> getProductSkuByProductId(String productId, Integer state) {
        LambdaQueryChainWrapper<ProductSku> q = lambdaQuery();
        q.eq(ProductSku::getProductId, productId);
        return q.list();
    }

    /**
     * 根据商品id获取规格分页信息
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listSkuByProductId(JSONObject jsonObject, LambdaQueryWrapper<ProductSku> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String productId = (String) innerMap.get("productId");
        String keywords = (String) innerMap.get("keywords");
        if (StringUtils.isEmpty(productId)) {
            throw new BusinessException(PublicEnum.VALIDATE_FAILED.getCode(), "请输入商品id！");
        } else {
            q.eq(ProductSku::getProductId, productId);
        }
        q.like(org.apache.commons.lang.StringUtils.isNotBlank(keywords),ProductSku::getSkuName,keywords);
        q.eq(ProductSku::getIsDelete,PublicEnum.IS_DELETE_NO.getCode());
        q.orderByDesc(ProductSku::getGmtModified);
        IPage<ProductSku> page = this.page(
                new Query<ProductSku>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    /**
     * 根据id查询商品sku信息
     * @param skuId
     * @param state
     * @return
     */
    @Override
    public ProductSku getProductSkuById(String skuId, Integer state) {
        LambdaQueryChainWrapper<ProductSku> q = lambdaQuery();
        q.eq(ProductSku::getSkuId, skuId);
        return q.one();
    }


    @Override
    public ProductSku getLocalDataList(String skuId) {
        ProductSku sku=  baseMapper.getLocalDataList(skuId);
        return sku;
    }

    @Override
    public ProductSku getByProductId(String productId) {
        return this.lambdaQuery().eq(ProductSku::getProductId, productId).one();
    }
}
