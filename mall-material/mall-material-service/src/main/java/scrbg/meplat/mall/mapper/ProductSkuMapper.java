package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.ProductSku;

import java.util.List;

/**
 * @描述：商品sku Mapper 接口
 * @作者: y
 * @日期: 2022-11-02
 */
@Mapper
@Repository
public interface ProductSkuMapper extends BaseMapper<ProductSku> {

    @Delete("DELETE FROM product_sku WHERE product_id=#{productId}")
    int deleteBypProductId(String productId);


    /**
     * 根据商品id删除
     *
     * @param ids
     */
    @Delete("<script>" +
            "delete from product_sku where product_id in " +
            "<foreach collection='ids' open='(' item='id_' separator=',' close=')'> #{id_}" +
            "</foreach>" +
            "</script>")
    void removeRealByProductIds(List<String> ids);



    @Select("SELECT sku_id " +
            "FROM product_sku " +
            "where is_delete = -1")
    List<String> getDeleteIds();

    @Select("SELECT * " +
            "FROM product_sku " +
            "where sku_id = #{skuId}")
    ProductSku getLocalDataList(String skuId);
}
