package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.MaterialMasterPlanDtl;
import scrbg.meplat.mall.mapper.MaterialMasterPlanDtlMapper;
import scrbg.meplat.mall.service.MaterialMasterPlanDtlService;

/**
 * @描述：物资总计划明细 服务类
 * @作者: y
 * @日期: 2022-11-10
 */
@Service
public class MaterialMasterPlanDtlServiceImpl extends ServiceImpl<MaterialMasterPlanDtlMapper, MaterialMasterPlanDtl> implements MaterialMasterPlanDtlService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMasterPlanDtl> queryWrapper) {
        IPage<MaterialMasterPlanDtl> page = this.page(
                new Query<MaterialMasterPlanDtl>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(MaterialMasterPlanDtl materialMasterPlanDtl) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(materialMasterPlanDtl);
    }

    @Override
    public void update(MaterialMasterPlanDtl materialMasterPlanDtl) {
        super.updateById(materialMasterPlanDtl);
    }


    @Override
    public MaterialMasterPlanDtl getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
