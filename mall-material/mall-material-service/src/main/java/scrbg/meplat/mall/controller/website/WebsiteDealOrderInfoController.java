package scrbg.meplat.mall.controller.website;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.service.DealOrderInfoService;
import scrbg.meplat.mall.vo.w.DealOrderInfoDTO;

import javax.validation.Valid;


@RestController
@RequestMapping("/w/dealOrderInfo")
@Api(tags = "结算订单明细（对接）")
public class WebsiteDealOrderInfoController {

    @Autowired
    public DealOrderInfoService dealOrderInfoService;

    /**
     * 对外接口
     * @param dto
     * @return
     */
    @PostMapping("/batchCreate")
    @ApiOperation(value = "批量新增结算")
    public R saveBatch(@RequestBody @Valid DealOrderInfoDTO dto) {
        String keyId = IdWorker.getIdStr();
        dealOrderInfoService.batchOutAdd(dto, keyId);
        return R.success();
    }


}

