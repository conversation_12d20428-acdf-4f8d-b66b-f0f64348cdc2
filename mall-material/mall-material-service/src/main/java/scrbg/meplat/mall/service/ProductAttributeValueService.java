package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ProductAttributeValue;

/**
 * @描述：商品属性值 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface ProductAttributeValueService extends IService<ProductAttributeValue> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductAttributeValue> queryWrapper);

    void create(ProductAttributeValue productAttributeValue);

    void update(ProductAttributeValue productAttributeValue);

    ProductAttributeValue getById(String id);

    void delete(String id);
}
