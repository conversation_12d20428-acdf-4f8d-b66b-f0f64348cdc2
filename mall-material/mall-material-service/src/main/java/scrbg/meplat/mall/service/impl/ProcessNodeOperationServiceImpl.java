package scrbg.meplat.mall.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import scrbg.meplat.mall.entity.ProcessNodeOperation;
import scrbg.meplat.mall.mapper.ProcessNodeOperationMapper;
import scrbg.meplat.mall.service.ProcessNodeOperationService;

/**
 * 流程节点操作记录表 服务类
 * <AUTHOR>
 * @date: 2025年6月20日 上午10:06:41
 */
@Service
public class ProcessNodeOperationServiceImpl extends ServiceImpl<ProcessNodeOperationMapper, ProcessNodeOperation> implements ProcessNodeOperationService {

    @Autowired
    ProcessNodeOperationService processNodeOperationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProcessNodeOperation processNodeOperation) {
        super.save(processNodeOperation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProcessNodeOperation processNodeOperation) {
        super.updateById(processNodeOperation);
    }

    @Override
    public ProcessNodeOperation getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        super.removeById(id);
    }

}
