package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.dto.product.ListPlatformProductPageByClassIdDTO;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.SelfOperatedStoreRecord;
import scrbg.meplat.mall.vo.product.MaterialSupplierListVO;
import scrbg.meplat.mall.vo.product.ProductFullInfoVO;
import scrbg.meplat.mall.vo.product.ProductStatisticsVO;
import scrbg.meplat.mall.vo.product.device.PlatformDeviceListVO;
import scrbg.meplat.mall.vo.product.material.PlatformMaterialListVO;
import scrbg.meplat.mall.vo.product.website.device.WDeviceVO;
import scrbg.meplat.mall.vo.product.website.material.WMaterialVO;
import scrbg.meplat.mall.vo.product.website.restsServe.WOtherServicesVO;
import scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo;
import scrbg.meplat.mall.vo.shopManage.reportForms.ProductFromVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @描述：店铺商品信息 Mapper 接口
 * @作者: y
 * @日期: 2022-11-02
 */
@Mapper
@Repository
public interface ProductMapper extends BaseMapper<Product> {


    /**
     * 根据分类id等参数查询商品列表
     *
     * @return
     */
    List<PlatformDeviceListVO> listPlatformProductPageByClassId(@Param("dto") ListPlatformProductPageByClassIdDTO dto);

    /**
     * 根据分类id等参数查询商品列表总记录数
     *
     * @return
     */
    int countPlatformProductPageByClassId(@Param("dto") ListPlatformProductPageByClassIdDTO dto);


    // 最新

    /**
     * 查询平台设备列表
     *
     * @param pages
     * @param map
     * @return
     */
    List<PlatformDeviceListVO> listPlatformDevicePage(Page<PlatformDeviceListVO> pages, @Param("dto") Map<String, Object> map);


    /**
     * 查询平台设备列表总记录数
     *
     * @param innerMap
     * @return
     */
    int listPlatformDevicePageCount(@Param("dto") Map<String, Object> innerMap);


    /**
     * 查询平台物资列表
     *
     * @param pages
     * @param map
     * @return
     */
    List<PlatformMaterialListVO> listPlatformMaterialPage(Page<PlatformMaterialListVO> pages, @Param("dto") Map<String, Object> map);

    /**
     * 查询平台物资列表
     *
     * @param pages
     * @param map
     * @return
     */
    List<PlatformMaterialListVO> listPlatformMaterialPagePVP(Page<PlatformMaterialListVO> pages, @Param("dto") Map<String, Object> map);

    /**
     * 查询平台设备列表总记录数
     * @param innerMap
     * @return
     */
    int listPlatformMaterialPageCount(@Param("dto") Map<String, Object> innerMap);

    /**
     * 查询平台设备列表总记录数
     * @param innerMap
     * @return
     */
    int listPlatformMaterialPageCountPVP(@Param("dto") Map<String, Object> innerMap);


    /**
     * 查询商品完整信息列表，共外部导入选择商品
     *
     * @param pages
     * @param innerMap
     * @return
     */
    List<ProductFullInfoVO> listProductFullInfoVOPage(Page<ProductFullInfoVO> pages, @Param("dto") Map<String, Object> innerMap);

    /**
     * 查询商品完整信息列表，共外部导入选择商品
     * @param innerMap
     * @return
     */
    int listProductFullInfoVOPageCount(@Param("dto") Map<String, Object> innerMap);

    /**
     * 获取物资
     *
     * @param pages
     * @param innerMap
     * @return
     */
    List<WMaterialVO> listHomeMaterialPage(Page<WMaterialVO> pages, @Param("dto") Map<String, Object> innerMap);

    /**
     * 获取物资
     * @param innerMap
     * @return
     */
    int listHomeMaterialPageCount(@Param("dto") Map<String, Object> innerMap);

    /**
     * 获取物资
     *
     * @param pages
     * @param innerMap
     * @return
     */
    List<SelfOperatedStoreRecord> listSelfMaterialPage(Page<SelfOperatedStoreRecord> pages, @Param("dto") Map<String, Object> innerMap);

    /**
     * 获取物资
     * @param innerMap
     * @return
     */
    int listSelfMaterialPageCount(@Param("dto") Map<String, Object> innerMap);

    /**
     * 获取设备
     *
     * @param pages
     * @param innerMap
     * @return
     */
    List<WDeviceVO> listHomeDevicePage(Page<WDeviceVO> pages, @Param("dto") Map<String, Object> innerMap);

    /**
     * 获取设备
     * @param innerMap
     * @return
     */
    int listHomeDevicePageCount(@Param("dto") Map<String, Object> innerMap);

    /**
     * 其他服务列表
     *
     * @param pages
     * @param innerMap
     * @return
     */
    List<WOtherServicesVO> otherServicesPage(Page<WOtherServicesVO> pages, @Param("dto") Map<String, Object> innerMap);

    /**
     * 其他服务列表
     * @param innerMap
     * @return
     */
    int otherServicesPageCount(@Param("dto") Map<String, Object> innerMap);

    /**
     * 删除商品
     * @param id
     * @return
     */
    @Delete("DELETE FROM product WHERE product_id=#{id}")
    int deleteByProductId(String id);

    /**
     * 查询商品id
     * @param size
     * @param isDelete
     * @return
     */
    @Select("select product_id from product WHERE is_delete = #{isDelete} limit #{size}")
    List<String> getProductIdBySizeIsDelete(int size,int isDelete);

    /**
     * 物资店铺查询供方商品
     *
     * @param pages
     * @param dto
     * @return
     */
    List<MaterialSupplierListVO> listShopManageMaterialSupplierPage(Page<MaterialSupplierListVO> pages, @Param("dto")  Map<String, Object> dto);

    /**
     * 物资店铺查询供方商品
     * @param dto
     * @return
     */
    Integer listShopManageMaterialSupplierPageCount(@Param("dto")  Map<String, Object> dto);

    /**
     * 查询供方提供的商品
     *
     * @param pages
     * @param dto
     * @return
     */
    List<MaterialSupplierListVO> listSupplierMaterialSupplierPage(Page<MaterialSupplierListVO> pages, @Param("dto")  Map<String, Object> dto);

    /**
     * 查询供方提供的商品
     * @param innerMap
     * @return
     */
    Integer listSupplierMaterialSupplierPageCount(@Param("dto")  Map<String, Object> innerMap);

    List<ProductFromVo> productFromList( IPage<ProductFromVo> pages, @Param("ew") QueryWrapper<ProductFromVo> wrapper);


    /**
     * 确认全部商品查询id
     * @param innerMap
     * @return
     */
    List<String> batchAffirmSupplierProductQueryIds(@Param("dto") Map<String, Object> innerMap);

    List<ProductFromVo> productFromListLoad( @Param("ew") QueryWrapper<ProductFromVo> wrapper);

    /**
     * 管理员频台  商品报表
     * @param pages
     * @param wrapper
     * @return
     */
    List<PlatformProductFromVo> platformProductFromList(IPage<PlatformProductFromVo> pages, @Param("ew") QueryWrapper<PlatformProductFromVo> wrapper);

    List<ProductFromVo> productFromListLoad2(@Param("ew")QueryWrapper<ProductFromVo> q);

    List<PlatformProductFromVo> platformProductFromListLoad(@Param("ew")QueryWrapper<PlatformProductFromVo> wrapper);


    String getDataByClassIdParentPath(@Param("classId") String classId);

    /**
     * 全部通过商品获取店铺id
     * @param dto
     * @return
     */
    List<String> listPlatformMaterialProductIds(@Param("dto") Map<String, Object> dto);



    /**
     * 主键集合批量真实删除
     *
     * @param ids
     */
    @Delete("<script>" +
            "delete from product where product_id in " +
            "<foreach collection='ids' open='(' item='id_' separator=',' close=')'> #{id_}" +
            "</foreach>" +
            "</script>")
    void removeRealByIds(List<String> ids);



    @Select("SELECT product_id " +
            "FROM product " +
            "where is_delete = -1")
    List<String> getDeleteProductIds();

    List<Product> findAllProduct(@Param("ew") QueryWrapper<Product> q);
    @Select("SELECT * " +
            "FROM product " +
            "where product_id = #{productId}")
    Product getByIdLocal(String productId);

    List<Product> calculateProductAveragePrice();

    Integer selectTypeCount(@Param("ew")LambdaQueryWrapper<Product> q, @Param("type")Integer type);
/*
* ArrayList<MaterialMonthSupplyPlan> secondLevelPlanMonthList(@Param("page") IPage<MaterialMonthSupplyPlan> page, @Param("ew")QueryWrapper<MaterialMonthSupplyPlan> q);
* */
    /**
     * 统计店铺商品类型及供应商维度的商品状态分布（支持分页和条件查询）
     *
     * @param page 分页对象
     * @param params 查询参数（如 shopId, productType, supplierName 等）
     * @return 分页结果
     */
    ArrayList<ProductStatisticsVO> statisticsByShopAndProductTypeWithSupplier(@Param("page") IPage<ProductStatisticsVO> page, @Param("params")Map<String, Object> params);

    Integer selectStatisticsTypeVoCount(@Param("params")Map<String, Object> params, @Param("type")Integer type);
}

