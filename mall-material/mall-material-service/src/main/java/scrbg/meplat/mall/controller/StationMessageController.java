package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.StationMessage;
import scrbg.meplat.mall.entity.StationMessageReceive;
import scrbg.meplat.mall.service.StationMessageReceiveService;
import scrbg.meplat.mall.service.StationMessageService;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;
import scrbg.meplat.mall.vo.product.material.StationMessageReceiveVo;

import java.util.List;

/**
 * @描述：站点消息控制类
 * @作者: y
 * @日期: 2022-11-24
 */
@RestController
@RequestMapping("")
@ApiSort(value = 500)
@Api(tags = "站点消息")
public class StationMessageController {

    @Autowired
    public StationMessageService stationMessageService;
    @Autowired
    public StationMessageReceiveService stationMessageReceiveService;

    @PostMapping("/platform/stationMessage/sendMsgList")
    @ApiOperation(value = "根据消息id查询发件箱）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（发件人名称，标题）", dataTypeClass = String.class),
            @DynamicParameter(name = "stationMessageId", value = "消息ID", dataTypeClass = String.class),
            @DynamicParameter(name = "title", value = "标题", dataTypeClass = String.class),
            @DynamicParameter(name = "sendName", value = "发件人名称", dataTypeClass = String.class),
            @DynamicParameter(name = "content", value = "内容", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "消息状态（0未读 1已读）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "sendDate", value = "发送时间", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间1:按排序值排序2:按创建时间排序)", dataTypeClass = Integer.class),
    })
    public PageR<StationMessage>queryPage(@RequestBody JSONObject jsonObject) {
        PageUtils page = stationMessageService.queryPageListSystem(jsonObject, new QueryWrapper<StationMessage>());
        return PageR.success(page);
    }

    @PostMapping("/shopManage/stationMessage/sendMsgList")
    @ApiOperation(value = "根据消息id查询收件箱）")
    @DynamicParameters(name = "根据实体属性分页查询")
    public PageR<StationMessage> shopManage(@RequestBody JSONObject jsonObject) {
        PageUtils page = stationMessageService.queryEnterprisePageList(jsonObject, new QueryWrapper<StationMessage>());
        return PageR.success(page);
    }
    @PostMapping("/platform/stationMessage/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<StationMessage> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = stationMessageService.queryPage(jsonObject, new LambdaQueryWrapper<StationMessage>());
        return PageR.success(page);
    }

    @GetMapping("/stationMessage/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<StationMessage> findById(String id) {
        StationMessage stationMessage = stationMessageService.getById(id);
        return R.success(stationMessage);
    }

    @PostMapping("/stationMessage/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody StationMessage stationMessage) {
        stationMessageService.create(stationMessage);
        return R.success();
    }

    @PostMapping("/stationMessage/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody StationMessage stationMessage) {
        stationMessageService.update(stationMessage);
        return R.success();
    }

    @GetMapping("/stationMessage/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        stationMessageService.delete(id);
        return R.success();
    }


    @PostMapping("/stationMessage/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        stationMessageService.removeByIds(ids);
        return R.success();
    }

//    @PostMapping("/stationMessage/createBatch")
//    @ApiOperation(value = "根据主键批量删除")
//
//    public R deleteBatch1(Object object) {
//
//        System.out.println("wwwwww");
//
//        return R.success();
//    }
    @PostMapping("/stationMessage/createBatch")
    @ApiOperation(value = "根据主键批量添加")
    public R createBatch(@RequestBody StationMessageReceiveVO vo) {
        if (vo.getReceiveList()!=null||vo.getEnterpriseIdList()!=null){
            stationMessageService.createBatch(vo);
            return R.success();
        }else {
            return R.failed();
        }

    }
    @GetMapping("/stationMessage/getMessageNum")
    @ApiOperation(value = "查询新消息数量")
    public R getMessageNum(Integer receiveType) {
        List<StationMessageReceive>  list= stationMessageReceiveService.getMessageNum( receiveType);
        return R.success(list);
    }


}

