package scrbg.meplat.mall.service;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;

import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.entity.excelTemplate.Supplier;
import scrbg.meplat.mall.vo.platform.RegisterPcwpFileVo;
import scrbg.meplat.mall.vo.platform.enterprise.EnterpriseArrearageVo;
import scrbg.meplat.mall.vo.user.ImportSupplerExcelResultVO;

/**
 * <AUTHOR>
 * @description 针对表【enterprise_info】的数据库操作Service
 * @createDate 2022-11-03 11:46:18
 */
public interface EnterpriseInfoService extends IService<EnterpriseInfo> {

    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<EnterpriseInfo> queryWrapper);

    void create(EnterpriseInfo enterpriseInfo);


    EnterpriseInfo getById(String id);

    void delete(String id);

    /**
     * 企业用户注册、个体户注册（外部）
     * @param enterpriseInfo
     * @return
     */
    R enterpriseRegistration(EnterpriseInfo enterpriseInfo);

    void updateIsSupplier(List<String> ids, String s);

    PageUtils queryPageAll(JSONObject jsonObject, LambdaQueryWrapper<EnterpriseInfo> objectLambdaQueryWrapper);

    EnterpriseInfo findOuterByCreditCode(String socialCreditCode);

    /**
     * 根据登陆用户获取当前企业
     * @return
     */
    EnterpriseInfo getInfo(String enterpriseId, Integer mallType, String shopId);

    /**
     * 修改企业
     * @param enterpriseInfo
     */
    void updateEnterprise(EnterpriseInfo enterpriseInfo);

    /**
     * 成为企业
     * @param enterpriseInfo
     */
    void becomeEnterprise(EnterpriseInfo enterpriseInfo);

    /**
     * 修改如果有店铺修改店铺
     * @param enterpriseInfos
     */
    void updateBatchAndShopById(List<EnterpriseInfo> enterpriseInfos);

    /**
     * 根据企业id查询供应商数量
     * @param enterpriseId
     * @param isSupplier
     * @return
     */
    Integer getOrgIsSupperCountById(String enterpriseId, Integer isSupplier);

    /**
     * 根据企业id获取企业信息好附件信息
     * @param enterpriseId
     * @return
     */
    EnterpriseInfo getEnterInfoAndFileList(String enterpriseId);

    /**
     * 根据企业id获取附件列表
     * @param enterpriseId
     * @return
     */
    List<File> getEnterFileList(String enterpriseId);

    /**
     * 导入供应商及诶扣
     * @param file
     * @return
     */
    List<ImportSupplerExcelResultVO> uploadSupplierExcelFile(MultipartFile file);



    /**
     * excel导入供应商
     * @param supplier
     */
    void saveImportSupplier(Supplier supplier);
   //判断供应商企业是否通过审核
    EnterpriseInfo getAuditState(String enterpriseId);

    Boolean findSupperByCreditCodeAndType(String creditCode, Integer supplierType);

    RegisterPcwpFileVo selectIsPcwpUserByCode(String socialCreditCode,String programaKey);

    /**
     * 根据内部企业id查询企业信息
     * @param orderId
     * @return
     */
    EnterpriseInfo findByInteriorId(String orderId);

    void updateIsSupplierById(String id);

    void updateIspcwpState();

    /**
     *   二级供应商专用
     * 根据企业名称模糊匹配二级企业信息，排除已关联的企业和自身
     * @param enterpriseName
     * @return
     */

   List<EnterpriseInfo>  selectListByEnterPriseName(String enterpriseName);

    /**
     *   供应商专用
     * 查询供应商自营店
     * @param
     * @return
     */

    PageUtils getEnterpriceList(JSONObject jsonObject, LambdaQueryWrapper<EnterpriseInfo> shopSupplierReleLambdaQueryWrapper);

    /**
     * 根据PCWP企业id列表查询本地机构id
     * @param orgIds
     * @return
     */
    List<String> getLocalEnterpriseByOrgIds(List<String> orgIds);


    /**
     * 批量修改欠费数据和可欠费时间
      * @param enterpriseInfos
     */
    void updateArrearageBatch(List<EnterpriseInfo> enterpriseInfos);
    /**
     * 检测更新蜀道集团的数据表
     */
    void enterpriseShuDaoStatus();

    void updateBatchArrearage(EnterpriseArrearageVo vo);

    JSONObject getShopProgress();

    //获取当前机构的税率
    BigDecimal getEnterpriseInfoTaxRate();

    /**
     * 根据企业id重置其密码
     * 只有非pcwp企业才能重置密码
     * 具体来说 enterprise_info 表中interior_id为空的企业才能重置其关联的user的密码
     * @param id
     */
    void resetPwd(String id);

    /**
     * 供应商台账信息查询
     * @param jsonObject
     * @return
     */
    PageUtils listByEntityLedger(JSONObject jsonObject);

    PageUtils outPurchaserLedger(JSONObject jsonObject);

    void reminderForEnterpriseLicenseTermDueDate();


}
