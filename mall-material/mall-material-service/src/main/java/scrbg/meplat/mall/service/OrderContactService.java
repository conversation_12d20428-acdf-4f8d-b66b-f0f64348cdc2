package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.OrderContact;
/**
 * @描述：订单合同关联信息 服务类
 * @作者: ye
 * @日期: 2023-03-15
 */
public interface OrderContactService extends IService<OrderContact> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderContact> queryWrapper);

        void create(OrderContact orderContact);

        void update(OrderContact orderContact);

        OrderContact getById(String id);

        void delete(String id);
}