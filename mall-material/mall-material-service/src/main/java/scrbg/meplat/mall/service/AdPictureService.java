package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.entity.AdPicture;

import java.util.List;

/**
 * @描述： 服务类
 * @作者: y
 * @日期: 2022-11-08
 */
public interface AdPictureService extends IService<AdPicture> {




    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<AdPicture> queryWrapper);


    /**
     * 新增广告图片
     *
     * @param adPicture
     */
    void create(AdPicture adPicture);

    /**
     * 更新广告图片
     *
     * @param adPicture
     */
    void update(AdPicture adPicture);

    /**
     * 通过图片id查询
     *
     * @param id
     * @return
     */
    AdPicture getById(String id);

    /**
     * 通过id删除图片
     *
     * @param id
     */
    void delete(String id);

    /**
     * 通过图片类型查询
     *
     * @param type
     * @return
     */
    List<AdPicture> getByType(Integer type);

    /**
     * 通过图片类型获取该类型所有图片
     *
     * @param adPicture
     * @return
     */
    List<AdPicture> findByCondition(AdPicture adPicture);

    PageUtils queryAdPicturePage(JSONObject jsonObject, LambdaQueryWrapper<AdPicture> adPictureLambdaQueryWrapper);

    void updateByPublish(List<String> ids, String type);

     R queryHomeInfo(JSONObject jsonObject, Integer mallType);


    /**
     * 查询广告图
     * @param jsonObject
     * @return
     */
    List<AdPicture> getListAdPicture(JSONObject jsonObject);

    List<AdPicture> queryPublishAdPicturePage(JSONObject jsonObject, LambdaQueryWrapper<AdPicture> objectLambdaQueryWrapper);


}
