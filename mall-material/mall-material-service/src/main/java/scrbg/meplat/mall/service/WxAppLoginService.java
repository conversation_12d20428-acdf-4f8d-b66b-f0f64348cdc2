package scrbg.meplat.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import scrbg.meplat.mall.entity.ReceiptPerson;
import scrbg.meplat.mall.vo.user.WxLoginVo;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/14
 */
public interface WxAppLoginService extends IService<ReceiptPerson> {
    /***
     * 供应商登录方法
     *
     */

    /**
     * 微信收料登录发送验证码
     */
    public void sendWxLoginCode(WxLoginVo wxLoginVo, String code, HttpServletRequest request);

    /**
     * 微信收料验证码登录
     *
     * @param phone
     * @param code
     * @param openId
     * @return
     */
    public Map wxLoginSlByPhoneCode(String phone, String code, String openId);

    /**
     * 收料 通过openID登录
     *
     * @param openId
     * @return
     */
    public Map loginSlByOpenId(String openId);

    /**
     * 微信-供应商登录-验证码
     */
    public Map wxLoginSupplierByPhoneCode(String phone, String code, String openId);


    /**
     * 退出登录，通过手机号删除redis用户信息
     *
     * @param wxLoginVo
     * @param type
     */
    public void logout(WxLoginVo wxLoginVo, String type);

    /***
     * 供货商使用openid登录
     * @param openid
     * @return 用户token 以及用户数据
     */
    Map loginSupplierByOpenId(String openid);

    /***
     * 收料 根据code请求微信获取openid
     * @param code
     * @return
     */
    String getSlWxOpenIdByCode(String code);
    /**
     * @param code
     * 供应商登录 根据微信code获取openid
     */
    String getSupplierOpenIdByCode(String code);

    /**
     * 通过手机号直接登录 正式环境不可用
     * @param phone
     * @return
     */
    Map loginByPhone(String phone);

    /**
     * 通过手机号直接登录 正式环境不可用
     * @param phone
     * @return
     */
    Map loginSupplierByPhone(String phone);


}
