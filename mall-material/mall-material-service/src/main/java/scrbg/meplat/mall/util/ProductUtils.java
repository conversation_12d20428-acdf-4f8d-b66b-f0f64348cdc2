package scrbg.meplat.mall.util;

import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.exception.BusinessException;

import java.math.BigDecimal;
import java.util.List;

public class ProductUtils {


    public static BigDecimal averageSellPrice(List<Product> productList) {
        if (productList!=null&&productList.size()>0){
            BigDecimal sumSellPrice = productList.stream()
                    .map(Product::getSellPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal averageSellPrice = productList.isEmpty() ? BigDecimal.ZERO :
                    sumSellPrice.divide(BigDecimal.valueOf(productList.size()), 2, BigDecimal.ROUND_HALF_UP);
            return averageSellPrice;
        }else {
            throw new BusinessException(500,"没有商品");
        }
    }
}
