package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.AskAnswer;
import scrbg.meplat.mall.vo.user.userCenter.AskAnswerVo;

/**
 * @描述：问答 服务类
 * @作者: y
 * @日期: 2022-11-22
 */
public interface AskAnswerService extends IService<AskAnswer> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<AskAnswer> queryWrapper);

        void create(AskAnswer askAnswer);

        void update(AskAnswer askAnswer);

        AskAnswer getById(String id);

        void delete(String id);

    PageUtils listByDeviceDemand(JSONObject jsonObject, QueryWrapper<AskAnswerVo> askAnswerVoQueryWrapper);
}
