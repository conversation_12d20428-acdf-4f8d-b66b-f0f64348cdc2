package scrbg.meplat.mall.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import scrbg.meplat.mall.entity.BiddingBidRecordItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

import scrbg.meplat.mall.vo.bidding.BidSummarySubVo;
import scrbg.meplat.mall.vo.bidding.GetBidingRecordItemVO;

import java.util.List;

/**
 * @描述：竞价记录 Mapper 接口
 * @作者: ye
 * @日期: 2023-07-19
 */
@Mapper
@Repository
public interface BiddingBidRecordItemMapper extends BaseMapper<BiddingBidRecordItem> {

    /**
     * 查询订单明细
     *
     * @param pages
     * @param jsonObject
     * @return
     */
    @Select("SELECT " +
            "bp.product_name AS productName, " +
            "bp.brand AS brand, " +
            "bp.spec AS spec, " +
            "bp.unit AS unit, " +
            "bp.num AS num, " +
            "bp.product_texture AS productTexture, " +
            "br.bid_record_item_id AS bidRecordItemId, " +
            "br.bid_record_id AS bidRecordId, " +
            "br.bidding_product_id AS biddingProductId, " +
            "br.bidding_id AS biddingId, " +
            "br.bid_price AS bidPrice, " +
            "br.tax_rate AS taxRate, " +
            "br.bid_rate_price AS bidRatePrice, " +
            "br.bid_rate_amount AS bidRateAmount, " +
            "br.bid_amount AS bidAmount, " +
            "br.remarks AS remarks " +
            "FROM bidding_bid_record_item br " +
            "inner join bidding_product bp ON br.bidding_product_id = bp.bidding_product_id " +
            "WHERE br.bid_record_id = #{dto.bidRecordId} and br.is_delete = 0 and bp.is_delete = 0 ")
    List<GetBidingRecordItemVO> listSupplierBiddingRecordItems(Page<GetBidingRecordItemVO> pages, @Param("dto") JSONObject jsonObject);


    /**
     * 查询订单明细
     * @param jsonObject
     * @return
     */
    @Select("SELECT count(*) " +
            "FROM bidding_bid_record_item br " +
            "inner join bidding_product bp ON br.bidding_product_id = bp.bidding_product_id " +
            "WHERE br.bid_record_id = #{dto.bidRecordId} and br.is_delete = 0 and bp.is_delete = 0 ")
    int listSupplierBiddingRecordItemsCount(@Param("dto") JSONObject jsonObject);

    /**
     * 平台查询
     *
     * @param pages
     * @param jsonObject
     * @return
     */
    @Select("SELECT " +
            "bp.product_name AS productName, " +
            "bp.brand AS brand, " +
            "bp.spec AS spec, " +
            "bp.unit AS unit, " +
            "bp.num AS num, " +
            "bp.product_texture AS productTexture, " +
            "br.bid_record_item_id AS bidRecordItemId, " +
            "br.bid_record_id AS bidRecordId, " +
            "br.bidding_product_id AS biddingProductId, " +
            "br.bidding_id AS biddingId, " +
            "br.bid_price AS bidPrice, " +
            "br.tax_rate AS taxRate, " +
            "br.bid_rate_price AS bidRatePrice, " +
            "br.bid_rate_amount AS bidRateAmount, " +
            "br.bid_amount AS bidAmount, " +
            "br.remarks AS remarks " +
            "FROM bidding_bid_record_item br " +
            "inner join bidding_product bp ON br.bidding_product_id = bp.bidding_product_id " +
            "WHERE br.bid_record_id = #{dto.bidRecordId} and br.is_delete = 0 and bp.is_delete = 0 ")
    List<GetBidingRecordItemVO> listPlatformBiddingRecordItems(Page<GetBidingRecordItemVO> pages, @Param("dto") JSONObject jsonObject);

    @Select("SELECT count(*) " +
            "FROM bidding_bid_record_item br " +
            "inner join bidding_product bp ON br.bidding_product_id = bp.bidding_product_id " +
            "WHERE br.bid_record_id = #{dto.bidRecordId} and br.is_delete = 0 and bp.is_delete = 0 ")
    int listPlatformBiddingRecordItemsCount(@Param("dto") JSONObject jsonObject);

    List<GetBidingRecordItemVO> getQuotationsBySynTempSn(@Param("synTemSn") String synTemSn);
}