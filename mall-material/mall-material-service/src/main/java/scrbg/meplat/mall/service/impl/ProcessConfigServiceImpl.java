package scrbg.meplat.mall.service.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

import scrbg.meplat.mall.dto.process.ProcessConfigDtlDTO;
import scrbg.meplat.mall.entity.ProcessConfig;
import scrbg.meplat.mall.entity.ProcessInstance;
import scrbg.meplat.mall.entity.ProcessNode;
import scrbg.meplat.mall.entity.ProcessNodeOperation;
import scrbg.meplat.mall.entity.ProcessRole;
import scrbg.meplat.mall.entity.ProcessUser;
import scrbg.meplat.mall.entity.ProcessWorkflowResult;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ProcessConfigMapper;
import scrbg.meplat.mall.mapper.ProcessInstanceMapper;
import scrbg.meplat.mall.mapper.ProcessNodeMapper;
import scrbg.meplat.mall.mapper.ProcessNodeOperationMapper;
import scrbg.meplat.mall.service.ProcessConfigService;
import scrbg.meplat.mall.service.ProcessNodeService;
import scrbg.meplat.mall.service.ProcessRoleService;
import scrbg.meplat.mall.service.ProcessUserService;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.processConfig.ProcessConfigDtlItemVO;
import scrbg.meplat.mall.vo.processConfig.ProcessConfigDtlVO;

/**
 * 流程配置表 服务类
 * <AUTHOR>
 * @date: 2025年6月20日 上午10:06:41
 */
@Service
public class ProcessConfigServiceImpl extends ServiceImpl<ProcessConfigMapper, ProcessConfig> implements ProcessConfigService {

    @Autowired
    ProcessConfigService processConfigService;
    @Autowired
    ProcessConfigMapper processConfigMapper;
    @Autowired
    ProcessInstanceMapper processInstanceMapper;
    @Autowired
    ProcessNodeService processNodeService;
    @Autowired
    ProcessNodeOperationMapper processNodeOperationMapper;
    @Autowired
    ProcessRoleService processRoleService;
    @Autowired
    ProcessUserService processUserService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProcessConfig> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        if (org.apache.commons.lang.StringUtils.isNotEmpty(keywords)) {
            queryWrapper.and(wrapper -> wrapper.like(ProcessConfig::getProcessName, keywords));
        }

        //排序方式(排序值升序)
        queryWrapper.orderByAsc(ProcessConfig::getSort);
        IPage<ProcessConfig> page = this.page(
                new Query<ProcessConfig>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProcessConfig processConfig) {
        super.save(processConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProcessConfigDtlDTO processConfigDtlDTO) {
        ProcessConfig processConfig = new ProcessConfig();
        super.updateById(processConfig);
        List<ProcessConfigDtlItemVO> processConfigDtlItemVOs = processConfigDtlDTO.getProcessConfigDtlItemVOs();
        for (ProcessConfigDtlItemVO processConfigDtlItemVO : processConfigDtlItemVOs) {
            processUserService.lambdaUpdate().eq(ProcessUser::getProcessUserId, processConfigDtlItemVO.getProcessUserId())
            .set(ProcessUser::getUserId, processConfigDtlItemVO.getUserId())
                    .set(ProcessUser::getUserName, processConfigDtlItemVO.getUserName()).update();
            processRoleService.lambdaUpdate().eq(ProcessRole::getProcessRoleId, processConfigDtlItemVO.getProcessRoleId())
            .set(ProcessRole::getRoleNo, processConfigDtlItemVO.getRoleNo())
            .set(ProcessRole::getRoleName, processConfigDtlItemVO.getRoleName()).update();
        }
    }

    @Override
    public ProcessConfigDtlVO getProcessConfigDtlById(String processId) {
        ProcessConfigDtlVO processConfigDtlVO =  processConfigMapper.getProcessConfigDtlById(processId);
        return processConfigDtlVO;
    }
    
    @Override
    public ProcessConfig getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        super.removeById(id);
    }
    
    @Override
    public ProcessWorkflowResult myFunc(String processId, UserLogin currentUser, Integer operation, String businessKey, String remark) {
        try {
            // 检查业务主表ID是否已关联流程实例
            ProcessInstance instance = processInstanceMapper.getProcessInstanceByKey(businessKey);
            if (instance == null) {
                // 业务主表ID未关联流程实例，创建新流程
                return startNewProcess(processId, businessKey, currentUser, remark);
            } else {
                // 业务主表ID已关联流程实例，处理现有流程
                return processExistingProcess(instance, operation, currentUser, remark);
            }
        } catch (Exception e) {
            ProcessWorkflowResult processWorkflowResult = new ProcessWorkflowResult();
            processWorkflowResult.setRemark("流程处理失败: " + e.getMessage());
            processWorkflowResult.setSuccess(false);
            return processWorkflowResult;
        }
    }
    
    private ProcessWorkflowResult startNewProcess(String processId, String businessKey, UserLogin currentUser, String remark) {
        // 查询流程配置
        ProcessConfigDtlVO processConfigDtlVO =  processConfigMapper.getProcessConfigDtlById(processId);
        if (processConfigDtlVO == null) {
            throw new BusinessException("流程不存在！");
        }

        // 获取第一个节点
        ProcessConfigDtlItemVO firstNode = processConfigDtlVO.getProcessConfigDtlItemVOs().get(0);

        // 权限校验：操作人是否有第一个节点的权限
//        if (!hasPermission(operator, firstNode.getId())) {
//            throw new RuntimeException("无权限发起此流程");
//        }

        // 创建流程实例
        ProcessInstance processInstance = new ProcessInstance();
        processInstance.setProcessId(processId);
        processInstance.setBusinessKey(businessKey);
        processInstance.setCurrentNodeId(firstNode.getProcessNodeId());
        processInstance.setStatus(PublicEnum.ProcessStatus_RUNNING.getCode());
        processInstance.setOperaterId(currentUser.getUserId());
        processInstance.setOperaterName(currentUser.getUserName());//
        processInstanceMapper.insert(processInstance);

        // 记录操作（提交节点的操作）
        ProcessNodeOperation processNodeOperation = new ProcessNodeOperation();
        processNodeOperation.setProcessInstanceId(processInstance.getProcessInstanceId());
        processNodeOperation.setProcessNodeId(firstNode.getProcessNodeId());
        processNodeOperation.setOperation(PublicEnum.OperationType_SUBMIT.getCode());
        processNodeOperation.setOperaterId(currentUser.getUserId());
        processNodeOperation.setOperaterName(currentUser.getUserName());//
        processNodeOperationMapper.insert(processNodeOperation);
        
        // 获取下一个节点（运营人员审核节点）
        List<ProcessConfigDtlItemVO> processConfigDtlItemVOs =  processConfigMapper.getProcessConfigDtlById(processInstance.getProcessId()).getProcessConfigDtlItemVOs();
        Pair<ProcessConfigDtlItemVO, ProcessConfigDtlItemVO> result = getAdjacentItems(processConfigDtlItemVOs, processInstance.getCurrentNodeId());
        ProcessConfigDtlItemVO nextNode = result.getRight();
        if (nextNode != null) {
            processInstance.setCurrentNodeId(nextNode.getProcessNodeId()); // 流转到下一节点
        } else {
            processInstance.setStatus(PublicEnum.ProcessStatus_COMPLETED.getCode()); // 若没有下一节点，流程直接完成
        }
        processInstanceMapper.updateById(processInstance); // 保存实例变更
        
        ProcessWorkflowResult processWorkflowResult = new ProcessWorkflowResult();
        processWorkflowResult.setInstanceId(processInstance.getProcessInstanceId());
        processWorkflowResult.setCurrentNodeId(nextNode != null ? nextNode.getProcessNodeId() : firstNode.getProcessNodeId());
        processWorkflowResult.setStatus(processInstance.getStatus());
        processWorkflowResult.setRemark("流程已启动，当前节点: " + (nextNode != null ? nextNode.getNodeName() : "流程已完成"));
        processWorkflowResult.setSuccess(true);
        return processWorkflowResult;
    }
    
    private ProcessWorkflowResult processExistingProcess(ProcessInstance processInstance, Integer operation,UserLogin currentUser, String remark) {
        // 检查流程状态
        if (!processInstance.getStatus().equals(PublicEnum.ProcessStatus_RUNNING.getCode())) {
            throw new RuntimeException("流程已结束，无法操作");
        }
        // 获取当前待处理节点
        ProcessNode currentNode = processNodeService.getById(processInstance.getCurrentNodeId());

        // 权限校验
//        if (!hasPermission(operator, currentNode.getId())) {
//            throw new RuntimeException("无权限操作此节点");
//        }

        // 处理流转逻辑（关键点：先计算下一个节点，再记录操作）
        ProcessConfigDtlItemVO nextNode = null;
        Integer newStatus = processInstance.getStatus();
        String message = "";
        
        // 记录操作（当前待处理节点的操作）
        ProcessNodeOperation processNodeOperation = new ProcessNodeOperation();
//        processNodeOperation.setProcessRoleId();
        processNodeOperation.setProcessInstanceId(processInstance.getProcessInstanceId());
        processNodeOperation.setProcessNodeId(currentNode.getProcessNodeId());
        processNodeOperation.setOperation(operation);
        processNodeOperation.setOperaterId(currentUser.getUserId());
        processNodeOperation.setOperaterName(currentUser.getUserName());//
        processNodeOperation.setRemark(remark);
        processNodeOperationMapper.insert(processNodeOperation);
        
        // 查询流程配置
        List<ProcessConfigDtlItemVO> processConfigDtlItemVOs =  processConfigMapper.getProcessConfigDtlById(processInstance.getProcessId()).getProcessConfigDtlItemVOs();
        Pair<ProcessConfigDtlItemVO, ProcessConfigDtlItemVO> result = getAdjacentItems(processConfigDtlItemVOs, processInstance.getCurrentNodeId());
        // 获取上一个节点
        //ProcessConfigDtlItemVO prevItem = result.getLeft();
        
        if (operation == PublicEnum.OperationType_SUBMIT.getCode() || operation == PublicEnum.OperationType_APPROVE.getCode()) {
            // 获取下一个节点
            nextNode = result.getRight();
            if (nextNode != null) {
                // 流转到下一个节点
                processInstance.setCurrentNodeId(nextNode.getProcessNodeId());
                message = "操作成功，流程已流转至下一个节点: " + nextNode.getNodeName();
            } else {
                // 没有下一个节点，流程完成
                processInstance.setStatus(PublicEnum.ProcessStatus_COMPLETED.getCode());
                processInstance.setCurrentNodeId("0"); // 流程完成后当前节点设为null
                newStatus = PublicEnum.ProcessStatus_COMPLETED.getCode();
                message = "操作成功，流程已完成";
            }
        } else if (operation == PublicEnum.OperationType_REJECT.getCode()) {
            // 驳回逻辑 - 回到第一个节点
            nextNode = processConfigDtlItemVOs.get(0);
            processInstance.setCurrentNodeId(nextNode.getProcessNodeId());
            message = "操作成功，流程已退回至: " + nextNode.getNodeName();
        }
        
        // 保存实例变更
        processInstanceMapper.updateById(processInstance);
        
        ProcessWorkflowResult processWorkflowResult = new ProcessWorkflowResult();
        processWorkflowResult.setInstanceId(processInstance.getProcessInstanceId());
        processWorkflowResult.setCurrentNodeId(nextNode != null ? nextNode.getProcessNodeId() : currentNode.getProcessNodeId());
        processWorkflowResult.setStatus(newStatus);
        processWorkflowResult.setRemark(message);
        processWorkflowResult.setSuccess(true);
        return processWorkflowResult;
    }
    
    //获取当前节点的相邻节点
    public Pair<ProcessConfigDtlItemVO, ProcessConfigDtlItemVO> getAdjacentItems(
            List<ProcessConfigDtlItemVO> processConfigDtlItemVOs,
            String targetProcessNodeId) {

        ProcessConfigDtlItemVO prevItem = null;
        ProcessConfigDtlItemVO nextItem = null;
        int targetIndex = -1;

        // 查找目标记录的索引
        for (int i = 0; i < processConfigDtlItemVOs.size(); i++) {
            if (processConfigDtlItemVOs.get(i).getProcessNodeId().equals(targetProcessNodeId)) {
                targetIndex = i;
                break;
            }
        }

        // 如果找到目标记录，获取相邻记录
        if (targetIndex != -1) {
            // 获取上一条记录（如果存在）
            if (targetIndex > 0) {
                prevItem = processConfigDtlItemVOs.get(targetIndex - 1);
            }
            // 获取下一条记录（如果存在）
            if (targetIndex < processConfigDtlItemVOs.size() - 1) {
                nextItem = processConfigDtlItemVOs.get(targetIndex + 1);
            }
        }
        return Pair.of(prevItem, nextItem);
    }

}
