package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @描述：招标项目信息
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "招标项目信息")
@Data
@TableName("bidding_project_info")
public class BiddingProjectInfo extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "目录id")

    private String projectId;

    @ApiModelProperty(value = "招标类型id")

    private String biddingTypeId;

    @ApiModelProperty(value = "项目编号")

    private String projectCode;

    @ApiModelProperty(value = "项目名称")

    private String projectName;

    @ApiModelProperty(value = "预算金额")

    private BigDecimal budgetMoney;

    @ApiModelProperty(value = "项目地址")

    private String address;

    @ApiModelProperty(value = "招标组织形式")

    private String organizationWay;

    @ApiModelProperty(value = "招标方式")

    private String biddingWay;

    @ApiModelProperty(value = "报名方式")

    private String applyWay;

    @ApiModelProperty(value = "资料审查方式")

    private String examineWay;

    @ApiModelProperty(value = "项目描述")

    private String projectDescribe;

    @ApiModelProperty(value = "状态：1启用 0停用")

    private Integer state;


}
