package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.Links;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.mapper.LinksMapper;
import scrbg.meplat.mall.service.LinksService;

import java.util.List;
import java.util.Map;

/**
 * @描述：友情链接 服务类
 * @作者: sund
 * @日期: 2022-11-09
 */
@Service
public class LinksServiceImpl extends ServiceImpl<LinksMapper, Links> implements LinksService {
    @Autowired
    MallConfig mallConfig;


    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Links> lambdaQuery) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();

        Integer state = (Integer) innerMap.get("state");

        lambdaQuery.orderByDesc(Links::getGmtModified);
        lambdaQuery.eq(Links::getState,1);

        IPage<Links> page =
                this.page(
                new Query<Links>().getPage(jsonObject),
                lambdaQuery
        );

        IPage<Links> size = page.setSize(10);

        return new PageUtils(page);
    }

    @Override
    public void create(Links links) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(links);
    }

    @Override
    public void update(Links links) {
        super.updateById(links);
    }


    @Override
    public Links getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void updateLinksState(String id, int state) {
        Links byId = getById(id);
        byId.setState(state);
        update(byId);
    }

    /**
     * 根据条件分页查询
     *
     * @param jsonObject
     * @param queryWrapper
     * @return
     */
    @Override
    public PageUtils queryByConfigPage(JSONObject jsonObject, LambdaQueryWrapper<Links> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();

        Integer state = (Integer) innerMap.get("state");
        Integer mallType = Integer.valueOf(mallConfig.mallType);
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String name = (String) innerMap.get("name");
        String url = (String) innerMap.get("url");
        if(org.apache.commons.lang.StringUtils.isNotEmpty(name)) {
            queryWrapper.like(Links::getName, name);
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(keywords)) {
            queryWrapper.like(Links::getName, keywords);
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(url)) {
            queryWrapper.like(Links::getUrl, url);
        }
        if (mallType != null) {
            queryWrapper.eq(Links::getMallType, mallType);
        }
        if (state != null) {
            queryWrapper.eq(Links::getState, state);
        }


        //排序方式(排序值升序)
        if (orderBy == null) {

        } else if (orderBy == PublicEnum.ORDER_BY_SORT.getCode()) {
            queryWrapper.orderByDesc(Links::getSort)
                    .orderByDesc(Links::getGmtCreate);
            //排序方式(创建时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_CREATE.getCode()) {
            queryWrapper.orderByDesc(Links::getGmtModified);

            //排序方式(修改时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(Links::getGmtCreate);
        }


        IPage<Links> page = this.page(
                new Query<Links>().getPage(jsonObject),queryWrapper);
        return new PageUtils(page);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByPublish(List<String> ids, String type) {
        List<Links> resutls = listByIds(ids);
        for (Links links : resutls) {
            if ("1".equals(type)) {
                links.setState(PublicEnum.STATE_PUBLISH.getCode());
            } else {
                links.setState(PublicEnum.STATE_UNPUBLISH.getCode());
            }
        }
        super.saveOrUpdateBatch(resutls);
    }


}
