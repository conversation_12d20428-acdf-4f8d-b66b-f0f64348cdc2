package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.entity.ShopSupplierRele;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ShopSupplierRele;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.List;

/**
 * @描述：店铺供方关联表 服务类
 * @作者: ye
 * @日期: 2023-06-05
 */
public interface ShopSupplierReleService extends IService<ShopSupplierRele> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShopSupplierRele> queryWrapper);

    void create(ShopSupplierRele shopSupplierRele);

    void update(ShopSupplierRele shopSupplierRele);

    ShopSupplierRele getById(String id);

    void delete(String id);

    /**
     * 根据店铺查询对应关联的供应商
     *
     * @param jsonObject
     * @param shopSupplierReleLambdaQueryWrapper
     * @return
     */
    PageUtils listByShopId(JSONObject jsonObject, LambdaQueryWrapper<ShopSupplierRele> shopSupplierReleLambdaQueryWrapper);

    List getSupplierIdByShopId(String shopId);

    /**
     * 获取本机构下供店铺
     *
     * @param jsonObject
     * @param shopSupplierReleLambdaQueryWrapper
     * @return
     */
    PageUtils listShopListBySupplierId(JSONObject jsonObject, LambdaQueryWrapper<ShopSupplierRele> shopSupplierReleLambdaQueryWrapper);

    /**
     * 获取下供店铺
     *
     * @param jsonObject
     * @param shopSupplierReleLambdaQueryWrapper
     * @return
     */
    PageUtils listShopList(JSONObject jsonObject, LambdaQueryWrapper<ShopSupplierRele> shopSupplierReleLambdaQueryWrapper);

    /**
     * 判断是否子供应商
     *
     * @return
     */
    ShopSupplierRele isTwoSupper();

    PageUtils listBySupplierId(JSONObject jsonObject, LambdaQueryWrapper<ShopSupplierRele> q);

    /**
     * 根据订单条件筛选供应商分页查询
     * @param jsonObject 查询参数
     * @return 分页结果
     */
    PageUtils querySuppliersByOrderConditions(JSONObject jsonObject);

    /**
     * 设置供应商类型
     * @param shopSupplierRele
     */
    void setSupplierType(ShopSupplierRele shopSupplierRele);

    void setSupplierGroup(ShopSupplierRele shopSupplierRele);

    List<File> querySupplierFile(String id);

    void changeStatus(String id, int state);


}
