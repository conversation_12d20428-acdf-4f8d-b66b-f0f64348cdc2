package scrbg.meplat.mall.service;

import java.time.LocalDate;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;

import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.fee.DealRecordDto;
import scrbg.meplat.mall.entity.PlatformDealFeeRecord;

/**
 * @描述：平台交易费缴费记录 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
public interface PlatformDealFeeRecordService extends IService<PlatformDealFeeRecord> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeRecord> queryWrapper);

    void create(PlatformDealFeeRecord platformDealFeeRecord);

    void update(PlatformDealFeeRecord platformDealFeeRecord);

    PlatformDealFeeRecord getById(String id);

    void delete(String id);

    void deleteBatch(List<String> ids);

    /**
     * 查询交易费记录
     * @param jsonObject
     * @param q
     * @return
     */
    PageUtils platformListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeRecord> q);

    /**
     * 供应商查询交易费记录-分页查询
     * @param jsonObject
     * @param q
     * @return
     */
    IPage<PlatformDealFeeRecord> supplierListByEntity(DealRecordDto dealRecordDto);


    /**
     * 供应商查询交易费记录
     *
     */
    List<PlatformDealFeeRecord> platformDealFeeRecordList(PlatformDealFeeRecord platformDealFeeRecord);

    /**
     * 交易结算日平台自动创建缴费单
     * @param quarterStartDate
     * @param quarterEndDate
     * @return
     */
    void generateQuarterlyServiceFee(LocalDate quarterStartDate, LocalDate quarterEndDate);

    /**
     * 缴费单截止日期完成缴费提醒
     */
    void reminderForPaymentDueDate();

    /**
     * 供应商修改交易费缴费
     * @param platformDealFeeRecord
     */
    void supplierUpdateDealFee(PlatformDealFeeRecord platformDealFeeRecord);

    /**
     * 根据编号获取数据
     * @param sn
     * @return
     */
    PlatformDealFeeRecord findBySn(String sn);

    /**
     * 根据id删除交易服务缴费记录
     * @param id
     */
    void deleteDealFeeRecord(String id);

    /**
     * 审核缴费
     * @param dto
     */
    void audit(AuditDTO dto);

    void changeState(PlatformDealFeeRecord platformDealFeeRecord);
}
