package scrbg.meplat.mall.controller.website;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@RestController
@RequestMapping("/w/statisticsData")
@ApiSort(value = 100)
@Api(tags = "物资数据统计")
public class WebsiteStatisticsNumController {
    @Autowired
    public MallConfig mallConfig;
    @Autowired
    public EnterpriseInfoService enterpriseInfoService;
    @Autowired
    public ShopService shopService;

    @Autowired
    public OrdersService ordersService;

    @Autowired
    public UserService userService;

    @Autowired
    public OrderShipService orderShipService;

    @Autowired
    public ProductService productService;
    @Autowired
    StatisticsDataService statisticsDataService;
    public void exportDataTwoById(HttpServletResponse response){

        String src = mallConfig.templateFormUrl;
        StatisticsData statisticsData = new StatisticsData();
        int userNum = userService.count(new LambdaQueryWrapper<User>().eq(User::getMallType,mallConfig.mallType));
        statisticsData.setUserNum((long) userNum);
        int shopCount = shopService.count(new LambdaQueryWrapper<Shop>().eq(MustBaseEntity::getMallType,mallConfig.mallType));
        statisticsData.setShopNum((long) shopCount);
        int enterPriseNum = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>().eq(MustBaseEntity::getMallType, mallConfig.mallType));
        statisticsData.setEnterpriseNum((long) enterPriseNum);
        int supplierNum = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>().eq(MustBaseEntity::getMallType, mallConfig.mallType)
                .eq(EnterpriseInfo::getIsPcwp,1));
        statisticsData.setSupplierNum((long) supplierNum);

        int productNum = productService.count(new LambdaQueryWrapper<Product>().eq(MustBaseEntity::getMallType, mallConfig.mallType));
        statisticsData.setProductNum((long) productNum);
        int upProductNum = productService.count(new LambdaQueryWrapper<Product>().eq(MustBaseEntity::getMallType, mallConfig.mallType).eq(Product::getState, 1));
        statisticsData.setUpProductNum((long) upProductNum);
        int orderNum = ordersService.count(new LambdaQueryWrapper<Orders>().eq(Orders::getProductType,10).in(Orders::getOrderClass,1,2));
        statisticsData.setOrdersNum((long) orderNum);
        Map<String, Object> orderPrise = ordersService.getMap(
                new QueryWrapper<Orders>()
                        .eq("product_type", 10)
                        .in("order_class", 1, 2)
                        .select("sum(total_amount) as totalAmount"));

        if (orderPrise!=null){
            statisticsData.setOrdersTotalAmount((BigDecimal) orderPrise.get("totalAmount"));
        }

        Map<String, Object> orderShipPrise = orderShipService.getMap(new QueryWrapper<OrderShip>()
                .eq("product_type", 10)
                .eq("type",2)
                .in("order_class", 1, 3)
                .select("sum(total_price) as totalAmount"));
        if (orderShipPrise!=null){
            statisticsData.setOrderShipTotalAmount((BigDecimal) orderShipPrise.get("totalAmount"));
        }
        statisticsData.setGmtCreateStr(DateUtil.getyyymmddHHmmss(new Date()));

        try {
            ExcelForWebUtil.exportExcel(response, (Map<String, Object>) statisticsData, "发货单模板.xlsx", src, "发货单.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(500, "导出失败！");
        }


    }


}
