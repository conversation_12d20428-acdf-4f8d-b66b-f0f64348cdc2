package scrbg.meplat.mall.config.rabbitMQ;

import lombok.Data;

/**
 * 待办事项
 */
@Data
public class ToDoMessageBody {

    private String toDoId;           // 待办唯一标识
    private String orginSystem;      // 源系统名称
    private String module;           // 模块名称
    private String employeeNumber;   // 员工号
    private String userId;           // TT用户ID
    private String todoType;         // 待办类型
    private String title;            // 标题
    private String description;      // 描述
    private Integer status;          // 状态：0待办，1已完成，-1失效
    private String lastupdateTime;   // 更新时间
    private String appId;            // 应用ID
    private String webUrl;           // 网页地址
    private String uwpUrl;           // 桌面端地址
    private String iosUrl;           // IOS地址
    private String androidUrl;       // Android地址
}
