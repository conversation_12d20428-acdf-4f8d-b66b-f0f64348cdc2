package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.FinancialProducts;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @描述：金融产品 服务类
 * @作者: sund
 * @日期: 2022-11-10
 */
public interface FinancialProductsService extends IService<FinancialProducts> {
    PageUtils frontPageQueryPage(JSONObject jsonObject, LambdaQueryWrapper<FinancialProducts> queryWrapper);

    PageUtils platformQueryPage(JSONObject jsonObject, LambdaQueryWrapper<FinancialProducts> queryWrapper);

    PageUtils shopManagementQueryPage(JSONObject jsonObject, LambdaQueryWrapper<FinancialProducts> queryWrapper, HttpServletRequest request);

    void create(FinancialProducts financialProducts, HttpServletRequest request);

    void update(FinancialProducts financialProducts);

    FinancialProducts getById(String id, HttpServletRequest request);

    void delete(String id);

    void updateFinancialState(FinancialProducts dto, HttpServletRequest request);

    void updateByPublish(List<String> ids, String type);
}
