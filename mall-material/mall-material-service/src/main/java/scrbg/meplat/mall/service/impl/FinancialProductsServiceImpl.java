package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.FinancialProducts;
import scrbg.meplat.mall.entity.Links;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.FinancialProductsMapper;
import scrbg.meplat.mall.service.FinancialProductsService;
import scrbg.meplat.mall.util.ThreadLocalUtil;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @描述：金融产品 服务类
 * @作者: sund
 * @日期: 2022-11-10
 */
@Service
public class FinancialProductsServiceImpl extends ServiceImpl<FinancialProductsMapper, FinancialProducts> implements FinancialProductsService {

    @Autowired
    private FinancialProductsService financialProductsService;

    @Override
    public PageUtils frontPageQueryPage(JSONObject jsonObject, LambdaQueryWrapper<FinancialProducts> lambdaQuery) {
//        Map<String, Object> innerMap = jsonObject.getInnerMap();
//
//        Integer productsType = (Integer) innerMap.get("productsType");

        lambdaQuery.orderByDesc(FinancialProducts::getSort).orderByDesc(FinancialProducts::getGmtCreate);
        lambdaQuery.eq(FinancialProducts::getState, 0);

        IPage<FinancialProducts> page = this.page(
                new Query<FinancialProducts>().getPage(jsonObject),
                lambdaQuery
        );
        return new PageUtils(page);
    }

    @Override
    public PageUtils platformQueryPage(JSONObject jsonObject, LambdaQueryWrapper<FinancialProducts> lambdaQuery) {
//        Map<String, Object> innerMap = jsonObject.getInnerMap();
//
//        Integer productsType = (Integer) innerMap.get("productsType");

        lambdaQuery.orderByDesc(FinancialProducts::getGmtModified);


        IPage<FinancialProducts> page = this.page(
                new Query<FinancialProducts>().getPage(jsonObject),
                lambdaQuery
        );
        return new PageUtils(page);
    }

    @Override
    public PageUtils shopManagementQueryPage(JSONObject jsonObject, LambdaQueryWrapper<FinancialProducts> lambdaQuery, HttpServletRequest request) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();

        Integer shopId = (Integer) innerMap.get("shopId");
        lambdaQuery.orderByDesc(FinancialProducts::getGmtModified);

        if (shopId.equals(ThreadLocalUtil.getCurrentUser().getShopId())) {
            IPage<FinancialProducts> page = this.page(
                    new Query<FinancialProducts>().getPage(jsonObject),
                    lambdaQuery
            );
            return new PageUtils(page);
        } else {
            throw new BusinessException(400, "店铺信息不正确，请重新登录---");
        }

    }

    @Override
    public void create(FinancialProducts financialProducts, HttpServletRequest request) {

        if (financialProducts.getShopId() != ThreadLocalUtil.getCurrentUser().getShopId()) {
            super.save(financialProducts);
        } else {
            throw new BusinessException(400, "店铺信息异常，请重新登录");
        }


    }

    @Override
    public void update(FinancialProducts financialProducts) {
        super.updateById(financialProducts);
    }


    @Override
    public FinancialProducts getById(String id, HttpServletRequest request) {

        if (id != ThreadLocalUtil.getCurrentUser().getShopId()) {
            return super.getById(id);
        } else {
            throw new BusinessException(400, "店铺信息异常，请重新登录");
        }

    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * Description:id修改发布状态
     */
    @Override
    public void updateFinancialState(FinancialProducts dto, HttpServletRequest request) {
        if (Objects.equals(dto.getShopId(), ThreadLocalUtil.getCurrentUser().getShopId())) {
            boolean b = updateById(dto);
            if (!b) {
                throw new BusinessException(400, "修改失败！");
            }
        } else {
            throw new BusinessException(400, "店铺信息异常，请重新登录");
        }

    }

    @Override
    public void updateByPublish(List<String> ids, String type) {
        List<FinancialProducts> resutls = listByIds(ids);
        for (FinancialProducts links : resutls) {
            if ("1".equals(type)) {
                links.setState(1);
            } else {
                links.setState(2);
            }
        }
        super.saveOrUpdateBatch(resutls);
    }

}
