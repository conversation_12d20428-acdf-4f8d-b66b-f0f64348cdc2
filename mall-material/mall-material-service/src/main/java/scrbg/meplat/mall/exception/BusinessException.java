package scrbg.meplat.mall.exception;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BusinessException extends RuntimeException {

    @ApiModelProperty(value = "异常编码")
    private Integer code;

    public BusinessException(String msg) {
        super(msg);
        this.code = 500;
    }
    public BusinessException(Integer code, String msg) {
        super(msg);
        this.code = code;
    }
}
