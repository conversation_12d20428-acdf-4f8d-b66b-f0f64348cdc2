package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.Role;
import scrbg.meplat.mall.service.RoleService;

import java.util.List;

/**
 * @描述：企业类型表控制类
 * @作者: y
 * @日期: 2022-11-07
 */
@RestController
@RequestMapping("/platform/role")
@ApiSort(value = 500)
@Api(tags = "企业类型表（后台）")
public class RoleController {

    @Autowired
    public RoleService roleService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<Role> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = roleService.queryPage(jsonObject, new LambdaQueryWrapper<Role>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Role> findById(String id) {
        Role role = roleService.getById(id);
        return R.success(role);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增单个角色")
    public R save(@RequestBody Role role) {
        roleService.create(role);
        return R.success();
    }

    @PostMapping("/createList")
    @ApiOperation(value = "新增多个角色")

    public R createList(@RequestBody List<Role> roleList) {
        R list = roleService.createList(roleList);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody Role role) {
        roleService.update(role);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        roleService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        roleService.removeByIds(ids);
        return R.success();
    }

    /**
     * 根据商城类型查询可见角色
     *
     * @param role
     * @return
     */
    @GetMapping("/findAllByMallType")
    @ApiOperation(value = "查询当前商城所有角色")
    public R<R> findAllByMallType(
            @RequestBody Role role) {
        R roles = roleService.findAllByMallType(role.getMallType());
        return R.success(roles);
    }


    /**
     * 根据店铺id增加角色
     *
     * @param shopRoles shopId
     * @return
     */
    @PostMapping("/insertRoleToShop")
    @ApiOperation(value = "根据店铺id批量增加角色")
    public R insertRoleToShop(
            @RequestParam("shopRoles") List<Role> shopRoles,
            @RequestParam("shopId") String shopId) {
        roleService.insertRoleToShop(shopRoles, shopId);
        return R.success();
    }
}

