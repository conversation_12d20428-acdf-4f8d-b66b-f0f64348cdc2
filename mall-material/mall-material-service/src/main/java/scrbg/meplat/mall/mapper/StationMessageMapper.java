package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.dto.mail.StationMessageReceiveDTO;
import scrbg.meplat.mall.entity.StationMessage;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;
import scrbg.meplat.mall.vo.product.material.StationMessageVo;

import java.util.List;

/**
 * @描述：站点消息 Mapper 接口
 * @作者: y
 * @日期: 2022-11-24
 */
@Mapper
@Repository
public interface StationMessageMapper extends BaseMapper<StationMessage> {

    /**
     * 根据消息id等参数查询消息发送列表
     * @return
     */
    List<StationMessageReceiveVO> listStationMessageReceiveById(@Param("dto")StationMessageReceiveDTO dto);

    /**
     * 根据发送信息id等参数查询发送信息总记录数
     * @return
     */
    int countStationMessageReceiveById(@Param("dto")StationMessageReceiveDTO dto);

    /**
     * 根据消息接收者查询消息发送者信息
     * @param pages
     * @param wrapper
     * @return
     */
    List<StationMessageVo> findByCondition(IPage<StationMessageVo> pages,@Param("ew") QueryWrapper<StationMessage> wrapper);
}
