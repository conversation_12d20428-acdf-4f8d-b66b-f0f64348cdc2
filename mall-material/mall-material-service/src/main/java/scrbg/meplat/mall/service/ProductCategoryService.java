package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.dto.category.UpdateCategorySateByIdDTO;
import scrbg.meplat.mall.dto.thirdapi.BatchUpdateMaterialDtlState;
import scrbg.meplat.mall.dto.thirdapi.CreateProductCategoryDTO;
import scrbg.meplat.mall.dto.thirdapi.MaterialDtlDTO;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.entity.excelTemplate.MaterialDtlInfos;
import scrbg.meplat.mall.vo.platform.MaterialVo;
import scrbg.meplat.mall.vo.product.ImportMaterialExcelResultVO;
import scrbg.meplat.mall.vo.product.material.CategoryClassIdAndClassNameVO;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * @描述：商品分类 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface ProductCategoryService extends IService<ProductCategory> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductCategory> queryWrapper);

    void create(ProductCategory productCategory, String idStr, String farLog);

    void update(ProductCategory productCategory);

    ProductCategory getById(String id);

    void delete(String id);

    /**
     * 获取分类树形
     *
     * @param productType
     * @param classIds
     * @param isHaveProduct
     * @param isLc 是否是临购
     * @return
     */
    List<ProductCategory> listWithTree(Integer productType, List<String> classIds,Integer state,Integer isHaveProduct,Integer isLc);

    /**
     * 根据ids批量逻辑删除
     *
     * @param ids
     */
    void removeLogicBatch(List<String> ids);

    /**
     * 根据分类名称模糊查询
     *
     * @param className
     * @param isLc
     * @return
     */
    List<ProductCategory> listByClassName(String className, Integer productType, Integer state, Integer isHaveProduct, Integer isLc);

    /**
     * 根据ids和分类类型查询分类对应的父级树
     *
     * @param productType
     * @param classs
     * @return
     */
    List<ProductCategory> ListByCategoryParentByIds(Integer productType,List<ProductCategory> classs);

    /**
     * 根据id获取分类
     *
     * @param classId
     * @param isDelete
     * @return
     */
    ProductCategory getProductCategoryById(String classId, Integer isDelete);


    /**
     * 根据id修改
     *
     * @param productCategory
     * @param idStr
     */
    void updateIfById(ProductCategory productCategory, String idStr);

    /**
     * 批量修改启用状态
     * @param dto
     */
    void updateCategorySateById(UpdateCategorySateByIdDTO dto,String ids );

    /**
     * 根据分类id批量修改是否有商品
     * @param classIds
     * @param isHaveProduct
     */
    void updateHaveProductStateById(List<String> classIds,Integer isHaveProduct);

    /**
     * 根据id查询出所有的父级id集合，包括本身
     * @param classId
     * @return
     */
    List<String> getCategoryParentIdList(String classId);

    /**
     * 根据分类id查询分类路径（id和名称）
     * @param classId
     * @return
     */
    List<CategoryClassIdAndClassNameVO> getCategoryParentPath(String classId);

    /**
     * 根据id修改该分类以及对应的父分类状态为都有商品
     * @param classId
     */
    void updateCategoryYesProduct(String classId);

    /**
     * 获取一级分类
     * @param productType
     * @return
     */
    List<ProductCategory> getLevelOne(Integer productType);


    /**
     * 根据分类名称和层级查询分类
     * @param className
     * @param level
     * @return
     */
    ProductCategory getCategoryByClassNameAndLevel(String className, Integer level,Integer productType);


    /**
     * 查询分类的所有子节点
     * @param className
     * @return
     */
    ProductCategory getAllChildItem(String className);

    /**
     * 查询没有下属的二级分类
     *
     * @param parentId
     * @param son
     * @return
     */
    List<ProductCategory> selectThreeCate(String name);


    /**
     * 根据父级id和种类名称获得数据
     *
     * @param parentId
     * @param son
     * @return
     */
    ProductCategory getDataName(String parentId, String son);


    /**
     * 获得集合二级中的所有三级分类
     * @param parentClassId
     * @param sonlist
     * @param parentNameClassLevel
     * @return
     */

    List<ProductCategory> getchildItemList(String parentClassId, List<String> sonlist, int parentNameClassLevel);

    /**
     * 找出统一父级下不在集合的分类的所有子集分类
     * @param parentClassId
     * @param sonlist
     * @param classLeavel
     * @return
     */
    List<ProductCategory> getNowchildItemList(String parentClassId, List<String> sonlist, Integer classLeavel);

    void shiftProduct(String oldClassName, String nowClassName);

    String getProductClassPath(String classId,StringBuffer prouctPath);

    /**
     * 测试分布式事务
     */
    void testClass();

    /**
     * 新增或修改类别
     * @param dto
     */
    void categoryCreateOrUpdate(CreateProductCategoryDTO dto);


    /**
     * 新增或修改物资
     * @param dto
     */
    void saveMaterialInfo(String keyId,MaterialDtlDTO dto);
    void aa(String keyId,MaterialDtlDTO dto);
    /**
     * 批量修改启用停用状态
     * @param dto
     */
    void thirdApiBatchUpdateClassState(UpdateCategorySateByIdDTO dto);

    void outputExcel(JSONObject jsonObject, HttpServletResponse response);

    /**
     * 更具参数查询分页数据
     * @param dto
     * @return
     */
    List<MaterialVo> selectMaterialByclassId(HashMap<String,Object> dto);

    /**
     * 根据分类路径和分类
     * @param classpathName
     * @param level
     * @param state  状态（1启用0停用）
     * @return
     */
    ProductCategory getDataByClassPathName(String classpathName, int level, int state);
    /**
     * 批量更新物资使用状态（提供给物资贸易平台
     * @param idStr
     * @param dto
     */
    void batchUpdateMaterialDtlState(String idStr, BatchUpdateMaterialDtlState dto,StringBuilder farArguments );

    /**
     * 批量更新物资类别使用状态（提供给物资贸易平台）
     * @param idStr
     * @param dto
     */
    void batchUpdateCategoryLibraryState(String idStr, BatchUpdateMaterialDtlState dto, StringBuilder farArguments);

    /**
     * 物资基础库批量导入
     * @param materialDtlInfos  文件
     * @param idStr  日志
     * @return
     */
    List<ImportMaterialExcelResultVO> platforUploadMaterialExcelFile(List<MaterialDtlInfos> materialDtlInfos, String idStr,StringBuilder farArguments);

    /**
     * 同步物资分类
     */
    void synchronizationProductCategory();




//    ProductCategory getClassName(String className);
//
//    ProductCategory getClassNameAndParentId(String className, String parentId,Integer classLevel);
}
