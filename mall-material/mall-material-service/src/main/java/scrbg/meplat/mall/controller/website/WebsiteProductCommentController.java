package scrbg.meplat.mall.controller.website;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.entity.ProductComment;
import scrbg.meplat.mall.service.ProductCommentService;
import scrbg.meplat.mall.vo.product.ProductDetailCommentPageListVO;

import java.util.List;

/**
 * @描述：商品评价控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/w/productComment")
@ApiSort(value = 100)
@Api(tags = "商品评价（前台）")
public class WebsiteProductCommentController {

    @Autowired
    public ProductCommentService productCommentService;

    @PostMapping("/listCommentByProductId")
    @ApiOperation(value = "根据商品id查询商品的评价")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productId", value = "商品id", dataTypeClass = String.class),
    })
    public PageR<ProductDetailCommentPageListVO> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = productCommentService.queryPage(jsonObject, new LambdaQueryWrapper<ProductComment>());
        return PageR.success(page);
    }


    @PostMapping("/getCommentListUserInfo")
    @ApiOperation(value = "补充评价信息列表")
    public scrbg.meplat.mall.util.R<List<ProductDetailCommentPageListVO>> getCommentListUserInfo (@RequestBody List<ProductDetailCommentPageListVO> dtos) {
        List<ProductDetailCommentPageListVO> vos = productCommentService.getCommentListUserInfo(dtos);
        return scrbg.meplat.mall.util.R.success(vos);
    }
}

