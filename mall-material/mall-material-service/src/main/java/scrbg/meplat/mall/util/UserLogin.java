package scrbg.meplat.mall.util;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.dto.user.OrgAndSon;
import scrbg.meplat.mall.entity.SysMenu;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-12-14 17:20
 */
@Data
public class UserLogin {

    private String userId;//用户id

    private String userName;//用户名称

    private String shopId;//店铺id

    private String shopName;//店铺名称

    private String enterpriseId;//本地企业id

    private String orgId;//远程企业id

    private String FarUserId;//远程用户id

    private List<String> orgIds;//组织以及子组织id集合

    private String userMobile;//外部用户的手机号

    private String token;

    private Integer isInterior;//是否是内部用户（0否1是）

    private String enterpriseName;//企业名称

    private Map orgInfo;//当前组织完整信息（用户前端接口请求头使用）

    private Integer enterpriseType;

    private Integer isSupplier;//是否是供应商（0否1是）

    private Integer isCheck;//是否有纪检权限（0否1是）

    private String socialCreditCode;//当前组织统一社会信用代码

    private String userNumber;//用户编号

    private String originalUserName;//原始用户名称

    private Integer isSubmitOrder;//是否有下单权限（0否1是）

    private Integer isMonthPlanAudit;//是否有月供审核权限（0否1是）

    private Integer isPlatformAdmin;//是否有平台管理员权限

    private List<String> roles;//角色

    private List<OrgAndSon> orgAndSon;//当前机构以及子机构（对象）

    private List<MallRole> mallRoles;//角色菜单

    private Map<String, Object> mapSysMenu;//角色菜单大类
}
