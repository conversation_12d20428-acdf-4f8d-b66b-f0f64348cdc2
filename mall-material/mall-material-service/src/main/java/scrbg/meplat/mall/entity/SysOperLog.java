package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @program: maill_api
 * @description: 操作日志
 * @author: 代文翰
 * @create: 2024-11-04 11:36
 **/
@Data
@TableName("sys_oper_log")
public class SysOperLog {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)

    private String id;


    /** 操作模块 */
    @ApiModelProperty(name = "操作模块")
    private String title;

    /** 业务类型（0其它 1新增 2修改 3删除） */
    @ApiModelProperty(name = "业务类型:0=其它,1=新增,2=修改,3=删除,4=上传,5=导出,6=导入")
    private Integer businessType;

    /** 业务类型数组 */
    //private Integer[] businessTypes;

    /** 请求方法 */
    @ApiModelProperty(name = "请求方法")
    private String method;

    /** 请求方式 */
    @ApiModelProperty(name = "请求方式")
    private String requestMethod;

    /** 操作类别（0商城 1后台用户 2小程序用户） */
    @ApiModelProperty(name = "操作类别：0商城 1后台用户 2小程序用户")
    private Integer operatorType;

    /** 操作人员 */
    @ApiModelProperty(name = "操作人员")
    private String operName;

    /** 部门名称 */
    @ApiModelProperty(name = "企业名称")
    private String orgName;

    /** 请求url */
    @ApiModelProperty(name = "请求地址")
    private String operUrl;

    /** 操作地址 */
    @ApiModelProperty(name = "操作地址")
    private String operIp;

    /** 操作地点 */
    @ApiModelProperty(name = "操作地点")
    private String operLocation;

    /** 请求参数 */
    @ApiModelProperty(name = "请求参数")
    private String operParam;

    /** 返回参数 */
    @ApiModelProperty(name = "返回参数")
    private String jsonResult;

    /** 操作状态（0正常 1异常） */
    @ApiModelProperty(name = "状态：0=正常,1=异常")
    private Integer status;

    /** 错误消息 */
    @ApiModelProperty(name = "错误消息")
    private String errorMsg;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "操作时间")
    private Date operTime;

    /** 消耗时间 */
    @ApiModelProperty(name = "消耗时间：毫秒")
    private Long costTime;
}
