package scrbg.meplat.mall.config.redis.redisson;
import org.redisson.Redisson;
import org.redisson.api.RBucket;
import org.redisson.api.RFuture;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;

import java.util.Scanner;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class RedisSentinelTest {

    public static void main(String[] args) {
        RedissonClient redisson = null;

        try {
            // 创建Redisson配置
            Config config = new Config();

            // 配置哨兵模式
            config.useSentinelServers()
                    .setMasterName("mymaster")
                    // 添加哨兵地址
                    .addSentinelAddress(
                            "redis://*************:26379",
                            "redis://*************:26379",
                            "redis://*************:26379"
                    )
                    .setPassword("9ImAtEIE")        // Redis服务器密码
                    .setSentinelPassword("9ImAtEIE") // 哨兵认证密码
                    .setDatabase(0)                 // 数据库索引
                    .setReadMode(ReadMode.SLAVE)   // 读模式：仅从主节点读取
                    .setRetryAttempts(3)           // 重试次数
                    .setRetryInterval(1500)        // 重试间隔，毫秒
                    .setConnectTimeout(3000)       // 连接超时，毫秒
                    .setTimeout(3000);             // 命令超时，毫秒

            // 创建Redisson客户端实例
            redisson = Redisson.create(config);

            System.out.println("成功连接到Redis哨兵模式");

            // 测试基本写入和读取功能
            String testKey = "test:sentinel:key";
            String testValue = "test_value_" + System.currentTimeMillis();

            // 获取分布式对象
            RBucket<String> bucket = redisson.getBucket(testKey);

            // 写入数据
            bucket.set(testValue);
            System.out.println("成功写入数据: " + testKey + " = " + testValue);

            // 读取数据
            String readValue = bucket.get();
            System.out.println("成功读取数据: " + testKey + " = " + readValue);

            if (testValue.equals(readValue)) {
                System.out.println("数据验证成功: 读取值与写入值匹配");
            } else {
                System.out.println("数据验证失败: 读取值与写入值不匹配");
            }

            // 第一次故障转移测试 - 关闭主节点
            System.out.println("\n===== 开始测试第一个节点故障转移 =====");
            System.out.println("请手动关闭当前主节点，然后按回车继续...");
            new Scanner(System.in).nextLine();

            // 暂停几秒钟，等待故障转移完成
            System.out.println("等待故障转移完成...");
            Thread.sleep(10000);

            // 尝试再次读取数据
            try {
                String newReadValue = bucket.get();
                System.out.println("第一次故障转移后读取数据: " + testKey + " = " + newReadValue);

                if (testValue.equals(newReadValue)) {
                    System.out.println("第一次故障转移测试成功: 能够正确读取之前写入的数据");
                } else {
                    System.out.println("读取的值不匹配: 期望 " + testValue + ", 实际 " + newReadValue);
                }

                // 测试写入新数据
                String newTestKey = "test:sentinel:new:key";
                String newTestValue = "new_test_value_after_first_failure_" + System.currentTimeMillis();

                RBucket<String> newBucket = redisson.getBucket(newTestKey);
                newBucket.set(newTestValue);
                System.out.println("第一次故障转移后成功写入新数据: " + newTestKey + " = " + newTestValue);

            } catch (Exception e) {
                System.err.println("第一次故障转移后操作失败: " + e.getMessage());
                e.printStackTrace();
            }

            // 第二次故障转移测试 - 关闭新的主节点
            System.out.println("\n===== 开始测试第二个节点故障转移 =====");
            System.out.println("请手动关闭当前新的主节点，然后按回车继续...");
            new Scanner(System.in).nextLine();

            // 暂停几秒钟，等待故障转移完成
            System.out.println("等待故障转移完成...");
            Thread.sleep(10000);

            // 尝试再次读取数据，使用异步获取带超时
            System.out.println("尝试在两个节点都故障后读取数据...");
            try {
                // 使用异步操作并设置超时
                RFuture<String> future = bucket.getAsync();
                String finalReadValue = future.get(5, TimeUnit.SECONDS); // 设置5秒超时

                System.out.println("两次故障转移后读取数据: " + testKey + " = " + finalReadValue);

                if (testValue.equals(finalReadValue)) {
                    System.out.println("两次故障转移测试成功: 能够正确读取之前写入的数据");
                } else {
                    System.out.println("读取的值不匹配: 期望 " + testValue + ", 实际 " + finalReadValue);
                }

                // 尝试写入新数据
                String finalTestKey = "test:sentinel:final:key";
                String finalTestValue = "final_test_value_after_second_failure_" + System.currentTimeMillis();

                RBucket<String> finalBucket = redisson.getBucket(finalTestKey);
                RFuture<Void> setFuture = finalBucket.setAsync(finalTestValue);
                setFuture.get(5, TimeUnit.SECONDS); // 设置5秒超时

                System.out.println("两次故障转移后成功写入新数据: " + finalTestKey + " = " + finalTestValue);

            } catch (TimeoutException te) {
                System.err.println("操作超时: " + te.getMessage());
                System.err.println("当两个Redis节点都故障时，操作预期会超时，这是正常的行为");
            } catch (ExecutionException ee) {
                System.err.println("执行异常: " + ee.getMessage());
                System.err.println("当两个Redis节点都故障时，操作预期会失败，这是正常的行为");
                ee.printStackTrace();
            } catch (Exception e) {
                System.err.println("两次故障转移后操作失败: " + e.getMessage());
                System.err.println("错误类型: " + e.getClass().getName());
                e.printStackTrace();
            }

            // 恢复测试 - 等待节点恢复
            System.out.println("\n===== 开始测试节点恢复 =====");
            System.out.println("请手动恢复至少一个Redis节点，然后按回车继续...");
            new Scanner(System.in).nextLine();

            // 暂停几秒钟，等待连接恢复
            System.out.println("等待连接恢复...");
            Thread.sleep(10000);

            // 尝试在恢复后读取数据
            try {
                String recoveryReadValue = bucket.get();
                System.out.println("节点恢复后读取数据: " + testKey + " = " + recoveryReadValue);

                if (testValue.equals(recoveryReadValue)) {
                    System.out.println("恢复测试成功: 能够正确读取之前写入的数据");
                } else {
                    System.out.println("读取的值不匹配: 期望 " + testValue + ", 实际 " + recoveryReadValue);
                }

                // 测试写入新数据
                String recoveryTestKey = "test:sentinel:recovery:key";
                String recoveryTestValue = "recovery_test_value_" + System.currentTimeMillis();

                RBucket<String> recoveryBucket = redisson.getBucket(recoveryTestKey);
                recoveryBucket.set(recoveryTestValue);
                System.out.println("节点恢复后成功写入新数据: " + recoveryTestKey + " = " + recoveryTestValue);

            } catch (Exception e) {
                System.err.println("节点恢复后操作失败: " + e.getMessage());
                e.printStackTrace();
            }

            // 清理资源
            System.out.println("\n测试完成，正在清理资源...");
            bucket.delete();

        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 释放资源
            if (redisson != null && !redisson.isShutdown()) {
                redisson.shutdown();
                System.out.println("Redisson客户端已关闭");
            }
        }
    }
}