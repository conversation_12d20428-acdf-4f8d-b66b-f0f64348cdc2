package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.BiddingInfo;
import scrbg.meplat.mall.mapper.BiddingInfoMapper;
import scrbg.meplat.mall.service.BiddingInfoService;

/**
 * @描述：招标详细信息 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
@Service
public class BiddingInfoServiceImpl extends ServiceImpl<BiddingInfoMapper, BiddingInfo> implements BiddingInfoService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingInfo> queryWrapper) {
        IPage<BiddingInfo> page = this.page(
                new Query<BiddingInfo>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(BiddingInfo biddingInfo) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingInfo);
    }

    @Override
    public void update(BiddingInfo biddingInfo) {
        super.updateById(biddingInfo);
    }


    @Override
    public BiddingInfo getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
