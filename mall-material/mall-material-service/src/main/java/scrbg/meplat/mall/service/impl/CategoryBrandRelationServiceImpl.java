package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.CategoryBrandRelation;
import scrbg.meplat.mall.mapper.CategoryBrandRelationMapper;
import scrbg.meplat.mall.service.CategoryBrandRelationService;

/**
 * @描述：品牌分类关联 服务类
 * @作者: y
 * @日期: 2022-11-10
 */
@Service
public class CategoryBrandRelationServiceImpl extends ServiceImpl<CategoryBrandRelationMapper, CategoryBrandRelation> implements CategoryBrandRelationService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<CategoryBrandRelation> queryWrapper) {
        IPage<CategoryBrandRelation> page = this.page(
                new Query<CategoryBrandRelation>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(CategoryBrandRelation categoryBrandRelation) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(categoryBrandRelation);
    }

    @Override
    public void update(CategoryBrandRelation categoryBrandRelation) {
        super.updateById(categoryBrandRelation);
    }


    @Override
    public CategoryBrandRelation getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
