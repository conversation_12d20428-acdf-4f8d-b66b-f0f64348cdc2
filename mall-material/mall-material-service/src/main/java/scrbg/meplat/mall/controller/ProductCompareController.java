package scrbg.meplat.mall.controller;

import cn.hutool.http.server.HttpServerResponse;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.ProductCompareService;
import scrbg.meplat.mall.entity.ProductCompare;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.vo.productCompare.ProductCompareDetail;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @描述：控制类
 * @作者: ye
 * @日期: 2023-12-12
 */
@RestController
@RequestMapping("/productCompare")
@Api(tags = "比价主表控制类")
public class ProductCompareController {

    @Autowired
    public ProductCompareService productCompareService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<ProductCompare> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = productCompareService.queryPage(jsonObject, new LambdaQueryWrapper<ProductCompare>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<ProductCompare> findById(String id) {
        ProductCompare productCompare = productCompareService.getById(id);
        return R.success(productCompare);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody ProductCompare productCompare) {
        productCompareService.create(productCompare);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody ProductCompare productCompare) {
        productCompareService.update(productCompare);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        productCompareService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        productCompareService.deleteBatch(ids);
        return R.success();
    }
    // 删除比价记录
    @GetMapping("/removeByID")
    @ApiOperation(value = "根据主键删除比价记录")
    public R removeByID(String id) {
        productCompareService.removeByID(id);
        return R.success();
    }
    // 获取比价详情明细
    @GetMapping("/getDetail")
    @ApiOperation(value = "获取比价详情明细")
    public R getDetail(String id) {
        ProductCompareDetail detail = productCompareService.getCompareDetail(id);
        return R.success(detail);
    }
    // 导出比价PDF文件
    @GetMapping("/exportPdf")
    @ApiOperation(value = "获取比价PDF（单个）")
    public void getDetail(String id, HttpServletResponse response) {
        try {
            String userName = ThreadLocalUtil.getCurrentUser().getUserName();
            response.setHeader("Content-Disposition",
                    "attachment; filename=\"" + new String(userName.getBytes("gb2312"), "ISO8859-1") + "\"");
            productCompareService.mergeCompareDetailToPDF1(Collections.singletonList(id),response.getOutputStream());
        } catch (IOException e) {
            throw new BusinessException("导出异常");
        }
    }
    // 合并比价明细并转换PDF
    @PostMapping("/mergePdf")
    @ApiOperation(value = "合并比价明细并转换PDF（多个）")
    public void mergePdf(@RequestBody List<String> ids, HttpServletResponse response) {
        productCompareService.mergeCompareDetail(ids,response);
    }
}

