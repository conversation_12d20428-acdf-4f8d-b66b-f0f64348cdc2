package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.BiddingWinRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.BiddingWinRecord;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
/**
 * @描述：竞价中标记录 服务类
 * @作者: ye
 * @日期: 2023-07-11
 */
public interface BiddingWinRecordService extends IService<BiddingWinRecord> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingWinRecord> queryWrapper);

        void create(BiddingWinRecord biddingWinRecord);

        void update(BiddingWinRecord biddingWinRecord);

        BiddingWinRecord getById(String id);

        void delete(String id);
}