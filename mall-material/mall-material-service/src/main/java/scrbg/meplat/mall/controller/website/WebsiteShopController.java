package scrbg.meplat.mall.controller.website;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.util.LinuxOperUtl;
import scrbg.meplat.mall.vo.product.shop.website.WebsiteShopVo;
import scrbg.meplat.mall.vo.w.IndexShopListVO;

import java.util.List;

@RestController
@RequestMapping("/w/shop")
@ApiSort(value = 100)
@Api(tags = "店铺（前台）")
public class WebsiteShopController {
    @Autowired
    public ShopService shopService;

    @Autowired
    private EnterpriseInfoService enterpriseInfoService;





    @GetMapping("/getShopInfo")
    @ApiOperation(value = "通过ID查询店铺信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "shopId", value = "店铺ID", required = true,
                    dataType = "String", paramType = "query"),
    })
    public R<WebsiteShopVo> getShopInfo(String shopId,Integer mallType) {
        WebsiteShopVo vo = shopService.getWebsiteShop(shopId,mallType);
        return R.success(vo);
    }
    @GetMapping("/getIndexShopList")
    @ApiOperation(value = "首页查询店铺")
    public R<List<IndexShopListVO>> getIndexShopList(Integer size) {
        List<IndexShopListVO> vos = shopService.getIndexShopList(size);
        return R.success(vos);
    }

    @GetMapping("/getShopPhone")
    @ApiOperation(value = "查询店铺电话")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "shopId", value = "店铺ID", required = true,
                    dataType = "String", paramType = "query")

    })
    public R<String> getShopPhone(String shopId) {
        String phone = shopService.getShopPhoneByShopId(shopId);
        return R.success(phone);
    }

    @PostMapping("/listShopInfo")
    @ApiOperation(value = "查询店铺列表")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（店铺名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "initial", value = "店铺首字母", dataTypeClass = Integer.class),
            @DynamicParameter(name = "area", value = "区域", dataTypeClass = String.class),
            @DynamicParameter(name = "type", value = "类型，1为自营店，2为路桥内容部店，3为其它", dataTypeClass = String.class),
    })
    public PageR<Shop> listShopInfo(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopService.findPublicShop(jsonObject);
        return PageR.success(page);

    }

    @PostMapping("/getIndexSupplierList")
    @ApiOperation(value = "首页查询供应商")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（店铺名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "initial", value = "店铺首字母", dataTypeClass = Integer.class),
            @DynamicParameter(name = "area", value = "区域", dataTypeClass = String.class),
    })
    public PageR<Shop> getIndexSupplierList(@RequestBody JSONObject jsonObject) {
        PageUtils page = shopService.getIndexSupplierList(jsonObject);
        return PageR.success(page);

    }

    @GetMapping("/getEnterpriseInfoNameById")
    @ApiOperation(value = "根据企业id查询企业名称")
    public String getEnterpriseInfoNameByShopId(String id) {
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getById(id);
        String name = enterpriseInfo.getEnterpriseName();
        return name;
    }
    @GetMapping("/getEnterpriseBySocialCreditCode")
    @ApiOperation(value = "根据信用代码查询企业")
    public EnterpriseInfo getEnterpriseBySocialCreditCode(String socialCreditCode) {
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getSocialCreditCode, socialCreditCode)
                .select(EnterpriseInfo::getEnterpriseId)
                .one();
        return enterpriseInfo;
    }

    @GetMapping("/getEnterpriseInfoById")
    @ApiOperation(value = "根据企业id查询企业部分信息")
    public EnterpriseInfo getEnterpriseInfoById(String id) {
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, id)
                .select(EnterpriseInfo::getSocialCreditCode, EnterpriseInfo::getShortCode, EnterpriseInfo::getInteriorId).one();
        return enterpriseInfo;
    }
    @GetMapping("/getEnterpriseInfoByOrgId")
    @ApiOperation(value = "根据远程企业id查询企业部分信息")
    public EnterpriseInfo getEnterpriseInfoByOrgId(String id) {
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getInteriorId, id)
                .select(EnterpriseInfo::getSocialCreditCode, EnterpriseInfo::getShortCode, EnterpriseInfo::getInteriorId,EnterpriseInfo::getEnterpriseName).one();
        return enterpriseInfo;
    }
    @GetMapping("/getEnterpriseIdByInsideId")
    @ApiOperation(value = "根据企业id查询企业信息")
    public EnterpriseInfo getEnterpriseIdByInsideId(String insideId) {
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getInteriorId, insideId)
                .select(EnterpriseInfo::getEnterpriseId,EnterpriseInfo::getShortCode).one();
        return enterpriseInfo;
    }

    @GetMapping("/getOrgIsSupperCountById")
    @ApiOperation(value = "根据企业id查询供应商数量")
    public Integer getEnterpriseInfoNameByShopId(String enterpriseId,Integer isSupplier) {
        Integer count =  enterpriseInfoService.getOrgIsSupperCountById(enterpriseId,isSupplier);
        return count;
    }

    @GetMapping("/getByEnterpriseId")
    @ApiOperation(value = "获取店铺信息")
    public scrbg.meplat.mall.util.R<Shop> getByEnterpriseId(String enterpriseId) {
        Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId,enterpriseId)
                .one();
        return scrbg.meplat.mall.util.R.success(shop);
    }
}

