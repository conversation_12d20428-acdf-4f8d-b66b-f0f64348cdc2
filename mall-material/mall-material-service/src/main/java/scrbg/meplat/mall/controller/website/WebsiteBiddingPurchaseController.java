package scrbg.meplat.mall.controller.website;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.entity.BiddingPurchase;
import scrbg.meplat.mall.service.BiddingPurchaseService;
import scrbg.meplat.mall.vo.bidding.BiddingPageListVO;


@RestController
@ApiSort(value = 100)
@RequestMapping("/w/biddingPurchase")
@Api(tags = "个人中心-竞价采购表")
public class WebsiteBiddingPurchaseController {

    @Autowired
    public BiddingPurchaseService biddingPurchaseService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<BiddingPurchase> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= biddingPurchaseService.unLoginqueryPage(jsonObject,new LambdaQueryWrapper<BiddingPurchase>());
        return PageR.success(page);
    }


    @PostMapping("/biddingPageList")
    @ApiOperation(value = "竞价公示列表")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<BiddingPageListVO> biddingPageList(@RequestBody JSONObject jsonObject){
        PageUtils page = biddingPurchaseService.biddingPageList(jsonObject,new LambdaQueryWrapper<BiddingPurchase>());
        return PageR.success(page);
    }

}
