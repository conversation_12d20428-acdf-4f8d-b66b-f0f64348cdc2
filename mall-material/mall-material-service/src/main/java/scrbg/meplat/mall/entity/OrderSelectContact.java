package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @描述：订单关联合同
 * @作者: ye
 * @日期: 2023-04-23
 */
@ApiModel(value = "订单关联合同")
@Data
@TableName("order_select_contact")
public class OrderSelectContact extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "订单选择合同id")
    private String orderSelectContactId;

    @ApiModelProperty(value = "订单id")

    private String orderId;
    @ApiModelProperty(value = "机构id")

    private String orgId;

    @ApiModelProperty(value = "订单编号")

    private String orderSn;


    @ApiModelProperty(value = "订单项id")

    private String orderItemId;


    @ApiModelProperty(value = "合同设备明细id")

    private String dtlId;


    @ApiModelProperty(value = "设备名称")

    private String itemName;


    @ApiModelProperty(value = "已选数量")

    private BigDecimal count;


    @ApiModelProperty(value = "合同编号")

    private String billNo;


    @ApiModelProperty(value = "合同id")

    private String billId;


    @ApiModelProperty(value = "合同名称")

    private String billName;
    @ApiModelProperty(value = "合同设备型号")

    private String size;

    @ApiModelProperty(value = "状态")

    private Integer state;


    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备 6维修服务 7金融服务 8保险服务")

    private Integer productType;


}