package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.InsuranceProducts;
import scrbg.meplat.mall.service.InsuranceProductsService;

import java.util.List;

/**
 * @描述：保险产品控制类
 * @作者: sund
 * @日期: 2022-11-15
 */
@RestController
@RequestMapping("/insuranceProducts")
@ApiSort(value = 500)
@Api(tags = "保险产品")
public class InsuranceProductsController {

    @Autowired
    public InsuranceProductsService insuranceProductsService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<InsuranceProducts> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = insuranceProductsService.queryPage(jsonObject, new LambdaQueryWrapper<InsuranceProducts>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<InsuranceProducts> findById(String id) {
        InsuranceProducts insuranceProducts = insuranceProductsService.getById(id);
        return R.success(insuranceProducts);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody InsuranceProducts insuranceProducts) {
        insuranceProductsService.create(insuranceProducts);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody InsuranceProducts insuranceProducts) {
        insuranceProductsService.update(insuranceProducts);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        insuranceProductsService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        insuranceProductsService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("/updatePublish")
    @ApiOperation(value = "批量发布")
    public R updatePublish(@RequestBody List<String> ids) {
        insuranceProductsService.updateByPublish(ids, "1");
        return R.success();
    }

    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量取消发布")
    @ApiImplicitParam(name = "type", value = "1：发布  2：未发布", required = true,
            dataType = "Integer", paramType = "query")
    public R updateNotPublish(@RequestBody List<String> ids) {
        insuranceProductsService.updateByPublish(ids, "2");
        return R.success();
    }
}

