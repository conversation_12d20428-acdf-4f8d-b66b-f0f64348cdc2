package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.AskAnswer;
import scrbg.meplat.mall.service.AskAnswerService;
import scrbg.meplat.mall.vo.user.userCenter.AskAnswerVo;

import java.util.List;

/**
 * @描述：问答控制类
 * @作者: y
 * @日期: 2022-11-22
 */
@RestController
@RequestMapping("/askAnswer")
@Api(tags = "问答")
public class AskAnswerController {

    @Autowired
    public AskAnswerService askAnswerService;

    @PostMapping("/listByParameters")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（标题，发件人名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "relevanceId", value = "关联id", dataTypeClass = String.class),
            @DynamicParameter(name = "type", value = "信息类型（0求购1求租2招标）", dataTypeClass = Integer.class),
    })
    public PageR<AskAnswer> listByParameters(@RequestBody JSONObject jsonObject) {
        PageUtils page = askAnswerService.queryPage(jsonObject, new LambdaQueryWrapper<AskAnswer>());
        return PageR.success(page);
    }

    @PostMapping("/listByDeviceDemand")
    @ApiOperation(value = "参与需求--根据需求id查询回答")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（标题，发件人名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "relevanceId", value = "关联id", dataTypeClass = String.class),
            @DynamicParameter(name = "type", value = "信息类型（0求购1求租2招标）", dataTypeClass = Integer.class),
    })
    public PageR<AskAnswerVo> listByDeviceDemand(@RequestBody JSONObject jsonObject) {
        PageUtils page = askAnswerService.listByDeviceDemand(jsonObject, new QueryWrapper<AskAnswerVo>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<AskAnswer> findById(String id) {
        AskAnswer askAnswer = askAnswerService.getById(id);
        return R.success(askAnswer);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody AskAnswer askAnswer) {
        askAnswerService.create(askAnswer);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody AskAnswer askAnswer) {
        askAnswerService.update(askAnswer);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        askAnswerService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        askAnswerService.removeByIds(ids);
        return R.success();
    }
}

