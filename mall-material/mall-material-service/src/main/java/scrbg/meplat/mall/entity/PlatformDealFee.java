package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：平台交易费
 * @作者: ye
 * @日期: 2024-01-24
 */
@ApiModel(value = "平台交易费")
@Data
@TableName("platform_deal_fee")
public class PlatformDealFee extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "平台计费id")
    private String platformDealFeeId;

    @ApiModelProperty(value = "平台计费编号")
    private String platformDealFeeNu;

    @ApiModelProperty(value = "企业id")
    private String enterpriseId;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "服务类型（1店铺交易服务费")
    private Integer serveType;

    @ApiModelProperty(value = "总缴费金额（延后使用统计用于报表，不作为实时修改）")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "总交易费（延后使用统计用于报表，不作为实时修改）")
    private BigDecimal dealAmount;

    @ApiModelProperty(value = "总欠费金额（延后使用统计用于报表，不作为实时修改）")
    private BigDecimal arrearageAmount;

    @ApiModelProperty(value = "待缴费金额（延后使用统计用于报表，不作为实时修改）")
    private BigDecimal residuePayFee;

    @ApiModelProperty(value = "是否超过额度（延后使用统计用于报表，不作为实时修改）")
    private Integer exceedQuota;

    @ApiModelProperty(value = "是否停止服务（延后使用统计用于报表，不作为实时修改）")
    private Integer stopServe;

    @ApiModelProperty(value = "剩余余额")
    private BigDecimal remainingSum;

    @ApiModelProperty(value = "状态 没有使用")
    private Integer state;

    @ApiModelProperty(value = "欠费时间（如果为null没有欠费）欠费是指超过额度不是停止服务时间")
    private Date arrearageDateTime;

    @ApiModelProperty(value = "缴费明细")
    @TableField(exist = false)
    private List<PlatformDealFeeDtl> feeDtls;

    @ApiModelProperty(value = "余额变动明细")
    @TableField(exist = false)
    private List<PlatformBalanceOperate> balanceODtls;

    @ApiModelProperty(value = "乐观锁")
    @Version
    private Integer version;

    @ApiModelProperty(value = "可欠费额度   元")
    @TableField(exist = false)
    private BigDecimal arrearage;

    @ApiModelProperty(value = "欠费过期时间时长")
    @TableField(exist = false)
    private Integer arrearageDateNum;

    @ApiModelProperty(value = "可欠费过期时间类型（1天2月3年）")
    @TableField(exist = false)
    private Integer arrearageDateType;

    @ApiModelProperty(value = "总交易金额")
    @TableField(exist = false)
    private BigDecimal totalDeal;

    @ApiModelProperty(value = "总免费额度缴费")
    @TableField(exist = false)
    private BigDecimal totalMPay;

    @ApiModelProperty(value = "免费交易额度")
    @TableField(exist = false)
    private BigDecimal exemptTotalAmount;

    @ApiModelProperty(value = "当前占用免费交易额度")
    @TableField(exist = false)
    private BigDecimal thisTotalExempt;
}