package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.FileRecordRelated;

public interface FileRecordRelatedService {

    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<FileRecordRelated> fileRecordRelatedLambdaQueryWrapper);

    void create(FileRecordRelated recordRelated);


}
