package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.entity.ShopComment;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;

import java.util.List;
/**
 * @描述：商铺评价 服务类
 * @作者: ye
 * @日期: 2025-05-21
 */
public interface ShopCommentService extends IService<ShopComment> {

        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShopComment> q);

        R create(ShopComment shopComment);

        void update(ShopComment shopComment);

        ShopComment getById(String id);

        void delete(String id);

        void deleteBatch(List<String> ids);

        void  shopCommentCalculate();
}
