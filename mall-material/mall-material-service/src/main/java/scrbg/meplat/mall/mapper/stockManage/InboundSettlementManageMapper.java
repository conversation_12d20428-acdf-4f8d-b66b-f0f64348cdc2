package scrbg.meplat.mall.mapper.stockManage;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.InboundSettlementManage;

import java.time.LocalDate;

@Mapper
@Repository
public interface InboundSettlementManageMapper extends BaseMapper<InboundSettlementManage> {

    @Select("SELECT serial_num FROM stock_settlement_sequence WHERE current_month = #{date} and type = #{type}")
    Integer selectMaxSerialNumber(String date,int type);

    @Insert("insert into stock_settlement_sequence (current_month,serial_num,type) values(#{date},#{serialNum},#{type})")
    void insertSerial(String date, int serialNum,int type);

    @Update("update stock_settlement_sequence set serial_num = #{finalSerialNum} where current_month = #{date} and type = #{type}")
    void updateSerial(String date, int finalSerialNum,int type);
}
