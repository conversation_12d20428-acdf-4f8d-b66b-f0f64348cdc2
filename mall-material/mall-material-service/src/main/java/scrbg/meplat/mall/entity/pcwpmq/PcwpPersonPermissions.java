package scrbg.meplat.mall.entity.pcwpmq;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * PCWP云中心用户权限表实体类
 */
@Data
@TableName("pcwp_person_permissions")
public class PcwpPersonPermissions {
    /**
     * 用户ID（关联用户表）
     */
    @TableField("user_id")
    private String userId;

    /**
     * 用户姓名（冗余存储）
     */
    @TableField("user_name")
    private String userName;

    /**
     * 组织机构ID（关联机构表）
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 组织机构名称（冗余存储）
     */
    @TableField("org_name")
    private String orgName;

    /**
     * 机构简码（用于快速检索）
     */
    @TableField("short_code")
    private String shortCode;

    /**
     * 角色ID（关联角色表）
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 角色名称（冗余存储）
     */
    @TableField("role_name")
    private String roleName;
}