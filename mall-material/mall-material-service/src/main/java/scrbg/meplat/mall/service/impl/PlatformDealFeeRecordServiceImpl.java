package scrbg.meplat.mall.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.fee.DealRecordDto;
import scrbg.meplat.mall.entity.AuditRecord;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.PlatformDealFee;
import scrbg.meplat.mall.entity.PlatformDealFeeDtl;
import scrbg.meplat.mall.entity.PlatformDealFeeRecord;
import scrbg.meplat.mall.entity.PlatformFeeFile;
import scrbg.meplat.mall.entity.PlatformYearFeeRecord;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.PlatformDealFeeDtlMapper;
import scrbg.meplat.mall.mapper.PlatformDealFeeRecordDtlMapper;
import scrbg.meplat.mall.mapper.PlatformDealFeeRecordMapper;
import scrbg.meplat.mall.service.AuditRecordService;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.PlatformBalanceOperateService;
import scrbg.meplat.mall.service.PlatformDealFeeDtlService;
import scrbg.meplat.mall.service.PlatformDealFeeRecordDtlService;
import scrbg.meplat.mall.service.PlatformDealFeeRecordService;
import scrbg.meplat.mall.service.PlatformDealFeeService;
import scrbg.meplat.mall.service.PlatformFeeFileService;
import scrbg.meplat.mall.service.PlatformYearFeeRecordService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.StationMessageService;
import scrbg.meplat.mall.service.SystemParamService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.fee.DealFeePayDtlList;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;

/**
 * @描述：平台交易费缴费记录 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
@Service
public class PlatformDealFeeRecordServiceImpl extends ServiceImpl<PlatformDealFeeRecordMapper, PlatformDealFeeRecord> implements PlatformDealFeeRecordService {

    @Autowired
    ShopService shopService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    PlatformDealFeeDtlService platformDealFeeDtlService;

    @Autowired
    SystemParamService systemParamService;

    @Autowired
    private PlatformFeeFileService platformFeeFileService;

    @Autowired
    private PlatformDealFeeRecordDtlService platformDealFeeRecordDtlService;

    @Autowired
    StationMessageService stationMessageService;

    @Autowired
    PlatformDealFeeService platformDealFeeService;

    @Autowired
    PlatformBalanceOperateService platformBalanceOperateService;
    @Autowired
    PlatformDealFeeDtlMapper platformDealFeeDtlMapper;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    ProductService productService;

    @Autowired
    PlatformYearFeeRecordService platformYearFeeRecordService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeRecord> queryWrapper) {
        IPage<PlatformDealFeeRecord> page = this.page(
                new Query<PlatformDealFeeRecord>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(PlatformDealFeeRecord platformDealFeeRecord) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(platformDealFeeRecord);
    }

    @Override
    public void update(PlatformDealFeeRecord platformDealFeeRecord) {
        super.updateById(platformDealFeeRecord);
    }


    @Override
    public PlatformDealFeeRecord getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        PlatformDealFeeRecord byId = getById(id);
        if (byId.getState() == 1 || byId.getState() == 2) {
            return;
        }
        super.removeById(id);
        platformFeeFileService.lambdaUpdate().eq(PlatformFeeFile::getRelevanceId, id)
                .eq(PlatformFeeFile::getRelevanceType, 2).remove();

        auditRecordService.lambdaUpdate().eq(AuditRecord::getRelevanceId, id)
                .eq(AuditRecord::getRelevanceType, 11).remove();
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    /**
     * 交易结算日平台自动创建缴费单
     *
     * @param quarterStartDate
     * @param quarterEndDate
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateQuarterlyServiceFee(LocalDate quarterStartDate, LocalDate quarterEndDate) {
        // 查询所有供应商店铺
        List<Shop> supplierShops = shopService.lambdaQuery()
                .eq(Shop::getShopType, 1)  // 供应商店铺
                .eq(Shop::getState, 1)     // 正常状态
                .eq(Shop::getIsDelete, 0)  // 未删除
                .list();
        //交易服务费缴费截止天数，从结算日期起计算
        String paymentDeadlineDays = systemParamService.listByCode(PublicEnum.PlatformPaymentDeadlineDays.getRemark(), 1).get(0).getKeyValue();
        // 获取自营店铺ID，自营店铺不计算服务费
        String businessShopId = stringRedisTemplate.opsForValue().get("isBusinessShopId");
        int successCount = 0;
        int failCount = 0;
        // 为每个供应商店铺生成结算费用
        for (Shop shop : supplierShops) {
            try {
                // 跳过自营店铺
//                shop.setEnterpriseName("四川博远正成贸易有限公司");  //开发测试
//                shop.setEnterpriseId("1671060912485699585");  //开发测试
                if (businessShopId != null && businessShopId.equals(shop.getEnterpriseId())) {
                    continue;
                }
                // 检查企业类型
                EnterpriseInfo enterprise = enterpriseInfoService.getById(shop.getEnterpriseId());
                if (enterprise == null) {
                    log.warn("未找到企业信息，店铺ID: {}" + shop.getShopId());
                    continue;
                }
                // 获取店铺在该季度的交易总额
                BigDecimal quarterlyTradeAmount = BigDecimal.ZERO;
                List<PlatformDealFeeDtl> platformDealFeeDtlList = platformDealFeeDtlService.getNotPayQuarterlyDealDtlList(
                        enterprise.getEnterpriseId(), 1, quarterStartDate, quarterEndDate);
                // 使用流计算交易总额
                quarterlyTradeAmount = platformDealFeeDtlList.stream()
                        .map(PlatformDealFeeDtl::getDealAmount)
                        .filter(Objects::nonNull)  // 过滤掉null值
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 如果交易额为0，则不生成费用
                if (quarterlyTradeAmount == null || quarterlyTradeAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                // 计算店铺交易服务费
                BigDecimal serviceFee = platformDealFeeDtlList.stream()
                        .map(PlatformDealFeeDtl::getServeFee)
                        .filter(Objects::nonNull)  // 过滤掉null值
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 计算税额
                BigDecimal taxAmount = platformDealFeeDtlList.stream()
                        .map(PlatformDealFeeDtl::getTaxAmount)
                        .filter(Objects::nonNull)  // 过滤掉null值
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 计算不含税金额
                BigDecimal noTaxAmount = quarterlyTradeAmount.subtract(taxAmount);
                // 计算税率
                BigDecimal taxRate = BigDecimal.ZERO;
                if (quarterlyTradeAmount.compareTo(BigDecimal.ZERO) > 0) {
                    taxRate = taxAmount.divide(quarterlyTradeAmount, 2, RoundingMode.HALF_UP); // 保留4位小数
                }
                // 如果店铺交易服务费为0，则不生成费用
                if (serviceFee == null || serviceFee.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                //查询最新一条已审核通过的结算记录计算累计结算交易额=本次结算金交易额 + 上次结算交易额
                BigDecimal totalAmount = getTotalTransactionAmount(enterprise.getEnterpriseId(), shop.getShopId());
                LocalDate paymentDeadlineDate;
                // 获取店铺缴费天数（安全处理null值）
                Integer shopPayDays = shop.getPayDays();
                // 计算缴费截止日期
                if (shopPayDays != null && shopPayDays > 0) { // 先检查是否为null
                    // 店铺设置了交易截止日期天数
                    int days = shopPayDays + 1;
                    paymentDeadlineDate = quarterEndDate.plusDays(days);
                } else {
                    // 使用系统参数的年费到期提醒天数
                    int days = Integer.parseInt(paymentDeadlineDays) + 1;
                    paymentDeadlineDate = quarterEndDate.plusDays(days);
                }
                //平台店铺交易费收取比例
                String feeRatio = systemParamService.listByCode(PublicEnum.PlatformPaymentDeadlineDays.getRemark(), 1).get(0).getKeyValue();
                // 创建交易费用记录
                PlatformDealFeeRecord record = new PlatformDealFeeRecord();
                record.setDealFeeRecordUn(IdWorker.getIdStr());
                record.setShopId(shop.getShopId());
                record.setShopName(shop.getShopName());
                record.setEnterpriseId(shop.getEnterpriseId());
                record.setEnterpriseName(enterprise.getEnterpriseName());
                record.setPayAmount(serviceFee);
                record.setRecordType(1);
                record.setMallType(0);//0物资商场, 1设备商城
                record.setState(0);  // 0待确认1确认中2确认成功3确认失败4审核中5审核通过6审核未通过
                record.setSettleDate(LocalDate.now());//结算日期
                record.setPeriodStartDate(quarterStartDate);//当期结算交易开始日期
                record.setPeriodEndDate(quarterEndDate);//当期结算交易结束日期
//                record.setPaymentDeadline(null);//缴费截止日期(交易对账单确认后的5个工作日为缴费截止日期)
                record.setPeriodTransactionAmount(quarterlyTradeAmount);//本次结算交易额
                // 转换为BigDecimal类型
                BigDecimal  convertedFeeRatio = new BigDecimal(feeRatio.trim());
                // 可选：设置精度和舍入模式
                convertedFeeRatio = convertedFeeRatio.setScale(2, RoundingMode.HALF_UP);
                record.setFeeRatio(convertedFeeRatio);
                record.setTotalTransactionAmount(totalAmount);//累计结算交易额
                record.setIsDelete(0);

                record.setTaxAmount(taxAmount);
                record.setNoTaxAmount(noTaxAmount);
                record.setTaxRate(taxRate);
                // 保存缴费账单记录
                create(record);
                // 发送站内信
                StationMessageReceiveVO messageVO = new StationMessageReceiveVO();
                messageVO.setTitle("交易服务费结算提醒");
                messageVO.setContent("尊敬的用户：您好！您的交易服务费已经由系统自动计算完毕，请点击“查看详情”查看具体结算详情，并及时缴纳服务费，以免影响您的后续交易服务。我已知晓\n\n" +
                        "本期交易服务费缴费截止日期：" + paymentDeadlineDate);
                ArrayList<String> enterpriseIds = new ArrayList<>();
                enterpriseIds.add(enterprise.getEnterpriseId());
                messageVO.setEnterpriseIdList(enterpriseIds);
                stationMessageService.createSystemBatch(messageVO);// 调用站内信服务发送消息
//                break;  //开发测试
            } catch (Exception e) {
                log.error("生成店铺交易服务费记录失败，店铺ID: " + shop.getShopId() + ", 企业ID: " + shop.getEnterpriseId(), e);
            }
        }
    }

    /**
     * 获取供应商累计交易额
     * 计算方式：本次结算周期交易额 + 历史累计交易额
     *
     * @param enterpriseId 企业ID
     * @param shopId       店铺ID
     * @return 累计交易额
     */
    public BigDecimal getTotalTransactionAmount(String enterpriseId, String shopId) {

        try {
            // 查询最新一条已审核通过的结算记录
            PlatformDealFeeRecord lastRecord = this.lambdaQuery()
                    .eq(PlatformDealFeeRecord::getEnterpriseId, enterpriseId)
                    .eq(StringUtils.isNotEmpty(shopId), PlatformDealFeeRecord::getShopId, shopId)
                    .eq(PlatformDealFeeRecord::getRecordType, 1)  // 交易服务费
                    .eq(PlatformDealFeeRecord::getState, 2)       // 审核通过状态
                    .eq(PlatformDealFeeRecord::getIsDelete, 0)    // 未删除
                    .orderByDesc(PlatformDealFeeRecord::getPeriodEndDate)
                    .last("LIMIT 1")
                    .one();
            // 处理查询结果
            if (lastRecord == null) {
                // 没有历史记录，返回零
                return BigDecimal.ZERO;
            }
            // 安全地处理可能的空值情况
            BigDecimal totalAmount = BigDecimal.ZERO;
            if (lastRecord.getTotalTransactionAmount() != null) {
                totalAmount = totalAmount.add(lastRecord.getTotalTransactionAmount());
            }
            if (lastRecord.getPeriodTransactionAmount() != null) {
                totalAmount = totalAmount.add(lastRecord.getPeriodTransactionAmount());
            }
            return totalAmount;
        } catch (Exception e) {
            log.error("获取累计交易额失败，企业ID:{}, 店铺ID:{}");
            throw new RuntimeException("获取累计交易额失败", e);
        }
    }

    /**
     * 缴费单截止日期完成缴费提醒
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reminderForPaymentDueDate() {

        // 获取配置的提前提醒天数
        int reminderDays = Integer.parseInt(systemParamService.listByCode(PublicEnum.PlatformPaymentDeadlineDays.getRemark(), 1).get(0).getKeyValue());
        ;
        LocalDate reminderDate = LocalDate.now().minusDays(reminderDays);
        // 将 LocalDate 转换为 LocalDateTime (将时间设置为当天的开始时间)
        LocalDateTime reminderDateTime = reminderDate.atStartOfDay();
        // 将 LocalDateTime 转换为 Date
        Date reminderDateAsDate = Date.from(reminderDateTime.atZone(ZoneId.systemDefault()).toInstant());
        PlatformDealFeeRecord platformDealFeeRecord = new PlatformDealFeeRecord();
        platformDealFeeRecord.setGmtCreate(reminderDateAsDate);
        platformDealFeeRecord.setMallType(PublicEnum.MATERIALS.getCode());
        platformDealFeeRecord.setRecordType(1);
        // 查询需要提醒的交易费记录
        List<PlatformDealFeeRecord> recordList = platformDealFeeRecordList(platformDealFeeRecord);
        // 按企业ID分组，确保每个企业只收到一条提醒
        Map<String, PlatformDealFeeRecord> enterpriseMap = recordList.stream()
                .filter(record -> record.getEnterpriseId() != null && !record.getEnterpriseId().isEmpty())
                .collect(Collectors.toMap(
                        PlatformDealFeeRecord::getEnterpriseId,
                        record -> record,
                        (existing, replacement) -> existing  // 如果有重复的企业ID，保留第一个
                ));
        // 为每个企业发送站内信
        enterpriseMap.forEach((enterpriseId, record) -> {
            // 创建站内信
            StationMessageReceiveVO messageVO = new StationMessageReceiveVO();
            messageVO.setTitle("交易服务费结算提醒");
            String formattedDate = reminderDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            // 更新店铺下所有商品为不显示状态,无法接单
            productService.lambdaUpdate().eq(Product::getShopId, record.getShopId())
                    .set(Product::getShowState, 1).update();
            shopService.lambdaUpdate().eq(Shop::getShopId, record.getShopId())// 更新店铺状态为停用
                    .set(Shop::getState, 0).update();
            String messageContent = String.format(
                    "尊敬的用户：您好！截至%s日，我们未能收到你的交易服务费。因此，您目前将无法接收新的订单。" +
                            "为了尽快恢复店铺的正常运营，请您尽快缴纳交易服务费。缴费成功后，您的店铺将立即恢复接收新订单的功能。" +
                            "请知悉并尽快处理，以免对您的业务造成不必要的影响。如有任何疑问或需要帮助，请随时联系我们", formattedDate);
            messageVO.setContent(messageContent);
            ArrayList<String> enterpriseIds = new ArrayList<>();
            enterpriseIds.add(record.getEnterpriseId());
            messageVO.setEnterpriseIdList(enterpriseIds);
            try {
                stationMessageService.createSystemBatch(messageVO);// 调用站内信服务发送消息
            } catch (Exception e) {
                log.error("生成店铺交易服务费记录失败，店铺ID: " + record.getShopId() + ", 企业ID: " + record.getEnterpriseId(), e);
            }
        });
    }

    /**
     * 平台查询交易费记录
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils platformListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeRecord> q) {
        List<Integer> states = (ArrayList<Integer>) jsonObject.get("states");
        String shopName = (String) jsonObject.get("shopName");
        String enterpriseName = (String) jsonObject.get("enterpriseName");
        String getDealFeeRecordUn = (String) jsonObject.get("dealFeeRecordUn");
        String keywords = (String) jsonObject.get("keywords");
        String startGmtCreate = (String) jsonObject.get("startGmtCreate");
        String endGmtCreate = (String) jsonObject.get("endGmtCreate");
        String startGmtModified = (String) jsonObject.get("startGmtModified");
        String endGmtModified = (String) jsonObject.get("endGmtModified");
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        Integer recordType = (Integer) jsonObject.get("recordType");
        String startAuditTime = (String) jsonObject.get("startAuditTime");
        String endAuditTime = (String) jsonObject.get("endAuditTime");

        q.orderByDesc(orderBy == 0, PlatformDealFeeRecord::getGmtCreate);
        q.orderByDesc(orderBy == 1, PlatformDealFeeRecord::getGmtModified);
        q.orderByDesc(orderBy == 2, PlatformDealFeeRecord::getAuditOpenTime);
        q.between(StringUtils.isNotEmpty(startGmtCreate) && StringUtils.isNotEmpty(endGmtCreate), PlatformDealFeeRecord::getGmtCreate, startGmtCreate, endGmtCreate);
        q.between(StringUtils.isNotEmpty(startGmtModified) && StringUtils.isNotEmpty(endGmtModified), PlatformDealFeeRecord::getGmtModified, startGmtModified, endGmtModified);
        q.between(StringUtils.isNotEmpty(startAuditTime) && StringUtils.isNotEmpty(endAuditTime), PlatformDealFeeRecord::getAuditOpenTime, startAuditTime, endAuditTime);

        q.eq(StringUtils.isNotBlank(shopName), PlatformDealFeeRecord::getShopName, shopName);
        q.eq(StringUtils.isNotBlank(getDealFeeRecordUn), PlatformDealFeeRecord::getDealFeeRecordUn, getDealFeeRecordUn);
        q.eq(StringUtils.isNotBlank(enterpriseName), PlatformDealFeeRecord::getEnterpriseName, enterpriseName);
        q.eq(recordType != null, PlatformDealFeeRecord::getRecordType, recordType);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(PlatformDealFeeRecord::getShopName, keywords)
                        .or()
                        .like(PlatformDealFeeRecord::getEnterpriseName, keywords)
                        .or()
                        .like(PlatformDealFeeRecord::getDealFeeRecordUn, keywords);
            });
        }
        q.ne(PlatformDealFeeRecord::getState, 0); // 不包括草稿
        q.in(!CollectionUtils.isEmpty(states), PlatformDealFeeRecord::getState, states);


        IPage<PlatformDealFeeRecord> page = this.page(
                new Query<PlatformDealFeeRecord>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    /**
     * 供应商查询交易费记录
     * @param dealRecordDto
     * @return
     */
    @Override
    public IPage<PlatformDealFeeRecord>  supplierListByEntity(DealRecordDto dealRecordDto) {
        Page<PlatformDealFeeRecord> page = new Page<>(dealRecordDto.getPage(), dealRecordDto.getLimit());

        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        LambdaQueryChainWrapper<PlatformDealFeeRecord> wrapper = this.lambdaQuery();
        return wrapper.eq(PlatformDealFeeRecord::getEnterpriseId, enterpriseId)
            .gt(dealRecordDto.getStartDeadline()!=null, PlatformDealFeeRecord::getPaymentDeadline, dealRecordDto.getStartDeadline())
            .lt(dealRecordDto.getEndDeadline()!=null, PlatformDealFeeRecord::getPaymentDeadline, dealRecordDto.getEndDeadline())
            .eq(dealRecordDto.getState()!=-1, PlatformDealFeeRecord::getState, dealRecordDto.getState())
            .orderByDesc(PlatformDealFeeRecord::getPeriodStartDate)
            .page(page);
    }


    /**
     * 供应商查询交易费记录
     * 查询条件：未删除、状态不为2(非审核通过)、物资商城类型、店铺交易服务费类型、创建时间大于结算日期
     *
     * @param platformDealFeeRecord 查询参数
     * @return 交易费记录列表
     */
    @Override
    public List<PlatformDealFeeRecord> platformDealFeeRecordList(PlatformDealFeeRecord platformDealFeeRecord) {

        LambdaQueryWrapper<PlatformDealFeeRecord> queryWrapper = new LambdaQueryWrapper<>();
        // 状态不等于2(非审核通过)
        queryWrapper.ne(PlatformDealFeeRecord::getState, 2);
        // 物资商城类型(0)
        queryWrapper.eq(PlatformDealFeeRecord::getMallType, 0);
        // 店铺交易服务费类型(1)
        queryWrapper.eq(PlatformDealFeeRecord::getRecordType, 1);
        // 如果提供了企业ID，则按企业ID查询
//        if (StringUtils.hasText(platformDealFeeRecord.getEnterpriseId())) {
//            queryWrapper.eq(PlatformDealFeeRecord::getEnterpriseId, platformDealFeeRecord.getEnterpriseId());
//        }
//
//        // 如果提供了店铺ID，则按店铺ID查询
//        if (StringUtils.hasText(platformDealFeeRecord.getShopId())) {
//            queryWrapper.eq(PlatformDealFeeRecord::getShopId, platformDealFeeRecord.getShopId());
//        }

        // 创建时间大于结算日期截止日期
        if (platformDealFeeRecord.getGmtCreate() != null) {
            queryWrapper.gt(PlatformDealFeeRecord::getGmtCreate, platformDealFeeRecord.getGmtCreate().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate());
        }
        // 按创建时间降序排序
        queryWrapper.orderByDesc(PlatformDealFeeRecord::getGmtCreate);
        return this.list(queryWrapper);
    }

    /**
     * 供应商修改交易费缴费
     *
     * @param p
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void supplierUpdateDealFee(PlatformDealFeeRecord p) {
        PlatformDealFeeRecord byId = getById(p.getDealFeeRecordId());
        if (byId == null || byId.getState() == 1 || byId.getState() == 2) {
            return;
        } else {
            p.setVersion(byId.getVersion());
        }
        if (p.getSubmitAud() == 1) {
            p.setState(1);
        }
        update(p);

        // 明细不修改，明细操作是实时操作

        platformFeeFileService.lambdaUpdate().eq(PlatformFeeFile::getRelevanceId, p.getDealFeeRecordId())
                .eq(PlatformFeeFile::getRelevanceType, 2).remove();
        List<PlatformFeeFile> files = p.getFiles();
        for (PlatformFeeFile file : files) {
            file.setRelevanceId(p.getDealFeeRecordId());
            file.setRelevanceType(2);
            file.setFileId(null);
            platformFeeFileService.save(file);
        }
    }

    @Autowired
    private AuditRecordService auditRecordService;

    @Autowired
    PlatformDealFeeRecordDtlMapper platformDealFeeRecordDtlMapper;

    /**
     * 根据编号获取数据
     *
     * @param sn
     * @return
     */
    @Override
    public PlatformDealFeeRecord findBySn(String sn) {
        PlatformDealFeeRecord one = lambdaQuery().eq(PlatformDealFeeRecord::getDealFeeRecordUn, sn).one();
        if (one == null) {
            return one;
        } else {
            // 使用关联查询
            List<DealFeePayDtlList> vos = platformDealFeeRecordDtlMapper.getDealFeePayDtlList(one.getDealFeeRecordId());
            one.setDtlVOs(vos);
            //缴费凭证
            List<PlatformFeeFile> list = platformFeeFileService.lambdaQuery().eq(PlatformFeeFile::getRelevanceId, one.getDealFeeRecordId())
                    .eq(PlatformFeeFile::getRelevanceType, 2).list();
            one.setFiles(list);
            //审核记录
            List<AuditRecord> list1 = auditRecordService.lambdaQuery().eq(AuditRecord::getRelevanceId, one.getDealFeeRecordId())
                    .eq(AuditRecord::getRelevanceType, 11).list();
            one.setAuditRecords(list1);
            //获取供应商合同编号
            PlatformYearFeeRecord record = platformYearFeeRecordService.lambdaQuery()
                    .eq(PlatformYearFeeRecord::getShopId, one.getShopId())
                    .isNotNull(PlatformYearFeeRecord::getContractNo)
                    .ne(PlatformYearFeeRecord::getContractNo, "")
                    .eq(PlatformYearFeeRecord::getIsDelete, 0)
                    .orderByDesc(PlatformYearFeeRecord::getGmtModified)
                    .last("LIMIT 1")
                    .one();
            // 如果找到记录，返回合同编号，否则返回null
            if (record != null) {
                one.setContractNo(record.getContractNo());
            }
            return one;
        }
    }

    /**
     * 根据id删除交易服务缴费记录
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDealFeeRecord(String id) {
        PlatformDealFeeRecord byId = getById(id);
        if (byId.getState() == 1 || byId.getState() == 2) {
            return;
        }
        super.removeById(id);


        // 废弃
//        // 删除明细，修改缴费明细状态为初始
//        List<PlatformDealFeeRecordDtl> list1 = platformDealFeeRecordDtlService.lambdaQuery().eq(PlatformDealFeeRecordDtl::getDealFeeRecordId, id).list();
//        if(!CollectionUtils.isEmpty(list1)) {
//            List<String> ids = list1.stream().map(t -> t.getPlatformDealFeeDtlId()).collect(Collectors.toList());
//            platformDealFeeDtlService.lambdaUpdate().in(PlatformDealFeeDtl::getPlatformDealFeeDtlId, ids)
//                    .set(PlatformDealFeeDtl::getPayFeeState,0).update();
//            platformDealFeeRecordDtlService.lambdaUpdate().eq(PlatformDealFeeRecordDtl::getDealFeeRecordId, id).remove();
//        }

        // 删除附件、审核历史
        platformFeeFileService.lambdaUpdate().eq(PlatformFeeFile::getRelevanceId, id)
                .eq(PlatformFeeFile::getRelevanceType, 2).remove();

        auditRecordService.lambdaUpdate().eq(AuditRecord::getRelevanceId, id)
                .eq(AuditRecord::getRelevanceType, 11).remove();
    }

    /**
     * 审核交易缴费
     * freem4
     * @param dto
     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void audit(AuditDTO dto) {
//        Integer isOpen = dto.getIsOpen();
//        String id = dto.getId();
//        PlatformDealFeeRecord byId = getById(id);
//        if (byId.getState() == 2 || byId.getState() == 3) {
//            return;
//        }
//        if (byId == null || byId.getState() != 1) {
//        } else {
//            if (isOpen != null && isOpen == 1) {
//                AuditRecord auditRecord = new AuditRecord();
//                auditRecord.setRelevanceType(11);
//                auditRecord.setRelevanceId(id);
//                auditRecord.setResultType(1);
//                auditRecord.setAuditType(1);
//                auditRecord.setAuditResult("【同意】");
//                auditRecordService.create(auditRecord);
//                byId.setState(2);
//                byId.setAuditOpenTime(new Date());
////                List<PlatformDealFeeRecordDtl> list = platformDealFeeRecordDtlService.lambdaQuery().eq(PlatformDealFeeRecordDtl::getDealFeeRecordId, id).list();
////                for (PlatformDealFeeRecordDtl dtl : list) {
////                    PlatformDealFeeDtl one = platformDealFeeDtlService.lambdaQuery().eq(PlatformDealFeeDtl::getPlatformDealFeeDtlId, dtl.getPlatformDealFeeDtlId())
////                            .select(PlatformDealFeeDtl::getPlatformDealFeeDtlId, PlatformDealFeeDtl::getVersion).one();
////                    one.setFinishPayFee(1);
////                    platformDealFeeDtlService.update(one);
////                }
//                // 缴费金额 开始消耗金额
//                BigDecimal payAmount = byId.getPayAmount();
//                // 待缴费金额（不包含作废的）
//                QueryWrapper<PlatformDealFeeDtl> q1 = new QueryWrapper<>();
//                q1.eq("serve_type", byId.getRecordType())
//                        .eq("enterprise_id", byId.getEnterpriseId())
//                        .eq("finish_pay_fee", 0)
//                        .eq("state", 0) // 排除作废
//                        .select("sum(residue_pay_fee) as residuePayFee");
//                Map<String, Object> map1 = platformDealFeeDtlMapper.selectMaps(q1).get(0);
//                BigDecimal rePay = new BigDecimal(0);
//                if (map1 != null && map1.get("residuePayFee") != null) {
//                    rePay = new BigDecimal(map1.get("residuePayFee").toString());
//                }
//                if (payAmount.compareTo(rePay) == 1) {
//                    throw new BusinessException("缴费金额不能超过总欠费金额！");
//                }
//                List<PlatformDealFeeDtl> notPayDealDtlList = platformDealFeeDtlService
//                        .getNotPayDealDtlList(byId.getEnterpriseId(), byId.getRecordType());
//                PlatformDealFee platformDealFee = platformDealFeeService.lambdaQuery()
//                        .eq(PlatformDealFee::getEnterpriseId, byId.getEnterpriseId())
//                        .eq(PlatformDealFee::getServeType, byId.getRecordType())
//                        .one();
//                String platformDealFeeId = null;
//                if (platformDealFee == null) {
//                    // 如果为空直接新增
//                    PlatformDealFee pdf = new PlatformDealFee();
//                    pdf.setEnterpriseId(byId.getEnterpriseId());
//                    pdf.setPlatformDealFeeNu(IdWorker.getIdStr());
//                    pdf.setEnterpriseName(byId.getEnterpriseName());
//                    pdf.setServeType(byId.getRecordType());
//                    platformDealFeeService.save(pdf);
//                    platformDealFeeId = pdf.getPlatformDealFeeId();
//                } else {
//                    platformDealFeeId = platformDealFee.getPlatformDealFeeId();
//                }
//                if (CollectionUtils.isEmpty(notPayDealDtlList)) {
//                    PlatformDealFee p2 = platformDealFeeService.lambdaQuery()
//                            .eq(PlatformDealFee::getEnterpriseId, byId.getEnterpriseId())
//                            .eq(PlatformDealFee::getServeType, byId.getRecordType())
//                            .one();
//                    byId.setReturnBalance(payAmount);
//
//                    PlatformBalanceOperate pbo = new PlatformBalanceOperate();
//                    pbo.setTitle("平台交易费缴费审核通过增加余额");
//                    pbo.setRelevanceId(p2.getPlatformDealFeeId());
//                    pbo.setRelevanceType(1);
//                    pbo.setAmount(payAmount);
//                    pbo.setBeforeAmount(p2.getRemainingSum());
//                    pbo.setAfterAmount(p2.getRemainingSum().add(payAmount));
//                    pbo.setOperateId(byId.getDealFeeRecordId());
//                    pbo.setOperateUn(byId.getDealFeeRecordUn());
//                    pbo.setOperateType(1);
//                    platformBalanceOperateService.save(pbo);
//                    // 待缴费数据为空，直接放入余额
//                    p2.setRemainingSum(p2.getRemainingSum().add(payAmount));
//                    platformDealFeeService.update(p2);
//                } else {
//                    PlatformDealFee p3 = platformDealFeeService.lambdaQuery()
//                            .eq(PlatformDealFee::getEnterpriseId, byId.getEnterpriseId())
//                            .eq(PlatformDealFee::getServeType, byId.getRecordType())
//                            .one();
//                    // 先保存余额
//                    PlatformBalanceOperate pboTop = new PlatformBalanceOperate();
//                    pboTop.setTitle("平台交易费缴费审核通过增加余额");
//                    pboTop.setRelevanceId(platformDealFee.getPlatformDealFeeId());
//                    pboTop.setRelevanceType(1);
//                    pboTop.setAmount(payAmount);
//                    pboTop.setBeforeAmount(p3.getRemainingSum());
//                    pboTop.setAfterAmount(p3.getRemainingSum().add(payAmount));
//                    pboTop.setOperateId(byId.getDealFeeRecordId());
//                    pboTop.setOperateUn(byId.getDealFeeRecordId());
//                    pboTop.setOperateType(1);
//                    pboTop.setGmtCreate(new Date());
//                    platformBalanceOperateService.save(pboTop);
//                    p3.setRemainingSum(p3.getRemainingSum().add(payAmount));
//                    platformDealFeeService.update(p3);
//                    // 先存储原金额
//                    BigDecimal orPayAmount = new BigDecimal(String.valueOf(payAmount));
//                    // 存在待缴费的明细
//                    ArrayList<PlatformDealFeeRecordDtl> saveList = new ArrayList<>();
//                    ArrayList<Map> maps = new ArrayList<>();
//                    for (PlatformDealFeeDtl dtl : notPayDealDtlList) {
//                        // 如果消耗完毕直接return
//                        if (payAmount.compareTo(BigDecimal.ZERO) == 0) {
//                            return;
//                        }
//                        HashMap<Object, Object> objectHashMap = new HashMap<>();
//                        PlatformDealFeeRecordDtl saveDto = new PlatformDealFeeRecordDtl();
//                        saveDto.setDealFeeRecordId(byId.getDealFeeRecordId());
//                        // 剩余缴费金额
//                        BigDecimal residuePayFee = dtl.getResiduePayFee();
//                        // 剩余缴费金额大于支付金额
//                        if (residuePayFee.compareTo(payAmount) == 1) {
//                            BigDecimal subtract = residuePayFee.subtract(payAmount);
//                            dtl.setResiduePayFee(subtract);
//                            dtl.setPayFee(dtl.getPayFee().add(payAmount));
//                            saveDto.setPayAmount(payAmount);
//                            saveDto.setPlatformDealFeeDtlId(dtl.getPlatformDealFeeDtlId());
//                            payAmount = new BigDecimal(0);
//                            objectHashMap.put("useBalance", payAmount);
//                        } else if (residuePayFee.compareTo(payAmount) == 0) {
//                            dtl.setFinishPayFee(1);
//                            dtl.setPayFee(dtl.getPayFee().add(payAmount));
//                            dtl.setResiduePayFee(new BigDecimal(0));
//                            saveDto.setPayAmount(payAmount);
//                            saveDto.setPlatformDealFeeDtlId(dtl.getPlatformDealFeeDtlId());
//                            payAmount = new BigDecimal(0);
//                            objectHashMap.put("useBalance", payAmount);
//                        } else {
//                            // 支付金额大于本次缴费金额
//                            dtl.setFinishPayFee(1);
//                            payAmount = payAmount.subtract(residuePayFee);
//                            dtl.setResiduePayFee(new BigDecimal(0));
//                            saveDto.setPayAmount(residuePayFee);
//                            saveDto.setPlatformDealFeeDtlId(dtl.getPlatformDealFeeDtlId());
//                            dtl.setPayFee(dtl.getPayFee().add(residuePayFee));
//                            objectHashMap.put("useBalance", residuePayFee);
//                        }
//                        platformDealFeeDtlService.update(dtl);
//                        maps.add(objectHashMap);
//                        saveList.add(saveDto);
//                    }
//                    platformDealFeeRecordDtlService.saveBatch(saveList);
//
//                    PlatformDealFee p4 = platformDealFeeService.lambdaQuery()
//                            .eq(PlatformDealFee::getEnterpriseId, byId.getEnterpriseId())
//                            .eq(PlatformDealFee::getServeType, byId.getRecordType())
//                            .one();
//                    // 如果还有金额没有消耗，直接放余额
//                    PlatformBalanceOperate pbo = new PlatformBalanceOperate();
//                    pbo.setTitle("平台交易费缴费审核通过增加余额自动缴费");
//                    pbo.setRelevanceId(p4.getPlatformDealFeeId());
//                    pbo.setRelevanceType(1);
//                    pbo.setAmount(payAmount.subtract(orPayAmount));
//                    pbo.setBeforeAmount(p4.getRemainingSum());
//                    pbo.setAfterAmount(p4.getRemainingSum().add(payAmount.subtract(orPayAmount)));
//                    pbo.setOperateId(byId.getDealFeeRecordId());
//                    pbo.setOperateUn(byId.getDealFeeRecordUn());
//                    pbo.setFreeDtl(JSON.toJSONString(maps));
//                    pbo.setGmtCreate(new Date(new Date().getTime() + 1000));
//                    pbo.setOperateType(1);
//                    platformBalanceOperateService.save(pbo);
//                    // 减去消耗金额
//                    p4.setRemainingSum(p4.getRemainingSum().add(payAmount.subtract(orPayAmount)));
//                    byId.setReturnBalance(payAmount);
//                    platformDealFeeService.update(p4);
//                    // 统计当前是否还存在欠费，如果不存在欠费，将欠费时间设置为null
//                    checkIsFree(byId, platformDealFee, platformDealFeeId);
//                }
//                update(byId);
//            } else {
//                AuditRecord auditRecord = new AuditRecord();
//                auditRecord.setRelevanceType(11);
//                auditRecord.setRelevanceId(id);
//                auditRecord.setResultType(2);
//                auditRecord.setAuditType(1);
//                auditRecord.setAuditResult("【拒绝】" + dto.getAuditResult());
//                auditRecordService.create(auditRecord);
//                byId.setState(3);
//                byId.setAuditOpenTime(new Date());
//                update(byId);
//            }
//        }
//    }

    /**
     * 审核交易服务费缴费
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(AuditDTO dto) {
        Integer isOpen = dto.getIsOpen();
        String id = dto.getId();
        PlatformDealFeeRecord byId = getById(id);
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        //（0待确认1确认中2确认成功3确认失败4审核中5审核通过6审核未通过）
        if (byId.getState() == PublicEnum.TRADE_FEE_STATUS_CONFIRMING.getCode()) {
            //确认中
            dto.setRelevanceType(12);//12交易服务费对账单确认审核
            if (isOpen != null && isOpen == 1) {
                //确认成功
                saveAuditRecord(dto);
                byId.setState(2);
                byId.setAuditOpenTime(new Date());
                Shop shop = shopService.getById(byId.getShopId());
                byId.setPaymentDeadline(calculateFiveWorkingDaysAfter(shop));
            } else {
                //确认失败
                saveAuditRecord(dto);
                byId.setState(3);
                byId.setAuditOpenTime(new Date());
            }
        } else if (byId.getState() == PublicEnum.TRADE_FEE_STATUS_AUDITING.getCode()) {
            //审核中
            dto.setRelevanceType(11);//11交易服务费缴费审核
            if (isOpen != null && isOpen == 1) {
                //审核通过
                saveAuditRecord(dto);
                byId.setState(5);
                byId.setAuditOpenTime(new Date());
                byId.setAuditorId(user.getUserId());
                byId.setAuditorName(user.getUserName());
            } else {
                //审核不通过
                saveAuditRecord(dto);
                byId.setState(6);
                byId.setAuditOpenTime(new Date());
                byId.setAuditorId(user.getUserId());
                byId.setAuditorName(user.getUserName());
            }
        }
        update(byId);
    }

    /**
     * 计算当日后N个工作日的日期
     */
    public LocalDate calculateFiveWorkingDaysAfter(Shop shop) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算5个工作日后的日期
        int addedDays = 0;
        // 获取店铺缴费天数（安全处理null值）
        Integer workingDaysToAdd = 0;
        // 首先检查店铺配置
        Integer shopPayDays = Optional.ofNullable(shop)
                .map(Shop::getPayDays)
                .orElse(null);
        if(shopPayDays != null && shopPayDays > 0) {
            // 使用店铺缴费截止天数
            workingDaysToAdd = shopPayDays;
        }else {
            // 使用系统全局配置
            List<SystemParam> params = systemParamService.listByCode(
                    PublicEnum.PlatformPaymentDeadlineDays.getRemark(), 1);
            if (params != null && !params.isEmpty() && params.get(0) != null){
                String paymentDeadlineDaysStr = params.get(0).getKeyValue();
                workingDaysToAdd = Integer.valueOf(paymentDeadlineDaysStr);
            } else {
                // 系统参数不存在时使用默认值
                workingDaysToAdd = 5;
                log.warn("未找到缴费截止天数配置，使用默认值: "+workingDaysToAdd);
            }
        }
        while (addedDays < workingDaysToAdd) {
            // 增加一天
            currentDate = currentDate.plusDays(1);
            // 检查是否是工作日（周一到周五）
            if (!(currentDate.getDayOfWeek() == DayOfWeek.SATURDAY ||
                    currentDate.getDayOfWeek() == DayOfWeek.SUNDAY)) {
                addedDays++;
            }
        }
        return currentDate;
    }

    /**
     * 保存审核信息
     *
     * @param dto
     */
    public void saveAuditRecord(AuditDTO dto) {
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setRelevanceId(dto.getId());
        auditRecord.setRelevanceType(dto.getRelevanceType()); // 设置关联类型
        auditRecord.setAuditType(1);
        if (dto.getIsOpen() != null && dto.getIsOpen() == 1) {
            auditRecord.setResultType(1);
            auditRecord.setAuditResult("【同意】");
        } else {
            auditRecord.setResultType(2);
            auditRecord.setAuditResult("【拒绝】" + dto.getAuditResult());
        }
        auditRecordService.create(auditRecord);
    }

    /**
     * 交易缴费审核通过后，判断是否欠费如果未欠费则清除欠费时间
     * freem8
     *
     * @param byId
     * @param platformDealFee
     * @param platformDealFeeId
     */
    private void checkIsFree(PlatformDealFeeRecord byId, PlatformDealFee platformDealFee, String platformDealFeeId) {
        QueryWrapper<PlatformDealFeeDtl> q = new QueryWrapper<>();
        q.eq("enterprise_id", byId.getEnterpriseId())
                .eq("serve_type", byId.getRecordType())
                .eq("finish_pay_fee", 0)
                .eq("state", 0)
                .select("sum(residue_pay_fee) as totalAmount");
        List<Map<String, Object>> maps = platformDealFeeDtlMapper.selectMaps(q);
        BigDecimal totalAmount = null;
        if (maps.get(0) != null) {
            totalAmount = new BigDecimal(maps.get(0).get("totalAmount").toString());
        } else {
            totalAmount = new BigDecimal(0);
        }
        // 如果没有欠费的金额
        if (totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            platformDealFeeService.lambdaUpdate().eq(PlatformDealFee::getPlatformDealFeeId, platformDealFeeId)
                    .set(PlatformDealFee::getArrearageDateTime, null).update();
        } else {
            // 还存在欠费金额
            EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, byId.getEnterpriseId())
                    .select(EnterpriseInfo::getArrearage, EnterpriseInfo::getArrearageDateNum, EnterpriseInfo::getArrearageDateType).one();
            BigDecimal arrearage = supplier.getArrearage();
            if (arrearage.compareTo(BigDecimal.ZERO) == 0) {
                // 不操作，还是欠费
            } else {
                // 判断是否超过可欠费额度
                if (totalAmount.compareTo(arrearage) == 1) {
                    // 不操作，还是欠费
                } else {
                    // 未超过欠费额度，判断时间
                    Integer arrearageDateNum = supplier.getArrearageDateNum();
                    Integer arrearageDateType = supplier.getArrearageDateType();
                    if (arrearageDateNum != null && platformDealFee.getArrearageDateTime() != null) {
                        // 说明设置了欠费时间，并且已经欠费
                        Date arrearageDateTime = platformDealFee.getArrearageDateTime();
                        int day = 0;
                        if (arrearageDateType == 1) {
                            day = arrearageDateNum;
                        } else if (arrearageDateType == 2) {
                            day = arrearageDateNum * 30;
                        } else if (arrearageDateType == 3) {
                            day = arrearageDateNum * 365;
                        } else {
                            throw new BusinessException("欠费时长时间类型错误！");
                        }
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(arrearageDateTime);
                        calendar.add(Calendar.DATE, day);
                        Date newDate = calendar.getTime();
                        Date currentDate = new Date();  // 获取当前日期
                        if (currentDate.after(newDate)) { // 当前时间在欠费截止时间之后
                        } else if (currentDate.before(newDate)) {
                            platformDealFeeService.lambdaUpdate().eq(PlatformDealFee::getPlatformDealFeeId, platformDealFeeId)
                                    .set(PlatformDealFee::getArrearageDateTime, null).update();
                        }
                    } else {
                        // 没有无限期
                        platformDealFeeService.lambdaUpdate().eq(PlatformDealFee::getPlatformDealFeeId, platformDealFeeId)
                                .set(PlatformDealFee::getArrearageDateTime, null).update();
                    }

                }
            }
        }
    }

    @Override
    @Transactional
    public void changeState(PlatformDealFeeRecord platformDealFeeRecord) {
        String id = platformDealFeeRecord.getDealFeeRecordId();
        PlatformFeeFile file = platformDealFeeRecord.getFiles().get(0);

        PlatformDealFeeRecord origin = getById(id);
        int state = origin.getState();
        int changeTo = platformDealFeeRecord.getState();
        // 只有 待确认 和 确认失败 可以变为 确认中
        if (changeTo == PublicEnum.TRADE_FEE_STATUS_CONFIRMING.getCode()) {
            if (state!=PublicEnum.TRADE_FEE_STATUS_WAIT_CONFIRM.getCode()&&state!=PublicEnum.TRADE_FEE_STATUS_CONFIRM_FAIL.getCode()) {
                throw new BusinessException("状态异常，请刷新页面重试");
            }
        // 只有 确认成功 和 审核未通过 可以变为 审核中
        }else if (changeTo == PublicEnum.TRADE_FEE_STATUS_AUDITING.getCode()) {
            if (state!=PublicEnum.TRADE_FEE_STATUS_CONFIRM_SUCCESS.getCode()&&state!=PublicEnum.TRADE_FEE_STATUS_AUDIT_REJECT.getCode()) {
                throw new BusinessException("状态异常，请刷新页面重试");
            }
            origin.setRemarks(platformDealFeeRecord.getRemarks());
        }
        int relevanceType = changeTo==PublicEnum.TRADE_FEE_STATUS_CONFIRMING.getCode()? 3:2;
        PlatformFeeFile platformFeeFile = PlatformFeeFile.empty();
        platformFeeFile.setFileFarId(file.getFileFarId());
        platformFeeFile.setName(file.getName());
        platformFeeFile.setRelevanceType(relevanceType);
        platformFeeFile.setUrl("");// 前端不需要这个字段
        origin.setState(changeTo);
        updateById(origin);
        platformFeeFile.setRelevanceId(id);
        // 一个交易服务费对应多个交易服务费缴费单或者交易服务费对账单，时间最近的代表当前缴费单或者对账单
        platformFeeFileService.save(platformFeeFile);
    }


}
