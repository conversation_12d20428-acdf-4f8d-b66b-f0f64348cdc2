package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.ErrorInfo;
import scrbg.meplat.mall.mapper.ErrorInfoMapper;
import scrbg.meplat.mall.service.ErrorInfoService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：异常信息记录表 服务类
 * @作者: ye
 * @日期: 2023-10-25
 */
@Service
public class ErrorInfoServiceImpl extends ServiceImpl<ErrorInfoMapper, ErrorInfo> implements ErrorInfoService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ErrorInfo> queryWrapper) {
        IPage<ErrorInfo> page = this.page(
        new Query<ErrorInfo>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ErrorInfo errorInfo) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(errorInfo);
    }

    @Override
    public void update(ErrorInfo errorInfo) {
        super.updateById(errorInfo);
    }


    @Override
    public ErrorInfo getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
