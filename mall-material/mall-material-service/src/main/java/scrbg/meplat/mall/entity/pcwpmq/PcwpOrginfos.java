package scrbg.meplat.mall.entity.pcwpmq;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * HR机构表实体类
 */
@Data
@TableName("pcwp_orginfos")
public class PcwpOrginfos {
    /**
     * 组织内码（主键，组织ID，区分大小写）
     */
    @TableField("id")
    private String id;

    /**
     * 组织名称
     */
    @TableField("name")
    private String name;

    /**
     * 组织编号
     */
    @TableField("number")
    private String number;

    /**
     * 父级id（区分大小写）
     */
    @TableField("parentid")
    private String parentid;

    /**
     * 类型（O：组织、D：部门）
     */
    @TableField("type")
    private String type;

    /**
     * 创建时间
     */
    @TableField("createtime")
    private String createtime;

    /**
     * 上次更新时间（最后更新时间）
     */
    @TableField("flastupdatetime")
    private String flastupdatetime;

    /**
     * 组织层级ID
     */
    @TableField("orglayertypeid")
    private String orglayertypeid;

    /**
     * 组织层级编码（1:集团 3:公司 4:站/办事处/事业部 7:项目部归集 8:集团直管大经理部 9:分子公司自管项目部 11:部门）
     */
    @TableField("orglayertypenumber")
    private String orglayertypenumber;

    /**
     * 组织层级名称
     */
    @TableField("orglayertypename")
    private String orglayertypename;

    /**
     * 项目部所属大经理部内码
     */
    @TableField("djlbid")
    private String djlbid;

    /**
     * 项目部所属大经理部编码
     */
    @TableField("djlbnumber")
    private String djlbnumber;

    /**
     * 项目部所属大经理部名称
     */
    @TableField("djlbname")
    private String djlbname;

    /**
     * 上级法定组织
     */
    @TableField("glzzid")
    private String glzzid;

    /**
     * 上级法定组织编码
     */
    @TableField("glzznumber")
    private String glzznumber;

    /**
     * 上级法定组织名称
     */
    @TableField("glzzname")
    private String glzzname;

    /**
     * 排序码
     */
    @TableField("sortcode")
    private String sortcode;

    /**
     * 是否启用（0是启用，1 是停用）
     */
    @TableField("issealup")
    private Integer issealup;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    @TableField("mdmstate")
    private Integer mdmstate;
}