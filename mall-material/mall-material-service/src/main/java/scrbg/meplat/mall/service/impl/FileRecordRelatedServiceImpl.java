package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.Brand;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.entity.FileRecordRelated;
import scrbg.meplat.mall.mapper.FileMapper;
import scrbg.meplat.mall.mapper.FileRecordRelatedMapper;
import scrbg.meplat.mall.service.FileRecordRelatedService;

import java.util.Map;

@Service
public class FileRecordRelatedServiceImpl extends ServiceImpl<FileRecordRelatedMapper, FileRecordRelated> implements FileRecordRelatedService {

    @Autowired
    FileRecordRelatedMapper fileRecordRelatedMapper;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<FileRecordRelated> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String relatedId = (String) innerMap.get("relatedId");
        if (relatedId != null) {
            queryWrapper.eq(FileRecordRelated::getRelatedId, relatedId);
        }
        IPage<FileRecordRelated> page = this.page(
                new Query<FileRecordRelated>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(FileRecordRelated recordRelated) {
        super.save(recordRelated);
    }
}
