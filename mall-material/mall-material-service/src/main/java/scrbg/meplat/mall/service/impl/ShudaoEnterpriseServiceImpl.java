package scrbg.meplat.mall.service.impl;

import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.dto.payment.SdExportVo;
import scrbg.meplat.mall.dto.payment.SdImportDto;
import scrbg.meplat.mall.dto.payment.SdImportResultVo;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.ShudaoEnterprise;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ShudaoEnterpriseMapper;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.ShudaoEnterpriseService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2024-01-29
 */

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-08-15
 */
@Service
public class ShudaoEnterpriseServiceImpl extends ServiceImpl<ShudaoEnterpriseMapper, ShudaoEnterprise> implements ShudaoEnterpriseService {
    @Resource
    private EnterpriseInfoService enterpriseInfoService;


    @Resource
    private ShopService shopService;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShudaoEnterprise> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        if (StringUtils.isNotBlank(keywords)){
            queryWrapper.like(ShudaoEnterprise::getEnterpriseName,keywords);
        }
        IPage<ShudaoEnterprise> page = this.page(
                new Query<ShudaoEnterprise>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ShudaoEnterprise shudaoEnterprise) {
        //调用父类方法即可
        //也可以baseMapper.insert
        if (StringUtils.isNotBlank(shudaoEnterprise.getEnterpriseName())){
            shudaoEnterprise.setEnterpriseName(shudaoEnterprise.getEnterpriseName().trim());
            enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseName,shudaoEnterprise.getEnterpriseName()).set(EnterpriseInfo::getShuDaoFlag,1).update();
            Integer count = lambdaQuery().eq(ShudaoEnterprise::getEnterpriseName, shudaoEnterprise.getEnterpriseName()).count();
            if (count > 0){
                throw new BusinessException("该蜀道企业已存在");
            }
        }
        super.save(shudaoEnterprise);
    }

    @Override
    public void update(ShudaoEnterprise shudaoEnterprise) {
        super.updateById(shudaoEnterprise);
    }


    @Override
    public ShudaoEnterprise getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        ShudaoEnterprise byId = getById(id);
        EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseName, byId.getEnterpriseName()).one();
        if (one!=null){
            shopService.lambdaUpdate().eq(Shop::getEnterpriseId,one.getEnterpriseId()).set(Shop::getShuDaoFlag,0).update();
            one.setShuDaoFlag(0);
            enterpriseInfoService.updateById(one);
        }

        // 真实删除
        baseMapper.deleteReal(id);
        //super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    @Override
    public void uploadSdExcelFile(MultipartFile file, HttpServletResponse response) {
        try {
            List<SdImportDto> sdImportDtos = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), SdImportDto.class);
            List<SdImportResultVo> resultVoList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(sdImportDtos)){
                for (SdImportDto sdImportDto : sdImportDtos) {
                    if (StringUtils.isBlank(sdImportDto.getEnterpriseName())){
                        SdImportResultVo vo = new SdImportResultVo();
                        vo.setFailReason("企业名称不可为空");
                        vo.setStatus("失败");
                        resultVoList.add(vo);
                        continue;
                    }
                    Integer count = super.lambdaQuery().eq(ShudaoEnterprise::getEnterpriseName, sdImportDto.getEnterpriseName()).count();
                    if (count>0){
                        SdImportResultVo vo = new SdImportResultVo();
                        vo.setEnterpriseName(sdImportDto.getEnterpriseName());
                        vo.setFailReason("该蜀道企业已存在");
                        vo.setStatus("失败");
                        resultVoList.add(vo);
                        continue;
                    }
                    // 保存数据
                    ShudaoEnterprise enterprise = new ShudaoEnterprise();
                    enterprise.setEnterpriseName(sdImportDto.getEnterpriseName());
                    enterprise.setAffiliationEnterprise(sdImportDto.getAffiliationEnterprise());
                    int category = 1;
                    if (sdImportDto.getEnterpriseCategory().contains("二")){
                        category = 2;
                    }
                    if (sdImportDto.getEnterpriseCategory().contains("三")){
                        category = 2;
                    }
                    enterprise.setEnterpriseCategory(category);
                    enterprise.setEnterpriseNature(sdImportDto.getEnterpriseNature());
                    enterprise.setAdjust(sdImportDto.getAdjust()!=null? sdImportDto.getAdjust() : null);
                    boolean b = super.saveOrUpdate(enterprise);
                    SdImportResultVo vo = new SdImportResultVo();
                    vo.setEnterpriseName(enterprise.getEnterpriseName());
                    vo.setAffiliationEnterprise(enterprise.getAffiliationEnterprise());
                    vo.setAdjust(enterprise.getAdjust());
                    vo.setEnterpriseNature(enterprise.getEnterpriseNature());
                    String cat = "";
                    if (enterprise.getEnterpriseCategory() == 1){
                        cat="一类";
                    }
                    if (enterprise.getEnterpriseCategory() == 2){
                        cat="二类";
                    }else {
                        cat="三类";
                    }
                    vo.setEnterpriseCategory(cat);
                    if (b){
                        vo.setStatus("成功");
                    }else {
                        vo.setStatus("失败");
                    }
                }
            }
            EasyExcelUtils.writeWeb2("蜀道企业导入结果",SdImportResultVo.class,resultVoList,"导入结果",response);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
        }

    }

    @Override
    public void exportShopPayExcelFile(JSONObject jsonObject, HttpServletResponse response) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        List<SdExportVo> list = new ArrayList<>();
        List<String> ids = (List<String>) innerMap.get("ids");
        List<ShudaoEnterprise> shudaoEnterprises = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ids)){
            // 根据ID查询导出
            List<ShudaoEnterprise> enterprises = super.baseMapper.selectBatchIds(ids);
            if (!CollectionUtils.isEmpty(enterprises)){
                shudaoEnterprises.addAll(enterprises);
            }
        }else {
            // 条件查询
            innerMap.put("limit",4000);
            PageUtils page = queryPage(jsonObject, new LambdaQueryWrapper<>());
            shudaoEnterprises.addAll(page.getList());
        }
        for (ShudaoEnterprise enterprise : shudaoEnterprises) {
            SdExportVo exportVo = new SdExportVo();
            exportVo.setEnterpriseName(enterprise.getEnterpriseName());
            exportVo.setAffiliationEnterprise(enterprise.getAffiliationEnterprise());
            Integer integer = enterprise.getEnterpriseCategory();
            if (integer == null){
                exportVo.setEnterpriseCategory("");
            }else {
                if (integer == 1){
                    exportVo.setEnterpriseCategory("一类");
                }
                if (integer == 2){
                    exportVo.setEnterpriseCategory("二类");
                }
                if (integer == 3){
                    exportVo.setEnterpriseCategory("三类");
                }else {
                }
            }

            //exportVo.setEnterpriseCategory(enterprise.getEnterpriseCategory());
            exportVo.setAdjust(enterprise.getAdjust());
            exportVo.setEnterpriseNature(enterprise.getEnterpriseNature());
            list.add(exportVo);
        }

        try {
            EasyExcelUtils.writeWeb2("蜀道企业",SdExportVo.class,list,"蜀道企业",response);
        } catch (IOException e) {
            throw new BusinessException("导出异常");
        }

    }
}
