package scrbg.meplat.mall.enums.product;

/**
 * <AUTHOR>
 * @create 2022-11-07 17:14
 */
public enum OrderEnum {


    RESULT_CODE_500200(500200,"订单保存失败！"),
    RESULT_CODE_500201(500201,"商品不存在，订单保存失败！"),
    RESULT_CODE_500202(500202,"商品规格不存在，订单保存失败！"),

    RESULT_CODE_500203(500203,"订单商品价格发生变化，请确认后再次提交！"),



    PAY_WAY_ON_LINE(1,"线上方式"),
    PAY_WAY_INTERIOR_SETTLEMENT(2,"内部结算"),

    /**
     * 交易类型
     */
    DEAL_TYPE_WD_WY(1,"外部店铺-外部用户"),
    DEAL_TYPE_ND_WU(2,"内部店铺-外部用户"),
    DEAL_TYPE_WD_NU(3,"外部店铺-内部用户"),
    DEAL_TYPE_ND_NU(4,"内部店铺-内部用户"),

    /**
     * 对内对外状态
     */
    ORDER_DEAL_TYPE_INSIDE(1,"内"),
    ORDER_DEAL_TYPE_OUTER(0,"外"),

    /**
     * 发票状态
     */
    ORDER_BILL_STATE_INIT(0,"初始"),
    ORDER_BILL_STATE_APPLY(1,"已申请"),
    ORDER_BILL_STATE_BILL(2,"已开票"),

    /**
     * 评价状态
     */
    ORDER_ITEM_NOT_COMMENT(0,"未评价"),
    ORDER_ITEM_YES_COMMENT(1,"已评价"),

    /**
     * 订单状态
     */
    ORDER_STATE_STAY_PAY(1,"待付款"),
    ORDER_STATE_STAY_DELIVER(2,"待发货"),
    ORDER_STATE_STAY_TAKE(3,"待收货"),
    ORDER_STATE_STAY_EVALUATE(4,"待评价"),
    ORDER_STATE_ACCOMPLISH(5,"已完成"),
    ORDER_STATE_CLOSE(6,"已关闭"),
    ORDER_STATE_SETTLEMENT(7,"结算中"),


    DISPATCH_STATE_UNASSIGNED(0,"订单未派单"),
    DISPATCH_STATE_ASSIGNED(1,"订单已派单"),


    STATE_DRAFT(0,"草稿"),
    STATE_SUBMIT(1,"已提交"),
    STATE_STAY_AFFIRM(2,"待确认"),
    STATE_AFFIRM(3,"已确认"),
    STATE_STAY_CONTRACT(4,"待签订合同"),
    STATE_ALREADY_CONTRACT(5,"已签订合同"),
//    STATE_FINISH(6,"已完成"),
    STATE_FINISH(6,"待发货"),
    STATE_CLOSE(7,"已关闭"),

    STATE_YES_FREIGHT(8,"已发货"),
    STATE_AWAIT_FREIGHT(9,"待收货"),
    STATE_OK(10,"已完成");

    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    OrderEnum() {
    }

    OrderEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
