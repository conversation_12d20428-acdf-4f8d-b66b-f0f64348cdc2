package scrbg.meplat.mall.controller.website.userCenter;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.entity.OrderReturn;
import scrbg.meplat.mall.service.OrderReturnService;
import scrbg.meplat.mall.vo.user.userCenter.OrderReturnVo;

/**
 * @描述：用户控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/userCenter/orderReturn")
@ApiSort(value = 200)
@Api(tags = "用户(订单退货)")
public class UserOrderReturnController {
    @Autowired
    public OrderReturnService orderReturnService;

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<OrderReturn> findById(String id){
        OrderReturn orderReturn = orderReturnService.getById(id);
        return R.success(orderReturn);
    }

    @PostMapping("/create")
    @ApiOperation(value = "订单项退货")
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public R save(@RequestBody OrderReturn orderReturn){
        orderReturnService.createText(orderReturn);
        return R.success();
    }

    @PostMapping("/createtext")
    @ApiOperation(value = "订单项退货")
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public R screatetextave(@RequestBody OrderReturn orderReturn){
        orderReturnService.createText(orderReturn);
        return R.success();
    }


    //        @PostMapping("/addDate")
//    @ApiOperation(value = "提交退货数据")
//    public R save(@RequestBody OrderReturnVo orderReturnVo){
//        orderReturnService.addDate(orderReturnVo);
//        return R.success();
//    }
    @PostMapping("/getOrderReturnPageList")
    @ApiOperation(value = "查询我的退货")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称,商品编号,订单号）", dataTypeClass = String.class),
            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "退货状态 1:已申请 2:退货中 3:退款成功 4:退款失败", dataTypeClass = Integer.class),
    })
    public R getOrderReturnPageList(@RequestBody JSONObject jsonObject){
        PageUtils page = orderReturnService.getUserOrderReturnPageList(jsonObject,new QueryWrapper<OrderReturn>());
        return R.success(page);
    }

//    @PostMapping("/getOrderReturnPageList")
//    @ApiOperation(value = "查询我的退货（更新2）")
//    @DynamicParameters(name = "根据实体属性分页查询", properties = {
//            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
//            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
//            @DynamicParameter(name = "keywords", value = "关键字（商品名称,商品编号,订单号）", dataTypeClass = String.class),
//            @DynamicParameter(name = "startDate", value = "创建时间开始时间", dataTypeClass = String.class),
//            @DynamicParameter(name = "endDate", value = "创建时间结束时间", dataTypeClass = String.class),
//            @DynamicParameter(name = "state", value = "退货状态 1:已申请 2:退货中 3:退款成功 4:退款失败", dataTypeClass = Integer.class),
//    })
//    public R getReturnOrderList(@RequestBody JSONObject jsonObject){
//        PageUtils page = orderReturnService.getReturnOrderList(jsonObject,new QueryWrapper<OrderReturnVo>());
//        return R.success(page);
//    }
}
