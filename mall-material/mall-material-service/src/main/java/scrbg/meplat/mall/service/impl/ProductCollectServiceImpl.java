package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.product.CollectEnum;
import scrbg.meplat.mall.mapper.ProductCollectMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.product.ProductCollectVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @描述：个人商品收藏 服务类
 * @作者: sund
 * @日期: 2022-11-10
 */
@Service
public class ProductCollectServiceImpl extends ServiceImpl<ProductCollectMapper, ProductCollect> implements ProductCollectService {

    @Autowired
    private MallConfig mallConfig;

    @Autowired
    private ProductCollectService productCollectService;

    @Autowired
    ShopService shopService;

    @Autowired
    ProductService productService;
    @Autowired
    ProductSkuService productSkuService;
    @Autowired
    ProductCollectMapper productCollectMapper;
    @Autowired
    DeviceDemandService deviceDemandService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductCollect> queryWrapper) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        IPage<ProductCollect> page = this.page(
                new Query<ProductCollect>().getPage(jsonObject),
                queryWrapper.eq(ProductCollect::getUserId, user.getUserId())
                        .eq(ProductCollect::getMallType,mallConfig.mallType)
        );
        List<ProductCollect> records = page.getRecords();
        List<ProductCollectVO> list = new ArrayList<>();
        if (records.size() == 0) return new PageUtils(page);
        List<ProductCollect> collectList = page.getRecords();


        for (ProductCollect productCollect : collectList) {
            ProductCollectVO vo = new ProductCollectVO();
            BeanUtils.copyProperties(productCollect, vo);
            vo.setCollectId(productCollect.getCollectId());
            vo.setProductId(productCollect.getProductId());
            Product product = productService.getById(productCollect.getProductId());
            if (product != null) {
                vo.setPictureUrl(product.getProductMinImg());
                vo.setTitle(product.getProductName());
                if (productCollect.getCollectType() == 1) {
                    LambdaQueryWrapper<ProductSku> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(ProductSku::getProductId, productCollect.getProductId());
                    List<ProductSku> skulist = productSkuService.list(wrapper);
                    if (skulist.size() > 0) {
                        ProductSku productSku = skulist.get(0);
                        vo.setPrice(productSku.getSellPrice());
                        vo.setRemain(productSku.getStock());
                        vo.setTitle(product.getProductName() + "/" + productSku.getSkuName());
                    }

                }
                list.add(vo);
            }

        }
        page.setRecords(null);
        PageUtils pageUtils = new PageUtils(page);
        pageUtils.setList(list);
        return pageUtils;
    }

    /**
     * Description:  根据userId进行新增
     */
    @Override
    public void create(ProductCollect productCollect) {
        // 用户ID
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        productCollect.setUserId(userId);
        productCollectService.save(productCollect);
    }

    @Override
    public void update(ProductCollect productCollect) {
        super.updateById(productCollect);
    }


    @Override
    public ProductCollect getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        //逻辑删除
        productCollectMapper.deleteBycollectId(id);

    }

    @Override
    public int selectCountByUserId(String userId, int countType) {
        LambdaQueryWrapper<ProductCollect> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCollect::getUserId, userId)
        .eq(ProductCollect::getMallType,mallConfig.mallType);
        wrapper.eq(ProductCollect::getCollectType, countType);
        int count = productCollectService.count(wrapper);
        return count;
    }

    @Override
    public List<Object> selectByUserId(String userId, int type) {
        LambdaQueryWrapper<ProductCollect> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCollect::getUserId, userId);
        wrapper.eq(ProductCollect::getCollectType, type)
                .eq(ProductCollect::getMallType,mallConfig.mallType);
        List<ProductCollect> productCollectList = productCollectService.list(wrapper);
        ArrayList arrayList = new ArrayList();
        for (ProductCollect productCollect : productCollectList) {
            arrayList.add(productCollect.getProductId());
        }
        if (type == CollectEnum.COLLECT_TYPE_SHOP.getCode()) {
            List<Object> shopList = shopService.listByIds(arrayList);
            return shopList;
        }
        if (type == CollectEnum.COLLECT_TYPE_PRODUCT.getCode()) {
            List<Object> productList = productService.listByIds(arrayList);
            return productList;
        }
        return null;
    }

    @Override
    public R addProductCollect(ProductCollect productCollect) {

        UserLogin user = ThreadLocalUtil.getCurrentUser();

        productCollect.setUserId(user.getUserId());
        LambdaQueryWrapper<ProductCollect> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCollect::getProductId, productCollect.getProductId());
        wrapper.eq(ProductCollect::getUserId, productCollect.getUserId())
                .eq(ProductCollect::getMallType,mallConfig.mallType);

        ProductCollect info = productCollectService.getOne(wrapper);
        productCollect.setFounderId(user.getUserId());
        //产品
        if (productCollect.getCollectType() == 1) {
            if (info == null) {
                Product product = productService.getById(productCollect.getProductId());
                productCollect.setProductId(product.getProductId());
                productCollect.setProductType(product.getProductType());
                productCollectService.save(productCollect);
            } else {
                productCollectService.delete(info.getCollectId());
                return R.success("", "取消收藏成功");
            }

        }
        //商铺
        if (productCollect.getCollectType() == 2) {
            if (info == null) {
                Shop shop = shopService.getById(productCollect.getProductId());
                productCollect.setProductId(shop.getShopId());
                productCollectService.save(productCollect);

            } else {
                productCollectService.delete(info.getCollectId());
                return R.success("", "取消收藏成功");
            }

        }
        //需求
        if (productCollect.getCollectType() == 3) {
            if (info == null) {
                DeviceDemand deviceDemand = deviceDemandService.getById(productCollect.getProductId());
                productCollect.setProductId(deviceDemand.getDemandId());
                productCollectService.save(productCollect);

            } else {
                productCollectService.delete(info.getCollectId());
                return R.success("", "取消收藏成功");
            }
        }
        return R.success("", "收藏成功");
    }

    @Override
    public List<Product> selUserColloctNum(JSONObject jsonObject) {
        UserLogin userInfo = ThreadLocalUtil.getCurrentUser();
        LambdaQueryWrapper<ProductCollect> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCollect::getUserId, userInfo.getUserId())
                .eq(ProductCollect::getMallType,mallConfig.mallType);

        Map<String, Object> innerMap = jsonObject.getInnerMap();

        Integer collectType = (Integer) innerMap.get("collectType");
        if (collectType == 1) {
            wrapper.eq(ProductCollect::getCollectType, innerMap.get("collectType"));
        }
        wrapper.select(ProductCollect::getProductId);


        List<ProductCollect> productCollectList = productCollectService.list(wrapper);
        List<String> productIdList = productCollectList.stream().map(item -> item.getProductId()).collect(Collectors.toList());
        List<Product> list = new ArrayList<>();
        if (productIdList.size() > 0) {
            //产品
            if (collectType == 1) {
                LambdaQueryWrapper<Product> productWrapper = new LambdaQueryWrapper<>();
                productWrapper.in(Product::getProductId, productIdList);
                list = productService.list(productWrapper);
                return list;
            }
//            //商铺
//            if (productCollect.getCollectType()==2){
//                List<String> productIdList = ProductCollectlist.stream().map(item -> item.getProductId()).collect(Collectors.toList());
//                LambdaQueryWrapper<Shop> productWrapper = new LambdaQueryWrapper<>();
//                productWrapper.in(Shop::getShopId, productIdList);
//                List<Shop> list = shopService.list(productWrapper);
////                return list;
//                return null;
//            }
//            if (productCollect.getCollectType()==3){
//                List<String> productIdList = ProductCollectlist.stream().map(item -> item.getProductId()).collect(Collectors.toList());
//                LambdaQueryWrapper<DeviceDemand> productWrapper = new LambdaQueryWrapper<>();
//                productWrapper.in(DeviceDemand::getDemandId, productIdList);
//                List<DeviceDemand> list = deviceDemandService.list(productWrapper);
////                return list;
//                return null;
//            }

        }
        return list;
    }

    @Override
    public ProductCollect selectByProductId(String productId) {
        LambdaQueryWrapper<ProductCollect> wrapper = new LambdaQueryWrapper<>();
        UserLogin userInfo = ThreadLocalUtil.getCurrentUser();
        wrapper.eq(ProductCollect::getUserId, userInfo.getUserId())
                .eq(ProductCollect::getProductId, productId)
                .eq(ProductCollect::getMallType,mallConfig.mallType);
        ProductCollect productCollect = productCollectService.getOne(wrapper);
        return productCollect;

    }

    @Override
    public Integer isCollect(ProductCollect productCollect) {
        UserLogin userInfo = ThreadLocalUtil.getCurrentUser();
        LambdaQueryWrapper<ProductCollect> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCollect::getUserId, userInfo.getUserId())
                .eq(ProductCollect::getMallType,mallConfig.mallType)
                .eq(ProductCollect::getCollectType,"1")
                .eq(ProductCollect::getProductId, productCollect.getProductId());
        ProductCollect info = productCollectService.getOne(wrapper);
        if (info == null) {
            return 0;
        } else {
            return 1;
        }

    }

    @Override
    public Integer getCollectNum(ProductCollect productCollect) {
        UserLogin userInfo = ThreadLocalUtil.getCurrentUser();
        LambdaQueryWrapper<ProductCollect> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCollect::getUserId, userInfo.getUserId())
                .eq(ProductCollect::getMallType,mallConfig.mallType)
                .eq(ProductCollect::getCollectType, productCollect.getCollectType())
                .select(ProductCollect::getProductId);
        int count = productCollectService.count(wrapper);
        return count;
    }

    @Override
    public void addBatch(List<String> productIdList) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        //获取已经收藏的商品信息
//        LambdaQueryWrapper<ProductCollect> wrapper = new LambdaQueryWrapper<>();
//        wrapper.in(ProductCollect::getProductId, productIdList);
//        wrapper.eq(ProductCollect::getUserId, user.getUserId())
//                .eq(ProductCollect::getMallType,mallConfig.mallType)
//                .eq(ProductCollect::getCollectType,1);
//        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("userId",user.getUserId());
//        hashMap.put("malltype",mallConfig.mallType);
        Integer CollectType=1;
        productCollectMapper.delCollectByProductIds(user.getUserId(),mallConfig.mallType,CollectType,productIdList);

//        List<ProductCollect> list = productCollectService.list(wrapper);
//        if (list.size()>0){
//            //去除已经收藏的商品信息，去除
//            List<String> collect = list.stream().map(item -> item.getCollectId()).collect(Collectors.toList());
//            //删除全部集合中的Id
//            productCollectMapper.deleBath(collect);
//        }
        //插入商品收藏信息
        List<Product> productList = productService.listByIds(productIdList);
        if (productList.size()>0){
            ArrayList<ProductCollect> productCollectList = new ArrayList<>();
            for (Product product : productList) {
                ProductCollect productCollect = new ProductCollect();
                productCollect.setProductId(product.getProductId());
                productCollect.setProductType(product.getProductType());
                productCollect.setCollectType(1);
                productCollect.setUserId(user.getUserId());
                productCollectList.add(productCollect);
            }
            //批量保存信息
            saveBatch(productCollectList);
        }

    }
    /**
     * 根据商品id查询当前登陆用户是否收藏
     * @param productId
     * @return
     */
    @Override
    public boolean getIsCollectByProductId(String productId) {
        Integer count = lambdaQuery().eq(ProductCollect::getProductId, productId).eq(ProductCollect::getUserId, ThreadLocalUtil.getCurrentUser().getUserId()).eq(ProductCollect::getMallType,mallConfig.mallType).count();
        if(count > 0){
            return true;
        }
        return false;
    }


}
