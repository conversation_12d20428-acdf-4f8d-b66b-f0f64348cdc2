package scrbg.meplat.mall.service.impl;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.entity.PlatformDealFeeDtl;
import scrbg.meplat.mall.entity.PlatformDealFeeRecord;
import scrbg.meplat.mall.entity.PlatformDealFeeRecordDtl;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.PlatformDealFeeRecordDtlMapper;
import scrbg.meplat.mall.service.PlatformDealFeeDtlService;
import scrbg.meplat.mall.service.PlatformDealFeeRecordDtlService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.service.PlatformDealFeeRecordService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @描述：平台交易费缴费记录明细 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
@Service
public class PlatformDealFeeRecordDtlServiceImpl extends ServiceImpl<PlatformDealFeeRecordDtlMapper, PlatformDealFeeRecordDtl> implements PlatformDealFeeRecordDtlService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<PlatformDealFeeRecordDtl> queryWrapper) {
        IPage<PlatformDealFeeRecordDtl> page = this.page(
                new Query<PlatformDealFeeRecordDtl>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(PlatformDealFeeRecordDtl platformDealFeeRecordDtl) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(platformDealFeeRecordDtl);
    }

    @Override
    public void update(PlatformDealFeeRecordDtl platformDealFeeRecordDtl) {
        super.updateById(platformDealFeeRecordDtl);
    }


    @Override
    public PlatformDealFeeRecordDtl getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    @Autowired
    PlatformDealFeeDtlService platformDealFeeDtlService;

    @Autowired
    PlatformDealFeeRecordService platformDealFeeRecordService;




    /**
     * 批量新增明细
     *
     * @param dtos
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreate(List<PlatformDealFeeRecordDtl> dtos) {
        BigDecimal amount = new BigDecimal(0);
        for (PlatformDealFeeRecordDtl dto : dtos) {
            amount = amount.add(dto.getPayAmount());
            save(dto);
            String platformDealFeeDtlId = dto.getPlatformDealFeeDtlId();
            PlatformDealFeeDtl byId = platformDealFeeDtlService.lambdaQuery().eq(PlatformDealFeeDtl::getPlatformDealFeeDtlId, platformDealFeeDtlId)
                    .select(PlatformDealFeeDtl::getPlatformDealFeeDtlId, PlatformDealFeeDtl::getPayFeeState, PlatformDealFeeDtl::getVersion).one();
            if(byId.getPayFeeState() != 0) {
                throw new BusinessException(500,"交易编号：" + byId.getRelevanceNu() + "，已生成缴费，不可重复生成！");
            }else {
                byId.setPayFeeState(1);
            }
            platformDealFeeDtlService.update(byId);
        }
        // 修改主表的总缴费金额
        PlatformDealFeeRecord byId = platformDealFeeRecordService.getById(dtos.get(0).getDealFeeRecordId());
        byId.setPayAmount(byId.getPayAmount().add(amount));
        if(byId.getState() == 2 || byId.getState() == 1) {
            throw new BusinessException(500,"拒绝新增！");
        }
        platformDealFeeRecordService.update(byId);

    }

    /**
     * 批量删除明细
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        BigDecimal amount = new BigDecimal(0);
        List<PlatformDealFeeRecordDtl> platformDealFeeRecordDtls = listByIds(ids);
        List<String> dealDtlIds = new ArrayList<>();
        for (PlatformDealFeeRecordDtl dtl : platformDealFeeRecordDtls) {
            amount = amount.add(dtl.getPayAmount());
            dealDtlIds.add(dtl.getPlatformDealFeeDtlId());
        }
        // 修改主表的总缴费金额
        PlatformDealFeeRecord byId = platformDealFeeRecordService.getById(platformDealFeeRecordDtls.get(0).getDealFeeRecordId());
        if(byId.getState() == 2 || byId.getState() == 1) {
            throw new BusinessException(500,"拒绝修改！");
        }
        byId.setPayAmount(byId.getPayAmount().subtract(amount));
        platformDealFeeRecordService.update(byId);

        // 修改缴费为初始
        platformDealFeeDtlService.lambdaUpdate()
                .in(PlatformDealFeeDtl::getPlatformDealFeeDtlId,dealDtlIds)
                .set(PlatformDealFeeDtl::getPayFeeState,0).update();


        deleteBatch(ids);
    }


}
