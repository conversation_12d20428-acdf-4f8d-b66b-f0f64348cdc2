package scrbg.meplat.mall.enums;

/**
 * <AUTHOR>
 * @create 2022-11-08 12:00
 */
public enum CodeEnum {

    /**
     * 店铺
     */
    CODE_DP(-1,"dp"),

    /**
     * 用户
     */
    CODE_YH(-1,"yh"),

    /**
     * 企业
     */
    CODE_QY(-1,"qy"),

    /**
     * 栏目
     */
    CODE_LM(-1,"lm"),

    /**
     * 商品
     */
    CODE_WZ(0,"wz"), // 物资
    CODE_ZB(1,"zb"), // 装备
    CODE_ES(4,"es"), // 二手装备
    CODE_ZL(5,"zl"), // 租赁设备
    CODE_WX(6,"wx"), // 维修服务
    CODE_JR(7,"jr"), // 金融服务
    CODE_BJ(8,"bj"); // 保险服务
    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    CodeEnum() {
    }

    CodeEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


}

