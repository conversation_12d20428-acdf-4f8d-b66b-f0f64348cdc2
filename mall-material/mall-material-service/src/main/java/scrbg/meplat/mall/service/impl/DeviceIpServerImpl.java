package scrbg.meplat.mall.service.impl;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.service.DeviceIpServer;
import scrbg.meplat.mall.service.UserService;
import scrbg.meplat.mall.util.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
public class DeviceIpServerImpl  implements DeviceIpServer {

    @Autowired
    MallConfig mallConfig;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private UserService userService;
    @Override
    public HashMap<String, Object> getUserInfo(String userId) {
        ArrayList<String> list = new ArrayList<>();
        list.add(userId);
        List<User> users= userService.getUserListByids(list);
        User user = users.get(0);
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        BeanUtils.copyProperties(user,stringObjectHashMap);
        return stringObjectHashMap;
    }

}
