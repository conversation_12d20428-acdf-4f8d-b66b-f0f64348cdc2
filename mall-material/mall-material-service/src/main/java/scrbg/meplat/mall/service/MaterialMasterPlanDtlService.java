package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.MaterialMasterPlanDtl;

/**
 * @描述：物资总计划明细 服务类
 * @作者: y
 * @日期: 2022-11-10
 */
public interface MaterialMasterPlanDtlService extends IService<MaterialMasterPlanDtl> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMasterPlanDtl> queryWrapper);

    void create(MaterialMasterPlanDtl materialMasterPlanDtl);

    void update(MaterialMasterPlanDtl materialMasterPlanDtl);

    MaterialMasterPlanDtl getById(String id);

    void delete(String id);
}
