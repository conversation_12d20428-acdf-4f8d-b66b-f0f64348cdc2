package scrbg.meplat.mall.adapter;

import com.scrbg.common.utils.UserInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import scrbg.meplat.mall.interceptor.CheckTokenInterceptor;
import scrbg.meplat.mall.interceptor.CheckTokenInterceptor1;

@Configuration
public class ServerConfigurerAdapter implements WebMvcConfigurer {

    @Autowired
    private CheckTokenInterceptor checkTokenInterceptor;

    @Autowired
    private CheckTokenInterceptor1 checkTokenInterceptor1;
    @Bean
    public HandlerInterceptor userInterceptor() {
        return new UserInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //registry.addInterceptor(userInterceptor());
        registry.addInterceptor(checkTokenInterceptor1)
                        .addPathPatterns("/**");
//        // 拦截
        registry.addInterceptor(checkTokenInterceptor)
                .addPathPatterns("/**")
//                .excludePathPatterns("/**")
                // 排除
                .excludePathPatterns("/w/**")
                //.excludePathPatterns("/outer/usedDevice/**")//这是装备业务，可以删除掉
                // 排除文档路径
                .excludePathPatterns("/doc.html","/webjars/**","/swagger-resources","/error","/favicon.ico");
    }
}
