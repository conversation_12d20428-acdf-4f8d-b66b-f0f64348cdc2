package scrbg.meplat.mall.controller.website;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.common.redis.RedisKey;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.enums.product.ProductCategoryEnum;
import scrbg.meplat.mall.service.ProductCategoryService;
import scrbg.meplat.mall.util.R;

import java.util.List;

/**
 * @描述：商品分类控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/w/category")
@ApiSort(value = 100)
@Api(tags = "商品分类（前台）")
public class WebsiteProductCategoryController {
    @Autowired
    public ProductCategoryService productCategoryService;

    // ----------------------------通用------------------------------------------------------------------------------
    @GetMapping("/listTree")
    @ApiOperation(value = "获取分类树（通用）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productType", value = "分类类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备",
                    dataType = "Integer", paramType = "query", required = true),
            @ApiImplicitParam(name = "mallType", value = "商城类型：0物资商场, 1设备商城 ",
                    dataType = "Integer", paramType = "query", required = true),
    })
//    @Cacheable(value = {RedisKey.category}, key = "#productType + '_' + #mallType + '_getTree'",cacheResolver = "redisExpireCacheResolver")
//    @CacheExpire(ttl = 20,unit= TimeUnit.SECONDS)
    @Cacheable(value = {RedisKey.CATEGORY}, key = "#productType + '_' + #mallType + '_getTree'")
    public R<List<ProductCategory>> getTree(Integer productType, Integer mallType) {
        List<ProductCategory> entities = productCategoryService.listWithTree(productType, null, ProductCategoryEnum.TYPE_OPEN.getCode(), null,0);
        return R.success(entities);
    }

    @GetMapping("/getLevelOne")
    @ApiOperation(value = "获取一级分类（通用）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productType", value = "分类类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备",
                    dataType = "Integer", paramType = "query", required = true),
    })
    public R<List<ProductCategory>> getLevelOne(Integer productType) {
        List<ProductCategory> entities = productCategoryService.getLevelOne(productType);
        return R.success(entities);
    }


    @GetMapping("/testClass")
    @ApiOperation(value = "测试分布式事务")
    public R testClass() {
        productCategoryService.testClass();
        return R.success();
    }

}

