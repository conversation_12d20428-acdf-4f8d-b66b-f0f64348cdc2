package scrbg.meplat.mall.util;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.vo.product.material.PlatformMaterialListVO;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-08-21 10:09
 */
public class pageUtils {

    public static void pageDispose(JSONObject jsonObject,int total) {
        Integer page = (Integer) jsonObject.get("page");
        if(page == null) {
            page = 1;
        }
        Integer limit = (Integer) jsonObject.get("limit");
        if(limit == null) {
            limit = 20;
        }
        int totalPages = (int) Math.ceil((double) total / limit);
        if (totalPages == 0) {
            page = 1;
        } else if (page > totalPages) {
            page = totalPages;
        }
        jsonObject.put("page",page);
        jsonObject.put("limit",limit);
    }
}
