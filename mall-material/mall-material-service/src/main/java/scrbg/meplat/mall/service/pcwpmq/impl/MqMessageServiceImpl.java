package scrbg.meplat.mall.service.pcwpmq.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.dto.pcwpmq.*;
import scrbg.meplat.mall.entity.pcwpmq.PcwpPersonPermissions;
import scrbg.meplat.mall.mapper.pcwpmq.MqMessageMapper;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.service.pcwpmq.MqMessageService;

import java.util.*;

@Service
//@Log4j2
public class MqMessageServiceImpl implements MqMessageService {

    @Autowired
    private MqMessageMapper mqMessageMapper;

    @Autowired
    private MqMessageService mqMessageService;

    /**
     * 固定token
     */
    @Value("${mall.thirdApiToken}")
    private String token;

    @Autowired
    private PcwpService pcwpService;

    /**
     * 系统编码
     */
    private static final String sysCode = "pcwp2";

    private static final String org = "%7B%22orgId%22%3A%22af1f01246cc6-ab00-084c-79e7-76f6022e%22%2C%22shortCode%22%3A%22LQZB%22%2C%22orgName%22%3A%22%E5%9B%9B%E5%B7%9D%E8%B7%AF%E6%A1%A5%E5%BB%BA%E8%AE%BE%E9%9B%86%E5%9B%A2%E8%82%A1%E4%BB%BD%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%E8%A3%85%E5%A4%87%E6%8A%80%E6%9C%AF%E6%9C%8D%E5%8A%A1%E5%88%86%E5%85%AC%E5%8F%B8%22%7D";


    private static final Set<String> ALLOWED_TABLES = new HashSet<>(Arrays.asList(
            "pcwp_zcinfos", "pcwp_zyzginfos", "pcwp_tt_account",
            "pcwp_rzjlinfos", "pcwp_project", "pcwp_personinfos",
            "pcwp_person_permissions", "pcwp_orginfos", "pcwp_org",
            "pcwp_grhjinfos", "pcwp_conorg"
    ));

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMQdata(MqMessageDTO mqMessageDTO) {
        String tableName = null;
        PCWPMQ pcwpmq = null;
        switch (mqMessageDTO.getType()){
            case "Personinfo_Full":
                pcwpmq = new PcwpPersoninfos2DTO();
                tableName = "pcwp_personinfos";
                return processPersonInfo(mqMessageDTO,pcwpmq,tableName);
            case "Zcinfo_Full":
                pcwpmq = new PcwpZcinfos2DTO();
                tableName = "pcwp_zcinfos";
                return processZcinfo(mqMessageDTO,pcwpmq,tableName);
            case "Zyzginfo_Full":
                pcwpmq = new PcwpZyzginfos2DTO();
                tableName = "pcwp_zyzginfos";
                return processZyzginfo(mqMessageDTO,pcwpmq,tableName);
            case "Grhjinfo_Full":
                pcwpmq = new PcwpGrhjinfos2DTO();
                tableName = "pcwp_grhjinfos";
                return processGrhjinfo(mqMessageDTO,pcwpmq,tableName);
            case "Rzjlinfo_Full":
                pcwpmq = new PcwpRzjlinfos2DTO();
                tableName = "pcwp_rzjlinfos";
                return processRzjlinfo(mqMessageDTO,pcwpmq,tableName);
            case "Orginfo_Full":
                pcwpmq = new PcwpOrginfos2DTO();
                tableName = "pcwp_orginfos";
                return processOrginfo(mqMessageDTO,pcwpmq,tableName);
            case "Sys_Org_Full":
                pcwpmq = new PcwpOrg2DTO();
                tableName = "pcwp_org";
                return processSysOrginfo(mqMessageDTO,pcwpmq,tableName);
            case "Sys_ConOrg_Full":
                pcwpmq = new PcwpConorg2DTO();
                tableName = "pcwp_conorg";
                return processSysConOrginfo(mqMessageDTO,pcwpmq,tableName);
            case "Project_Project_Full":
                pcwpmq = new PcwpProject2DTO();
                tableName = "pcwp_project";
                return processProjectinfo(mqMessageDTO,pcwpmq,tableName);
            case "TTAccount_Full":
                pcwpmq = new PcwpTtAccount2DTO();
                tableName = "pcwp_tt_account";
                return processTTAccountinfo(mqMessageDTO,pcwpmq,tableName);
        }
        return 0;
    }

    @Override
    public int updateMQdataInterval() {
        List<MqMessageDTO> list = mqMessageMapper.getLastestMqData();
        for(MqMessageDTO mqMessageDTO:list){
            try{
                int status = mqMessageService.updateMQdata(mqMessageDTO);
                if(status > 0){
                    //log.info("更新"+mqMessageDTO.getType()+"队列数据成功");
                }else{
                    //log.info("更新"+mqMessageDTO.getType()+"队列数据失败");
                }
            }catch (Exception e){
                //log.error(e.getMessage());
            }
        }
        return 1;
    }



    private int processTTAccountinfo(MqMessageDTO mqMessageDTO, PCWPMQ pcwpmq, String tableName) throws Exception {
        return univelProcess(mqMessageDTO,pcwpmq,tableName);
    }

    private int processProjectinfo(MqMessageDTO mqMessageDTO, PCWPMQ pcwpmq, String tableName) throws Exception {
        return univelProcess(mqMessageDTO,pcwpmq,tableName);
    }

    private int processSysConOrginfo(MqMessageDTO mqMessageDTO, PCWPMQ pcwpmq, String tableName) throws Exception {
        return univelProcess(mqMessageDTO,pcwpmq,tableName);
    }

    private int processSysOrginfo(MqMessageDTO mqMessageDTO, PCWPMQ pcwpmq, String tableName) throws Exception {
        return univelProcess(mqMessageDTO,pcwpmq,tableName);
    }

    private int processOrginfo(MqMessageDTO mqMessageDTO, PCWPMQ pcwpmq, String tableName) throws Exception {
        return univelProcess(mqMessageDTO,pcwpmq,tableName);
    }

    private int processRzjlinfo(MqMessageDTO mqMessageDTO, PCWPMQ pcwpmq, String tableName) throws Exception {
        return univelProcess(mqMessageDTO,pcwpmq,tableName);
    }

    private int processGrhjinfo(MqMessageDTO mqMessageDTO, PCWPMQ pcwpmq, String tableName) throws Exception {
        return univelProcess(mqMessageDTO,pcwpmq,tableName);
    }

    private int processZyzginfo(MqMessageDTO mqMessageDTO, PCWPMQ pcwpmq, String tableName) throws Exception {
        return univelProcess(mqMessageDTO,pcwpmq,tableName);
    }

    private int processZcinfo(MqMessageDTO mqMessageDTO, PCWPMQ pcwpmq, String tableName) throws Exception {
        return univelProcess(mqMessageDTO,pcwpmq,tableName);
    }

    private int processPersonInfo(MqMessageDTO mqMessageDTO, PCWPMQ pcwpmq, String tableName) throws Exception {
        return univelProcess(mqMessageDTO,pcwpmq,tableName);
    }

    private int univelProcess(MqMessageDTO mqMessageDTO, PCWPMQ pcwpmq, String tableName) throws Exception {
        boolean jsonArray = getData(mqMessageDTO, tableName, pcwpmq);
        if(!jsonArray){
            return 0;
        }
        deleteUnUseData(mqMessageDTO);
        return 1;
    }

    private void batchInser(JSONArray jsonArray, PCWPMQ pcwpmq) {
        int batchSize = 500; // 推荐500-1000条/批次
        int totalCount = jsonArray.size();
        for (int i = 0; i < totalCount; i += batchSize) {
            int currentBatchSize = Math.min(batchSize, totalCount - i);
            List<PCWPMQ> batchList = new ArrayList<>(currentBatchSize);
            // 填充当前批次数据
            for (int j = 0; j < currentBatchSize; j++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i + j);
                PCWPMQ dto = JSONObject.toJavaObject(jsonObject, pcwpmq.getClass());
                batchList.add(dto);
            }
            // 执行批量插入
            if (!batchList.isEmpty()) {
                if(pcwpmq instanceof PcwpPersoninfos2DTO){
                    mqMessageMapper.batchInsertPcwpPersoninfos(batchList);
                }else if(pcwpmq instanceof PcwpZcinfos2DTO){
                    mqMessageMapper.batchInsertPcwpZcinfos(batchList);
                }else if(pcwpmq instanceof PcwpZyzginfos2DTO){
                    mqMessageMapper.batchInsertPcwpZyzginfos(batchList);
                }else if(pcwpmq instanceof PcwpGrhjinfos2DTO){
                    mqMessageMapper.batchInsertPcwpGrhjinfos(batchList);
                }else if(pcwpmq instanceof PcwpRzjlinfos2DTO){
                    mqMessageMapper.batchInsertPcwpRzjlinfos(batchList);
                }else if(pcwpmq instanceof PcwpOrginfos2DTO){
                    mqMessageMapper.batchInsertPcwpOrginfos(batchList);
                }else if(pcwpmq instanceof PcwpConorg2DTO){
//                    mqMessageMapper.batchInsert(batchList);
                }else if(pcwpmq instanceof PcwpProject2DTO){
//                    mqMessageMapper.batchInsert(batchList);
                }else if(pcwpmq instanceof PcwpTtAccount2DTO){
                    mqMessageMapper.batchInsertPcwpTtAccount(batchList);
                }
            }
        }
    }

    private void deleteTableData(String tableName) throws Exception {
        if(ALLOWED_TABLES.contains(tableName)){
            mqMessageMapper.deleteTableInfo(tableName);
        }else{
            //log.error("非法表操作");
            throw new Exception("非法表操作");
        }
    }

    private void deleteUnUseData(MqMessageDTO mqMessageDTO) {
        mqMessageMapper.updateStatus(mqMessageDTO.getType());
        mqMessageMapper.deleteUnUseDada(mqMessageDTO.getDeliveryTag(),mqMessageDTO.getType());
    }

    private boolean getData(MqMessageDTO mqMessageDTO, String tableName, PCWPMQ pcwpmq) throws Exception {
        int currentPatch = 1;
        int num = 0;
        while(true) {
            List<MqMessageDTO> batchList = mqMessageMapper.selectUnconsumedBydeliveryTag(
                    mqMessageDTO.getDeliveryTag(),
                    mqMessageDTO.getType(),
                    currentPatch);
            if(batchList.isEmpty()) {
                break;
            }
            MqMessageDTO dto = batchList.get(0);
            if("".equals(dto.getJsonText()) || dto.getJsonText() == null){
                continue;
            }
            currentPatch = dto.getPatch() + 1;
            JSONArray patchArray = JSONArray.parseArray(dto.getJsonText());
            if(pcwpmq != null){
                if(num == 0){//表只删除一次就行
                    deleteTableData(tableName);
                    num++;
                }
                batchInser(patchArray,pcwpmq);
            }
        }
        return currentPatch>1;//代表数据库中有未消化的队列数据
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updatePCWPCloudCenterdataInterval() throws Exception {
        int batchSize = 1000; // 每批数据量
        int totalProcessed = 0; // 总处理记录数
        long startTime = System.currentTimeMillis();
        try {
            // 初始请求参数
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("userName", "");
            requestParams.put("orgId", "");
            requestParams.put("orgName", "");
            requestParams.put("userId", "");
            requestParams.put("page", 1);
            requestParams.put("limit", batchSize);

            // 获取第一页数据以确定总量
            Map<String, Object> firstPageResponse = pcwpService
                    .getPersonPermissons(requestParams, token, sysCode, org)
                    .getData();

            validateResponse(firstPageResponse);

            deleteTableData("pcwp_person_permissions");

            JSONObject firstPageData = new JSONObject(firstPageResponse);
            int totalCount = firstPageData.getInteger("totalCount");
            int totalPage = firstPageData.getInteger("totalPage");
            //log.info("开始同步数据，总量：{}条，共{}页", totalCount, totalPage);
            // 处理第一页数据
            totalProcessed += processBatch(firstPageData.getJSONArray("list"));

            // 处理剩余页数据
            for (int currentPage = 2; currentPage <= totalPage; currentPage++) {
                requestParams.put("page", currentPage);

                Map<String, Object> response = pcwpService
                        .getPersonPermissons(requestParams, token, sysCode, org)
                        .getData();

                validateResponse(response);

                JSONObject pageData = new JSONObject(response);
                totalProcessed += processBatch(pageData.getJSONArray("list"));

                // 每处理10页打印一次进度
                if (currentPage % 10 == 0) {
                    //log.info("已处理 {}/{} 页，{}% 完成",currentPage, totalPage,(currentPage * 100 / totalPage));
                }
            }

            long elapsedTime = (System.currentTimeMillis() - startTime) / 1000;
            //log.info("数据同步完成！共处理 {} 条记录，耗时 {} 秒",totalProcessed, elapsedTime);

            return totalProcessed;
        } catch (Exception e) {
            //log.error("数据同步过程中发生异常", e);
            throw e;
        }
    }
    /**
     * 处理单批数据并返回处理记录数
     */
    private int processBatch(JSONArray batchData) {
        if (batchData == null || batchData.isEmpty()) {
            return 0;
        }

        List<PcwpPersonPermissions> batchList = new ArrayList<>(batchData.size());
        for (int i = 0; i < batchData.size(); i++) {
            JSONObject jsonObject = batchData.getJSONObject(i);
            PcwpPersonPermissions dto = JSONObject.toJavaObject(jsonObject, PcwpPersonPermissions.class);
            batchList.add(dto);
        }
        // 批量插入数据库
        return mqMessageMapper.batchInsertPcwpPersonPermissions(batchList);
    }

    /**
     * 验证接口响应是否有效
     */
    private void validateResponse(Map<String, Object> response) throws Exception {
        if (response == null) {
            throw new Exception("接口异常，未返回任何数据");
        }
//
//        Integer code = (Integer) response.get("code");
//        if (code == null || code != 200) {
//            String message = (String) response.get("message");
//            throw new Exception("接口异常，错误码：" + code + "，错误信息：" + message);
//        }
//
//        if (response.get("data") == null) {
//            throw new Exception("接口返回数据格式异常，缺少data字段");
//        }
    }
}
