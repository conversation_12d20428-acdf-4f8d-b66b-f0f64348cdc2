package scrbg.meplat.mall.controller.stockManage.vo;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OutboundSettlementVO implements Serializable {

    private String relevanceName;

    private String productName;

    private String spec;

    private String unit;

    private BigDecimal quantity;

    private BigDecimal xsPrice;

    private String purchasingOrgName;

    private BigDecimal xsTotalAmount;

    private BigDecimal xsNoRateAmount;

    private BigDecimal cgPrice;

    private BigDecimal cgTotalAmount;

    private BigDecimal cgNoRateAmount;

    private String supplierName;
}
