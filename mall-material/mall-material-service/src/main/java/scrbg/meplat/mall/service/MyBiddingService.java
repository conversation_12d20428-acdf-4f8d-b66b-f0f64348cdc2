package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.dto.bidding.BidOfferVo;
import scrbg.meplat.mall.dto.bidding.BidRowInfo;
import scrbg.meplat.mall.entity.BiddingBidRecord;
import scrbg.meplat.mall.entity.BiddingPurchase;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
public interface MyBiddingService {
    PageUtils queryMyBidList(JSONObject jsonObject);

    /***
     * 查询我的报价数据
     */
    BidOfferVo queryMyOfferPrice(String bidId);

    void udateMyOfferPrice(JSONObject jsonObject,String biddingSn);

    void exportBidLetterExcel(String biddingSn, HttpServletResponse response);

    void saveFileRecord();

    /***
     * 保存附件数据
     * 更改竞价状态
     * @param biddingRecordId
     * @param jsonObject
     */
    void saveFileAndStatus(String biddingRecordId, JSONObject jsonObject);

    /**
     * 根据竞价编号和商品ID获取当前行数据明细
     * @param jsonObject
     * @return 商品整行明细数据
     */
    BidRowInfo getProductInfo(JSONObject jsonObject);

    /**
     * 临购报价接口
     * @param jsonObject
     * @param biddingSn
     */

    void udateMyLgOfferPrice(JSONObject jsonObject, String biddingSn);

    /**
     * 处理新的报价数据
     * @param purchase
     * @param jsonArray
     * @param biddingSn
     */
    public void handleNewBidRecord(BiddingPurchase purchase, JSONObject jsonObject, String biddingSn);

    /**
     * 处理已存在的报价数据
     * @param jsonObject
     * @param biddingBidRecord
     */
    public void handleExistingBidRecord(BiddingPurchase purchase,JSONObject jsonObject, BiddingBidRecord biddingBidRecord);



}
