package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.CategoryBrandRelation;

/**
 * @描述：品牌分类关联 服务类
 * @作者: y
 * @日期: 2022-11-10
 */
public interface CategoryBrandRelationService extends IService<CategoryBrandRelation> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<CategoryBrandRelation> queryWrapper);

    void create(CategoryBrandRelation categoryBrandRelation);

    void update(CategoryBrandRelation categoryBrandRelation);

    CategoryBrandRelation getById(String id);

    void delete(String id);
}
