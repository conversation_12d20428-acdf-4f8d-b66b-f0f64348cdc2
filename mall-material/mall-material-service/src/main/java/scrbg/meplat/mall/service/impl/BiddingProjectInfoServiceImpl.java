package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.BiddingProjectInfo;
import scrbg.meplat.mall.mapper.BiddingProjectInfoMapper;
import scrbg.meplat.mall.service.BiddingProjectInfoService;

/**
 * @描述：招标项目信息 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
@Service
public class BiddingProjectInfoServiceImpl extends ServiceImpl<BiddingProjectInfoMapper, BiddingProjectInfo> implements BiddingProjectInfoService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingProjectInfo> queryWrapper) {
        IPage<BiddingProjectInfo> page = this.page(
                new Query<BiddingProjectInfo>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(BiddingProjectInfo biddingProjectInfo) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingProjectInfo);
    }

    @Override
    public void update(BiddingProjectInfo biddingProjectInfo) {
        super.updateById(biddingProjectInfo);
    }


    @Override
    public BiddingProjectInfo getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
