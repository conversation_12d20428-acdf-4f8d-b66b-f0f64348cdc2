package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.entity.Floor;
import scrbg.meplat.mall.service.FloorService;

import java.util.List;

/**
 * @描述：客户端商品展示楼层控制类
 * @作者: 胡原武
 * @日期: 2022-11-10
 */
@RestController
@RequestMapping("/platform/floor")
@ApiSort(value = 500)
@Api(tags = "商品展示楼层（后台）")
public class FloorController {

    @Autowired
    public FloorService floorService;

    /**
     * 查询全部数据并分页
     *
     * @param jsonObject
     * @return
     */
    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "columnId", value = "栏目id", dataTypeClass = String.class),
            @DynamicParameter(name = "floorName", value = "楼层名称", dataTypeClass = String.class),
            @DynamicParameter(name = "columnName", value = "栏目名称", dataTypeClass = String.class),
            @DynamicParameter(name = "floorNameText", value = "小标题", dataTypeClass = String.class),
            @DynamicParameter(name = "floorProductType", value = "商品类别", dataTypeClass = String.class),
            @DynamicParameter(name = "mallType", value = "商城类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "usePage", value = "使用页面", dataTypeClass = Integer.class),
            @DynamicParameter(name = "state", value = "显示状态", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<Floor> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = floorService.queryPage(jsonObject, new LambdaQueryWrapper<Floor>());
        return PageR.success(page);
    }


    @PostMapping("/updateByPublish")
    @ApiOperation(value = "批量发布")
    public R updatePublish(@RequestBody List<String> ids) {
        floorService.updateByPublish(ids, "1");
        return R.success();
    }

    @PostMapping("/updateNotPublish")
    @ApiOperation(value = "批量取消发布")
    public R updateNotPublish(@RequestBody List<String> ids) {
        floorService.updateByPublish(ids, "0");
        return R.success();
    }

    /**
     * 批量更新栏目信息
     *
     * @param
     * @return
     */
    @PostMapping("/updateBatchById")
    @ApiOperation(value = "批量更新栏目信息")
    public R update(@RequestBody List<Floor> floorList) {
        floorService.updateBatchById(floorList);
        return R.success();
    }

    /**
     * 新增一条信息
     *
     * @param floor
     * @return
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增楼层")
    public R save(@RequestBody Floor floor) {
        floor.setState(2);
        floorService.create(floor);
        return R.success();
    }

    /**
     * 根据主键修改相关信息
     *
     * @param floor
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "通过Id修改相关楼层信息")
    public R update(@RequestBody Floor floor) {
        floorService.update(floor);
        return R.success();
    }

    /**
     * 根据主键修改相关信息
     *
     * @param id
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation(value = "通过Id修改相关楼层信息")
    public R update(@RequestBody String id) {
        floorService.removeById(id);
        return R.success();
    }

    /**
     * 根据主键批量删除
     *
     * @param ids
     * @return
     */

    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除楼层")
    public R deleteBatch(@RequestBody List<String> ids) {
        floorService.removeByIds(ids);
        return R.success();
    }


}

