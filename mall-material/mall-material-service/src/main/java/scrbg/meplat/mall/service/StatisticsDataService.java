package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.StatisticsData;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.StatisticsData;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
/**
 * @描述： 物资统计服务类
 * @作者: ye
 * @日期: 2023-07-07
 */
public interface StatisticsDataService extends IService<StatisticsData> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<StatisticsData> queryWrapper);

        void create(StatisticsData statisticsData);

        void update(StatisticsData statisticsData);

        StatisticsData getById(String id);

        void delete(String id);
}
