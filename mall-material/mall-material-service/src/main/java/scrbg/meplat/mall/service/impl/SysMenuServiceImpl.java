package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.SysMenu;
import scrbg.meplat.mall.mapper.SysMenuMapper;
import scrbg.meplat.mall.service.SysMenuService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：菜单表 服务类
 * @作者: ye
 * @日期: 2023-12-21
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysMenu> queryWrapper) {
        IPage<SysMenu> page = this.page(
        new Query<SysMenu>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(SysMenu sysMenu) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(sysMenu);
    }

    @Override
    public void update(SysMenu sysMenu) {
        super.updateById(sysMenu);
    }


    @Override
    public SysMenu getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


        }
