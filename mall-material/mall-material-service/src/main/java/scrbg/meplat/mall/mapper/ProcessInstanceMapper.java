package scrbg.meplat.mall.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import scrbg.meplat.mall.entity.ProcessInstance;

/**
 * 流程实例表 Mapper 接口
 * <AUTHOR>
 * @date: 2025年6月20日 上午9:47:33
 */
@Mapper
@Repository
public interface ProcessInstanceMapper extends BaseMapper<ProcessInstance> {
    /**
     * 根据业务表主键 id获取流程实例
     *
     * @param businessKey
     * @return
     */
    ProcessInstance getProcessInstanceByKey(@Param("businessKey") String businessKey);
}