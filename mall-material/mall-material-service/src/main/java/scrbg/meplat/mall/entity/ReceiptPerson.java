package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：
 * @作者: ye
 * @日期: 2023-07-13
 */
@ApiModel(value="收料人员表")
@Data
@TableName("receipt_person")
public class ReceiptPerson extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "收料人员id")
    private String receiptPersonId;

    @ApiModelProperty(value = "姓名")

    private String name;


    @ApiModelProperty(value = "电话")

    private String phone;


    @ApiModelProperty(value = "wx的openId")

    private String openId;


    @ApiModelProperty(value = "本地机构名称")

    private String localOrgName;


    @ApiModelProperty(value = "本地机构id")

    private String localOrgId;
















    @ApiModelProperty(value = "状态（0停用1启用）")

    private Integer state;

    /***
     * 微信小程序返回的token段
     */
    @TableField(exist = false)
    private String token;





}