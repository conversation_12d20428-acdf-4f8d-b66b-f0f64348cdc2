package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：内容目录表
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "内容目录表")
@Data
@TableName("content_catalogue")
public class ContentCatalogue extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "内容目录id")

    private String catalogueId;

    @ApiModelProperty(value = "名称")

    private String name;

    @ApiModelProperty(value = "类型")

    private String type;

    @ApiModelProperty(value = "状态")

    private Integer state;

    @ApiModelProperty(value = "注解")

    private String comments;


    @ApiModelProperty(value = "程序代码")

    private String programaCode;

    @ApiModelProperty(value = "是否为主页")

    private Integer isHome;

    @ApiModelProperty(value = "是否为导航")

    private Integer isNavigation;

    @ApiModelProperty(value = "页面地址")

    private String pageUrl;

    @ApiModelProperty(value = "父页id")

    private String parentId;


}
