package scrbg.meplat.mall.entity.plan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import scrbg.meplat.mall.entity.parent.MallBaseEntity;

@Data
@EqualsAndHashCode(callSuper = true)
public class PlanAudit extends MallBaseEntity{
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 计划id
     */
    private String billId;
    private String reason;
}
