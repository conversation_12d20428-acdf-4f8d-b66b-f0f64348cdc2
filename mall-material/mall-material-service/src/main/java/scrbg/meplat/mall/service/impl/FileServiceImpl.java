package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.enums.file.FileEnum;
import scrbg.meplat.mall.mapper.FileMapper;
import scrbg.meplat.mall.service.FileService;
import scrbg.meplat.mall.vo.product.ProductFileVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @描述：通用附件 服务类
 * @作者: y
 * @日期: 2022-11-24
 */
@Service
public class FileServiceImpl extends ServiceImpl<FileMapper, File> implements FileService {
    @Autowired
    MallConfig mallConfig;
    @Autowired
    private FileMapper fileMapper;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<File> queryWrapper) {

        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer relevanceType = (Integer) innerMap.get("relevanceType");
        String programaKey = (String) innerMap.get("programaKey");
        String relevanceId = (String) innerMap.get("relevanceId");
        if (relevanceId != null) {
            queryWrapper.eq(File::getRelevanceId, relevanceId);
        }
        if (programaKey != null) {
            queryWrapper.eq(File::getProgramaKey, programaKey);
        }
        if (relevanceType != null) {
            queryWrapper.eq(File::getRelevanceType, relevanceType);
        }
        queryWrapper.eq(File::getMallType, mallConfig.mallType);
        IPage<File> page = this.page(
                new Query<File>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(File file) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(file);
    }

    @Override
    public void update(File file) {
        super.updateById(file);
    }


    @Override
    public File getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 根据商品id获取商品媒体
     *
     * @param productId
     * @return
     */
    @Override
    public List<ProductFileVO> listProductMediumVO(String productId) {
        List<File> mediumList = lambdaQuery()
                .eq(File::getRelevanceId, productId)
                .ne(File::getImgType, 1)
                .eq(File::getRelevanceType, FileEnum.RELEVANCE_TYPE_PRODUCT.getCode())
                .list();
        ArrayList<ProductFileVO> vos = new ArrayList<>();
        if (mediumList.size() > 0) {
            mediumList.forEach(t -> {
                ProductFileVO productMediumVO = new ProductFileVO();
                BeanUtils.copyProperties(t, productMediumVO);
                vos.add(productMediumVO);
            });
        }
        return vos;
    }

    /**
     * 根据参数获取附件列表
     *
     * @param relevanceId
     * @param relevanceType
     * @param isMain
     * @param type
     * @param imgType
     * @return
     */
    @Override
    public List<File> listFileByParameters(String relevanceId, Integer relevanceType, Integer isMain, Integer type, Integer imgType) {
        LambdaQueryChainWrapper<File> q = lambdaQuery()
                .eq(File::getRelevanceId, relevanceId);
        q.eq(relevanceType != null, File::getRelevanceType, relevanceType);
        q.eq(isMain != null, File::getIsMain, isMain);
        q.eq(type != null, File::getFileType, type);
        q.eq(imgType != null, File::getImgType, imgType);
        List<File> list = q.list();
        return list;
    }

    /**
     * 根据id和类型删除关联的图片
     *
     * @param relevanceId
     * @param relevanceType
     */
    @Override
    public void deleteBatchFileByRelevanceIdAndType(String relevanceId, Integer relevanceType) {
        fileMapper.deleteBatchFileByRelevanceIdAndType(relevanceId, relevanceType);
    }

    @Override
    public void deleteBatchFileByRelevanceIdAndTypeAndProgramaKey(String relevanceId, Integer relevanceType, String programaKey) {
        if (relevanceType != null) {
            fileMapper.deleteBatchFileByRelevanceIdAndProgramaKey(relevanceId, programaKey);
        }
        fileMapper.deleteBatchFileByRelevanceIdAndTypeAndProgramaKey(relevanceId, relevanceType, programaKey);
    }

    @Override
    public void deleteBatchFile(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String relevanceId = (String) innerMap.get("relevanceId");
        Integer relevanceType = (Integer) innerMap.get("relevanceType");
        String programaKey = (String) innerMap.get("programaKey");
        String mallType = (String) innerMap.get("mallType");
        LambdaQueryWrapper<File> wrapper = new LambdaQueryWrapper<>();
        if (relevanceType != null) {
            wrapper.eq(File::getRelevanceType, relevanceType);
        }
        if (relevanceId != null) {
            wrapper.eq(File::getRelevanceId, relevanceId);
        }

        if (programaKey != null) {
            wrapper.eq(File::getProgramaKey, relevanceId);
        }
        if (mallType != null) {
            wrapper.eq(File::getMallType, mallType);
        }
        baseMapper.delete(wrapper);


    }

    @Override
    public List<File> listReIdAndTypeAndPKey(String relevanceId, int relevanceType, String programaKey) {
        LambdaQueryWrapper<File> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(File::getRelevanceId, relevanceId)
                .eq(File::getRelevanceType, relevanceType);
        if (programaKey!=null){
            wrapper .eq(File::getProgramaKey, programaKey);
        }

        List<File> list = list(wrapper);
        return list;
    }


    @Override
    public void createBathFiles(List<File> files) {
        for (File file : files) {
            file.setMallType(mallConfig.mallType);
        }
        saveBatch(files);
    }

    @Override
    public void updateBatchFileByEnterPriseInfo(EnterpriseInfo enterpriseInfo) {
        Integer mallType = enterpriseInfo.getMallType();
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        LambdaQueryWrapper<File> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(File::getRelevanceId, enterpriseInfo.getEnterpriseId())
                .eq(File::getRelevanceType, 8);
        if (mallType != null) {
            wrapper.eq(File::getMallType, mallType);
        }
        wrapper.eq(File::getMallType, mallType);
        remove(wrapper);
        List<File> files = enterpriseInfo.getFiles();
        if (files != null && files.size() > 0) {
            for (File file : files) {
                file.setFileId(null);
                file.setRelevanceType(8);
                file.setRelevanceId(enterpriseInfo.getEnterpriseId());
                file.setMallType(mallType);
                switch (file.getCategory()){
                    case "营业执照":
                        file.setStartTime(enterpriseInfo.getCreationTime());
                        file.setEndTime(enterpriseInfo.getLicenseTerm());
                        break;
                    case "法定代表人身份证人像面照": case "法定代表人身份证国徽面照": case "法定代表人身份证明":
                        file.setStartTime(enterpriseInfo.getLpStartTime());
                        file.setEndTime(enterpriseInfo.getLpEndTime());
                        break;
                    case "其他证书":
                        file.setStartTime(enterpriseInfo.getOtherStartTime());
                        file.setEndTime(enterpriseInfo.getOtherEndTime());
                        break;
                    case "资质证书":
                        file.setStartTime(enterpriseInfo.getQcStartTime());
                        file.setEndTime(enterpriseInfo.getQcEndTime());
                        break;
                    case "中国执行信息公开网查询情况":
                        file.setStartTime(enterpriseInfo.getZxgkStartTime());
                        file.setEndTime(enterpriseInfo.getZxgkEndTime());
                        break;
                    case "信用中国报告":
                        file.setStartTime(enterpriseInfo.getCcrStartTime());
                        file.setEndTime(enterpriseInfo.getCcrEndTime());
                        break;
                    case "税务评级证明":
                        file.setStartTime(enterpriseInfo.getTrcStartTime());
                        file.setEndTime(enterpriseInfo.getTrcEndTime());
                        break;
                    case "最近一期完税证明":
                        file.setStartTime(enterpriseInfo.getTpcStartTime());
                        file.setEndTime(enterpriseInfo.getTpcEndTime());
                        break;
                }
            }
            saveBatch(files);
        }

    }

    @Override
    public List<File> findBatchFileByEnterPriseInfo(EnterpriseInfo enterpriseInfo) {
        Integer mallType = enterpriseInfo.getMallType();
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        LambdaQueryWrapper<File> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(File::getRelevanceId, enterpriseInfo.getEnterpriseId())
                .eq(File::getRelevanceType, 8);
        List<File> list = list(wrapper);
        return list;
    }

    @Override
    public void deleteByfileFarId(String recordId) {
        LambdaQueryWrapper<File> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(File::getFileFarId, recordId);
        remove(wrapper);
    }

    @Override
    public List<File> listTypeAndPkeyAndPkeyTwo(int relevanceType, String programaKey, String programaKeyTwo) {
         LambdaQueryWrapper<File> q = new LambdaQueryWrapper<>();
        Integer mallType = mallConfig.mallType;
        q.eq(File::getRelevanceType, relevanceType)
                .eq(File::getProgramaKey, programaKey)
                .eq(File::getProgramaKeyTwo,programaKeyTwo)
                .eq(File::getMallType, mallType);
        List<File> list = list(q);
        return list;
    }



    @Override
    public List<File> selectFileAll(File file) {
        LambdaQueryWrapper<File> queryWrapper = new LambdaQueryWrapper<>();
        String relevanceId = file.getRelevanceId();
        String programaKey = file.getProgramaKey();
        Integer relevanceType = file.getRelevanceType();
        if (relevanceId != null) {
            queryWrapper.eq(File::getRelevanceId, relevanceId);
        }
        if (programaKey != null) {
            queryWrapper.eq(File::getProgramaKey, programaKey);
        }
        if (relevanceType != null) {
            queryWrapper.eq(File::getRelevanceType, relevanceType);
        }
        List<File> list = list(queryWrapper);
        return list;
    }
}
