package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.BiddingSuppliers;
import scrbg.meplat.mall.mapper.BiddingSuppliersMapper;
import scrbg.meplat.mall.service.BiddingSuppliersService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-07-19
 */
@Service
public class BiddingSuppliersServiceImpl extends ServiceImpl<BiddingSuppliersMapper, BiddingSuppliers> implements BiddingSuppliersService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingSuppliers> queryWrapper) {
        IPage<BiddingSuppliers> page = this.page(
        new Query<BiddingSuppliers>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(BiddingSuppliers biddingSuppliers) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingSuppliers);
    }

    @Override
    public void update(BiddingSuppliers biddingSuppliers) {
        super.updateById(biddingSuppliers);
    }


    @Override
    public BiddingSuppliers getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}