package scrbg.meplat.mall.controller.stockManage;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.SecondarySupplierRecord;
import scrbg.meplat.mall.service.stockManage.SecondarySupplierLogService;

@RestController
@RequestMapping("/stock/secondarySupplierLog")
@ApiSort(value = 500)
@Api(tags = "二级供应商出入库记录（后台）")
public class SecondarySupplierLogController {
    private SecondarySupplierLogService logService;

    @Autowired
    public void setLogService(SecondarySupplierLogService logService) {
        this.logService = logService;
    }


    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<SecondarySupplierRecord> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils<SecondarySupplierRecord> page = logService.queryPage(jsonObject, new LambdaQueryWrapper<SecondarySupplierRecord>());
        return PageR.success(page);
    }
}
