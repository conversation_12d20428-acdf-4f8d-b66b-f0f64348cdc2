package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：品牌分类关联
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "品牌分类关联")
@Data
@TableName("category_brand_relation")
public class CategoryBrandRelation extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "关联id")

    private String categoryBrandRelationId;

    @ApiModelProperty(value = "品牌id")

    private String brandId;

    @ApiModelProperty(value = "分类id")

    private String classId;

    @ApiModelProperty(value = "品牌名称")

    private String brandName;

    @ApiModelProperty(value = "分类名称")

    private String className;


    @ApiModelProperty(value = "状态")

    private Integer state;


}
