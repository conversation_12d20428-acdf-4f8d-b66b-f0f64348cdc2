package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.InsuranceProducts;

import java.util.List;

/**
 * @描述：保险产品 服务类
 * @作者: sund
 * @日期: 2022-11-15
 */
public interface InsuranceProductsService extends IService<InsuranceProducts> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<InsuranceProducts> queryWrapper);

        void create(InsuranceProducts insuranceProducts);

        void update(InsuranceProducts insuranceProducts);

        InsuranceProducts getById(String id);

        void delete(String id);

        /**
         * 根据链接id修改保险产品状态
         */
        void updateByPublish(List<String> ids, String type);
}
