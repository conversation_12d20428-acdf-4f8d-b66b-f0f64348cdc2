package scrbg.meplat.mall.controller.website.userCenter;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.DeviceDemand;
import scrbg.meplat.mall.service.DeviceDemandService;

/**
 * @描述：设备需求控制类
 * @作者: y
 * @日期: 2022-11-21
 */
@RestController
@RequestMapping("/userCenter/deviceDemand")
@ApiSort(value = 200)
@Api(tags = "设备需求（个人中心）")
public class UserCenterDeviceDemandController {

    @Autowired
    public DeviceDemandService deviceDemandService;


    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<DeviceDemand> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = deviceDemandService.userDeviceDemand(jsonObject, new LambdaQueryWrapper<DeviceDemand>());
        return PageR.success(page);
    }

    @PostMapping("/attendList")
    @ApiOperation(value = "根据实体属性分页查询")
    public PageR<DeviceDemand> attendList(@RequestBody JSONObject jsonObject) {
        PageUtils page = deviceDemandService.attendList(jsonObject, new QueryWrapper<DeviceDemand>());
        return PageR.success(page);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody DeviceDemand deviceDemand) {
        deviceDemandService.create(deviceDemand);
        return R.success();
    }
    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<DeviceDemand> findById(String id) {
        DeviceDemand deviceDemand = deviceDemandService.getById(id);
        return R.success(deviceDemand);
    }
}
