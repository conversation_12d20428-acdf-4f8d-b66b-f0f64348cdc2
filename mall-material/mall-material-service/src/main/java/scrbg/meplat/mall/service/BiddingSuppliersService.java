package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.BiddingSuppliers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.BiddingSuppliers;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-07-19
 */
public interface BiddingSuppliersService extends IService<BiddingSuppliers> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingSuppliers> queryWrapper);

        void create(BiddingSuppliers biddingSuppliers);

        void update(BiddingSuppliers biddingSuppliers);

        BiddingSuppliers getById(String id);

        void delete(String id);
}