package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.PcwpOrg;
import scrbg.meplat.mall.mapper.PcwpOrgMapper;
import scrbg.meplat.mall.service.PcwpOrgService;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PcwpOrgServiceImpl  extends ServiceImpl<PcwpOrgMapper, PcwpOrg> implements PcwpOrgService {
    @Override
    public List<PcwpOrg> getTree(String name) {
        QueryWrapper<PcwpOrg> pcwpOrgQueryWrapper = new QueryWrapper<>();
        if(name != null && !name.isEmpty()){
            pcwpOrgQueryWrapper.like("name", name);
        }
        pcwpOrgQueryWrapper.ne("mdmstate", -1);
        pcwpOrgQueryWrapper.eq("type", "O");// O: 组织，D: 部门
        pcwpOrgQueryWrapper.notIn("orglayertypenumber", "4","8");// 组织层级编码过滤
        pcwpOrgQueryWrapper.orderByAsc("sortcode");

        List<PcwpOrg> pcwpOrgs = baseMapper.selectList(pcwpOrgQueryWrapper);

        // 如果查询结果为空，直接返回空列表
        if (pcwpOrgs == null || pcwpOrgs.isEmpty()) {
            return Collections.emptyList();
        }

        // 过滤出根节点（parentid为null或空的节点）
        List<PcwpOrg> rootNodes = pcwpOrgs.stream()
                .filter(org -> org.getParentid() == null || org.getParentid().isEmpty())
                .collect(Collectors.toList());

        // 如果根节点为空，但查询结果不为空，则将所有节点视为根节点
        if (rootNodes.isEmpty()) {
            rootNodes = new ArrayList<>(pcwpOrgs);
        }

        // 为根节点设置显示名称并构建树形结构
        for (PcwpOrg rootNode : rootNodes) {
            // 为根节点添加组织层级类型名称
            if (rootNode.getName() != null && rootNode.getOrglayertypename() != null) {
                rootNode.setName(rootNode.getName() + "(" + rootNode.getOrglayertypename() + ")");
            }
            buildTree(rootNode, pcwpOrgs);
        }

        return rootNodes;
    }

    /**
     * 递归构建树形结构
     * @param parentNode 父节点
     * @param allOrgs 所有组织列表
     */
    private void buildTree(PcwpOrg parentNode, List<PcwpOrg> allOrgs) {
        List<PcwpOrg> children = allOrgs.stream()
                .filter(org -> org.getParentid() != null && org.getParentid().equals(parentNode.getId()))
                .collect(Collectors.toList());

        if (!children.isEmpty()) {
            parentNode.setChildren(children);
            for (PcwpOrg child : children) {
                // 为子节点添加组织层级类型名称
                if (child.getName() != null && child.getOrglayertypename() != null) {
                    child.setName(child.getName() + "(" + child.getOrglayertypename() + ")");
                }
                buildTree(child, allOrgs);
            }
        }
    }
}
