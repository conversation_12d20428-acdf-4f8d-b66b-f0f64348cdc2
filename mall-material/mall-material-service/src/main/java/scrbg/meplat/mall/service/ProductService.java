package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.dto.product.*;
import scrbg.meplat.mall.dto.product.material.*;
import scrbg.meplat.mall.dto.product.material.lcProduct.CuterCreateLcMaterialDTO;
import scrbg.meplat.mall.dto.thirdapi.MaterialDtlDTO;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.ProductRulesClass;
import scrbg.meplat.mall.entity.excelTemplate.*;
import scrbg.meplat.mall.vo.product.ImportExcelResultVO;
import scrbg.meplat.mall.vo.product.ImportOuterProductVO;
import scrbg.meplat.mall.vo.product.material.MaterInfoVO;
import scrbg.meplat.mall.vo.product.website.IndexMaterialVO;
import scrbg.meplat.mall.vo.product.website.ProductDetailDealRecordVO;
import scrbg.meplat.mall.vo.product.website.material.WMaterialnfoVO;
import scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo;
import scrbg.meplat.mall.vo.shopManage.reportForms.ProductFromVo;
import scrbg.meplat.mall.vo.w.CheckIsPutawayVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * @描述：店铺商品信息 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface ProductService extends IService<Product> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Product> queryWrapper);

    void create(Product product);

    void update(Product product);

    Product getById(String id);

    void delete(String id);

//    /**
//     * 批量导入物资
//     *
//     * @param createMaterialDTOS
//     * @return
//     */
//    void importBatchMaterial(List<CreateMaterialDTO> createMaterialDTOS);

//    /**
//     * 根据物资id修改物资信息
//     *
//     * @param dto
//     */
//    void updateMaterialInfo(UpdateProductInfoDTO dto);

    /**
     * 根据ids批量逻辑删除
     *
     * @param ids
     */
    void removeLogicBatch(List<String> ids);

    /**
     * 根据商品id集合批量修改上下架状态
     *
     * 2024-1-10 商品上架变更  原价<=销售价格
     *
     *
     * @param dto
     * @return
     */
    void updateProductState(UpdateProductStateDTO dto);

    /**
     * 下架店铺所有商品
     * @param shopIds
     */
    void updateProductStateStop(List<String> shopIds);


    /**
     * 根据物资id获取物资商品信息（物资C端）
     *
     * @param productId
     * @return
     */
    WMaterialnfoVO materialInfo(String productId);


    /**
     * 根据id获取商品
     * @param productId
     * @param state
     * @return
     */
    Product getProductById(String productId, Integer state);




    /**
     * 店铺商品分页列表（店铺）
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils listShopManageDevicePage(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery);



    /**
     * 物资分页列表（店铺）
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils listShopManageMaterialPage(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery);

    /**
     * 批量导入物资（店铺）
     * @param dtos
     */
    void importBatchMaterial(List<ImportMaterialDTO> dtos);

    /**
     * "获取物资（通用）
     * @param dto
     * @return
     */
    MaterInfoVO getMaterialInfo(GetMaterialInfoDTO dto);

    /**
     * 新增物资（店铺）
     * @param dto
     */
    void createMaterial(CreateMaterialDTO dto);

    /**
     * 修改物资（店铺）
     * @param dto
     */
    void updateMaterial(UpdateMaterialDTO dto);

    /**
     * 物资分页列表（平台）
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils listPlatformMaterialPage(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery);

    /**
     * 物资分页列表（平台）
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils listPlatformMaterialPagePVP(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery);

    void outputExcel(JSONObject jsonObject, HttpServletResponse response);

    /**
     * 商品列表
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils materialPageList(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery);


    /**
     * 根据商品id获取商品列表（外部用）
     * @param productId
     * @return
     */
    IndexMaterialVO getIndexMaterialVO(String productId);


    /**
     * 获取商品信息列表
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils listProductFullInfoVOPage(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery);


    /**
     * 批量修改
     * @param products
     */
    void updateBatch(List<Product> products);




    /**
     * 根据id查询商品并且排除商品描述
     * @param productId
     * @return
     */
    Product getProductExcludeRemarkById(String productId);

    /**
     * 获取商品交易记录
     * @param jsonObject
     * @return
     */
    PageUtils getProductDetailDealRecord(JSONObject jsonObject);



    /**
     * 导入商品
     *
     * @param file
     * @param productType
     * @return
     */
    List<ImportExcelResultVO> uploadExcelFile(MultipartFile file, Integer productType);

    /**
     * excel导入物资
     * @param material
     * @param productType
     */
    void saveImportMaterial(Material material, Integer productType);

    /**
     * excel导入店铺物资
     * @param shopMaterial
     * @param shopId
     */
    void saveImportSupplierMaterial(ShopMaterial shopMaterial,String shopId);

    /**
     * 补充成交记录用户信息
     * @param dtos
     * @return
     */
    List<ProductDetailDealRecordVO> getRecordListUserInfo(List<ProductDetailDealRecordVO> dtos);

    /**
     * 外部批量导入物资
     *
     * @param dtos
     * @param request
     * @return
     */
    List<ImportOuterProductVO> outerImportBatchMaterial(List<CuterCreateMaterialDTO> dtos, HttpServletRequest request);

    /**
     * 外部导入物资
     *
     * @param dto
     * @param vo
     * @param request
     * @return
     */
    void outerImportMaterial(CuterCreateMaterialDTO dto, ImportOuterProductVO vo, HttpServletRequest request);


    /**
     * 外部导入物资，店铺导入
     *
     * @param dto
     * @param vo
     * @param request
     * @return
     */
    void outerImportMaterialShop(CuterCreateMaterialDTO dto, ImportOuterProductVO vo, HttpServletRequest request);

    WMaterialnfoVO materialInfoLogin(String productId);

    /**
     * 检查商品是否上架
     * @param dto
     * @return
     */
    List<CheckIsPutawayVO> checkIsPutaway(CheckIsPutawayPDTO dto);

    /**
     * 获取物资（检查物资详情用）
     * @param dto
     * @return
     */
    MaterInfoVO getCheckMaterialInfo(GetMaterialInfoDTO dto);

    /**
     * 物资店铺查询供方商品
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils listMaterialSupplier(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery);


    /**
     * 修改物资（供应商）
     * @param dto
     */
    void updateMaterialSupplier(UpdateMaterialSupplierDTO dto);

    /**
     * 批量修改供方提交状态
     * @param dto
     */
    void updateProductSupplierSubState(UpdateProductSupplierSubStateDTO dto);

    /**
     * 新增物资
     * @param dto
     */
    void createMaterialSupplier(CreateMaterialSupplierDTO dto);

    /**
     * 获取物资（供应商查询专用）
     * @param dto
     * @return
     */
    MaterInfoVO getMaterialInfoSupplier(GetMaterialInfoDTO dto);

    /**
     * 查询供方提供的商品（店铺）
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils listMaterialSupplierAffirm(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery);

    /**
     *   商品数据展示报表
     * @param jsonObject
     * @param productFromVoQueryWrapper
     * @return
     */
    PageUtils productFromList(JSONObject jsonObject, QueryWrapper<ProductFromVo> productFromVoQueryWrapper);

    /**
     * 确认全部商品（店铺）
     * @param jsonObject
     * @param lambdaQuery
     */
    void batchAffirmSupplierProduct(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery);



    /**
     * 商品报表  管理平台
     * @param jsonObject
     * @param platformProductFromVoQueryWrapper
     * @return
     */
    PageUtils getPlatformProductFromList(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> platformProductFromVoQueryWrapper);

    void productFromListLoad(JSONObject jsonObject, QueryWrapper<ProductFromVo> wrapper, HttpServletResponse response);
    /**
     * 商品报表  管理平台(excel下载)
     * @param jsonObject
     * @param
     * @return
     */
    void getPlatformProductFromListLoad(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> wrapper, HttpServletResponse response);

    /**
     * 供方商品Excel导入
     * @param file
     * @param shopId
     * @return
     */
    List<ImportExcelResultVO> supplierUploadExcelFile(MultipartFile file, String shopId);


    /**
     * 根据种类id查询商品信息
     * @param classId
     * @return
     */
    List<Product> findAllByClassId(String classId);

    void updateClassId(Object nowClassId, Object oldclassId);

    List<Product> getDataByClassIdList(String classId);

    String getDataByClassIdParentPath(String nowparentId);

    /**
     * 根据当前查询条件全部通过商品
     * @param jsonObject
     */
    void allProductStatePass(JSONObject jsonObject);

    /**
     * 商品导入（店铺导入）
     * @param productList
     * @param request
     * @return
     */
    List<ImportOuterProductVO> importBatchMaterialShop(List<CuterCreateMaterialDTO> productList, HttpServletRequest request);
    /**
     * 店铺商品Excel导入（店铺导入）
     * @param
     * @param
     * @return
     */
    List<ImportExcelResultVO> shopUploadProductMallExcelFile(MultipartFile file);
    List<ImportExcelResultVO> shopUploadLcProductMallExcelFile(MultipartFile file);

    void onSubmitMaterialSupplier(CreateMaterialSupplierDTO dto);

    /**
     * 临购
     * @param file
     * @param shopId
     * @return
     */

    List<ImportExcelResultVO> supplierUploadLcExcelFile(MultipartFile file, String shopId);
    /**
     * excel导入店铺物资
     * @param shopMaterial
     * @param shopId
     */
    void saveImportSupplierMaterial(LcShopMaterial shopMaterial,String shopId);

    /**
     * 二级供应商 临购商品API导入
     * @param productList
     * @param request
     * @return
     */
    List<ImportOuterProductVO> outerImportBatchLcMaterial(List<CuterCreateLcMaterialDTO> productList, HttpServletRequest request);

    /**
     * 临购商品导入
     * @param dto
     * @param vo
     * @param request
     */
    void outerImportLcMaterial(CuterCreateLcMaterialDTO dto, ImportOuterProductVO vo, HttpServletRequest request);

    /**
     * 店铺临购商品导入
     * @param productList
     * @param request
     * @return
     */
    List<ImportOuterProductVO> importBatchLcMaterialShop(List<CuterCreateLcMaterialDTO> productList, HttpServletRequest request);

    /**
     * 临购商品API导入店铺
     * @param dto
     * @param vo
     * @param request
     */
    void outerImportLcMaterialShop(CuterCreateLcMaterialDTO dto, ImportOuterProductVO vo, HttpServletRequest request);

    /**
     * 物资保存并上架
     * @param dto
     */
    void updateMaterialAndState(UpdateProductAndStateDTO dto);

    /**
     * 出售中的商品导出
     * @param jsonObject
     * @param response
     * @return
     */
    void putawayProductExport(JSONObject jsonObject, HttpServletResponse response);

    void listMaterialSupplierExport(JSONObject jsonObject, HttpServletResponse response);

    void updateClassLJDLSFJ();


    List<Product> selectAllByRulesClass(ProductRulesClass productRulesClass);

    BigDecimal selfShopDataByRulesClass(ProductRulesClass productRulesClass);

    /**
     *  //查询条件筛选出的商品
     * @param productRulesClass
     * @return
     */
    List<Product> getAverageWarningProductList(ProductRulesClass productRulesClass);

    /**
     * 获取商品列表的平均值
     * @param productList  商品集合
     * @return  商品的平均
     */
    BigDecimal averageSellPrice(List<Product> productList);

    /**
     *
     * @param file  文件销售区域集合
     * @param shopId  店铺 null 供应商商品
     *                不是null 自营店商品
     * @return
     */
    List<ImportExcelResultVO> shopUploadProductMallZoneExcelFile(MultipartFile file,String shopId);

    /**
     * 设置商品的平均销售价格、
     * 预警规则 不比较区域
     * @param productList
     */
    void setProdctSellPrise(List<Product> productList);

    Product getByIdLocal(String productId);

    /**
     * 设置物资缓存数据,只针对低值易耗
     * @param materialDtlDTOList
     */
    public  void setMaterialDtlDTOList(List<MaterialDtlDTO> materialDtlDTOList);


    // 获取物资缓存数据 key: 物资分类id+物资编号
    public    MaterialDtlDTO getRedisMaterialInfo(String classId, String billNo, String basicsMaterialName);

    Product validateProduct(String productId, Object o);

    PageUtils getProductCountList(JSONObject jsonObject, LambdaQueryWrapper<Product> productLambdaQueryWrapper);

    void calculateProductAveragePrice();

    void updateProductJcState(Product product);

    void oneClickStockUp(List<String> ids);

    PageUtils<Product> getStockUpInfo(JSONObject jsonObject);

    void updateProductStock(Product product);

    PageUtils statisticsByShopAndProductTypeWithSupplierPage(JSONObject jsonObject);
}
