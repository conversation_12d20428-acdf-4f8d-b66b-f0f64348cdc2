package scrbg.meplat.mall.service;

import java.time.LocalDate;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;

import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.fee.FeeManagementQueryDto;
import scrbg.meplat.mall.dto.fee.FeeManagementResultVO;
import scrbg.meplat.mall.dto.fee.PlatformYearFeeRecordDto;
import scrbg.meplat.mall.dto.free.FeeInputTemplateDTO;
import scrbg.meplat.mall.entity.PlatformYearFeeRecord;

/**
 * @描述：年费缴费记录 服务类
 * @作者: ye
 * @日期: 2024-01-24
 */
public interface PlatformYearFeeRecordService extends IService<PlatformYearFeeRecord> {
    PageUtils platformQueryListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformYearFeeRecord> queryWrapper);

    void create(PlatformYearFeeRecord platformYearFeeRecord);

    void update(PlatformYearFeeRecord platformYearFeeRecord);

    PlatformYearFeeRecord getById(String id);

    void delete(String id);

    void deleteBatch(List<String> ids);

    /**
     * 查詢我的繳費记录
     * @param pyfrDto
     * @return
     */
    IPage<PlatformYearFeeRecord> myQueryListByEntity(PlatformYearFeeRecordDto pyfrDto);

    /**
     * 供应商新增缴费记录
     *
     * @param platformYearFeeRecord
     */
    String supplierCreateFee(PlatformYearFeeRecord platformYearFeeRecord);

    /**
     * 根据编号查询
     *
     * @param s
     * @return
     */
    PlatformYearFeeRecord findBySn(String s);

    /**
     * 修改缴费
     *
     * @param platformYearFeeRecord
     */
    void supplierUpdateFee(PlatformYearFeeRecord platformYearFeeRecord);

    /**
     * 审核服务年费
     *
     * @param dto
     */
    void auditFee(AuditDTO dto);

    /**
     * 供应商查询缴费管理列表
     * @param jsonObject
     * @param platformYearFeeRecordLambdaQueryWrapper
     * @return
     */
    PageUtils myQueryPayFreeListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformYearFeeRecord> platformYearFeeRecordLambdaQueryWrapper);

    /**
     * 供应商新增缴费（包括交易和年费）
     * @param platformYearFeeRecord
     * @return
     */
    String createFeeAndDealFree(PlatformYearFeeRecord platformYearFeeRecord);

    /**
     * 平台查询缴费记录（包括交易）
     * @param jsonObject
     * @param q
     * @return
     */
    PageUtils platformQueryYearAndDealFreeListByEntity(JSONObject jsonObject, LambdaQueryWrapper<PlatformYearFeeRecord> q);

    /**
     * 导入缴费信息
     * @param file
     * @param response
     */
    void feeInputData(MultipartFile file, HttpServletResponse response);

    /**
     * 导入缴费信息
     * @param dto
     */
    void feeInputDataOne(FeeInputTemplateDTO dto);

    String reCreateContract();

    /**
     * 获取最近一个
     * @param enterpriseId
     * @return
     */
    LocalDate getNextAnnuaFeeCycle(String enterpriseId);

    /**
     * 创建或者更新
     * 日期最近的状态是已审核则新增，否则更新
     * @param platformYearFeeRecord
     * @return
     */
    void createOrUpdate(PlatformYearFeeRecord platformYearFeeRecord);

    /**
     * 缴费管理查询接口 - 查询所有企业的交易服务费和年费
     * @param queryDto 查询条件
     * @return 分页结果
     */
    PageUtils getFeeManagementList(FeeManagementQueryDto queryDto);

}
