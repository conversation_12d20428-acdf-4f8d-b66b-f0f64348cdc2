package scrbg.meplat.mall.adapter.configAutoBean;//package scrbg.meplat.mall.adapter.configAutoBean;
//
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.Signature;
//import org.aspectj.lang.annotation.AfterThrowing;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Before;
//import org.aspectj.lang.annotation.Pointcut;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.Arrays;
//
///**
// * 记录方法
// */
//@Aspect
//public class SystemRequestLogAspect2 {
//    private Logger logger = LoggerFactory.getLogger(SystemRequestLogAspect2.class);
//
//    /**
//     * 对各个Api类进行切面
//     */
//    @Pointcut("execution(* scrbg.meplat.mall.*..*Controller.*(..))")
//    public void aspectApiLog() {
//    }
//
//
//    /**
//     * 输出进入切面之前的日志信息AccountApi
//     *
//     * @param joinPoint 连接点
//     */
//    @Before("aspectApiLog()")
//    public void beforeInfoLog(JoinPoint joinPoint) {
//        Signature signature = joinPoint.getSignature();
//        String msg = "\n" +
//                "┌────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────" + "\n" +
//                "│In to Method \n\n" +
//                "│\t into:    " + signature.getDeclaringTypeName() + "." + signature.getName() + "\n" +
//                "│\t args:    " + Arrays.toString(joinPoint.getArgs()) + "\n" +
//                "└────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────";
//        logger.info(msg);
//    }
//
////    /**
////     * 输出进入切面之后的日志信息
////     *
////     * @param joinPoint 连接点
////     * @param returnObj obj
////     */
////    @AfterReturning(pointcut = "aspectApiLog()", returning = "returnObj")
////    public void returnInfoLog(JoinPoint joinPoint, Object returnObj) {
////        Signature signature = joinPoint.getSignature();
////        String msg = "\n" +
////                "==========================================================================================================================================" + "\n" +
////                "=Method Return \n\n" +
////                "=\t class:   " + signature.getDeclaringTypeName() + "\n" +
////                "=\t method:  " + signature.getName() + "\n" +
////                "=\t args:    " + Arrays.toString(joinPoint.getArgs()) + "\n" +
////                "=\n" +
////                "= - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - " + "\n" +
////                "=\n" +
////                "=\t retrun:  " + returnObj + "\n" +
////                "==========================================================================================================================================";
////        logger.info(msg);
////    }
//
//    @AfterThrowing(pointcut = "aspectApiLog()", throwing = "e")
//    public void throwingLog(JoinPoint joinPoint, Exception e) {
//        logger.error("aspect api log error: " + e.getMessage(), e);
//    }
//}
