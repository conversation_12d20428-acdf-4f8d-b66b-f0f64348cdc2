package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.Floor;

import java.util.List;

/**
 * @描述：客户端商品展示楼层 服务类
 * @作者: 胡原武
 * @日期: 2022-11-10
 */
public interface FloorService extends IService<Floor> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Floor> queryWrapper);

    void create(Floor floor);

    void update(Floor floor);

    Floor getById(String id);

    void delete(String id);

    void updateByPublish(List<String> ids, String s);
}
