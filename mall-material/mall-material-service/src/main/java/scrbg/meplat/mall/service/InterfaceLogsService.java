package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.InterfaceLogs;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.InterfaceLogs;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-07-07
 */
public interface InterfaceLogsService extends IService<InterfaceLogs> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<InterfaceLogs> queryWrapper);

        void create(InterfaceLogs interfaceLogs);

        void update(InterfaceLogs interfaceLogs);

        InterfaceLogs getById(String id);

        void delete(String id);

        void createSuccessLog(InterfaceLogs interfaceLogs);

        void createFailLog(InterfaceLogs interfaceLogs);


        void rollBackFailLog(InterfaceLogs iLog);

        void rollBackSuccessLog(InterfaceLogs iLog);
}