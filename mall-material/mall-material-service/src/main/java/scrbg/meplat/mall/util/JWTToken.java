package scrbg.meplat.mall.util;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * JWT工具类
 */
@Slf4j
public class JWTToken {

    /**
     * 私有化构造器
     */
    private JWTToken(){}

    /**
     * 生成Token
     */
    public static String createAccessToken(String userid){
        // 登陆成功生成JWT
        String token = Jwts.builder()
                // 放入用户名和用户ID
                .setId(userid)
                // 主题
//                .setSubject()
                // 签发时间
                .setIssuedAt(new Date())
                // 签发者
                .setIssuer("ye")
                // 自定义属性 放入用户拥有权限
//                .claim("authorities", JSON.toJSONString(memberDetails.getAuthorities()))
                // 失效时间（无需配置使用redis判断失效）
//                .setExpiration(new Date(System.currentTimeMillis() + JWTConfig.expiration))
                // 签名算法和密钥
                .signWith(SignatureAlgorithm.HS512, JWTConfig.secret)
                .compact();
        return token;
    }
}