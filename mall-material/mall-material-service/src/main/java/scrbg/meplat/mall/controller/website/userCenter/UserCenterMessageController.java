package scrbg.meplat.mall.controller.website.userCenter;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.entity.StationMessage;
import scrbg.meplat.mall.entity.StationMessageReceive;
import scrbg.meplat.mall.service.StationMessageReceiveService;
import scrbg.meplat.mall.service.StationMessageService;

import java.util.List;

@RestController
@RequestMapping("/userCenter/message")
@ApiSort(value = 200)
@Api(tags = "我的消息(个人中心)")
public class UserCenterMessageController {
    @Autowired
    StationMessageService stationMessageService;
    @Autowired
    StationMessageReceiveService stationMessageReceiveService;
    /**
     * 根据用户ID查询用户订单详详情
     *
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<Orders> msgList(@RequestBody JSONObject jsonObject) {
        PageUtils page = stationMessageService.queryPageList(jsonObject,new QueryWrapper<StationMessage>());
        return PageR.success(page);
    }
    @GetMapping("/updateStateById")
    @ApiOperation(value = "根据Id修改已读信息")
    public R updateById(String id) {
        stationMessageReceiveService.changStateById(id);
        return R.success();
    }
    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        stationMessageReceiveService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("/updateBathState")
    @ApiOperation(value = "根据接收消息Id批量修改消息状态")
    public PageR updateBathState(@RequestBody  List<String> receiceId ) {
        stationMessageReceiveService.updateBathState(receiceId);
        return PageR.success();
    }
    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        stationMessageReceiveService.delete(id);
        return R.success();
    }

    @GetMapping("getMessageNum")
    @ApiOperation(value = "查询新消息数量")
    public R getMessageNum(Integer receiveType) {
        List<StationMessageReceive>  list= stationMessageReceiveService.getMessageNum( receiveType);
        return R.success(list);
    }
}
