package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.SynthesizeTemporaryDtl;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SynthesizeTemporaryDtl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-10-07
 */
public interface SynthesizeTemporaryDtlService extends IService<SynthesizeTemporaryDtl> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SynthesizeTemporaryDtl> queryWrapper);

        void create(SynthesizeTemporaryDtl synthesizeTemporaryDtl);
        void update(SynthesizeTemporaryDtl synthesizeTemporaryDtl);
        SynthesizeTemporaryDtl getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
