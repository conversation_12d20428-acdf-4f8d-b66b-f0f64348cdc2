package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.SysOrg;
import scrbg.meplat.mall.mapper.SysOrgMapper;
import scrbg.meplat.mall.service.SysOrgService;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.List;
/**
 * @描述：机构表 服务类
 * @作者: ye
 * @日期: 2024-04-30
 */
@Service
public class SysOrgServiceImpl extends ServiceImpl<SysOrgMapper, SysOrg> implements SysOrgService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysOrg> queryWrapper) {
        IPage<SysOrg> page = this.page(
        new Query<SysOrg>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(SysOrg sysOrg) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(sysOrg);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(SysOrg sysOrg) {
        super.updateById(sysOrg);
    }


    @Override
    public SysOrg getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     @Transactional(rollbackFor = Exception.class)
     @NotResubmit
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
         }
}
