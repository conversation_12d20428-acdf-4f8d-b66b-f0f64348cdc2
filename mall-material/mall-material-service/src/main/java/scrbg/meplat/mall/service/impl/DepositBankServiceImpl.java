package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.DepositBank;
import scrbg.meplat.mall.mapper.DepositBankMapper;
import scrbg.meplat.mall.service.DepositBankService;

/**
 * @描述：开户银行信息表 服务类
 * @作者: y
 * @日期: 2022-11-13
 */
@Service
public class DepositBankServiceImpl extends ServiceImpl<DepositBankMapper, DepositBank> implements DepositBankService {
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<DepositBank> queryWrapper) {
        IPage<DepositBank> page = this.page(
                new Query<DepositBank>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(DepositBank depositBank) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(depositBank);
    }

    @Override
    public void update(DepositBank depositBank) {
        super.updateById(depositBank);
    }


    @Override
    public DepositBank getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}
