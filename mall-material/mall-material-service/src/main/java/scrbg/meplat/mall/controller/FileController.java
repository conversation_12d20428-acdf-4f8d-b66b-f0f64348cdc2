package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.service.FileService;

import java.util.List;

/**
 * @描述：通用附件控制类
 * @作者: y
 * @日期: 2022-11-24
 */
@RestController
@RequestMapping("/file")
@Api(tags = "通用附件")
public class FileController {

    @Autowired
    public FileService fileService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<File> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = fileService.queryPage(jsonObject, new LambdaQueryWrapper<File>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<File> findById(String id) {
        File file = fileService.getById(id);
        return R.success(file);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody File file) {
        fileService.create(file);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody File file) {
        fileService.update(file);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        fileService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        fileService.removeByIds(ids);
        return R.success();
    }

    /**
     * 单一接口，只适用于查询pcwp资料
     * @param file
     * @return
     */
    @PostMapping("/getPcwpFileList")
    @ApiOperation(value = "根据类型查询pcwp入（未入）库资料")
    public R getPcwpFileList(@RequestBody File file) {
        List<File> files=fileService.selectFileAll(file);
        return R.success();
    }
}

