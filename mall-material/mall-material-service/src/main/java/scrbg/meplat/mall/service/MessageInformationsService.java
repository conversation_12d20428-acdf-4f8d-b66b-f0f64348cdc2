package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.MessageInformations;

import java.util.List;

/**
 * @描述：反馈中心 服务类
 * @作者: y
 * @日期: 2022-11-22
 */
public interface MessageInformationsService extends IService<MessageInformations> {

    /**
     * 新增新留言
     * @param
     * @return
     */
    void createMessage(MessageInformations messageInformations);
    /**
     * 用户查询留言
     * @param
     * @return
     */
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MessageInformations> stationMessageServiceLambdaQueryWrapper);
    /**
     * 用户查看公开留言
     * @param
     * @return
     */
    PageUtils queryShowPage(JSONObject jsonObject, LambdaQueryWrapper<MessageInformations> wrapper);
    /**
     * 管理员查询所有留言
     * @param
     * @return
     */
    PageUtils queryAllPage(JSONObject jsonObject, LambdaQueryWrapper<MessageInformations> stationMessageServiceLambdaQueryWrapper);
    /**
     * 根据主键删除留言
     * @param
     * @return
     */
    void delete(String id);
    /**
     * 新增回复
     * @param
     * @return
     */
    void createRespond(MessageInformations messageInfor);
    /**
     * 批量取消展示或展示
     * @param
     * @return
     */
    void updateByPublish(List<String> ids, String type);
}
