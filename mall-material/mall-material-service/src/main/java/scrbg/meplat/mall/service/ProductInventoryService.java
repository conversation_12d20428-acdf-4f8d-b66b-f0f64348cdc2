package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.dto.product.CreateBatchByBillIdDTO;
import scrbg.meplat.mall.dto.product.UpdateProductInventoryStateDTO;
import scrbg.meplat.mall.entity.ProductInventory;

import java.util.List;

/**
 * @描述：商品库 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface ProductInventoryService extends IService<ProductInventory> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductInventory> queryWrapper);

    void create(ProductInventory productInventory);

    void update(ProductInventory productInventory);

    ProductInventory getById(String id);

    void delete(String id);

    /**
     * 根据物资id集合批量导入到商品库
     *
     * @param billIds
     * @return
     */
    void createBatchByBillIds(String[] billIds);

    /**
     * 根据ids批量逻辑删除
     *
     * @param ids
     */
    void removeLogicBatch(List<String> ids);

    /**
     * 批量修改商品状态
     * @param dto
     */
    void updateProductInventoryState(UpdateProductInventoryStateDTO dto);

    /**
     * 批量导入到商品库
     * @param dtos
     */
    void importBatch(List<CreateBatchByBillIdDTO> dtos);

}
