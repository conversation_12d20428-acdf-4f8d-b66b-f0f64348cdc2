package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.vo.product.ProductFileVO;

import java.util.List;

/**
 * @描述：通用附件 服务类
 * @作者: y
 * @日期: 2022-11-24
 */
public interface FileService extends IService<File> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<File> queryWrapper);

        void create(File file);

        void update(File file);

        File getById(String id);

        void delete(String id);

        /**
         * 根据商品id获取商品媒体
         * @param productId
         * @return
         */
        List<ProductFileVO> listProductMediumVO(String productId);

        /**
         * 根据参数获取附件列表
         * @param relevanceId
         * @param relevanceType
         * @param isMain
         * @param type
         * @param imgType
         * @return
         */
        List<File> listFileByParameters(String relevanceId, Integer relevanceType, Integer isMain,Integer type,Integer imgType);

        /**
         * 根据id和类型删除关联的图片
         * @param relevanceId
         * @param relevanceType
         */
        void deleteBatchFileByRelevanceIdAndType(String relevanceId, Integer relevanceType);

        void deleteBatchFileByRelevanceIdAndTypeAndProgramaKey(String relevanceId, Integer relevanceType, String programaKey);

        /**
         * relevanceId 关联id
         * relevanceType 关联类型
         * programaKey 栏目
         * mallType  商城类型
         * @param jsonObject
         */
        void deleteBatchFile(JSONObject jsonObject);

        List<File> listReIdAndTypeAndPKey(String relevanceId, int relevanceType , String programaKey);

        void createBathFiles(List<File> files);


        void updateBatchFileByEnterPriseInfo(EnterpriseInfo enterpriseInfo);




        List<File> findBatchFileByEnterPriseInfo(EnterpriseInfo enterpriseInfo);

        void deleteByfileFarId(String recordId);



    List<File> selectFileAll(File file);

        List<File> listTypeAndPkeyAndPkeyTwo(int i, String programaKey, String programaKeyTwo);
}
