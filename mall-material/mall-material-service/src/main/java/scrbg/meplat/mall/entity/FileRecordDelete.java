package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @描述：附件删除表
 * @作者: y
 * @日期: 2022-11-28
 */
@ApiModel(value="附件删除表")
@Data
@TableName("file_record_delete")
public class FileRecordDelete  implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "记录id")
    private String fileRecordId;

    @ApiModelProperty(value = "删除记录id")

    private String recordId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    @ApiModelProperty(value = "创建人名称")
    @TableField(value = "founder_name", fill = FieldFill.INSERT)
    private String founderName;

    @ApiModelProperty(value = "创建人Id")
    @TableField(value = "founder_id", fill = FieldFill.INSERT)
    private String founderId;









}
