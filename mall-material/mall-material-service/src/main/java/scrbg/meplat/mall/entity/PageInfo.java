package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.util.Date;
/**
 * @描述：
 * @作者: y
 * @日期: 2022-11-14
 */
@ApiModel(value="")
@Data
@TableName("page_info")
public class PageInfo extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "页面id")

        private String pageId;

    @ApiModelProperty(value = "页面名称")

        private String pageName;

    @ApiModelProperty(value = "产品分类ids")

        private String productCategoryIds;

    @ApiModelProperty(value = "商品分类楼层名称")

        private String productCategoryName;

    @ApiModelProperty(value = "企业ids")

        private String enterpriseIds;

    @ApiModelProperty(value = "企业楼层名称")

        private String enterpriseName;

    @ApiModelProperty(value = "楼层ids")

        private String floorIds;

    @ApiModelProperty(value = "商品楼层名称")

        private String floorName;

    @ApiModelProperty(value = "创建时间")

        private Date
gmtCreate;

    @ApiModelProperty(value = "更新时间")

        private Date gmtModified;

    @ApiModelProperty(value = "创建人Id")

        private String founderId;

    @ApiModelProperty(value = "创建人名称")

        private String founderName;

    @ApiModelProperty(value = "备注")

        private String remarks;

    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")

        private Integer isDelete;

    @ApiModelProperty(value = "排序")

        private Integer sort;

    @ApiModelProperty(value = "状态")

        private Integer state;

    @ApiModelProperty(value = "商城类型：0物资商场, 1设备商城 ")

        private Integer mallType;


}
