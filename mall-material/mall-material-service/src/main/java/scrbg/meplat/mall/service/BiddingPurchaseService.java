package scrbg.meplat.mall.service;

import scrbg.meplat.mall.dto.bidding.AuditBidingInfoDTO;
import scrbg.meplat.mall.dto.bidding.BatchUpdateBiddingItemInfoDTO;
import scrbg.meplat.mall.dto.bidding.CreateBidingOrderItemsByBiddingIdDTO;
import scrbg.meplat.mall.entity.BiddingBidRecord;
import scrbg.meplat.mall.entity.BiddingPurchase;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.vo.bidding.*;

import java.util.Date;
import java.util.List;

/**
 * @描述：竞价采购表 服务类
 * @作者: ye
 * @日期: 2023-07-11
 */
public interface BiddingPurchaseService extends IService<BiddingPurchase> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> queryWrapper);

    void create(BiddingPurchase biddingPurchase);

    void update(BiddingPurchase biddingPurchase);

    BiddingPurchase getById(String id);

    void delete(String id);

    /**
     * 未登录时产看全部竞价信息
     *
     * @param jsonObject
     * @param biddingPurchaseLambdaQueryWrapper
     * @return
     */
    PageUtils unLoginqueryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> biddingPurchaseLambdaQueryWrapper);

    /**
     * 查询我发布竞价列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    PageUtils listMyCreateBiding(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> q);

    /**
     * 获取竞价详细信息
     *
     * @param biddingSn
     */
    BiddingPurchaseInfoVO getBiddingPurchaseInfo(String biddingSn);

    /**
     * 根据竞价id追加竞价明细
     *
     * @param dto
     */
    void createBidingOrderItemsByBiddingId(CreateBidingOrderItemsByBiddingIdDTO dto);

    /**
     * 根据竞价明细修改明细信息
     * @param dtos
     */
    void batchUpdateBiddingItemInfo(List<BatchUpdateBiddingItemInfoDTO> dtos);

    /**
     * 提交审核竞价信息
     * @param bidingIds
     */
    void submitBidingByIds(List<String> bidingIds);

    /**
     * 查询发布竞价列表-平台
     * @param jsonObject
     * @param biddingPurchaseLambdaQueryWrapper
     * @return
     */
    PageUtils listAllCreateBiding(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> biddingPurchaseLambdaQueryWrapper);

    /**
     * 审核竞价
     * @param dto
     */
    void auditBidingInfo(AuditBidingInfoDTO dto);

    /**
     * 平台获取竞价详细信息
     * @param biddingSn
     * @return
     */
    PlatformBiddingPurchaseInfoVO getPlatformBiddingPurchaseInfo(String biddingSn);

    /**
     * 竞价公示列表
     * @param jsonObject
     * @param biddingPurchaseLambdaQueryWrapper
     * @return
     */
    PageUtils biddingPageList(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> biddingPurchaseLambdaQueryWrapper);

    /**
     * 查询竞价详情
     * @param biddingSn
     * @return
     */
    BiddingPurchaseAndItemVO getBidingDetail(String biddingSn);

    /**
     * 参与竞价
     * @param biddingSn
     */
    void putBidingSupplierInfo(String biddingSn);

    /**
     * 检查是否参与竞价
     * @param biddingSn
     */
    void checkIsBiddingSupplier(String biddingSn);

    /**
     * 中标提交审核
     *
     * @param bidRecordId
     * @param biddingId
     */
    void isHitBidingSubmit(String bidRecordId, String biddingId);

    /**
     * 中标审核竞价
     * @param dto
     */
    void auditHitBidding(AuditBidingInfoDTO dto);

    /**
     * 流标
     * @param biddingId
     */
    void loseEfficacyBidding(String biddingId);

    /**
     * 根据竞价id删除竞价明细
     * @param biddingProductIds
     */
    void deleteBidingOrderItemsByBiddingId(List<String> biddingProductIds);


    /**
     * 删除竞价
     * @param biddingId
     */
    void deleteBidingByBiddingId(String biddingId);

    /**
     * 根据竞价id追加竞价明细
     * @param dto
     */
    void createInventoryBidingByBiddingId(CreateBidingOrderItemsByBiddingIdDTO dto);
    // 根据清单编号查询中标的数据

    /**
     * 查询已中标！的竞价数据，用于生成订单
     * @param synthesizeTemporarySn 大宗清单编号
     * @return
     */
    List<HitBidVo> getBiddingPurchaseBySynthesizeTemporarySn(String synthesizeTemporarySn);


    void bidOpening(String id);

    void deadlineTime(BiddingPurchase biddingPurchase);



}
