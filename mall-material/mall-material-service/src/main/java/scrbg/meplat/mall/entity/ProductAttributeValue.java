package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;

/**
 * @描述：商品属性值
 * @作者: y
 * @日期: 2022-11-13
 */
@ApiModel(value = "商品属性值")
@Data
@TableName("product_attribute_value")
public class ProductAttributeValue extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "商品属性id")

    private String skuAttributeId;

    @ApiModelProperty(value = "商品id")

    private String productId;

    @ApiModelProperty(value = "属性id")

    private String attributeId;

    @ApiModelProperty(value = "属性名")

    private String attributeName;

    @ApiModelProperty(value = "属性值")

    private String attributeValue;


    @ApiModelProperty(value = "状态")

    private Integer state;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;
}
