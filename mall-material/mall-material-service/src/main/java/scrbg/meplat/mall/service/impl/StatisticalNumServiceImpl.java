package scrbg.meplat.mall.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.StatisticalNumMapper;
import scrbg.meplat.mall.service.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.vo.platform.StatisticalNumVo;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @描述：物资数据统计 服务类
 * @作者: ye
 * @日期: 2023-08-27
 */
@Service
public class StatisticalNumServiceImpl extends ServiceImpl<StatisticalNumMapper, StatisticalNum> implements StatisticalNumService{
    @Autowired
    public MallConfig mallConfig;
    @Autowired
    public EnterpriseInfoService enterpriseInfoService;
    @Autowired
    public ShopService shopService;
    @Autowired
    public ShopBusinessService shopBusinessService;

    @Autowired
    public OrdersService ordersService;

    @Autowired
    public UserService userService;

    @Autowired
    public OrderShipService orderShipService;

    @Autowired
    public ProductService productService;
    @Autowired
    public StatisticalNumService statisticalNumService;
    @Resource
    public MaterialReconciliationService materialReconciliationService;
    @Autowired
    DealOrderInfoService dealOrderInfoService;
    @Resource
    private OrderShipDtlService orderShipDtlService;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<StatisticalNum> queryWrapper) {
        IPage<StatisticalNum> page = this.page(
        new Query<StatisticalNum>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(StatisticalNum statisticalNum) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(statisticalNum);
    }

    @Override
    public void update(StatisticalNum statisticalNum) {
        super.updateById(statisticalNum);
    }


    @Override
    public StatisticalNum getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }

    @Override
    public List<StatisticalNumVo> staticsSystem(int type) {
        switch (type) {
            case 1:
                return staticsSystem();
            case 2:
                return staticsSystemBusiness();
            case 3:
                return staticsSystemNotBusiness();
        }
        return null;
    }
    // 全部统计
    private List<StatisticalNumVo> staticsSystem() {
        //System.out.println("当前时间：" + LocalDateTime.now());
         List<StatisticalNumVo> list = new ArrayList<>();
        List<DealOrderInfo> dealOrderInfos = dealOrderInfoService.lambdaQuery().select(DealOrderInfo::getAmount).list();
        BigDecimal bigDecimal = new BigDecimal(0);
        for (DealOrderInfo dealOrderInfo : dealOrderInfos) {
            bigDecimal = bigDecimal.add(dealOrderInfo.getAmount());
        }
        StatisticalNumVo vo12= new StatisticalNumVo();
        vo12.setNum(bigDecimal);
        vo12.setName("总结算金额");
        list.add(vo12);
        //上架商品总数
        int upProductNum = productService.count(new LambdaQueryWrapper<Product>().eq(Product::getState,1));
        //System.out.println("上架商品总数：" + upProductNum);
        StatisticalNumVo vo4= new StatisticalNumVo();
        vo4.setName("上架商品总数");
        vo4.setNum(upProductNum);
        list.add(vo4);


       //查询审核通过的对账单信息  订单类型和对账单金额
        List<MaterialReconciliation> ls1= materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getState, 3)
                .select(MaterialReconciliation::getReconciliationAmount,
                        MaterialReconciliation::getReconciliationProductType)
                .list();

        //查询订单数据
        List<Orders> ordersAllList = ordersService.list(new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrderClass, 1, 2)
                .select(Orders::getProductType,Orders::getActualAmount)
        );

        //大宗临购订单
        List<Orders> clgOrderLists = ordersAllList.stream().filter(item -> item.getProductType() == 1).collect(Collectors.toList());
        StatisticalNumVo vo14= new StatisticalNumVo();
        vo14.setName("大宗临购订单数");
        vo14.setNum(clgOrderLists.size());
        list.add(vo14);
        // 大宗临购订单金额
        BigDecimal sumAmount2 = new BigDecimal(0);
        for (Orders orders : clgOrderLists) {
            sumAmount2=sumAmount2.add(orders.getActualAmount());
        }
        //System.out.println("大宗临购订单金额：" + sumAmount2);
        StatisticalNumVo vo15= new StatisticalNumVo();
        vo15.setName("大宗临购订单金额");
        vo15.setNum(sumAmount2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo15);

        // 大宗临购对账金额
        List<MaterialReconciliation> list3 = ls1.stream().filter(item -> item.getReconciliationProductType() == 1 ).collect(Collectors.toList());
        BigDecimal toavbleA3 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list3) {
            toavbleA3 = toavbleA3.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo16= new StatisticalNumVo();
        vo16.setName("大宗临购对账金额");
        vo16.setNum(toavbleA3.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo16);

        // 周转材料订单数
        List<Orders> contractPlanLists = ordersAllList.stream().filter(item -> item.getProductType() == 2).collect(Collectors.toList());
        StatisticalNumVo vo9= new StatisticalNumVo();
        vo9.setName("周转材料订单数");
        vo9.setNum(contractPlanLists.size());
        list.add(vo9);
        // 周转材料订单对账总金额
        List<MaterialReconciliation> ls = ls1.stream().filter(item -> item.getReconciliationProductType() == 2).collect(Collectors.toList());
        BigDecimal amount = new BigDecimal(0).setScale(2,BigDecimal.ROUND_HALF_UP);
        for (MaterialReconciliation reconciliation : ls) {
            amount =   reconciliation.getReconciliationAmount().add(amount);
        }
        StatisticalNumVo vo11= new StatisticalNumVo();
        vo11.setName("周转材料订单对账总金额");
        vo11.setNum(amount);
        list.add(vo11);


        //零星订单
        List<Orders> ordersList = ordersAllList.stream().filter(item -> item.getProductType() == 0).collect(Collectors.toList());
        BigDecimal sumAmount = new BigDecimal(0);
        for (Orders orders : ordersList) {
            sumAmount=sumAmount.add(orders.getActualAmount());
        }
        StatisticalNumVo vo6= new StatisticalNumVo();
        vo6.setName("零星采购订单数");
        vo6.setNum(ordersList.size());
        list.add(vo6);


        StatisticalNumVo vo7= new StatisticalNumVo();
        vo7.setName("零星采购订单金额");
        vo7.setNum(sumAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo7);

        //零星对账单
        List<MaterialReconciliation> list2 = ls1.stream().filter(item -> item.getReconciliationProductType() == 0).collect(Collectors.toList());
        // 零星采购对账金额
        BigDecimal toavbleA2 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list2) {
            toavbleA2 = toavbleA2.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo13= new StatisticalNumVo();
        vo13.setName("零星采购对账金额");
        vo13.setNum(toavbleA2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo13);
        // 零星采购发货金额
        List<OrderShip> orderShips = orderShipService.list(new LambdaQueryWrapper<OrderShip>().eq(OrderShip::getProductType, 0)
                .in(OrderShip::getType, 1,2));
        BigDecimal orderShipAmount = new BigDecimal(0);
        if(!orderShips.isEmpty()){
            List<String> billIds = orderShips.stream().map(OrderShip::getBillId).collect(Collectors.toList());
            List<OrderShipDtl> itemList = orderShipDtlService.lambdaQuery().in(OrderShipDtl::getBillId, billIds)
                    .select(OrderShipDtl::getReturnCounts,OrderShipDtl::getProductPrice,OrderShipDtl::getShipNum).list();
//        for (OrderShip orderShip : orderShips) {
            //orderShipAmount=orderShipAmount.add(orderShip.getRateAmount());
            // 查询明细计算

            for (OrderShipDtl orderShipDtl : itemList) {
                // 已退货
                BigDecimal returnCounts = orderShipDtl.getReturnCounts();
                // 已收货数量
                BigDecimal shipNum = orderShipDtl.getShipNum();
                BigDecimal productPrice = orderShipDtl.getProductPrice();
                BigDecimal realAmount = shipNum.subtract(returnCounts).multiply(productPrice).setScale(2, BigDecimal.ROUND_UP);
                orderShipAmount = orderShipAmount.add(realAmount);
            }
//        }
            //System.out.println("零星采购发货金额：" + orderShipAmount);
        }

        StatisticalNumVo vo8= new StatisticalNumVo();
        vo8.setName("零星采购发货金额");
        vo8.setNum(orderShipAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo8);


        //店铺数 排除审核未通过
        // TODO 店铺bug
        int shopCoounts = shopService.count(new LambdaQueryWrapper<Shop>().ne(Shop::getAuditStatus, 3));
        //System.out.println("店铺数：" + shopCoounts);
        StatisticalNumVo vo1= new StatisticalNumVo();
        vo1.setName("店铺数");
        vo1.setNum(shopCoounts);
        list.add(vo1);

        //商品总数
        int productNum = productService.count();
        //System.out.println("商品总数：" + productNum);
        StatisticalNumVo vo5= new StatisticalNumVo();
        vo5.setName("商品总数");
        vo5.setNum(productNum);
        list.add(vo5);

        //供应商数
        int supplierNum = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getIsPcwp,1).isNotNull(EnterpriseInfo::getSocialCreditCode));
        //System.out.println("外部供应商数：" + supplierNum);
        StatisticalNumVo vo0 = new StatisticalNumVo();
        vo0.setName("外部供应商数");
        vo0.setNum(supplierNum);
        list.add(vo0);

        //内部用户数
        int inUserCounts = userService.count(new LambdaQueryWrapper<User>().eq(User::getIsInternalUser,1));
        //System.out.println("使用过平台路桥用户数：" + inUserCounts);
        StatisticalNumVo vo2= new StatisticalNumVo();
        vo2.setName("使用过平台路桥用户数");
        vo2.setNum(inUserCounts);
        list.add(vo2);
        //内部企业数
        int inEnterPrise = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>().isNotNull(EnterpriseInfo::getInteriorId));
        //System.out.println("使用过平台路桥企业数据：" + inEnterPrise);
        StatisticalNumVo vo3= new StatisticalNumVo();
        vo3.setName("使用过平台路桥企业数据");
        vo3.setNum(inEnterPrise);
        list.add(vo3);


        return list;
    }

    // 自营店统计
    private List<StatisticalNumVo> staticsSystemBusiness() {
        //System.out.println("当前时间：" + LocalDateTime.now());
        List<StatisticalNumVo> list = new ArrayList<>();
        // 先找到自营店id
        List<String> shopIds = shopBusinessService.lambdaQuery()
                .eq(ShopBusiness::getState,1)//状态为启用
                .select(ShopBusiness::getShopId)
                .list()
                .stream()
                .map(ShopBusiness::getShopId)
                .collect(Collectors.toList());
        // 或者关联的供应商id
        List<String> enterpriseIds = shopBusinessService.lambdaQuery()
                .eq(ShopBusiness::getState,1)//状态为启用
                .select(ShopBusiness::getEnterpriseId)
                .list()
                .stream()
                .map(ShopBusiness::getEnterpriseId)
                .collect(Collectors.toList());


        List<DealOrderInfo> dealOrderInfos = dealOrderInfoService.lambdaQuery()
                .in(DealOrderInfo::getShopId, shopIds)
                .select(DealOrderInfo::getAmount)
                .list();
        BigDecimal bigDecimal = new BigDecimal(0);
        for (DealOrderInfo dealOrderInfo : dealOrderInfos) {
            bigDecimal = bigDecimal.add(dealOrderInfo.getAmount());
        }
        StatisticalNumVo vo12= new StatisticalNumVo();
        vo12.setNum(bigDecimal);
        vo12.setName("总结算金额");
        list.add(vo12);
        //上架商品总数
        int upProductNum = productService.count(new LambdaQueryWrapper<Product>()
                .in(Product::getShopId, shopIds)
                .eq(Product::getState,1));
        //System.out.println("上架商品总数：" + upProductNum);
        StatisticalNumVo vo4= new StatisticalNumVo();
        vo4.setName("上架商品总数");
        vo4.setNum(upProductNum);
        list.add(vo4);


        //查询订单数据
        List<Orders> ordersAllList = ordersService.list(new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrderClass, 1, 2)
                .in(Orders::getShopId, shopIds)
                .select(Orders::getProductType,Orders::getActualAmount)
        );
        //查询审核通过的对账单信息  订单类型和对账单金额
        List<MaterialReconciliation> ls1= materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getState, 3)
                .in(MaterialReconciliation::getEnterpriseId, enterpriseIds) // 添加供应商id
                .select(MaterialReconciliation::getReconciliationAmount,
                        MaterialReconciliation::getReconciliationProductType)
                .list();

        //大宗临购订单
        List<Orders> clgOrderLists = ordersAllList.stream().filter(item -> item.getProductType() == 1).collect(Collectors.toList());
        StatisticalNumVo vo14= new StatisticalNumVo();
        vo14.setName("大宗临购订单数");
        vo14.setNum(clgOrderLists.size());
        list.add(vo14);
        // 大宗临购订单金额
        BigDecimal sumAmount2 = new BigDecimal(0);
        for (Orders orders : clgOrderLists) {
            sumAmount2=sumAmount2.add(orders.getActualAmount());
        }
        //System.out.println("大宗临购订单金额：" + sumAmount2);
        StatisticalNumVo vo15= new StatisticalNumVo();
        vo15.setName("大宗临购订单金额");
        vo15.setNum(sumAmount2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo15);
        // 大宗临购对账金额
        List<MaterialReconciliation> list3 = ls1.stream().filter(item -> item.getReconciliationProductType() ==1 ).collect(Collectors.toList());
        BigDecimal toavbleA3 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list3) {
            toavbleA3 = toavbleA3.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo16= new StatisticalNumVo();
        vo16.setName("大宗临购对账金额");
        vo16.setNum(toavbleA3.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo16);

        // 改为周转材料订单订单数
        List<Orders> contractPlanLists = ordersAllList.stream().filter(item -> item.getProductType() == 2).collect(Collectors.toList());
        StatisticalNumVo vo9= new StatisticalNumVo();
        vo9.setName("周转材料订单数");
        vo9.setNum(contractPlanLists.size());
        list.add(vo9);

        // 改为周转材料订单对账总金额
        List<MaterialReconciliation> ls = ls1.stream().filter(item -> item.getReconciliationProductType() == 2).collect(Collectors.toList());
        BigDecimal amount = new BigDecimal(0).setScale(2,BigDecimal.ROUND_HALF_UP);
        for (MaterialReconciliation reconciliation : ls) {
            amount =   reconciliation.getReconciliationAmount().add(amount);
        }
        StatisticalNumVo vo11= new StatisticalNumVo();
        vo11.setName("周转材料订单对账总金额");
        vo11.setNum(amount);
        list.add(vo11);


        //零星订单
        List<Orders> ordersList = ordersAllList.stream().filter(item -> item.getProductType() == 0).collect(Collectors.toList());
        BigDecimal sumAmount = new BigDecimal(0);
        for (Orders orders : ordersList) {
            sumAmount=sumAmount.add(orders.getActualAmount());
        }
        //零星采购订单数
        StatisticalNumVo vo6= new StatisticalNumVo();
        vo6.setName("零星采购订单数");
        vo6.setNum(ordersList.size());
        list.add(vo6);
        StatisticalNumVo vo7= new StatisticalNumVo();
        vo7.setName("零星采购订单金额");
        vo7.setNum(sumAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo7);

        //零星对账单
        List<MaterialReconciliation> list2 = ls1.stream().filter(item -> item.getReconciliationProductType() == 0).collect(Collectors.toList());
        // 零星采购对账金额
        BigDecimal toavbleA2 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list2) {
            toavbleA2 = toavbleA2.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo13= new StatisticalNumVo();
        vo13.setName("零星采购对账金额");
        vo13.setNum(toavbleA2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo13);

        //零星发货金额
        List<OrderShip> orderShips = orderShipService.list(new LambdaQueryWrapper<OrderShip>().eq(OrderShip::getProductType,0)
                .in(OrderShip::getShopId, shopIds)
                .in(OrderShip::getType, 1,2));
        BigDecimal orderShipAmount = new BigDecimal(0);
        if(!orderShips.isEmpty()){
            List<String> billIds = orderShips.stream().map(OrderShip::getBillId).collect(Collectors.toList());
            List<OrderShipDtl> itemList = orderShipDtlService.lambdaQuery().in(OrderShipDtl::getBillId, billIds)
                    .select(OrderShipDtl::getReturnCounts,OrderShipDtl::getProductPrice,OrderShipDtl::getShipNum).list();
//        for (OrderShip orderShip : orderShips) {
            //orderShipAmount=orderShipAmount.add(orderShip.getRateAmount());
            // 查询明细计算

            for (OrderShipDtl orderShipDtl : itemList) {
                // 已退货
                BigDecimal returnCounts = orderShipDtl.getReturnCounts();
                // 已收货数量
                BigDecimal shipNum = orderShipDtl.getShipNum();
                BigDecimal productPrice = orderShipDtl.getProductPrice();
                BigDecimal realAmount = shipNum.subtract(returnCounts).multiply(productPrice).setScale(2, BigDecimal.ROUND_UP);
                orderShipAmount = orderShipAmount.add(realAmount);
            }
//        }
            //System.out.println("零星采购发货金额：" + orderShipAmount);
        }
        StatisticalNumVo vo8= new StatisticalNumVo();
        vo8.setName("零星采购发货金额");
        vo8.setNum(orderShipAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo8);


        //店铺数 排除审核未通过 自营店铺数为shopIds的长度
        // TODO 店铺bug
        int shopCoounts = shopService.count(new LambdaQueryWrapper<Shop>().ne(Shop::getAuditStatus, 3));
        //System.out.println("店铺数：" + shopCoounts);
        StatisticalNumVo vo1= new StatisticalNumVo();
        vo1.setName("店铺数");
        vo1.setNum(shopIds.size());// 自营店先给1
        list.add(vo1);


        //商品总数
        int productNum = productService.count(new LambdaQueryWrapper<Product>().in(Product::getShopId, shopIds));
        //System.out.println("商品总数：" + productNum);
        StatisticalNumVo vo5= new StatisticalNumVo();
        vo5.setName("商品总数");
        vo5.setNum(productNum);
        list.add(vo5);


        //供应商数 自营店外部供应商给0
        int supplierNum = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getIsPcwp,1).isNotNull(EnterpriseInfo::getSocialCreditCode));
        //System.out.println("外部供应商数：" + supplierNum);
        StatisticalNumVo vo0 = new StatisticalNumVo();
        vo0.setName("外部供应商数");
        vo0.setNum(0);// 直接先给0
        list.add(vo0);



        //内部用户数
        int inUserCounts = userService.count(new LambdaQueryWrapper<User>().eq(User::getIsInternalUser,1));
        //System.out.println("使用过平台路桥用户数：" + inUserCounts);
        StatisticalNumVo vo2= new StatisticalNumVo();
        vo2.setName("使用过平台路桥用户数");
        vo2.setNum(inUserCounts);
        list.add(vo2);
        //内部企业数
        int inEnterPrise = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>().isNotNull(EnterpriseInfo::getInteriorId));
        //System.out.println("使用过平台路桥企业数据：" + inEnterPrise);
        StatisticalNumVo vo3= new StatisticalNumVo();
        vo3.setName("使用过平台路桥企业数据");
        vo3.setNum(inEnterPrise);
        list.add(vo3);






        return list;
    }
    // 非自营店统计
    private List<StatisticalNumVo> staticsSystemNotBusiness() {
        //System.out.println("当前时间：" + LocalDateTime.now());
        List<StatisticalNumVo> list = new ArrayList<>();
        // 先找到自营店id
        List<String> shopIds = shopBusinessService.lambdaQuery()
                .eq(ShopBusiness::getState,1)//状态为启用
                .select(ShopBusiness::getShopId)
                .list()
                .stream()
                .map(ShopBusiness::getShopId)
                .collect(Collectors.toList());
        // 或者关联的供应商id
        List<String> enterpriseIds = shopBusinessService.lambdaQuery()
                .eq(ShopBusiness::getState,1)//状态为启用
                .select(ShopBusiness::getEnterpriseId)
                .list()
                .stream()
                .map(ShopBusiness::getEnterpriseId)
                .collect(Collectors.toList());


        List<DealOrderInfo> dealOrderInfos = dealOrderInfoService.lambdaQuery()
                .notIn(DealOrderInfo::getShopId, shopIds)
                .select(DealOrderInfo::getAmount)
                .list();
        BigDecimal bigDecimal = new BigDecimal(0);
        for (DealOrderInfo dealOrderInfo : dealOrderInfos) {
            bigDecimal = bigDecimal.add(dealOrderInfo.getAmount());
        }
        StatisticalNumVo vo12= new StatisticalNumVo();
        vo12.setNum(bigDecimal);
        vo12.setName("总结算金额");
        list.add(vo12);
        //上架商品总数
        int upProductNum = productService.count(new LambdaQueryWrapper<Product>()
                .eq(Product::getState,1)
                .notIn(Product::getShopId, shopIds));
        //System.out.println("上架商品总数：" + upProductNum);
        StatisticalNumVo vo4= new StatisticalNumVo();
        vo4.setName("上架商品总数");
        vo4.setNum(upProductNum);
        list.add(vo4);


        //查询订单数据    todo  productType
        List<Orders> ordersAllList = ordersService.list(new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrderClass, 1, 2)
                .notIn(Orders::getShopId, shopIds)
                .select(Orders::getProductType,Orders::getActualAmount)
        );
        //查询审核通过的对账单信息  订单类型和对账单金额
        List<MaterialReconciliation> ls1= materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getState, 3)
                .notIn(MaterialReconciliation::getEnterpriseId, enterpriseIds) // 添加供应商id
                .select(MaterialReconciliation::getReconciliationAmount,
                        MaterialReconciliation::getReconciliationProductType)
                .list();

        //大宗临购订单
        List<Orders> clgOrderLists = ordersAllList.stream().filter(item -> item.getProductType() == 1).collect(Collectors.toList());
        StatisticalNumVo vo14= new StatisticalNumVo();
        vo14.setName("大宗临购订单数");
        vo14.setNum(clgOrderLists.size());
        list.add(vo14);
        // 大宗临购订单金额
        BigDecimal sumAmount2 = new BigDecimal(0);
        for (Orders orders : clgOrderLists) {
            sumAmount2=sumAmount2.add(orders.getActualAmount());
        }
        //System.out.println("大宗临购订单金额：" + sumAmount2);
        StatisticalNumVo vo15= new StatisticalNumVo();
        vo15.setName("大宗临购订单金额");
        vo15.setNum(sumAmount2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo15);
        // 大宗临购对账金额
        List<MaterialReconciliation> list3 = ls1.stream().filter(item -> item.getReconciliationProductType() ==1 ).collect(Collectors.toList());
        BigDecimal toavbleA3 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list3) {
            toavbleA3 = toavbleA3.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo16= new StatisticalNumVo();
        vo16.setName("大宗临购对账金额");
        vo16.setNum(toavbleA3.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo16);

        //大宗月供订单 todo 改为周转材料订单订单数
        List<Orders> contractPlanLists = ordersAllList.stream().filter(item -> item.getProductType() == 2).collect(Collectors.toList());
        StatisticalNumVo vo9= new StatisticalNumVo();
        vo9.setName("周转材料订单数");
        vo9.setNum(contractPlanLists.size());
        list.add(vo9);

        // 大宗对账单 todo 改为周转材料订单对账总金额
        List<MaterialReconciliation> ls = ls1.stream().filter(item -> item.getReconciliationProductType() == 2).collect(Collectors.toList());
        BigDecimal amount = new BigDecimal(0).setScale(2,BigDecimal.ROUND_HALF_UP);
        for (MaterialReconciliation reconciliation : ls) {
            amount =   reconciliation.getReconciliationAmount().add(amount);
        }
        StatisticalNumVo vo11= new StatisticalNumVo();
        vo11.setName("周转材料订单对账总金额");
        vo11.setNum(amount);
        list.add(vo11);


        //零星订单
        List<Orders> ordersList = ordersAllList.stream().filter(item -> item.getProductType() == 0).collect(Collectors.toList());
        BigDecimal sumAmount = new BigDecimal(0);
        for (Orders orders : ordersList) {
            sumAmount=sumAmount.add(orders.getActualAmount());
        }
        //零星采购订单数
        StatisticalNumVo vo6= new StatisticalNumVo();
        vo6.setName("零星采购订单数");
        vo6.setNum(ordersList.size());
        list.add(vo6);
        StatisticalNumVo vo7= new StatisticalNumVo();
        vo7.setName("零星采购订单金额");
        vo7.setNum(sumAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo7);

        //零星对账单
        List<MaterialReconciliation> list2 = ls1.stream().filter(item -> item.getReconciliationProductType() == 0).collect(Collectors.toList());
        // 零星采购对账金额
        BigDecimal toavbleA2 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list2) {
            toavbleA2 = toavbleA2.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo13= new StatisticalNumVo();
        vo13.setName("零星采购对账金额");
        vo13.setNum(toavbleA2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo13);

        //零星发货金额
        List<OrderShip> orderShips = orderShipService.list(new LambdaQueryWrapper<OrderShip>().eq(OrderShip::getProductType,0)
                .notIn(OrderShip::getShopId, shopIds)
                .in(OrderShip::getType, 1,2));
        BigDecimal orderShipAmount = new BigDecimal(0);
        if(!orderShips.isEmpty()){
            List<String> billIds = orderShips.stream().map(OrderShip::getBillId).collect(Collectors.toList());
            List<OrderShipDtl> itemList = orderShipDtlService.lambdaQuery().in(OrderShipDtl::getBillId, billIds)
                    .select(OrderShipDtl::getReturnCounts,OrderShipDtl::getProductPrice,OrderShipDtl::getShipNum).list();
//        for (OrderShip orderShip : orderShips) {
            //orderShipAmount=orderShipAmount.add(orderShip.getRateAmount());
            // 查询明细计算

            for (OrderShipDtl orderShipDtl : itemList) {
                // 已退货
                BigDecimal returnCounts = orderShipDtl.getReturnCounts();
                // 已收货数量
                BigDecimal shipNum = orderShipDtl.getShipNum();
                BigDecimal productPrice = orderShipDtl.getProductPrice();
                BigDecimal realAmount = shipNum.subtract(returnCounts).multiply(productPrice).setScale(2, BigDecimal.ROUND_UP);
                orderShipAmount = orderShipAmount.add(realAmount);
            }
//        }
            //System.out.println("零星采购发货金额：" + orderShipAmount);
        }
        StatisticalNumVo vo8= new StatisticalNumVo();
        vo8.setName("零星采购发货金额");
        vo8.setNum(orderShipAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo8);


        //店铺数 排除审核未通过 自营店铺数为shopIds的长度
        // TODO 店铺bug
        int shopCoounts = shopService.count(new LambdaQueryWrapper<Shop>().ne(Shop::getAuditStatus, 3));
        //System.out.println("店铺数：" + shopCoounts);
        StatisticalNumVo vo1= new StatisticalNumVo();
        vo1.setName("店铺数");
        vo1.setNum(shopCoounts - shopIds.size());// 非自营店
        list.add(vo1);


        //商品总数
        int productNum = productService.count(new LambdaQueryWrapper<Product>()
                .notIn(Product::getShopId, shopIds));
        //System.out.println("商品总数：" + productNum);
        StatisticalNumVo vo5= new StatisticalNumVo();
        vo5.setName("商品总数");
        vo5.setNum(productNum);
        list.add(vo5);


        //供应商数
        int supplierNum = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getIsPcwp,1).isNotNull(EnterpriseInfo::getSocialCreditCode));
        //System.out.println("外部供应商数：" + supplierNum);
        StatisticalNumVo vo0 = new StatisticalNumVo();
        vo0.setName("外部供应商数");
        vo0.setNum(supplierNum);
        list.add(vo0);



        //内部用户数
        int inUserCounts = userService.count(new LambdaQueryWrapper<User>().eq(User::getIsInternalUser,1));
        //System.out.println("使用过平台路桥用户数：" + inUserCounts);
        StatisticalNumVo vo2= new StatisticalNumVo();
        vo2.setName("使用过平台路桥用户数");
        vo2.setNum(inUserCounts);
        list.add(vo2);
        //内部企业数
        int inEnterPrise = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>().isNotNull(EnterpriseInfo::getInteriorId));
        //System.out.println("使用过平台路桥企业数据：" + inEnterPrise);
        StatisticalNumVo vo3= new StatisticalNumVo();
        vo3.setName("使用过平台路桥企业数据");
        vo3.setNum(inEnterPrise);
        list.add(vo3);



        return list;
    }
    @Override
    public List<StatisticalNumVo> staticsSystem(JSONObject jsonObject, int type) {
        switch (type) {
            case 1:
                return staticsSystemTime(jsonObject);
            case 2:
                return staticsSystemBusinessTime(jsonObject);
            case 3:
                return staticsSystemNotBusinessTime(jsonObject);
        }
        return null;
    }
    // 全部
    private List<StatisticalNumVo> staticsSystemTime(JSONObject jsonObject) {
        List<String> dateScope = (List) jsonObject.get("dateScope");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startDdate = null;
        Date endDdate = null;
        try {
            startDdate = sdf.parse(dateScope.get(0));
            endDdate = sdf.parse(dateScope.get(1));
        } catch (ParseException e) {
            throw new BusinessException("时间区间异常");
        }
        List<StatisticalNumVo> list = new ArrayList<>();
        List<DealOrderInfo> dealOrderInfos = dealOrderInfoService.lambdaQuery()
                .between(DealOrderInfo::getGmtCreate,startDdate,endDdate)
                .select(DealOrderInfo::getAmount)
                .list();
        BigDecimal bigDecimal = new BigDecimal(0);
        for (DealOrderInfo dealOrderInfo : dealOrderInfos) {
            bigDecimal = bigDecimal.add(dealOrderInfo.getAmount());
        }
        StatisticalNumVo vo12= new StatisticalNumVo();
        vo12.setNum(bigDecimal);
        vo12.setName("总结算金额");
        list.add(vo12);
        //上架商品总数
        int upProductNum = productService.count(new LambdaQueryWrapper<Product>()
                .between(Product::getPutawayDate,startDdate,endDdate)
                .eq(Product::getState,1));
        //System.out.println("上架商品总数：" + upProductNum);
        StatisticalNumVo vo4= new StatisticalNumVo();
        vo4.setName("上架商品总数");
        vo4.setNum(upProductNum);
        list.add(vo4);




        //查询审核通过的对账单信息  订单类型和对账单金额
        List<MaterialReconciliation> ls1= materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getState, 3)
                .between(MaterialReconciliation::getEndTime,startDdate,endDdate)
                .select(MaterialReconciliation::getReconciliationAmount,
                        MaterialReconciliation::getReconciliationProductType)
                .list();
        //查询订单数据
        List<Orders> ordersAllList = ordersService.list(new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrderClass, 1, 2)
                .between(Orders::getGmtCreate,startDdate,endDdate)
                .select(Orders::getProductType,Orders::getActualAmount)
        );

        //大宗临购订单
        List<Orders> clgOrderLists = ordersAllList.stream().filter(item -> item.getProductType() == 1).collect(Collectors.toList());
        StatisticalNumVo vo14= new StatisticalNumVo();
        vo14.setName("大宗临购订单数");
        vo14.setNum(clgOrderLists.size());
        list.add(vo14);
        // 大宗临购订单金额
        BigDecimal sumAmount2 = new BigDecimal(0);
        for (Orders orders : clgOrderLists) {
            sumAmount2=sumAmount2.add(orders.getActualAmount());
        }
        //System.out.println("大宗临购订单金额：" + sumAmount2);
        StatisticalNumVo vo15= new StatisticalNumVo();
        vo15.setName("大宗临购订单金额");
        vo15.setNum(sumAmount2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo15);

        // 大宗临购对账金额
        List<MaterialReconciliation> list3 = ls1.stream().filter(item -> item.getReconciliationProductType() == 1).collect(Collectors.toList());
//        List<MaterialReconciliation> list3 = materialReconciliationService.lambdaQuery()
//                .eq(MaterialReconciliation::getBusinessType, 6)
//                .eq(MaterialReconciliation::getState, 3)
//                .between(MaterialReconciliation::getEndTime,startDdate,endDdate)
//                .list();
        BigDecimal toavbleA3 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list3) {
            toavbleA3 = toavbleA3.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo16= new StatisticalNumVo();
        vo16.setName("大宗临购对账金额");
        vo16.setNum(toavbleA3.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo16);


        // 周转材料订单数
        List<Orders> contractPlanLists = ordersAllList.stream().filter(item -> item.getProductType() == 2).collect(Collectors.toList());
        StatisticalNumVo vo9= new StatisticalNumVo();
        vo9.setName("周转材料订单数");
        vo9.setNum(contractPlanLists.size());
        list.add(vo9);
        // 周转材料对账单
        List<MaterialReconciliation> ls = ls1.stream().filter(item -> item.getReconciliationProductType() == 2).collect(Collectors.toList());
        BigDecimal amount = new BigDecimal(0).setScale(2,BigDecimal.ROUND_HALF_UP);
        for (MaterialReconciliation reconciliation : ls) {
            amount =   reconciliation.getReconciliationAmount().add(amount);
        }
        StatisticalNumVo vo11= new StatisticalNumVo();
        vo11.setName("周转材料订单对账总金额");
        vo11.setNum(amount);
        list.add(vo11);


        //零星订单
        List<Orders> ordersList = ordersAllList.stream().filter(item -> item.getProductType() == 0).collect(Collectors.toList());
        BigDecimal sumAmount = new BigDecimal(0);
        for (Orders orders : ordersList) {
            sumAmount=sumAmount.add(orders.getActualAmount());
        }
        //System.out.println("零星采购订单数：" + ordersList.size());
        StatisticalNumVo vo6= new StatisticalNumVo();
        vo6.setName("零星采购订单数");
        vo6.setNum(ordersList.size());
        list.add(vo6);

        //System.out.println("零星采购订单金额：" + sumAmount);
        StatisticalNumVo vo7= new StatisticalNumVo();
        vo7.setName("零星采购订单金额");
        vo7.setNum(sumAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo7);



        // 零星采购对账金额
//        List<MaterialReconciliation> list2 = materialReconciliationService.lambdaQuery()
//                .eq(MaterialReconciliation::getBusinessType, 2)
//                .eq(MaterialReconciliation::getState, 3)
//                .between(MaterialReconciliation::getEndTime,startDdate,endDdate)
//                .list();
        List<MaterialReconciliation> list2 = ls1.stream().filter(item -> item.getReconciliationProductType() == 0).collect(Collectors.toList());
        // 零星采购对账金额
        BigDecimal toavbleA2 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list2) {
            toavbleA2 = toavbleA2.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo13= new StatisticalNumVo();
        vo13.setName("零星采购对账金额");
        vo13.setNum(toavbleA2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo13);
        // TODO  发货时间或创建时间？
        // 零星采购发货金额
        List<OrderShip> orderShips = orderShipService.list(new LambdaQueryWrapper<OrderShip>().eq(OrderShip::getProductType, 0)
                .between(OrderShip::getShipData,startDdate,endDdate).select(OrderShip::getBillId)
                .in(OrderShip::getType, 1,2));
        BigDecimal orderShipAmount = new BigDecimal(0).setScale(2,BigDecimal.ROUND_UP);
        if(!orderShips.isEmpty()){
            List<String> billIds = orderShips.stream().map(OrderShip::getBillId).collect(Collectors.toList());
            List<OrderShipDtl> itemList = orderShipDtlService.lambdaQuery().in(OrderShipDtl::getBillId, billIds)
                    .select(OrderShipDtl::getReturnCounts,OrderShipDtl::getProductPrice,OrderShipDtl::getShipNum).list();

//        for (OrderShip orderShip : orderShips) {
//            List<OrderShipDtl> itemList = orderShipDtlService.lambdaQuery().eq(OrderShipDtl::getBillId, orderShip.getBillId()).list();
            for (OrderShipDtl orderShipDtl : itemList) {
                // 已退货
                BigDecimal returnCounts = orderShipDtl.getReturnCounts();
                // 已收货数量
                BigDecimal shipNum = orderShipDtl.getShipNum();
                BigDecimal productPrice = orderShipDtl.getProductPrice();
                BigDecimal realAmount = shipNum.subtract(returnCounts).multiply(productPrice).setScale(2, BigDecimal.ROUND_UP);
                orderShipAmount = orderShipAmount.add(realAmount);
            }
//        }
            //System.out.println("零星采购发货金额：" + orderShipAmount);
        }

        StatisticalNumVo vo8= new StatisticalNumVo();
        vo8.setName("零星采购发货金额");
        vo8.setNum(orderShipAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo8);


        //店铺数 排除审核未通过
        // TODO 店铺bug
        int shopCoounts = shopService.count(new LambdaQueryWrapper<Shop>()
                .between(Shop::getGmtCreate,startDdate,endDdate)
                .ne(Shop::getAuditStatus, 3));
        //System.out.println("店铺数：" + shopCoounts);
        StatisticalNumVo vo1= new StatisticalNumVo();
        vo1.setName("店铺数");
        vo1.setNum(shopCoounts);
        list.add(vo1);


        //商品总数
        int productNum = productService.lambdaQuery()
                .between(Product::getGmtCreate,startDdate,endDdate)
                .count();
        //System.out.println("商品总数：" + productNum);
        StatisticalNumVo vo5= new StatisticalNumVo();
        vo5.setName("商品总数");
        vo5.setNum(productNum);
        list.add(vo5);


        //供应商数
        int supplierNum = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getIsPcwp,1)
                        .between(EnterpriseInfo::getGmtCreate,startDdate,endDdate)
                .isNotNull(EnterpriseInfo::getSocialCreditCode));
        //System.out.println("外部供应商数：" + supplierNum);
        StatisticalNumVo vo0 = new StatisticalNumVo();
        vo0.setName("外部供应商数");
        vo0.setNum(supplierNum);
        list.add(vo0);

        //内部用户数
        int inUserCounts = userService.count(new LambdaQueryWrapper<User>().eq(User::getIsInternalUser,1)
                .between(User::getGmtCreate,startDdate,endDdate));
        //System.out.println("使用过平台路桥用户数：" + inUserCounts);
        StatisticalNumVo vo2= new StatisticalNumVo();
        vo2.setName("使用过平台路桥用户数");
        vo2.setNum(inUserCounts);
        list.add(vo2);
        //内部企业数
        int inEnterPrise = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
                        .between(EnterpriseInfo::getGmtCreate,startDdate,endDdate)
                .isNotNull(EnterpriseInfo::getInteriorId));
        //System.out.println("使用过平台路桥企业数据：" + inEnterPrise);
        StatisticalNumVo vo3= new StatisticalNumVo();
        vo3.setName("使用过平台路桥企业数据");
        vo3.setNum(inEnterPrise);
        list.add(vo3);


        return list;
    }
    // 自营店
    private List<StatisticalNumVo> staticsSystemBusinessTime(JSONObject jsonObject) {
        List<String> dateScope = (List) jsonObject.get("dateScope");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startDdate = null;
        Date endDdate = null;
        try {
            startDdate = sdf.parse(dateScope.get(0));
            endDdate = sdf.parse(dateScope.get(1));
        } catch (ParseException e) {
            throw new BusinessException("时间区间异常");
        }
        List<StatisticalNumVo> list = new ArrayList<>();
        // 先找到自营店id
        List<String> shopIds = shopBusinessService.lambdaQuery()
                .eq(ShopBusiness::getState,1)//状态为启用
                .select(ShopBusiness::getShopId)
                .list()
                .stream()
                .map(ShopBusiness::getShopId)
                .collect(Collectors.toList());
        // 或者关联的供应商id
        List<String> enterpriseIds = shopBusinessService.lambdaQuery()
                .eq(ShopBusiness::getState,1)//状态为启用
                .select(ShopBusiness::getEnterpriseId)
                .list()
                .stream()
                .map(ShopBusiness::getEnterpriseId)
                .collect(Collectors.toList());


        List<DealOrderInfo> dealOrderInfos = dealOrderInfoService.lambdaQuery()
                .in(DealOrderInfo::getShopId, shopIds)
                .between(DealOrderInfo::getGmtCreate,startDdate,endDdate)
                .select(DealOrderInfo::getAmount)
                .list();
        BigDecimal bigDecimal = new BigDecimal(0);
        for (DealOrderInfo dealOrderInfo : dealOrderInfos) {
            bigDecimal = bigDecimal.add(dealOrderInfo.getAmount());
        }
        StatisticalNumVo vo12= new StatisticalNumVo();
        vo12.setNum(bigDecimal);
        vo12.setName("总结算金额");
        list.add(vo12);
        //上架商品总数
        int upProductNum = productService.count(new LambdaQueryWrapper<Product>()
                .between(Product::getPutawayDate,startDdate,endDdate)
                .in(Product::getShopId, shopIds)
                .eq(Product::getState,1));
        //System.out.println("上架商品总数：" + upProductNum);
        StatisticalNumVo vo4= new StatisticalNumVo();
        vo4.setName("上架商品总数");
        vo4.setNum(upProductNum);
        list.add(vo4);


        //查询订单数据
        List<Orders> ordersAllList = ordersService.list(new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrderClass, 1, 2)
                .in(Orders::getShopId, shopIds)
                .between(Orders::getGmtCreate,startDdate,endDdate)
                .select(Orders::getProductType,Orders::getActualAmount)
        );
        //查询审核通过的对账单信息  订单类型和对账单金额
        List<MaterialReconciliation> ls1= materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getState, 3)
                .in(MaterialReconciliation::getEnterpriseId, enterpriseIds)
                .between(MaterialReconciliation::getEndTime,startDdate,endDdate)
                .select(MaterialReconciliation::getReconciliationAmount,
                        MaterialReconciliation::getReconciliationProductType)
                .list();
        //大宗临购订单
        List<Orders> clgOrderLists = ordersAllList.stream().filter(item -> item.getProductType() == 1).collect(Collectors.toList());
        StatisticalNumVo vo14= new StatisticalNumVo();
        vo14.setName("大宗临购订单数");
        vo14.setNum(clgOrderLists.size());
        list.add(vo14);
        // 大宗临购订单金额
        BigDecimal sumAmount2 = new BigDecimal(0);
        for (Orders orders : clgOrderLists) {
            sumAmount2=sumAmount2.add(orders.getActualAmount());
        }
        //System.out.println("大宗临购订单金额：" + sumAmount2);
        StatisticalNumVo vo15= new StatisticalNumVo();
        vo15.setName("大宗临购订单金额");
        vo15.setNum(sumAmount2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo15);

        // 大宗临购对账金额
        List<MaterialReconciliation> list3 = ls1.stream().filter(item -> item.getReconciliationProductType() == 1).collect(Collectors.toList());
//        List<MaterialReconciliation> list3 = materialReconciliationService.lambdaQuery()
//                .eq(MaterialReconciliation::getBusinessType, 6)
//                .eq(MaterialReconciliation::getState, 3)
//                .between(MaterialReconciliation::getEndTime,startDdate,endDdate)
//                .list();
        BigDecimal toavbleA3 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list3) {
            toavbleA3 = toavbleA3.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo16= new StatisticalNumVo();
        vo16.setName("大宗临购对账金额");
        vo16.setNum(toavbleA3.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo16);


        // 周转材料订单数
        List<Orders> contractPlanLists = ordersAllList.stream().filter(item -> item.getProductType() == 2).collect(Collectors.toList());
        StatisticalNumVo vo9= new StatisticalNumVo();
        vo9.setName("周转材料订单数");
        vo9.setNum(contractPlanLists.size());
        list.add(vo9);
        // 周转材料对账单
        List<MaterialReconciliation> ls = ls1.stream().filter(item -> item.getReconciliationProductType() == 2).collect(Collectors.toList());
        BigDecimal amount = new BigDecimal(0).setScale(2,BigDecimal.ROUND_HALF_UP);
        for (MaterialReconciliation reconciliation : ls) {
            amount =   reconciliation.getReconciliationAmount().add(amount);
        }
        StatisticalNumVo vo11= new StatisticalNumVo();
        vo11.setName("周转材料订单对账总金额");
        vo11.setNum(amount);
        list.add(vo11);


        //零星订单
        List<Orders> ordersList = ordersAllList.stream().filter(item -> item.getProductType() == 0).collect(Collectors.toList());
        BigDecimal sumAmount = new BigDecimal(0);
        for (Orders orders : ordersList) {
            sumAmount=sumAmount.add(orders.getActualAmount());
        }
        //System.out.println("零星采购订单数：" + ordersList.size());
        StatisticalNumVo vo6= new StatisticalNumVo();
        vo6.setName("零星采购订单数");
        vo6.setNum(ordersList.size());
        list.add(vo6);

        //System.out.println("零星采购订单金额：" + sumAmount);
        StatisticalNumVo vo7= new StatisticalNumVo();
        vo7.setName("零星采购订单金额");
        vo7.setNum(sumAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo7);



        // 零星采购对账金额
//        List<MaterialReconciliation> list2 = materialReconciliationService.lambdaQuery()
//                .eq(MaterialReconciliation::getBusinessType, 2)
//                .eq(MaterialReconciliation::getState, 3)
//                .between(MaterialReconciliation::getEndTime,startDdate,endDdate)
//                .list();
        List<MaterialReconciliation> list2 = ls1.stream().filter(item -> item.getReconciliationProductType() == 0).collect(Collectors.toList());
        // 零星采购对账金额
        BigDecimal toavbleA2 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list2) {
            toavbleA2 = toavbleA2.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo13= new StatisticalNumVo();
        vo13.setName("零星采购对账金额");
        vo13.setNum(toavbleA2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo13);
        // TODO  发货时间或创建时间？
        // 零星采购发货金额
        List<OrderShip> orderShips = orderShipService.list(new LambdaQueryWrapper<OrderShip>().eq(OrderShip::getProductType, 0)
                .between(OrderShip::getShipData,startDdate,endDdate).select(OrderShip::getBillId)
                .in(OrderShip::getShopId, shopIds)
                .in(OrderShip::getType, 1,2));
        BigDecimal orderShipAmount = new BigDecimal(0).setScale(2,BigDecimal.ROUND_UP);
        if(!orderShips.isEmpty()){
            List<String> billIds = orderShips.stream().map(OrderShip::getBillId).collect(Collectors.toList());
            List<OrderShipDtl> itemList = orderShipDtlService.lambdaQuery().in(OrderShipDtl::getBillId, billIds)
                    .select(OrderShipDtl::getReturnCounts,OrderShipDtl::getProductPrice,OrderShipDtl::getShipNum).list();

//        for (OrderShip orderShip : orderShips) {
//            List<OrderShipDtl> itemList = orderShipDtlService.lambdaQuery().eq(OrderShipDtl::getBillId, orderShip.getBillId()).list();
            for (OrderShipDtl orderShipDtl : itemList) {
                // 已退货
                BigDecimal returnCounts = orderShipDtl.getReturnCounts();
                // 已收货数量
                BigDecimal shipNum = orderShipDtl.getShipNum();
                BigDecimal productPrice = orderShipDtl.getProductPrice();
                BigDecimal realAmount = shipNum.subtract(returnCounts).multiply(productPrice).setScale(2, BigDecimal.ROUND_UP);
                orderShipAmount = orderShipAmount.add(realAmount);
            }
//        }
            //System.out.println("零星采购发货金额：" + orderShipAmount);
        }

        StatisticalNumVo vo8= new StatisticalNumVo();
        vo8.setName("零星采购发货金额");
        vo8.setNum(orderShipAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo8);


        //店铺数 排除审核未通过
        // TODO 店铺bug
        int shopCoounts = shopService.count(new LambdaQueryWrapper<Shop>()
                .between(Shop::getGmtCreate,startDdate,endDdate)
                .ne(Shop::getAuditStatus, 3));
        //System.out.println("店铺数：" + shopCoounts);
        StatisticalNumVo vo1= new StatisticalNumVo();
        vo1.setName("店铺数");
        vo1.setNum(shopIds.size());
        list.add(vo1);


        //商品总数
        int productNum = productService.lambdaQuery()
                .between(Product::getGmtCreate,startDdate,endDdate)
                .in(Product::getShopId, shopIds)
                .count();
        //System.out.println("商品总数：" + productNum);
        StatisticalNumVo vo5= new StatisticalNumVo();
        vo5.setName("商品总数");
        vo5.setNum(productNum);
        list.add(vo5);


        //供应商数 自营店外部供应商给0
        int supplierNum = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getIsPcwp,1)
                .between(EnterpriseInfo::getGmtCreate,startDdate,endDdate)
                .isNotNull(EnterpriseInfo::getSocialCreditCode));
        //System.out.println("外部供应商数：" + supplierNum);
        StatisticalNumVo vo0 = new StatisticalNumVo();
        vo0.setName("外部供应商数");
        vo0.setNum(0);
        list.add(vo0);

        //内部用户数
        int inUserCounts = userService.count(new LambdaQueryWrapper<User>().eq(User::getIsInternalUser,1)
                .between(User::getGmtCreate,startDdate,endDdate));
        //System.out.println("使用过平台路桥用户数：" + inUserCounts);
        StatisticalNumVo vo2= new StatisticalNumVo();
        vo2.setName("使用过平台路桥用户数");
        vo2.setNum(inUserCounts);
        list.add(vo2);
        //内部企业数
        int inEnterPrise = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
                .between(EnterpriseInfo::getGmtCreate,startDdate,endDdate)
                .isNotNull(EnterpriseInfo::getInteriorId));
        //System.out.println("使用过平台路桥企业数据：" + inEnterPrise);
        StatisticalNumVo vo3= new StatisticalNumVo();
        vo3.setName("使用过平台路桥企业数据");
        vo3.setNum(inEnterPrise);
        list.add(vo3);


        return list;
    }
    // 非自营店
    private List<StatisticalNumVo> staticsSystemNotBusinessTime(JSONObject jsonObject) {
        List<String> dateScope = (List) jsonObject.get("dateScope");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startDdate = null;
        Date endDdate = null;
        try {
            startDdate = sdf.parse(dateScope.get(0));
            endDdate = sdf.parse(dateScope.get(1));
        } catch (ParseException e) {
            throw new BusinessException("时间区间异常");
        }
        List<StatisticalNumVo> list = new ArrayList<>();
        // 先找到自营店id
        List<String> shopIds = shopBusinessService.lambdaQuery()
                .eq(ShopBusiness::getState,1)//状态为启用
                .select(ShopBusiness::getShopId)
                .list()
                .stream()
                .map(ShopBusiness::getShopId)
                .collect(Collectors.toList());
        // 或者关联的供应商id
        List<String> enterpriseIds = shopBusinessService.lambdaQuery()
                .eq(ShopBusiness::getState,1)//状态为启用
                .select(ShopBusiness::getEnterpriseId)
                .list()
                .stream()
                .map(ShopBusiness::getEnterpriseId)
                .collect(Collectors.toList());


        List<DealOrderInfo> dealOrderInfos = dealOrderInfoService.lambdaQuery()
                .between(DealOrderInfo::getGmtCreate,startDdate,endDdate)
                .notIn(DealOrderInfo::getShopId, shopIds)
                .select(DealOrderInfo::getAmount)
                .list();
        BigDecimal bigDecimal = new BigDecimal(0);
        for (DealOrderInfo dealOrderInfo : dealOrderInfos) {
            bigDecimal = bigDecimal.add(dealOrderInfo.getAmount());
        }
        StatisticalNumVo vo12= new StatisticalNumVo();
        vo12.setNum(bigDecimal);
        vo12.setName("总结算金额");
        list.add(vo12);
        //上架商品总数
        int upProductNum = productService.count(new LambdaQueryWrapper<Product>()
                .between(Product::getPutawayDate,startDdate,endDdate)
                .eq(Product::getState,1)
                .notIn(Product::getShopId, shopIds));
        //System.out.println("上架商品总数：" + upProductNum);
        StatisticalNumVo vo4= new StatisticalNumVo();
        vo4.setName("上架商品总数");
        vo4.setNum(upProductNum);
        list.add(vo4);




        //查询审核通过的对账单信息  订单类型和对账单金额
        List<MaterialReconciliation> ls1= materialReconciliationService.lambdaQuery()
                .eq(MaterialReconciliation::getState, 3)
                .between(MaterialReconciliation::getEndTime,startDdate,endDdate)
                .notIn(MaterialReconciliation::getEnterpriseId, enterpriseIds) // 添加供应商id
                .select(MaterialReconciliation::getReconciliationAmount,
                        MaterialReconciliation::getReconciliationProductType)
                .list();
        //查询订单数据
        List<Orders> ordersAllList = ordersService.list(new LambdaQueryWrapper<Orders>()
                .in(Orders::getOrderClass, 1, 2)
                .between(Orders::getGmtCreate,startDdate,endDdate)
                .notIn(Orders::getShopId, shopIds)
                .select(Orders::getProductType,Orders::getActualAmount)
        );

        //大宗临购订单
        List<Orders> clgOrderLists = ordersAllList.stream().filter(item -> item.getProductType() == 1).collect(Collectors.toList());
        StatisticalNumVo vo14= new StatisticalNumVo();
        vo14.setName("大宗临购订单数");
        vo14.setNum(clgOrderLists.size());
        list.add(vo14);
        // 大宗临购订单金额
        BigDecimal sumAmount2 = new BigDecimal(0);
        for (Orders orders : clgOrderLists) {
            sumAmount2=sumAmount2.add(orders.getActualAmount());
        }
        //System.out.println("大宗临购订单金额：" + sumAmount2);
        StatisticalNumVo vo15= new StatisticalNumVo();
        vo15.setName("大宗临购订单金额");
        vo15.setNum(sumAmount2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo15);

        // 大宗临购对账金额
        List<MaterialReconciliation> list3 = ls1.stream().filter(item -> item.getReconciliationProductType() == 1).collect(Collectors.toList());
//        List<MaterialReconciliation> list3 = materialReconciliationService.lambdaQuery()
//                .eq(MaterialReconciliation::getBusinessType, 6)
//                .eq(MaterialReconciliation::getState, 3)
//                .between(MaterialReconciliation::getEndTime,startDdate,endDdate)
//                .list();
        BigDecimal toavbleA3 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list3) {
            toavbleA3 = toavbleA3.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo16= new StatisticalNumVo();
        vo16.setName("大宗临购对账金额");
        vo16.setNum(toavbleA3.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo16);


        // 周转材料订单数
        List<Orders> contractPlanLists = ordersAllList.stream().filter(item -> item.getProductType() == 2).collect(Collectors.toList());
        StatisticalNumVo vo9= new StatisticalNumVo();
        vo9.setName("周转材料订单数");
        vo9.setNum(contractPlanLists.size());
        list.add(vo9);
        // 周转材料对账单
        List<MaterialReconciliation> ls = ls1.stream().filter(item -> item.getReconciliationProductType() == 2).collect(Collectors.toList());
        BigDecimal amount = new BigDecimal(0).setScale(2,BigDecimal.ROUND_HALF_UP);
        for (MaterialReconciliation reconciliation : ls) {
            amount =   reconciliation.getReconciliationAmount().add(amount);
        }
        StatisticalNumVo vo11= new StatisticalNumVo();
        vo11.setName("周转材料订单对账总金额");
        vo11.setNum(amount);
        list.add(vo11);


        //零星订单
        List<Orders> ordersList = ordersAllList.stream().filter(item -> item.getProductType() == 0).collect(Collectors.toList());
        BigDecimal sumAmount = new BigDecimal(0);
        for (Orders orders : ordersList) {
            sumAmount=sumAmount.add(orders.getActualAmount());
        }
        //System.out.println("零星采购订单数：" + ordersList.size());
        StatisticalNumVo vo6= new StatisticalNumVo();
        vo6.setName("零星采购订单数");
        vo6.setNum(ordersList.size());
        list.add(vo6);

        //System.out.println("零星采购订单金额：" + sumAmount);
        StatisticalNumVo vo7= new StatisticalNumVo();
        vo7.setName("零星采购订单金额");
        vo7.setNum(sumAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo7);



        // 零星采购对账金额
//        List<MaterialReconciliation> list2 = materialReconciliationService.lambdaQuery()
//                .eq(MaterialReconciliation::getBusinessType, 2)
//                .eq(MaterialReconciliation::getState, 3)
//                .between(MaterialReconciliation::getEndTime,startDdate,endDdate)
//                .list();
        List<MaterialReconciliation> list2 = ls1.stream().filter(item -> item.getReconciliationProductType() == 0).collect(Collectors.toList());
        // 零星采购对账金额
        BigDecimal toavbleA2 = new BigDecimal(0);
        for (MaterialReconciliation materialReconciliation : list2) {
            toavbleA2 = toavbleA2.add(materialReconciliation.getReconciliationAmount());
        }
        StatisticalNumVo vo13= new StatisticalNumVo();
        vo13.setName("零星采购对账金额");
        vo13.setNum(toavbleA2.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo13);
        // TODO  发货时间或创建时间？
        // 零星采购发货金额
        List<OrderShip> orderShips = orderShipService.list(new LambdaQueryWrapper<OrderShip>().eq(OrderShip::getProductType, 0)
                .between(OrderShip::getShipData,startDdate,endDdate).select(OrderShip::getBillId)
                .notIn(OrderShip::getShopId, shopIds)
                .in(OrderShip::getType, 1,2));
        BigDecimal orderShipAmount = new BigDecimal(0).setScale(2,BigDecimal.ROUND_UP);
        if(!orderShips.isEmpty()){
            List<String> billIds = orderShips.stream().map(OrderShip::getBillId).collect(Collectors.toList());
            List<OrderShipDtl> itemList = orderShipDtlService.lambdaQuery().in(OrderShipDtl::getBillId, billIds)
                    .select(OrderShipDtl::getReturnCounts,OrderShipDtl::getProductPrice,OrderShipDtl::getShipNum).list();

//        for (OrderShip orderShip : orderShips) {
//            List<OrderShipDtl> itemList = orderShipDtlService.lambdaQuery().eq(OrderShipDtl::getBillId, orderShip.getBillId()).list();
            for (OrderShipDtl orderShipDtl : itemList) {
                // 已退货
                BigDecimal returnCounts = orderShipDtl.getReturnCounts();
                // 已收货数量
                BigDecimal shipNum = orderShipDtl.getShipNum();
                BigDecimal productPrice = orderShipDtl.getProductPrice();
                BigDecimal realAmount = shipNum.subtract(returnCounts).multiply(productPrice).setScale(2, BigDecimal.ROUND_UP);
                orderShipAmount = orderShipAmount.add(realAmount);
            }
//        }
            //System.out.println("零星采购发货金额：" + orderShipAmount);
        }

        StatisticalNumVo vo8= new StatisticalNumVo();
        vo8.setName("零星采购发货金额");
        vo8.setNum(orderShipAmount.setScale(2,BigDecimal.ROUND_UP));
        list.add(vo8);


        //店铺数 排除审核未通过
        // TODO 店铺bug
        int shopCoounts = shopService.count(new LambdaQueryWrapper<Shop>()
                .between(Shop::getGmtCreate,startDdate,endDdate)
                .ne(Shop::getAuditStatus, 3));
        //System.out.println("店铺数：" + shopCoounts);
        StatisticalNumVo vo1= new StatisticalNumVo();
        vo1.setName("店铺数");
        vo1.setNum(shopCoounts - shopIds.size());
        list.add(vo1);


        //商品总数
        int productNum = productService.lambdaQuery()
                .between(Product::getGmtCreate,startDdate,endDdate)
                .notIn(Product::getShopId, shopIds)
                .count();
        //System.out.println("商品总数：" + productNum);
        StatisticalNumVo vo5= new StatisticalNumVo();
        vo5.setName("商品总数");
        vo5.setNum(productNum);
        list.add(vo5);


        //供应商数
        int supplierNum = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
                .eq(EnterpriseInfo::getIsPcwp,1)
                .between(EnterpriseInfo::getGmtCreate,startDdate,endDdate)
                .isNotNull(EnterpriseInfo::getSocialCreditCode));
        //System.out.println("外部供应商数：" + supplierNum);
        StatisticalNumVo vo0 = new StatisticalNumVo();
        vo0.setName("外部供应商数");
        vo0.setNum(supplierNum);
        list.add(vo0);

        //内部用户数
        int inUserCounts = userService.count(new LambdaQueryWrapper<User>().eq(User::getIsInternalUser,1)
                .between(User::getGmtCreate,startDdate,endDdate));
        //System.out.println("使用过平台路桥用户数：" + inUserCounts);
        StatisticalNumVo vo2= new StatisticalNumVo();
        vo2.setName("使用过平台路桥用户数");
        vo2.setNum(inUserCounts);
        list.add(vo2);
        //内部企业数
        int inEnterPrise = enterpriseInfoService.count(new LambdaQueryWrapper<EnterpriseInfo>()
                .between(EnterpriseInfo::getGmtCreate,startDdate,endDdate)
                .isNotNull(EnterpriseInfo::getInteriorId));
        //System.out.println("使用过平台路桥企业数据：" + inEnterPrise);
        StatisticalNumVo vo3= new StatisticalNumVo();
        vo3.setName("使用过平台路桥企业数据");
        vo3.setNum(inEnterPrise);
        list.add(vo3);


        return list;
    }
}
