package scrbg.meplat.mall.util;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @package: scrbg.meplat.mall.util
 * @author: 胡原武
 * @date: 2022.11.08
 */
public class CommonUtil {

    /**
     * 百分比转小数
     *
     * @param percent
     * @return
     */
    public static BigDecimal getDecimals(String percent) {
        percent = percent.replace("%", "");
        Float fn = Float.valueOf(percent) / 100;
        BigDecimal d = new BigDecimal(fn);
        return d;
    }

    /**
     * 数乘以百分比的值
     *
     * @param percent
     * @param bigDecimal
     * @return
     */
    public static BigDecimal getMultiplyPercent(String percent, BigDecimal bigDecimal) {
        percent = percent.replace("%", "");
        Float fn = Float.valueOf(percent) / 100;
        BigDecimal d = new BigDecimal(fn);
        BigDecimal multiply = bigDecimal.multiply(d);
        return multiply;
    }

    /**
     * 获取6位验证码
     * @return
     */
    public static String getCode() {
        String code = String.valueOf((int)((Math.random() * 9 + 1) * Math.pow(10,5)));
        return code;
    }

    /**
     * 判断当前日期是否大于某个日期
     * @param date
     * @return
     */
    public static boolean afterDate(String date){
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime localTime=LocalDateTime.parse(date,dtf);
        return LocalDateTime.now().isAfter(localTime);
    }

    /**
     * 判断当前日期是否大于某个日期
     * @return
     */
    public static boolean afterDateA1(){
        String dataStr = "2023-07-30 00:00:00";
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime localTime=LocalDateTime.parse(dataStr,dtf);
        return LocalDateTime.now().isAfter(localTime);
    }

}
