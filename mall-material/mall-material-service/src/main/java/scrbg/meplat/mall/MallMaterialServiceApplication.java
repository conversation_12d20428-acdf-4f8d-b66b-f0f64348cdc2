package scrbg.meplat.mall;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import scrbg.meplat.mall.adapter.ApplicationConfig;
import scrbg.meplat.mall.config.MallConfig;

import javax.annotation.PostConstruct;

@SpringBootApplication
@EnableFeignClients
@Import(value = {ApplicationConfig.class})
@MapperScan("scrbg.meplat.mall.mapper")
public class MallMaterialServiceApplication {

    @Autowired
    public MallConfig mallConfig;
    @PostConstruct
    private void init(){
        if(mallConfig.mallType==0){
            System.out.println("系统环境：" + mallConfig.profilesActive);
            System.out.println("                     =================================================");
            System.out.println("                     ====================物资     商场==================");
            System.out.println("                     ====================服务器启动成功==================");
            System.out.println("                     =================================================");
        }
    }
    public static void main(String[] args) {
        SpringApplication.run(MallMaterialServiceApplication.class, args);
    }
}
