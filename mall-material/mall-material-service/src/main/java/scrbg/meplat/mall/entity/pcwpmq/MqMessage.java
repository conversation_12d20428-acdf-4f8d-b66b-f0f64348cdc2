package scrbg.meplat.mall.entity.pcwpmq;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("pcwp_mq_data") // 消息存储表
public class MqMessage {
    @TableId(type = IdType.AUTO) // 自增主键
    private Integer id;

    @TableField(value = "json_text")
    private String jsonText; // JSON类型字段

    @TableField("type")
    private String type; //  存储routingKey

    @TableField("time")
    private String time; // 消息队列时间

    @TableField("is_get")
    private String isGet; // 是否消费（0未消费，1消费）

    @TableField("delivery_tag")
    private Long deliveryTag; // 消息传送标识

    @TableField("patch")
    private int patch; // 数据片段序列号

}
