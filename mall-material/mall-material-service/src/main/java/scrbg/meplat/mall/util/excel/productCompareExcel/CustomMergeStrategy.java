package scrbg.meplat.mall.util.excel.productCompareExcel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: maill_api
 * @description: 自定义合并处理器
 * @author: 代文翰
 * @create: 2023-12-14 15:43
 **/
// 自定义合并策略 该类继承了AbstractMergeStrategy抽象合并策略，需要重写merge()方法
public class CustomMergeStrategy extends AbstractMergeStrategy {

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        // 每隔8行合并一次

        if (relativeRowIndex % 8 == 0 || relativeRowIndex % 8 == 1 || relativeRowIndex == 0 || relativeRowIndex == 1) {
            // 合并第一行的第1列到第5列
            CellRangeAddress cellRangeAddress = new CellRangeAddress(relativeRowIndex, relativeRowIndex, 0, 4);
            // 第二行的1到5列
            sheet.addMergedRegionUnsafe(cellRangeAddress);
            // 设置第一行样式
            if (relativeRowIndex % 8 == 0 || relativeRowIndex == 0) {
                CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
                // 垂直居中
                cellStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                Font font = sheet.getWorkbook().createFont();
                font.setFontName("宋体");
                font.setFontHeightInPoints((short) 20);
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
                //sheet.getRow(relativeRowIndex).getCell(0).setCellStyle(cellStyle);
            }
        }
        // 针对第二行单独设置样式
        // 设置第二行的样式（时间靠右）
        if (relativeRowIndex %8 == 1 || relativeRowIndex == 1) {
            CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
            // 垂直居中
            Font font = sheet.getWorkbook().createFont();
            font.setFontName("宋体");
            font.setFontHeightInPoints((short) 20);
            cellStyle.setFont(font);
            cellStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
            cellStyle.setAlignment(HorizontalAlignment.RIGHT);
            cell.setCellStyle(cellStyle);
            //sheet.getRow(relativeRowIndex).getCell(0).setCellStyle(cellStyle);
        } else {
             // 合并方法没在这里
            CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
            // 垂直居中
            cellStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            Font font = sheet.getWorkbook().createFont();
            font.setFontName("宋体");
            font.setFontHeightInPoints((short) 14);
            cellStyle.setFont(font);
            cell.setCellStyle(cellStyle);
            //sheet.getRow(relativeRowIndex).getCell(cell.get).setCellStyle(cellStyle);
        }


    }
}

