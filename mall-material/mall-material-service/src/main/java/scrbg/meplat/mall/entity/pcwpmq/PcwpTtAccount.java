package scrbg.meplat.mall.entity.pcwpmq;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 统一身份认证账户表实体类
 */
@Data
@TableName("pcwp_tt_account")
public class PcwpTtAccount {
    /**
     * 主键（用户ID，唯一标识）
     */
    @TableField("id")
    private String id;

    /**
     * 用户姓名（中文全名）
     */
    @TableField("name")
    private String name;

    /**
     * 用户登录名（系统唯一，用于登录认证）
     */
    @TableField("username")
    private String username;

    /**
     * 员工工号（企业唯一编号，关联HR系统）
     */
    @TableField("employeenumber")
    private String employeeNumber;

    /**
     * 账户创建时间（系统自动记录）
     */
    @TableField("created")
    private String created;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    @TableField("mdmstate")
    private Integer mdmState;
}
