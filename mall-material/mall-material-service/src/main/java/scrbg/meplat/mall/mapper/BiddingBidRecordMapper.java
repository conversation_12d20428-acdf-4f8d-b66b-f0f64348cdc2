package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import scrbg.meplat.mall.entity.BiddingBidRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;
import scrbg.meplat.mall.vo.bidding.BidSummarySubVo;

import java.util.List;
import java.util.Map;

/**
 * @描述：竞价记录 Mapper 接口
 * @作者: ye
 * @日期: 2023-07-11
 */
@Mapper
@Repository
public interface BiddingBidRecordMapper extends BaseMapper<BiddingBidRecord> {
    /**
     * 查询 商品和报价数据
     * @param bidSn
     */
    List<BidSummarySubVo> getBidSummaryWithBidRecordId(@Param("bSn") String bidSn);

    List<BiddingBidRecord> selectListPaging(Page<BiddingBidRecord> page,
                                            @Param("params") Map<String, Object> params);


}