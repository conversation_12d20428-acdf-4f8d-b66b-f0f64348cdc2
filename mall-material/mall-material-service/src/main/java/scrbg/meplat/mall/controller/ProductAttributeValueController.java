package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.ProductAttributeValue;
import scrbg.meplat.mall.service.ProductAttributeValueService;

import java.util.List;

/**
 * @描述：商品属性值控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/productAttributeValue")
//@Api(tags = "商品属性值")
public class ProductAttributeValueController {

    @Autowired
    public ProductAttributeValueService productAttributeValueService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<ProductAttributeValue> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = productAttributeValueService.queryPage(jsonObject, new LambdaQueryWrapper<ProductAttributeValue>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<ProductAttributeValue> findById(String id) {
        ProductAttributeValue productAttributeValue = productAttributeValueService.getById(id);
        return R.success(productAttributeValue);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody ProductAttributeValue productAttributeValue) {
        productAttributeValueService.create(productAttributeValue);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody ProductAttributeValue productAttributeValue) {
        productAttributeValueService.update(productAttributeValue);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        productAttributeValueService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        productAttributeValueService.removeByIds(ids);
        return R.success();
    }
}

