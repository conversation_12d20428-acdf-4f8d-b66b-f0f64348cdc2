package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：退货项表
 * @作者: ye
 * @日期: 2023-08-07
 */
@ApiModel(value="退货项表")
@Data
@TableName("order_return_item")
public class OrderReturnItem extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "退货项id")
    private String orderReturnItemId;

    @ApiModelProperty(value = "退货id")

    private String orderReturnId;

    @ApiModelProperty(value = "退货编号")

    private String orderReturnNo;

    @ApiModelProperty(value = "订单项")
    private String orderItemId;


    @ApiModelProperty(value = "订单id")

    private String orderId;


    @ApiModelProperty(value = "订单号")

    private String orderSn;


    @ApiModelProperty(value = "商品id")

    private String productId;


    @ApiModelProperty(value = "商品编号")

    private String productSn;


    @ApiModelProperty(value = "商品名称")

    private String productName;


    @ApiModelProperty(value = "商品图片")

    private String productImg;


    @ApiModelProperty(value = "skuid")

    private String skuId;


    @ApiModelProperty(value = "sku名称")

    private String skuName;





    @ApiModelProperty(value = "退货数量")

    private BigDecimal count;

    @ApiModelProperty(value = "材质")

    private String texture;
    @ApiModelProperty(value = "单位")

    private String unit;
    @ApiModelProperty(value = "乐观锁")
    @Version
    private Integer version;
    @ApiModelProperty(value = "退货来源")

    private Integer isOut;




    @ApiModelProperty(value = "订单id(二级)")

    private String otherOrderId;
    @ApiModelProperty(value = "订单项(二级)")
    private String otherOrderItemId;
    @ApiModelProperty(value = "订单编号(二级)")

    private String otherOrderSn;

    @ApiModelProperty(value = "商品总金额")
    private BigDecimal totalAmount;
    @ApiModelProperty(value = "不含税总金额")

    private BigDecimal noRateAmount;


    @ApiModelProperty(value = "商品价格")
    private BigDecimal productPrice;
    @ApiModelProperty(value = "商品价格（不含税）")
    private BigDecimal noRatePrice;

    @ApiModelProperty(value = "商品价格(二级)")
    private BigDecimal otherProductPrice;
    @ApiModelProperty(value = "商品价格(二级不含税)")
    private BigDecimal otherNoProductPrice;


    @ApiModelProperty(value = "商品总金额(二级)")
    private BigDecimal otherRateAmount ;
    @ApiModelProperty(value = "商品总金额(二级)")
    private BigDecimal otherNoRateAmount ;



    @ApiModelProperty(value = "品牌名称")
    private String brandName;
    @ApiModelProperty(value = "品牌")
    private String brandId;
    @ApiModelProperty(value = "购买数量")

    private BigDecimal buyCounts;

    @ApiModelProperty(value = "  0 未对账 1已对账  ")
    private Integer isReconciliation;

    @ApiModelProperty(value = "商品名称")
    @TableField(exist = false)
    private String title;

}
