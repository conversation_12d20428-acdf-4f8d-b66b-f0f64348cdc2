package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.service.MaterialReconciliationService;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrderShipDtlService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.vo.platform.ListShipByAffirmListVO;
import scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo;
import scrbg.meplat.mall.vo.shopManage.reportForms.ProductFromVo;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;

@RestController
@RequestMapping("/reportForms")
public class ReportFormsController {
    @Autowired
    ProductService productService;
    @Autowired
    public OrderShipDtlService orderShipDtlService;

    @Autowired
    public OrderItemService orderItemService;

    @Autowired
    public MaterialReconciliationService materialReconciliationService;



    @PostMapping("/productFromList")
    @ApiOperation(value = "商品统计报表（供应商管理）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "staProfitPrice", value = "差价上", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "endProfitPrice", value = "差价下", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "staPutawayDate", value = "开始上架时间之前", dataTypeClass = Date.class),
            @DynamicParameter(name = "endPutawayDate", value = "开始上架时间之后", dataTypeClass = Date.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class)
    })
    public PageR<ProductFromVo> productFromList(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.productFromList(jsonObject,new QueryWrapper<ProductFromVo>());
        return PageR.success(page);
    }

    @PostMapping("/productFromListLoad")
    @ApiOperation(value = "商品统计报表（导出）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "staProfitPrice", value = "差价上", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "endProfitPrice", value = "差价下", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "staPutawayDate", value = "开始上架时间之前", dataTypeClass = Date.class),
            @DynamicParameter(name = "endPutawayDate", value = "开始上架时间之后", dataTypeClass = Date.class),
    })
    public R productFromListLoad(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
       productService.productFromListLoad(jsonObject,new QueryWrapper<ProductFromVo>(),response);
       return  R.success("导出成功");
    }



    @PostMapping("/platform/productFromList")
    @ApiOperation(value = "商品统计报表（平台管理）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "staProfitPrice", value = "差价上", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "endProfitPrice", value = "差价下", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "staPutawayDate", value = "开始上架时间之前", dataTypeClass = Date.class),
            @DynamicParameter(name = "endPutawayDate", value = "开始上架时间之后", dataTypeClass = Date.class),
    })
    public PageR<PlatformProductFromVo> getPlatformProductFromList(HttpServletResponse response, @RequestBody JSONObject jsonObject) {
        PageUtils page = productService.getPlatformProductFromList(jsonObject,new QueryWrapper<PlatformProductFromVo>());
        return PageR.success(page);
    }

    @PostMapping("/platform/productFromListLoad")
    @ApiOperation(value = "平台采购交易量信息（导出）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "staProfitPrice", value = "差价上", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "endProfitPrice", value = "差价下", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "staPutawayDate", value = "开始上架时间之前", dataTypeClass = Date.class),
            @DynamicParameter(name = "endPutawayDate", value = "开始上架时间之后", dataTypeClass = Date.class),
    })
    public R getProductFromListLoad(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        productService.getPlatformProductFromListLoad(jsonObject,new QueryWrapper<PlatformProductFromVo>(),response);
        return  R.success("导出成功");
    }


    @PostMapping("/shopManage/getShopManageMaterialLoad")
    @ApiOperation(value = "商品统计报表（导出）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "staProfitPrice", value = "差价上", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "endProfitPrice", value = "差价下", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "staPutawayDate", value = "开始上架时间之前", dataTypeClass = Date.class),
            @DynamicParameter(name = "endPutawayDate", value = "开始上架时间之后", dataTypeClass = Date.class),
    })
    public R getShopManageMaterial(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        productService.getPlatformProductFromListLoad(jsonObject,new QueryWrapper<PlatformProductFromVo>(),response);
        return  R.success("导出成功");
    }


    @PostMapping("/shopManage/getShopManageMaterial")
    @ApiOperation(value = "物资(零星采购)交易量报表（供应商方）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "staProfitPrice", value = "差价上", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "endProfitPrice", value = "差价下", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "staPutawayDate", value = "开始上架时间之前", dataTypeClass = Date.class),
            @DynamicParameter(name = "endPutawayDate", value = "开始上架时间之后", dataTypeClass = Date.class),
    })
    public PageR<ListShipByAffirmListVO> getShopManageMaterial(HttpServletResponse response, @RequestBody JSONObject jsonObject) {
        PageUtils page = orderItemService.getShopManageMaterial(jsonObject,new QueryWrapper<PlatformProductFromVo>());
        return PageR.success(page);
    }
    @PostMapping("/shopManage/supplierOutputExcel")
    @ApiOperation(value = "物资(零星采购)交易量报表（导出）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "staProfitPrice", value = "差价上", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "endProfitPrice", value = "差价下", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "staPutawayDate", value = "开始上架时间之前", dataTypeClass = Date.class),
            @DynamicParameter(name = "endPutawayDate", value = "开始上架时间之后", dataTypeClass = Date.class),
    })
    public R supplierOutputExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        orderItemService.supplierOutputExcel(jsonObject,new QueryWrapper<PlatformProductFromVo>(),response);
        return  R.success("导出成功");
    }

    @PostMapping("/platform/getPlatformSettlementFromList")
    @ApiOperation(value = "平台结算报表")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "staProfitPrice", value = "差价上", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "endProfitPrice", value = "差价下", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "staPutawayDate", value = "开始上架时间之前", dataTypeClass = Date.class),
            @DynamicParameter(name = "endPutawayDate", value = "开始上架时间之后", dataTypeClass = Date.class),
    })
    public PageR<MaterialReconciliation> getPlatformSettlementFromList(HttpServletResponse response, @RequestBody JSONObject jsonObject) {
        PageUtils page = materialReconciliationService.getPlatformSettlementFromList(jsonObject,new QueryWrapper<MaterialReconciliation>());
        return PageR.success(page);
    }

    @PostMapping("/shopManage/getShopManageSettlementFromList")
    @ApiOperation(value = "商品结算报表（供应商方）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "staProfitPrice", value = "差价上", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "endProfitPrice", value = "差价下", dataTypeClass = BigDecimal.class),
            @DynamicParameter(name = "staPutawayDate", value = "开始上架时间之前", dataTypeClass = Date.class),
            @DynamicParameter(name = "endPutawayDate", value = "开始上架时间之后", dataTypeClass = Date.class),
    })
    public PageR<MaterialReconciliation> getShopManageSettlementFromList(HttpServletResponse response, @RequestBody JSONObject jsonObject) {
        PageUtils page = materialReconciliationService.getShopManageSettlementFromList(jsonObject,new QueryWrapper<MaterialReconciliation>());
        return PageR.success(page);
    }



}
