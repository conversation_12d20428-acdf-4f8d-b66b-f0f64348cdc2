package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.SysOperLog;

import java.util.List;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2024-11-04
 */
public interface SysOperLogService extends IService<SysOperLog> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysOperLog> queryWrapper);

    void create(SysOperLog sysOperLog);

    void update(SysOperLog sysOperLog);

    SysOperLog getById(String id);

    void delete(String id);

    void deleteBatch(List<String> ids);
}
