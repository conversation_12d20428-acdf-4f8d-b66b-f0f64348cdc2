package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.StatisticalNum;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.StatisticalNum;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.vo.platform.StatisticalNumVo;

import java.util.List;
/**
 * @描述：物资数据统计 服务类
 * @作者: ye
 * @日期: 2023-08-27
 */
public interface StatisticalNumService extends IService<StatisticalNum> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<StatisticalNum> queryWrapper);

        void create(StatisticalNum statisticalNum);
        void update(StatisticalNum statisticalNum);
        StatisticalNum getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
        /**
         * 系统运行统计(历史)
         * @return
         * 需要区分是否自营店
         */
        List<StatisticalNumVo> staticsSystem(int type);

        /**
         * 系统运行统计（时间）
         * @param jsonObject
         * @return
         * 需要区分是否自营店
         */
        List<StatisticalNumVo> staticsSystem(JSONObject jsonObject, int type);
}
