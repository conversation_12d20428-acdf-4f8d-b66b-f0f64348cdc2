package scrbg.meplat.mall.enums.adPicture;

/**
 * @package: scrbg.meplat.mall.enums.picture
 * @author: 胡原武
 * @date: 2022.11.08
 */
public enum PictuerEnum {

    /**
     * 图片类型
     */
    PICTURE_TYPR_NO(1,"无"),
    PICTURE_TYPR_IN(2,"内部链接"),
    PICTURE_TYPR_OUT(3,"外部链接");

    /**
     * 编码
     */
    private int code;

    /**
     * 说明
     */
    private String remark;

    PictuerEnum() {
    }

    PictuerEnum(int code, String remark) {
        this.code = code;
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
