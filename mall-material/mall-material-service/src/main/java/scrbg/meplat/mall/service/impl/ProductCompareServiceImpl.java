package scrbg.meplat.mall.service.impl;

import com.itextpdf.text.DocumentException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.ProductCompare;
import scrbg.meplat.mall.entity.ProductCompareItem;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ProductCompareMapper;
import scrbg.meplat.mall.service.ProductCompareItemService;
import scrbg.meplat.mall.service.ProductCompareService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;


import scrbg.meplat.mall.util.excel.excel2pdf.Excel2Pdf;
import scrbg.meplat.mall.util.excel.productCompareExcel.util.ExcelObject;
import scrbg.meplat.mall.util.excel.productCompareExcel.ProductCompareExcelUtil;
import scrbg.meplat.mall.util.excel.productCompareExcel.util.ProductCompareExcel2Pdf;
import scrbg.meplat.mall.vo.productCompare.CompareDto;
import scrbg.meplat.mall.vo.productCompare.ProductCompareDetail;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-12-12
 */
@Service
public class ProductCompareServiceImpl extends ServiceImpl<ProductCompareMapper, ProductCompare> implements ProductCompareService {
    @Resource
    private ProductCompareItemService compareItemService;

    @Resource
    private MallConfig config;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductCompare> queryWrapper) {
        // 只查询自己的比价记录
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        // 模糊查询
        String keywords = jsonObject.getString("keywords");
        // 时间
        if (StringUtils.isNotBlank(keywords)){
            queryWrapper.like(ProductCompare::getProductNames,keywords);
        }
        List timeScope = (List) jsonObject.get("timeScope");
        if (!CollectionUtils.isEmpty(timeScope) && timeScope.size() == 2){
            String start = (String) timeScope.get(0);
            String end = (String) timeScope.get(1);
            queryWrapper.between(ProductCompare::getCompareTime,start,end);
        }
        queryWrapper.eq(ProductCompare::getFounderId, currentUser.getUserId());
        queryWrapper.orderByDesc(ProductCompare::getCompareTime);
        IPage<ProductCompare> page = this.page(
                new Query<ProductCompare>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(ProductCompare productCompare) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(productCompare);
    }

    @Override
    public void update(ProductCompare productCompare) {
        super.updateById(productCompare);
    }


    @Override
    public ProductCompare getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)){
            throw new BusinessException("请选择需要删除历史");
        }
        super.removeByIds(ids);
        // 删除比价明细表
        QueryWrapper<ProductCompareItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("compare_id", ids);
        compareItemService.remove(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductCompare createUseCurrentUser() {
        ProductCompare productCompare = new ProductCompare();
        // 获取当前登录人的数据
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        BeanUtils.copyProperties(currentUser, productCompare);
        productCompare.setCompareTime(new Date());
        boolean save = super.save(productCompare);
        if (!save) {
            throw new BusinessException("新增比价记录失败");
        }
        return productCompare;
    }

    @Override
    public void removeByID(String id) {
        if (id == null) {
            return;
        }
        // 删除比价主表
        this.removeById(id);
        // 删除比价明细表
        QueryWrapper<ProductCompareItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("compare_id", id);
        compareItemService.remove(queryWrapper);
    }

    @Override
    public ProductCompareDetail getCompareDetail(String id) {
        ProductCompare productCompare = getById(id);
        if (productCompare != null) {
            ProductCompareDetail productCompareDetail = new ProductCompareDetail();
            productCompareDetail.setProductCompare(productCompare);
            //BeanUtils.copyProperties(productCompare, productCompareDetail);
            // 获取比价明细
            QueryWrapper<ProductCompareItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("compare_id", id);
            List<ProductCompareItem> list = compareItemService.list(queryWrapper);
            productCompareDetail.setProductList(list);
            return productCompareDetail;
        } else {
            throw new BusinessException("比价记录不存在");
        }
    }

    @Override
    public void exportCompareDetailToPDF(String id, HttpServletResponse response) {
        ProductCompareDetail compareDetail = getCompareDetail(id);
        // 全部比价数据（按行存）
        List<List<String>> productInfos = new ArrayList<>();
        if (compareDetail != null) {
            // 时间
            Date compareTime = compareDetail.getProductCompare().getCompareTime();
            CompareDto compareDto = new CompareDto();
            compareDto.setTitle("物资采购平台商品对比");
            compareDto.setTime("时间:"+ DateUtil.getYYYYMMDD(compareTime));
            // 比价明细
            List<ProductCompareItem> itemList = compareItemService.lambdaQuery().eq(ProductCompareItem::getCompareId, id).list();
            // 避免空指针
            if (CollectionUtils.isEmpty(itemList)){
                throw new BusinessException("比价明细为空");
            }
            // 组装Excel数据
            // 图标
            List<String> picInfo = new ArrayList<>();
            picInfo.add("图标");
            List<String> pics = itemList.stream().map(item->{
                if(item.getProductMinImg() == null){
                    return "";
                }
                return config.ossPrefix + item.getProductMinImg();
            }).collect(Collectors.toList());
            picInfo.addAll(pics);
            productInfos.add(picInfo);
            // 物资名称
            List<String> pNameInfo = new ArrayList<>();
            pNameInfo.add("物资名称");
            List<String> pName = itemList.stream().map(item->{
                if(item.getProductName() == null){
                    return "";
                }
                return  item.getProductName();
            }).collect(Collectors.toList());
            pNameInfo.addAll(pName);
            productInfos.add(pNameInfo);
            // 规格型号
            List<String> skuInfo = new ArrayList<>();
            skuInfo.add("规格型号");
            List<String> skuName = itemList.stream().map(item->{
                if(item.getSkuName() == null){
                    return "";
                }
                return  item.getSkuName();
            }).collect(Collectors.toList());
            skuInfo.addAll(skuName);
            productInfos.add(skuInfo);
            // 品牌
            List<String> brandInfo = new ArrayList<>();
            brandInfo.add("品牌");
            List<String> brandName = itemList.stream().map(item->{
                if(item.getBrandName() == null){
                    return "";
                }
                return  item.getBrandName();
            }).collect(Collectors.toList());
            brandInfo.addAll(brandName);
            productInfos.add(brandInfo);
            // 单位
            List<String> unitInfo = new ArrayList<>();
            unitInfo.add("单位");
            List<String> unitName = itemList.stream().map(item->{
                if(item.getUnit() == null){
                    return "";
                }
                return  item.getUnit();
            }).collect(Collectors.toList());
            unitInfo.addAll(unitName);
            productInfos.add(unitInfo);
            // 价格
            List<String> priceInfo = new ArrayList<>();
            priceInfo.add("价格");
            List<String> pricetName = itemList.stream().map(item->{
                if(item.getSellPrice() == null){
                    return "";
                }
                return  item.getSellPrice().toString();
            }).collect(Collectors.toList());
            priceInfo.addAll(pricetName);
            productInfos.add(priceInfo);
            // 供应商名称
            List<String> supplierInfo = new ArrayList<>();
            priceInfo.add("供应商名称");
            List<String> supplierName = itemList.stream().map(item->{
                if(item.getSupplierName() == null){
                    return "";
                }
                return  item.getSupplierName();
            }).collect(Collectors.toList());
            supplierInfo.addAll(supplierName);
            productInfos.add(supplierInfo);
            compareDto.setProductInfos(productInfos);
            //compareDto 包含了所有的Excel数据
            // Excel生成
            ByteArrayOutputStream excelOut = ProductCompareExcelUtil.createExcel(new ArrayList<>());
            ByteArrayInputStream excelIn = new ByteArrayInputStream(excelOut.toByteArray());
            // 转换PDF
            ExcelObject excelObject = new ExcelObject("", excelIn);
            try {
                String userName = ThreadLocalUtil.getCurrentUser().getUserName();
                response.setHeader("Content-Disposition",
                        "attachment; filename=\"" + new String(userName.getBytes("gb2312"), "ISO8859-1") + "\"");
                new ProductCompareExcel2Pdf(excelObject,response.getOutputStream()).convert();
            } catch (DocumentException e) {
                throw new BusinessException("文档导出异常");
            } catch (IOException e) {
                throw new BusinessException("文档导出异常");
            }
            // 关闭所有的流  excelOut  excelIn
            if (excelIn != null){
                try {
                    excelIn.close();
                } catch (IOException e) {
                    throw new BusinessException("文档导出异常");
                }
            }
            if (excelOut != null){
                try {
                    excelOut.close();
                } catch (IOException e) {
                    throw new BusinessException("文档导出异常");
                }
            }
        } else {
            throw new BusinessException("比价记录不存在");
        }
    }


    // 测试转换
    public void mergeCompareDetailToPDF1(List<String> ids, OutputStream  response) {
        // 全部比价数据（按行存）
        // 时间
        //Date compareTime = new Date();
        List<CompareDto> compareDtos = new ArrayList<>();

        // 比价明细
        //List<ProductCompareItem> itemList = new ArrayList<>();
        for (String id : ids) {
            List<List<String>> productInfos = new ArrayList<>();
            CompareDto compareDto = new CompareDto();
            // 获取比价明细
            QueryWrapper<ProductCompareItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("compare_id", id);
            ProductCompare compare = getById(id);
            if (compare != null) {
                compareDto.setTitle("物资采购平台商品对比");
                compareDto.setTime("时间:" + DateUtil.getYYYYMMDD(compare.getCompareTime()));
            }
            List<ProductCompareItem> itemList = compareItemService.list(queryWrapper);
            if (CollectionUtils.isEmpty(itemList)) {
                throw new BusinessException("比价明细为空");
            }
            //itemList.addAll(list);

        // 避免空指针
        if (CollectionUtils.isEmpty(itemList)) {
            throw new BusinessException("比价明细为空");
        }
        // 组装Excel数据
        // 图标 需求：图标暂时先屏蔽
        //List<String> picInfo = new ArrayList<>();
        //picInfo.add("图标");
        //List<String> pics = itemList.stream().map(item -> {
        //    if (item.getProductMinImg() == null) {
        //        return "";
        //    }
        //    return config.ossPrefix + item.getProductMinImg();
        //}).collect(Collectors.toList());
        //picInfo.addAll(pics);
        //productInfos.add(picInfo);
        // 物资名称
        List<String> pNameInfo = new ArrayList<>();
        pNameInfo.add("物资名称");
        List<String> pName = itemList.stream().map(item -> {
            if (item.getProductName() == null) {
                return "";
            }
            return item.getProductName();
        }).collect(Collectors.toList());
        pNameInfo.addAll(pName);
        productInfos.add(pNameInfo);
        // 规格型号
        List<String> skuInfo = new ArrayList<>();
        skuInfo.add("规格型号");
        List<String> skuName = itemList.stream().map(item -> {
            if (item.getSkuName() == null) {
                return "";
            }
            return item.getSkuName();
        }).collect(Collectors.toList());
        skuInfo.addAll(skuName);
        productInfos.add(skuInfo);
        // 品牌
        List<String> brandInfo = new ArrayList<>();
        brandInfo.add("品牌");
        List<String> brandName = itemList.stream().map(item -> {
            if (item.getBrandName() == null) {
                return "";
            }
            return item.getBrandName();
        }).collect(Collectors.toList());
        brandInfo.addAll(brandName);
        productInfos.add(brandInfo);
        // 单位
        List<String> unitInfo = new ArrayList<>();
        unitInfo.add("单位");
        List<String> unitName = itemList.stream().map(item -> {
            if (item.getUnit() == null) {
                return "";
            }
            return item.getUnit();
        }).collect(Collectors.toList());
        unitInfo.addAll(unitName);
        productInfos.add(unitInfo);
// 价格
        List<String> priceInfo = new ArrayList<>();
        priceInfo.add("价格");
        List<String> pricetName = itemList.stream().map(item -> {
            if (item.getSellPrice() == null) {
                return "";
            }
            return item.getSellPrice().toString();
        }).collect(Collectors.toList());
        priceInfo.addAll(pricetName);
        productInfos.add(priceInfo);
        // 供应商名称
        List<String> supplierInfo = new ArrayList<>();
            supplierInfo.add("供应商名称");
        List<String> supplierName = itemList.stream().map(item -> {
            if (item.getSupplierName() == null) {
                return "";
            }
            return item.getIsBusiness() == 1 ? (item.getShopId().equals("1645601878095495170")?"四川路桥建设集团股份有限公司物资分公司":"四川路桥建设集团物资有限责任公司"):item.getSupplierName();
        }).collect(Collectors.toList());
        supplierInfo.addAll(supplierName);
        productInfos.add(supplierInfo);
        compareDto.setProductInfos(productInfos);
            compareDtos.add(compareDto);

        }
        //compareDto 包含了所有的Excel数据
        // Excel生成
        List<CompareDto> compareDtos1 = compareDtos;
        //
        for (CompareDto dto : compareDtos) {
            // 行
            List<List<String>> productInfos = dto.getProductInfos();
            for (List<String> info : productInfos) {
                // 列
                if (info.size() < 5){
                    // 不够五列补全五列
                    int size = 5 - info.size();
                    for (int i = 0; i < size; i++) {
                        info.add("");
                    }
                }

            }
        }
        ByteArrayOutputStream excelOut = ProductCompareExcelUtil.createExcel(compareDtos);
        ByteArrayInputStream excelIn = new ByteArrayInputStream(excelOut.toByteArray());
        // 转换PDF
        ExcelObject excelObject = new ExcelObject("", excelIn);
        try {
            //String userName = ThreadLocalUtil.getCurrentUser().getUserName();

            //FileOutputStream outputStream = new FileOutputStream("/Volumes/westDisk/testExcel/test.pdf");
            //new ProductCompareExcel2Pdf(excelObject,outputStream).convert();
            new ProductCompareExcel2Pdf(excelObject,response).convert();
        } catch (DocumentException e) {
            throw new BusinessException("文档导出异常"+e.getMessage());
        } catch (IOException e) {
            throw new BusinessException("文档导出异常"+e.getMessage());
        }
        // 关闭所有的流  excelOut  excelIn
        if (excelIn != null){
            try {
                excelIn.close();
            } catch (IOException e) {
                throw new BusinessException("文档导出异常");
            }
        }
        if (excelOut != null){
            try {
                excelOut.close();
            } catch (IOException e) {
                throw new BusinessException("文档导出异常");
            }
        }
    }

    @Override
    public void mergeCompareDetail(List<String> ids, HttpServletResponse response) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("比价记录不存在");
        }
        try {
            // 设置响应头
            String userName = ThreadLocalUtil.getCurrentUser().getUserName()+"比价记录.pdf";
            response.setHeader("Content-Disposition",
                    "attachment; filename=\"" + new String(userName.getBytes("gb2312"), "ISO8859-1") + "\"");
            mergeCompareDetailToPDF1(ids,response.getOutputStream());
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new BusinessException("导出异常");
        }


    }
}
