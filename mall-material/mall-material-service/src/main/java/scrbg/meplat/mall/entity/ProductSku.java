package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @描述：商品sku
 * @作者: y
 * @日期: 2022-11-25
 */
@ApiModel(value = "商品sku")
@Data
@TableName("product_sku")
public class ProductSku extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "skuid")
    private String skuId;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "sku图片")
    private String skuImg;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "供方成本价")
    private BigDecimal sCostPrice;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "供方推荐销售价")
    private BigDecimal sSellPrice;

    @ApiModelProperty(value = "折扣力度")
    private BigDecimal discounts;

    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "sku状态(1启用，0停用)")
    private Integer state;

    @ApiModelProperty(value = "sku单位")
    private String unit;

    @ApiModelProperty(value = "临购副单位")
    private String secondUnit;

    @ApiModelProperty(value = "临购副单位对应主单位数量系数")
    private BigDecimal secondUnitNum;

    @ApiModelProperty(value = "销量")
    private BigDecimal soldNum;

    @ApiModelProperty(value = "sku标题")
    private String skuTitle;

    @ApiModelProperty(value = "sku副标题")
    private String skuSubtitle;

    @ApiModelProperty(value = "品牌id")
    private String brandId;

    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")
    private Integer productType;

    @ApiModelProperty(value = "租赁单位（天月年）")
    private String leaseUnit;

    @ApiModelProperty(value = "结算价")
    private BigDecimal settlePrice;

    @ApiModelProperty(value = "区域类型 1全区域，2区域")
    private Integer isZone;
}
