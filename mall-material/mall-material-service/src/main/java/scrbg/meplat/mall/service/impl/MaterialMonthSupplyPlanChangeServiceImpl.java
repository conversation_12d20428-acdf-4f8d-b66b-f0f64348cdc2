package scrbg.meplat.mall.service.impl;

import scrbg.meplat.mall.entity.MaterialMonthSupplyPlanChange;
import scrbg.meplat.mall.mapper.MaterialMonthSupplyPlanChangeMapper;
import scrbg.meplat.mall.service.MaterialMonthSupplyPlanChangeService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

/**
 * @描述：计划变更表 服务类
 * @作者: ye
 * @日期: 2023-06-27
 */
@Service
public class MaterialMonthSupplyPlanChangeServiceImpl extends ServiceImpl<MaterialMonthSupplyPlanChangeMapper, MaterialMonthSupplyPlanChange> implements MaterialMonthSupplyPlanChangeService{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialMonthSupplyPlanChange> queryWrapper) {
        IPage<MaterialMonthSupplyPlanChange> page = this.page(
        new Query<MaterialMonthSupplyPlanChange>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(MaterialMonthSupplyPlanChange materialMonthSupplyPlanChange) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(materialMonthSupplyPlanChange);
    }

    @Override
    public void update(MaterialMonthSupplyPlanChange materialMonthSupplyPlanChange) {
        super.updateById(materialMonthSupplyPlanChange);
    }


    @Override
    public MaterialMonthSupplyPlanChange getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}