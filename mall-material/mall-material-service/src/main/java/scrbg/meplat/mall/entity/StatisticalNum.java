package scrbg.meplat.mall.entity;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import java.math.BigDecimal;

import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：物资数据统计
 * @作者: ye
 * @日期: 2023-08-27
 */
@ApiModel(value="物资数据统计")
@Data
@TableName("statistical_num")
public class StatisticalNum implements Serializable {

private static final long serialVersionUID = 1L;



    @ApiModelProperty(value = "统计时间")

    private Date statisticalDate;


    @ApiModelProperty(value = "供应商数")

    private Integer supplierNum;


    @ApiModelProperty(value = "店铺数")

    private Integer shopNum;


    @ApiModelProperty(value = "内部企业数")

    private Integer inEnterprise;


    @ApiModelProperty(value = "内部用户数")

    private Integer inUserCounts;


    @ApiModelProperty(value = "用户数")

    private Integer userNum;


    @ApiModelProperty(value = "订单金额")

    private BigDecimal ordersTotalAmount;


    @ApiModelProperty(value = "商品总数")

    private Integer productNum;


    @ApiModelProperty(value = "商品上架总数")

    private Integer upProductNum;


    @ApiModelProperty(value = "零星采购订单数")

    private Integer ordersNum;


    @ApiModelProperty(value = "零星采购发货金额")

    private BigDecimal orderShipAmount;



}
