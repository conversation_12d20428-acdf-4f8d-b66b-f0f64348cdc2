package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.ProductCategory1;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ProductCategory1;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：商品分类 服务类
 * @作者: ye
 * @日期: 2023-09-27
 */
public interface ProductCategory1Service extends IService<ProductCategory1> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductCategory1> queryWrapper);

        void create(ProductCategory1 productCategory1);
        void update(ProductCategory1 productCategory1);
        ProductCategory1 getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);
}
