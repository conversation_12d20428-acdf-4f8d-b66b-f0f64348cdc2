<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <springProperty scope="context" name="springAppName" source="spring.application.name"/>
    
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [${springAppName:-},%X{X-B3-TraceId:-},%X{X-B3-SpanId:-},%X{X-Span-Export:-}] %thread %logger row:%L %msg%n"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

 
    <logger name="org.mybatis" level="info" additivity="false">
        <appender-ref ref="console"/>
    </logger>
    <logger name="org.springframework" level="info" additivity="false">
        <appender-ref ref="console"/>
    </logger>
    <logger name="com.zaxxer.hikari.pool" level="info" additivity="false">
        <appender-ref ref="console"/>
    </logger>
    <logger name="org.apache.http" level="info" additivity="false">
        <appender-ref ref="console"/>
    </logger>

    <root level="debug">
        <appender-ref ref="console"/>
    </root>
</configuration>
