<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.BiddingProjectInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.BiddingProjectInfo" id="BiddingProjectInfoMap">
        <result property="projectId" column="project_id"/>
        <result property="biddingTypeId" column="bidding_type_id"/>
        <result property="projectCode" column="project_code"/>
        <result property="projectName" column="project_name"/>
        <result property="budgetMoney" column="budget_money"/>
        <result property="address" column="address"/>
        <result property="organizationWay" column="organization_way"/>
        <result property="biddingWay" column="bidding_way"/>
        <result property="applyWay" column="apply_way"/>
        <result property="examineWay" column="examine_way"/>
        <result property="projectDescribe" column="project_describe"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="state" column="state"/>
        <result property="isDelete" column="is_delete"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>