<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.SysOperLogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.SysOperLog" id="SysOperLogMap">
        <result property="id" column="id"/>
        <result property="operIp" column="oper_ip"/>
        <result property="operatorType" column="operator_type"/>
        <result property="title" column="title"/>
        <result property="businessType" column="business_type"/>
        <result property="method" column="method"/>
        <result property="requestMethod" column="request_method"/>
        <result property="operParam" column="oper_param"/>
        <result property="jsonResult" column="json_result"/>
        <result property="status" column="status"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="operTime" column="oper_time"/>
        <result property="costTime" column="cost_time"/>
        <result property="operName" column="oper_name"/>
        <result property="orgName" column="org_name"/>
        <result property="operLocation" column="oper_location"/>
    </resultMap>


</mapper>