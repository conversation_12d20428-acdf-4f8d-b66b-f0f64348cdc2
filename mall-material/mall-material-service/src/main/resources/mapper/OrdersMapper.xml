<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.OrdersMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.Orders" id="OrdersMap">
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="shopName" column="shop_name"/>
        <result property="shopId" column="shop_id"/>
        <result property="userId" column="user_id"/>
        <result property="untitled" column="untitled"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverMobile" column="receiver_mobile"/>
        <result property="receiverAddress" column="receiver_address"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="actualAmount" column="actual_amount"/>
        <result property="dealType" column="deal_type"/>
        <result property="payType" column="pay_type"/>
        <result property="payWay" column="pay_way"/>
        <result property="orderRemark" column="order_remark"/>
        <result property="state" column="state"/>
        <result property="orderBillState" column="order_bill_state"/>
        <result property="deliveryType" column="delivery_type"/>
        <result property="deliveryFlowId" column="delivery_flow_id"/>
        <result property="orderFreight" column="order_freight"/>
        <result property="isDelete" column="is_delete"/>
        <result property="payTime" column="pay_time"/>
        <result property="deliveryTime" column="delivery_time"/>
        <result property="flishTime" column="flish_time"/>
        <result property="cancelTime" column="cancel_time"/>
        <result property="closeType" column="close_type"/>
        <result property="tradeNo" column="trade_no"/>
        <result property="productType" column="product_type"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderName" column="founder_name"/>
        <result property="founderId" column="founder_id"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>

    </resultMap>

    <select id="listUserOrderPageList" resultType="scrbg.meplat.mall.vo.user.userCenter.UserOrderPageListVO"
            parameterType="map">
        select
        o.`order_id`,
        o.`order_sn`,
        o.`shop_id`,
        o.`enterprise_id`,
        o.`enterprise_name`,
        oi.`order_item_id`,
        oi.`product_id`,
        oi.`product_name` untitled,
        o.`receiver_name`,
        oi.`total_amount` actualAmount,
        o.`state`,
        o.`order_bill_state`,
        o.`flish_time`,
        o.`gmt_create`,
        oi.`buy_counts`,
        oi.`product_img`,
        oi.`is_comment`,
        oi.`product_type`,
        oi.`invoice_state`,
        oi.`return_state`,
        oi.`return_counts`,
        oi.`pcwp_return`,
        oi.ship_counts,
        oi.confirm_counts
        from orders o
        inner join order_item oi on o.order_id = oi.order_id and o.is_delete = 0 and oi.is_delete = 0
        <where>
             o.mall_type = #{dto.mallType} and o.parent_order_id is null
        <if test="dto.enterpriseIds != null and dto.enterpriseIds.size()>0">
          and   o.enterprise_id in
            <foreach collection="dto.enterpriseIds" item="itemType" open="(" close=")" separator=",">
                #{itemType}
            </foreach>
        </if>
            <if test="dto.state != null">
                and o.`state` = #{dto.state}
            </if>
            <if test="dto.isComment != null">
                and oi.`is_comment` = #{dto.isComment}
            </if>
<!--            <if test="dto.productType != null">-->
<!--                and o.`product_type` = #{dto.productType}-->
<!--            </if>-->
            <if test="dto.productTypes != null and dto.productTypes.size()>0">
                and o.`product_type` in
                <foreach collection="dto.productTypes" item="productType" open="(" close=")" separator=",">
                    #{productType}
                </foreach>
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and o.`gmt_create` between #{dto.startDate} and #{dto.endDate}
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (o.`untitled` LIKE CONCAT('%',#{dto.keywords},'%') or oi.`product_sn` LIKE
                CONCAT('%',#{dto.keywords},'%') or o.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
        </where>
        order by o.`gmt_create` desc ,oi.order_item_id desc
    </select>

    <select id="listUserOrderPageListCount" resultType="int"
            parameterType="map">
        select count(*)
        from orders o
        inner join order_item oi on o.order_id = oi.order_id and o.is_delete = 0 and oi.is_delete = 0
        <where>
            o.mall_type = #{dto.mallType} and o.parent_order_id is null
            <if test="dto.enterpriseIds != null and dto.enterpriseIds.size()>0">
                and   o.enterprise_id in
                <foreach collection="dto.enterpriseIds" item="itemType" open="(" close=")" separator=",">
                    #{itemType}
                </foreach>
            </if>
            <if test="dto.state != null">
                and o.`state` = #{dto.state}
            </if>
            <if test="dto.isComment != null">
                and oi.`is_comment` = #{dto.isComment}
            </if>
<!--            <if test="dto.productType != null">-->
<!--                and o.`product_type` = #{dto.productType}-->
<!--            </if>-->
            <if test="dto.productTypes != null and dto.productTypes.size()>0">
                and o.`product_type` in
                <foreach collection="dto.productTypes" item="productType" open="(" close=")" separator=",">
                    #{productType}
                </foreach>
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and o.`gmt_create` between #{dto.startDate} and #{dto.endDate}
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (o.`untitled` LIKE CONCAT('%',#{dto.keywords},'%') or oi.`product_sn` LIKE
                CONCAT('%',#{dto.keywords},'%') or o.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
        </where>
    </select>

    <select id="listUserOrderCommentPageList" resultType="scrbg.meplat.mall.vo.user.userCenter.UserOrderPageListVO"
            parameterType="map">
        select
        o.`order_id`,
        o.`order_sn`,
        o.`shop_id`,
        o.`shop_name`,
        o.`enterprise_id`,
        o.`enterprise_name`,
        o.`receiver_name`,
        o.`total_amount` as `actualAmount`,
        o.`state`,
        o.`order_bill_state`,
        o.`flish_time`,
        o.`gmt_create`,
        o.`product_type`,
        pc.`gmt_create` as `commentTime`,
        op.`bill_no`
        from orders o
        left JOIN (select distinct order_id,bill_no from order_select_plan where is_delete='0') op
        ON o.order_id = op.order_id
        LEFT JOIN ( SELECT gmt_create,order_id FROM product_comment WHERE is_delete = '0' and product_id is null ) pc
        ON o.order_id = pc.order_id
        <where>
            and o.is_delete = 0 and o.mall_type = #{dto.mallType} and o.parent_order_id is null
            <if test="dto.enterpriseIds != null and dto.enterpriseIds.size()>0">
                and   o.enterprise_id in
                <foreach collection="dto.enterpriseIds" item="itemType" open="(" close=")" separator=",">
                    #{itemType}
                </foreach>
            </if>
            <if test="dto.state != null">
                and o.`state` = #{dto.state}
            </if>
            <if test="dto.isComment != null">
                and o.`is_comment` = #{dto.isComment}
            </if>
            <if test="dto.productTypes != null and dto.productTypes.size()>0">
                and o.`product_type` in
                <foreach collection="dto.productTypes" item="productType" open="(" close=")" separator=",">
                    #{productType}
                </foreach>
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and o.`gmt_create` between #{dto.startDate} and #{dto.endDate}
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (o.`untitled` LIKE CONCAT('%',#{dto.keywords},'%') or o.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
        </where>
        <if test="dto.isComment == 1">
            order by pc.`gmt_create` desc
        </if>
        <if test="dto.isComment == 0">
            order by o.`gmt_create` desc
        </if>
    </select>

    <select id="listUserOrderCommentPageListCount" resultType="int"
            parameterType="map">
        select count(*)
        from orders o
        left JOIN (select distinct order_id,bill_no from order_select_plan where is_delete='0') op ON o.order_id = op.order_id
        AND o.is_delete = 0
        <where>
            o.mall_type = #{dto.mallType} and o.parent_order_id is null
            <if test="dto.enterpriseIds != null and dto.enterpriseIds.size()>0">
                and   o.enterprise_id in
                <foreach collection="dto.enterpriseIds" item="itemType" open="(" close=")" separator=",">
                    #{itemType}
                </foreach>
            </if>
            <if test="dto.state != null">
                and o.`state` = #{dto.state}
            </if>
            <if test="dto.isComment != null">
                and o.`is_comment` = #{dto.isComment}
            </if>
            <if test="dto.productTypes != null and dto.productTypes.size()>0">
                and o.`product_type` in
                <foreach collection="dto.productTypes" item="productType" open="(" close=")" separator=",">
                    #{productType}
                </foreach>
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and o.`gmt_create` between #{dto.startDate} and #{dto.endDate}
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (o.`untitled` LIKE CONCAT('%',#{dto.keywords},'%') or o.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
        </where>
    </select>

    <select id="findOrderByorderSn" resultType="scrbg.meplat.mall.entity.Orders">
        select *  from orders  where order_sn=#{orderSn}
    </select>

    <select id="getPlatformWeekOrdersProfitPriceTotal" resultType="java.math.BigDecimal">
        SELECT
            SUM(profit_price_total)
        FROM
            orders
        <where>
            mall_type = #{dto.mallType} and `state` = 10 and `is_delete` = 0
            <if test="dto.shopId != null">
                and `shop_id` = #{dto.shopId}
            </if>
            <if test="dto.orderClass != null and dto.orderClass !='' ">
                and `order_class` != #{dto.orderClass}
            </if>
            <if test="dto.keywords != null and dto.keywords !='' ">
                and (`untitled` LIKE CONCAT('%',#{dto.keywords},'%')
                    or `order_sn` LIKE CONCAT('%',#{dto.keywords},'%')
                    or `enterprise_name` LIKE CONCAT('%',#{dto.keywords},'%')
                    or `supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.productType != null ">
                and `product_type` = #{dto.productType}
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and `success_date` between #{dto.startDate} and #{dto.endDate}
            </if>
        </where>
    </select>
    <select id="getPlatformWeekOrdersCount" resultType="int">
        SELECT
        count(*)
        FROM
        orders
        <where>
            mall_type = #{dto.mallType} and `state` = 10 and `is_delete` = 0
            <if test="dto.shopId != null">
                and `shop_id` = #{dto.shopId}
            </if>
            <if test="dto.orderClass != null and dto.orderClass !='' ">
                and `order_class` != #{dto.orderClass}
            </if>
            <if test="dto.keywords != null and dto.keywords !='' ">
                and (`untitled` LIKE CONCAT('%',#{dto.keywords},'%')
                or `order_sn` LIKE CONCAT('%',#{dto.keywords},'%')
                or `enterprise_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or `supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.productType != null ">
                and `product_type` = #{dto.productType}
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and `success_date` between #{dto.startDate} and #{dto.endDate}
            </if>
        </where>
    </select>

    <select id="getPlatformOrderTotalCount" resultType="scrbg.meplat.mall.vo.platform.PlatformShopCountVo">
        select count(order_id) as ordersCount,
               sum(cost_price_total) as costPriceTotal ,
               sum(profit_price_total) as profitPriceTotal,
               sum(total_amount) as totalAmount,
               sum(actual_amount) as actualAmount
        from orders
        where  ${ew.sqlSegment}
    </select>
    <select id="getPlatformShopOrderCount" resultType="scrbg.meplat.mall.vo.platform.ShopCountVo">
        select s.shop_name, count(o.order_id) as ordersCount,
               sum(o.cost_price_total) as costPriceTotal ,
               sum(o.profit_price_total) as profitPriceTotal,
               sum(o.total_amount) as totalAmount,
               sum(o.actual_amount) as actualAmount
        from orders  o
                 join shop    s
                      on   o.shop_id=s.shop_id
            where  ${ew.sqlSegment}
    </select>
    <resultMap id="getShopManageOrderOutZIPDataMap" type="scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPDataVO">
        <result column="supplier_name" property="supplierName"/>
        <result column="enterprise_name" property="enterpriseName"/>
        <result column="enterprise_name" property="enterpriseName"/>
        <collection property="dataList" ofType="scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPDataItemVO">
            <result property="orderSn" column="order_sn"/>
            <result property="createTime" column="gmt_create"/>
            <result property="productName" column="product_name"/>
            <result property="skuName" column="sku_name"/>
            <result property="unit" column="unit"/>
            <result property="buyCounts" column="buy_counts"/>
            <result property="productPrice" column="product_price"/>
            <result property="noRateAmount" column="no_rate_amount"/>
            <result property="totalAmount" column="total_amount"/>
            <result property="orderRemark" column="order_remark"/>
        </collection>
    </resultMap>

    <select id="getShopManageOrderOutZIPData"
            resultMap="getShopManageOrderOutZIPDataMap">
        SELECT
            o.supplier_name ,
            o.enterprise_name ,
            o.order_sn ,
            o.gmt_create ,
            oi.product_name ,
            oi.sku_name ,
            oi.unit ,
            oi.buy_counts ,
            oi.product_price ,
            oi.no_rate_amount ,
            oi.total_amount ,
            o.order_remark
        FROM
            `orders` o
                INNER JOIN order_item oi on o.order_id = oi.order_id
        where ${ew.sqlSegment}
    </select>



    <resultMap id="getShopManageOrderOutZIPDataMap2" type="scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPVO">
        <result column="supplier_name" property="supplierName"/>
        <result column="enterprise_name" property="enterpriseName"/>
        <result column="out_phase_interest" property="outPhaseInterest"/>
        <result column="payment_week" property="paymentWeek"/>
        <result column="success_date" property="successDate"/>
        <result property="orderSn" column="order_sn"/>
        <collection property="dataList" ofType="scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPDataItemVO">
            <result property="orderSn" column="order_sn"/>
            <result property="createTime" column="gmt_create"/>
            <result property="texture" column="texture"/>
            <result property="relevanceName" column="relevance_name"/>
            <result property="productName" column="product_name"/>
            <result property="skuName" column="sku_name"/>
            <result property="unit" column="unit"/>
            <result property="buyCounts" column="buy_counts"/>
            <result property="productPrice" column="product_price"/>
            <result property="noRateAmount" column="no_rate_amount"/>
            <result property="totalAmount" column="total_amount"/>
            <result property="orderRemark" column="order_remark"/>
        </collection>
    </resultMap>

    <select id="selectOutOrderList"
            resultMap="getShopManageOrderOutZIPDataMap2">
        SELECT
            o.supplier_name ,
            o.enterprise_name ,
            o.order_sn ,
            o.gmt_create ,
            o.payment_week,
            o.out_phase_interest,
            o.success_date,
            oi.product_name ,
            oi.sku_name ,
            oi.relevance_name ,
            oi.texture ,
            oi.unit ,
            oi.buy_counts ,
            oi.product_price ,
            oi.no_rate_amount ,
            oi.total_amount ,
            o.order_remark
        FROM
            `orders` o
                INNER JOIN order_item oi on o.order_id = oi.order_id
        where ${ew.sqlSegment}
    </select>
    <select id="getPlatformShopOrderCountExcel" resultType="scrbg.meplat.mall.vo.platform.ShopCountVo">
        select s.shop_name, count(o.order_id) as ordersCount,
               sum(o.cost_price_total) as costPriceTotal ,
               sum(o.profit_price_total) as profitPriceTotal,
               sum(o.total_amount) as totalAmount,
               sum(o.actual_amount) as actualAmount
        from orders  o
                 join shop    s
                      on   o.shop_id=s.shop_id
        where  ${ew.sqlSegment}
    </select>

    <!-- 查询有可对账物资的订单编号列表（去重，批量查询不分页） -->
    <select id="getReconcilableOrderSnList" resultType="java.lang.String">
        SELECT DISTINCT res.order_id
        FROM (
            SELECT
                ors.order_id,
                ors.supplier_id AS supplierOrgId,
                ors.confirm_time AS receivingDate,
                ors.ship_enterprise_id AS twoSupplierOrgId,
                dtl.is_reconciliation
            FROM order_ship ors
            INNER JOIN order_ship_dtl dtl ON ors.bill_Id = dtl.bill_id
            WHERE ors.type = 2
              AND ors.order_class != 1
              AND ors.is_delete = 0
              AND dtl.is_delete = 0
            UNION ALL
            SELECT
                ore.order_id,
                ore.supplier_id AS supplierOrgId,
                ore.flish_time AS receivingDate,
                ore.ship_enterprise_id AS twoSupplierOrgId,
                ori.is_reconciliation
            FROM order_return ore
            INNER JOIN order_return_item ori ON ore.order_return_id = ori.order_return_id
            WHERE ore.state = 3
              AND ore.is_out = 1
              AND ore.is_delete = 0
              AND ori.is_delete = 0
        ) res
        WHERE res.is_reconciliation = 0
        <if test="dto.supplierOrgId != null and dto.supplierOrgId != ''">
            AND res.supplierOrgId = #{dto.supplierOrgId}
        </if>
        <if test="dto.twoSupplierOrgId != null and dto.twoSupplierOrgId != ''">
            AND res.twoSupplierOrgId = #{dto.twoSupplierOrgId}
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND res.receivingDate &gt; #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND res.receivingDate &lt; #{dto.endTime}
        </if>
    </select>

</mapper>
