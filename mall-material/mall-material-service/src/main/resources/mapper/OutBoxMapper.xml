<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.OutBoxMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.EnterpriseInfo" id="EnterpriseInfoMap">
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="enterpriseNumber" column="enterprise_number"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="socialCreditCode" column="social_credit_code"/>
        <result property="legalRepresentative" column="legal_representative"/>
        <result property="email" column="email"/>
        <result property="operator" column="operator"/>
        <result property="creationTime" column="creation_time"/>
        <result property="placeOfBusiness" column="place_of_business"/>
        <result property="licenseTerm" column="license_term"/>
        <result property="registeredCapital" column="registered_capital"/>
        <result property="provinces" column="provinces"/>
        <result property="city" column="city"/>
        <result property="county" column="county"/>
        <result property="detailedAddress" column="detailed_address"/>
        <result property="mainBusiness" column="main_business"/>
        <result property="businessLicense" column="business_license"/>
        <result property="businessLicenseId" column="business_license_id"/>
        <result property="cardPortraitFaceId" column="card_portrait_face_id"/>
        <result property="cardPortraitFace" column="card_portrait_face"/>
        <result property="cardPortraitNationalEmblemId" column="card_portrait_national_emblem_id"/>
        <result property="cardPortraitNationalEmblem" column="card_portrait_national_emblem"/>
        <result property="adminName" column="admin_name"/>
        <result property="adminPhone" column="admin_phone"/>
        <result property="adminNumber" column="admin_number"/>
        <result property="incomeCallTime" column="income_call_time"/>
        <result property="incomeCallContent" column="income_call_content"/>
        <result property="processingDepartnment" column="processing_departnment"/>
        <result property="processingTime" column="processing_time"/>
        <result property="handlingResult" column="handling_result"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="isSupplier" column="is_supplier"/>
        <result property="enterpriseType" column="enterprise_type"/>
        <result property="enterpriseBusinessType" column="enterprise_business_type"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>
    <select id="getSupplyShop" resultType="scrbg.meplat.mall.vo.user.userCenter.OutBoxVo">
        select a.*,u.* from enterprise_info a
                                                   LEFT JOIN `shop` u
                                                             on  a.enterprise_id=u.enterprise_id

        where  a.is_supplier = '2' and is_material_mall='1'
        <if test="dto.keywords!=null and dto.keywords != ''">
          and (u.`shop_name` like CONCAT('%',#{dto.keywords},'%')  or a.`enterprise_name` like CONCAT('%',#{dto.keywords},'%'))
        </if>

    </select>

</mapper>
