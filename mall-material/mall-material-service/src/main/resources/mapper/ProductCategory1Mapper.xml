<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ProductCategory1Mapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ProductCategory1" id="ProductCategory1Map">
        <result property="classId" column="class_id"/>
        <result property="className" column="class_name"/>
        <result property="classKeyword" column="class_keyword"/>
        <result property="classLevel" column="class_level"/>
        <result property="parentId" column="parent_id"/>
        <result property="classIcon" column="class_icon"/>
        <result property="classBgColor" column="class_bg_color"/>
        <result property="sort" column="sort"/>
        <result property="classDescribe" column="class_describe"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="state" column="state"/>
        <result property="productType" column="product_type"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="isExhibition" column="is_exhibition"/>
        <result property="isHaveProduct" column="is_have_product"/>
        <result property="classPath" column="class_path"/>
        <result property="classNo" column="class_no"/>
    </resultMap>


</mapper>