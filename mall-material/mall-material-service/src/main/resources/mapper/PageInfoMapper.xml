<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PageInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.PageInfo" id="PageInfoMap">
        <result property="pageId" column="page_id"/>
        <result property="pageName" column="page_name"/>
        <result property="productCategoryIds" column="product_category_ids"/>
        <result property="productCategoryName" column="product_category_name"/>
        <result property="enterpriseIds" column="enterprise_ids"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="floorIds" column="floor_ids"/>
        <result property="floorName" column="floor_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>