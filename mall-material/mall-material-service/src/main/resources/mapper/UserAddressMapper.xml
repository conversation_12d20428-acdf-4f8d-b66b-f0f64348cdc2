<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.UserAddressMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.UserAddress" id="UserAddressMap">
        <result property="addressId" column="address_id"/>
        <result property="userId" column="user_id"/>
        <result property="aliasAddress" column="alias_address"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverMobile" column="receiver_mobile"/>
        <result property="fixedTelephone" column="fixed_telephone"/>
        <result property="email" column="email"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="county" column="county"/>
        <result property="detailAddress" column="detail_address"/>
        <result property="postCode" column="post_code"/>
        <result property="state" column="state"/>
        <result property="isDefaultAddress" column="is_default_address"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>