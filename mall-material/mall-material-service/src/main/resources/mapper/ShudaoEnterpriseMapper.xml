<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ShudaoEnterpriseMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ShudaoEnterprise" id="ShudaoEnterpriseMap">
        <result property="shudaoEnterpriseId" column="shudao_enterprise_id"/>
        <result property="affiliationEnterprise" column="affiliation_enterprise"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="enterpriseCategory" column="enterprise_category"/>
        <result property="adjust" column="adjust"/>
        <result property="enterpriseNature" column="enterprise_nature"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>