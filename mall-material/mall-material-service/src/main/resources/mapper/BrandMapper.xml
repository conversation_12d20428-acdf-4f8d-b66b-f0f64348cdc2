<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.BrandMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.Brand" id="BrandMap">
        <result property="brandId" column="brand_id"/>
        <result property="name" column="name"/>
        <result property="logo" column="logo"/>
        <result property="descript" column="descript"/>
        <result property="state" column="state"/>
        <result property="sort" column="sort"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="productType" column="product_type"/>
        <result property="mallType" column="mall_type"/>
        <result property="classId" column="class_id"/>
        <result property="className" column="class_name"/>
    </resultMap>


</mapper>