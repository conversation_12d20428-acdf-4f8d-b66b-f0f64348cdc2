<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.BiddingTypeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.BiddingType" id="BiddingTypeMap">
        <result property="biddingTypeId" column="bidding_type_id"/>
        <result property="biddingTypeName" column="bidding_type_name"/>
        <result property="state" column="state"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>