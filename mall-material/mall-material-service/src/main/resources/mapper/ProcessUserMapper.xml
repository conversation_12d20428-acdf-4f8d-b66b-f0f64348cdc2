<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ProcessUserMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ProcessUser" id="ProcessUserMap">
        <result column="process_user_id" property="processUserId"/>
        <result property="processId" column="process_id"/>
    	<result column="process_role_id" property="processRoleId"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="modifyId" column="modify_id"/>
        <result property="modifyName" column="modify_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

</mapper>