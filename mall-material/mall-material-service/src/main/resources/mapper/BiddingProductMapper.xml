<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.BiddingProductMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.BiddingProduct" id="BiddingProductMap">
        <result property="biddingProductId" column="bidding_product_id"/>
        <result property="biddingId" column="bidding_id"/>
        <result property="biddingSn" column="bidding_sn"/>
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="productName" column="product_name"/>
        <result property="brand" column="brand"/>
        <result property="spec" column="spec"/>
        <result property="unit" column="unit"/>
        <result property="num" column="num"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="classId" column="class_id"/>
        <result property="classPathName" column="class_path_name"/>
    </resultMap>


</mapper>