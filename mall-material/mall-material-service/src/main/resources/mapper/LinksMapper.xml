<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.LinksMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.Links" id="LinksMap">
        <result property="linkId" column="link_id"/>
        <result property="name" column="name"/>
        <result property="remarks" column="remarks"/>
        <result property="url" column="url"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="isDelete" column="is_delete"/>
        <result property="state" column="state"/>
        <result property="sort" column="sort"/>
        <result property="display" column="display"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>