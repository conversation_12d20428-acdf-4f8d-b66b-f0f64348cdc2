<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.SynthesizeTemporaryDtlMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.SynthesizeTemporaryDtl" id="SynthesizeTemporaryDtlMap">
        <result property="synthesizeTemporaryDtlId" column="synthesize_temporary_dtl_id"/>
        <result property="synthesizeTemporaryId" column="synthesize_temporary_id"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="classId" column="class_id"/>
        <result property="className" column="class_name"/>
        <result property="classIdPath" column="class_id_path"/>
        <result property="classNamePath" column="class_name_path"/>
        <result property="spec" column="spec"/>
        <result property="texture" column="texture"/>
        <result property="brandId" column="brand_id"/>
        <result property="brandName" column="brand_name"/>
        <result property="unit" column="unit"/>
        <result property="qty" column="qty"/>
        <result property="weightNum" column="weight_num"/>
        <result property="paymentWeek" column="payment_week"/>
        <result property="referencePrice" column="reference_price"/>
        <result property="synthesizePrice" column="synthesize_price"/>
        <result property="netPrice" column="net_price"/>
        <result property="managePrice" column="manage_price"/>
        <result property="fixationPrice" column="fixation_price"/>
        <result property="referenceAmount" column="reference_amount"/>
        <result property="synthesizeAmount" column="synthesize_amount"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>