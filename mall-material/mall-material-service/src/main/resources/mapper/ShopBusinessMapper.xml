<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ShopBusinessMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ShopBusiness" id="ShopBusinessMap">
        <result property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="state" column="state"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="interiorId" column="interior_id"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
        <result property="modifyName" column="modify_name"/>
        <result property="modifyId" column="modify_id"/>
    </resultMap>


</mapper>