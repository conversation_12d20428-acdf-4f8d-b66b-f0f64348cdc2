<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.OrderReturnMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.OrderReturn" id="OrderReturnMap">
        <result property="orderReturnId" column="order_return_id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="otherNoRateAmount" column="untitled"/>
        <result property="otherRateAmount" column="other_rate_amount"/>
        <result property="rateAmount" column="rate_amount"/>

        <result property="totalAmount" column="total_amount"/>
        <result property="orderReturnNo" column="order_return_no"/>
        <result property="remarks" column="remarks"/>

        <result property="submitReason" column="submit_reason"/>
        <result property="userId" column="user_id"/>

        <result property="returnMethod" column="return_method"/>
        <result property="address" column="address"/>
        <result property="state" column="state"/>
        <result property="deliveryFlowId" column="delivery_flow_id"/>
        <result property="deliveryType" column="delivery_type"/>
        <result property="flishTime" column="flish_time"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverMobile" column="receiver_mobile"/>
        <result property="receiverAddress" column="receiver_address"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderName" column="founder_name"/>
        <result property="founderId" column="founder_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="mallType" column="mall_type"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>

    <resultMap id="orderRetuenVo" type="scrbg.meplat.mall.entity.OrderReturn">
        <id column="order_return_id" property="orderReturnId"/>
        <result column="order_Id" property="orderId" />
        <result column="order_sn" property="orderSn" />
        <result column="shop_name" property="shopName"/>
        <result column="order_return_no" property="orderReturnNo"/>
        <result column="ship_enterprise_id" property="shipEnterpriseId"/>
        <result column="ship_enterprise_name" property="shipEnterpriseName"/>
        <result column="remarks" property="remarks"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="fish_time" property="flishTime"/>
        <collection property="orderReturnItems" ofType="scrbg.meplat.mall.entity.OrderReturnItem">
            <id column="order_return_id" property="orderReturnId"/>
            <result column="product_name" property="productName"/>
            <result column="sku_name" property="skuName"/>
            <result column="count" property="count"/>
            <result column="product_price" property="productPrice"/>
            <result column="brand_name" property="brandName"/>

            <result column="other_order_item_id" property="otherOrderItemId"/>
            <result column="no_rate_price" property="noRatePrice"/>
            <result column="no_rate_amount" property="noRateAmount"/>
            <result column="other_total_amount" property="OtherTotalAmount"/>
            <result column="other_order_id" property="otherOrderId"/>
            <result column="rate_amount" property="rateAmount"/>
            <result column="other_order_sn" property="otherOrderSn"/>
            <result column="other_product_price" property="otherProductPrice"/>
            <result column="buy_counts" property="buyCounts"/>
            <result column="product_img" property="productImg"/>
            <result column="product_id" property="productId"/>
            <result column="product_sn" property="productSn"/>
            <result column="sku_name" property="skuName"/>
            <result column="remarks" property="remarks"/>
        </collection>
    </resultMap>

    <select id="findByCondition" resultMap="orderRetuenVo">

        select ore.order_return_id,
               ore.order_id,
               ore.order_sn,
               ore.shop_name,
               ore.order_return_no,
               ore.gmt_create,
               ore.flish_time,
               ori.product_name,
               ori.order_item_id,
               ori.order_return_id,
               ori.count,
               ori.product_img,
               ori.brand_name,
               ori.sku_name,
               ori.product_price,
               ori.product_sn,
               ori.founder_name,
               ori.other_order_item_id,
               ori.no_rate_price,
               ori.no_rate_amount,
               ori.other_product_price,
               ori.other_total_amount,
               ori.other_order_id,
               ori.rate_amount,
               ori.product_id,
               ori.buy_counts,
               ori.remarks
        from order_return ore
                 left join order_return_item ori
                           on ore.order_return_id=ori.order_return_id
          where   ${ew.sqlSegment}

    </select>


    <select id="shopTwoOrderReturnPage" resultType="scrbg.meplat.mall.entity.OrderReturn">
        SELECT ore.*, oi.product_img as productMinImg
        FROM order_return ore
                 join order_item oi
                      on ore.order_item_id = oi.order_item_id
        where ${ew.sqlSegment}

    </select>
    <select id="getDataVoById" resultType="scrbg.meplat.mall.entity.OrderReturn">
        select r.*, i.product_img as productMinImg
        from order_return r
                 left join order_item i
                           on r.order_item_id = i.order_item_id
        where r.order_return_id = #{id}
    </select>
</mapper>
