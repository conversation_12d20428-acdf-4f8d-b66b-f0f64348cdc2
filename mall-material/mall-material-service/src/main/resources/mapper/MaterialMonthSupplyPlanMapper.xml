<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.MaterialMonthSupplyPlanMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.MaterialMonthSupplyPlan" id="MaterialMonthSupplyPlanMap">
        <result property="planId" column="plan_id"/>
        <result property="planNo" column="plan_no"/>
        <result property="planDate" column="plan_date"/>
        <result property="planCreateDate" column="plan_create_date"/>
        <result property="businessType" column="business_type"/>
        <result property="contractId" column="contract_id"/>
        <result property="contractNo" column="contract_no"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="orgId" column="org_id"/>
        <result property="orgName" column="org_name"/>
        <result property="state" column="state"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
        <result property="localSupplierId" column="local_supplier_id"/>
        <result property="localOrgId" column="local_org_id"/>
    </resultMap>
    <select id="secondLevelPlanMonthList" resultType="scrbg.meplat.mall.entity.MaterialMonthSupplyPlan">
        select distinct p.* from material_month_supply_plan_dtl dtl left join material_month_supply_plan p
        on dtl.plan_id=p.plan_id
        where  ${ew.sqlSegment}
    </select>


</mapper>
