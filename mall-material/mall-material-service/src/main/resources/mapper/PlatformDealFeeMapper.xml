<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PlatformDealFeeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.PlatformDealFee" id="PlatformDealFeeMap">
        <result property="platformDealFeeId" column="platform_deal_fee_id"/>
        <result property="platformDealFeeNu" column="platform_deal_fee_nu"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="serveType" column="serve_type"/>
        <result property="paymentAmount" column="payment_amount"/>
        <result property="dealAmount" column="deal_amount"/>
        <result property="arrearageAmount" column="arrearage_amount"/>
        <result property="exceedQuota" column="exceed_quota"/>
        <result property="stopServe" column="stop_serve"/>
        <result property="remainingSum" column="remaining_sum"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
        <result property="version" column="version"/>
    </resultMap>


</mapper>