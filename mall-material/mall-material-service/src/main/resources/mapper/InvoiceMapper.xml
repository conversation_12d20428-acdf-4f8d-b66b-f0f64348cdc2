<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.InvoiceMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.Invoice" id="InvoiceMap">
        <result property="invoiceId" column="invoice_id"/>
        <result property="enterpriceId" column="enterprice_id"/>
        <result property="enterpriceName" column="enterprice_name"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="invoiceState" column="invoice_state"/>
        <result property="invoiceType" column="invoice_type"/>
        <result property="riseType" column="rise_type"/>
        <result property="userName" column="user_name"/>
        <result property="userAddress" column="user_address"/>
        <result property="userPhone" column="user_phone"/>
        <result property="email" column="email"/>
        <result property="company" column="company"/>
        <result property="dutyParagraph" column="duty_paragraph"/>
        <result property="registerAddress" column="register_address"/>
        <result property="registerPhone" column="register_phone"/>
        <result property="bank" column="bank"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="gmtApply" column="gmt_apply"/>
        <result property="gmtAdopt" column="gmt_adopt"/>
        <result property="invoiceNo" column="Invoice_no"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="detailAddr" column="detail_addr"/>
        <result property="invoiceTitle" column="invoice_title"/>
        <result property="rateAmouns" column="rate_amouns"/>
        <result property="noRateAmouns" column="no_rate_amouns"/>
        <result property="taxRate" column="tax_rate"/>
    </resultMap>


</mapper>
