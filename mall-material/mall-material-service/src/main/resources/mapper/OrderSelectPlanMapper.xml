<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.OrderSelectPlanMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.OrderSelectPlan" id="OrderSelectPlanMap">
        <result property="orderSelectPlanId" column="order_select_plan_id"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="dtlId" column="dtl_id"/>
        <result property="count" column="count"/>
        <result property="billNo" column="bill_no"/>
        <result property="billId" column="bill_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>