<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.MaterialMonthSupplyPlanDtlChangeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtlChange" id="MaterialMonthSupplyPlanDtlChangeMap">
        <result property="planDtlChangeId" column="plan_dtl_change_id"/>
        <result property="planChangeId" column="plan_change_id"/>
        <result property="planId" column="plan_id"/>
        <result property="planDtlId" column="plan_dtl_id"/>
        <result property="materialName" column="material_name"/>
        <result property="spec" column="spec"/>
        <result property="unit" column="unit"/>
        <result property="texture" column="texture"/>
        <result property="thisPlanQty" column="this_plan_qty"/>
        <result property="sourceQty" column="source_qty"/>
        <result property="state" column="state"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
        <result property="contractDtlId" column="contract_dtl_id"/>
        <result property="orderQty" column="order_qty"/>
    </resultMap>


</mapper>