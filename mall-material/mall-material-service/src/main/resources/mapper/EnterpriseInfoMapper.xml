<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.EnterpriseInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.EnterpriseInfo" id="EnterpriseInfoMap">
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="enterpriseNumber" column="enterprise_number"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="socialCreditCode" column="social_credit_code"/>
        <result property="legalRepresentative" column="legal_representative"/>
        <result property="email" column="email"/>
        <result property="operator" column="operator"/>
        <result property="creationTime" column="creation_time"/>
        <result property="placeOfBusiness" column="place_of_business"/>
        <result property="licenseTerm" column="license_term"/>
        <result property="registeredCapital" column="registered_capital"/>
        <result property="provinces" column="provinces"/>
        <result property="city" column="city"/>
        <result property="county" column="county"/>
        <result property="detailedAddress" column="detailed_address"/>
        <result property="mainBusiness" column="main_business"/>
        <result property="businessLicense" column="business_license"/>
        <result property="businessLicenseId" column="business_license_id"/>
        <result property="cardPortraitFaceId" column="card_portrait_face_id"/>
        <result property="cardPortraitFace" column="card_portrait_face"/>
        <result property="cardPortraitNationalEmblemId" column="card_portrait_national_emblem_id"/>
        <result property="cardPortraitNationalEmblem" column="card_portrait_national_emblem"/>
        <result property="adminName" column="admin_name"/>
        <result property="adminPhone" column="admin_phone"/>
        <result property="adminNumber" column="admin_number"/>
        <result property="incomeCallTime" column="income_call_time"/>
        <result property="incomeCallContent" column="income_call_content"/>
        <result property="processingDepartnment" column="processing_departnment"/>
        <result property="processingTime" column="processing_time"/>
        <result property="handlingResult" column="handling_result"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="isSupplier" column="is_supplier"/>
        <result property="enterpriseType" column="enterprise_type"/>
        <result property="enterpriseBusinessType" column="enterprise_business_type"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>
    <select id="ledgerListCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM enterprise_info e
        LEFT JOIN shop s ON e.enterprise_id COLLATE utf8mb4_unicode_ci = s.enterprise_id
                                AND s.is_delete = 0

        LEFT JOIN (
        SELECT
        contract_no,
        party_b_org_id,
        ROW_NUMBER() OVER (PARTITION BY party_b_org_id ORDER BY gmt_create DESC) AS rn
        FROM contract
        WHERE is_delete = 0
        ) c ON e.enterprise_id COLLATE utf8mb4_unicode_ci = c.party_b_org_id AND c.rn = 1

        LEFT JOIN (
        SELECT
        enterprise_id,
        gmt_create,
        serve_end_time,
        ROW_NUMBER() OVER (PARTITION BY enterprise_id ORDER BY gmt_create DESC) AS rn
        FROM platform_year_fee
        WHERE is_delete = 0
        ) p ON e.enterprise_id COLLATE utf8mb4_unicode_ci = p.enterprise_id AND p.rn = 1

        LEFT JOIN (
        SELECT
        SUM(deal_amount) AS totalDealAmount,
        SUM(pay_fee) AS totalDealFee,
        enterprise_id,
        project_enterprise_id
        FROM platform_deal_fee_dtl
        WHERE is_delete = 0
        GROUP BY enterprise_id, project_enterprise_id
        ) pd ON e.enterprise_id COLLATE utf8mb4_unicode_ci = pd.enterprise_id
        OR e.enterprise_id COLLATE utf8mb4_unicode_ci = pd.project_enterprise_id

        WHERE e.is_delete = 0
        AND e.is_supplier = 2

        <if test="dto.enterpriseName != null and dto.enterpriseName != ''">
            AND e.enterprise_name LIKE CONCAT('%', #{dto.enterpriseName}, '%')
        </if>
        <if test="dto.contractNo != null and dto.contractNo != ''">
            AND c.contract_no LIKE CONCAT('%', #{dto.contractNo}, '%')
        </if>
        <if test="dto.keywords != null and dto.keywords != ''">
            and (e.enterprise_name LIKE CONCAT('%',#{dto.keywords},'%')
            or c.contract_no LIKE CONCAT('%',#{dto.keywords},'%')
            or s.shop_name LIKE CONCAT('%',#{dto.keywords},'%'))
        </if>
        <if test="dto.orderBy == 1">
            ORDER BY e.sort, e.gmt_create DESC
        </if>
        <if test="dto.orderBy == 2">
            ORDER BY e.gmt_create DESC
        </if>
        <if test="dto.orderBy == 3">
            ORDER BY e.gmt_modified DESC
        </if>
    </select>
    <select id="ledgerList" resultType="scrbg.meplat.mall.vo.platform.enterprise.EnterpriseLedgerVo">
        SELECT
        e.enterprise_id AS enterpriseId,
        e.enterprise_name AS enterpriseName,
        s.shop_id AS shopId,
        s.shop_name AS shopName,
        c.contract_no AS agreementNo,
        py.audit_open_time AS annuaFeeCycle,
        p.serve_end_time AS serveEndTime,
        pd.totalDealAmount AS totalDealAmount,
        pd.totalDealFee AS totalDealFee
        FROM enterprise_info e
        LEFT JOIN shop s ON e.enterprise_id COLLATE utf8mb4_unicode_ci = s.enterprise_id
        AND s.is_delete = 0

        LEFT JOIN (
        SELECT
        contract_no,
        party_b_org_id,
        ROW_NUMBER() OVER (PARTITION BY party_b_org_id ORDER BY gmt_create DESC) AS rn
        FROM contract
        WHERE is_delete = 0
        ) c ON e.enterprise_id COLLATE utf8mb4_unicode_ci = c.party_b_org_id AND c.rn = 1

        LEFT JOIN (
        SELECT
        enterprise_id,
        audit_open_time,
        ROW_NUMBER() OVER (PARTITION BY enterprise_id ORDER BY gmt_create DESC) AS rn
        FROM platform_year_fee_record
        WHERE is_delete = 0
        ) py ON e.enterprise_id COLLATE utf8mb4_unicode_ci = py.enterprise_id AND py.rn = 1

        LEFT JOIN (
        SELECT
        enterprise_id,
        serve_end_time,
        ROW_NUMBER() OVER (PARTITION BY enterprise_id ORDER BY gmt_create DESC) AS rn
        FROM platform_year_fee
        WHERE is_delete = 0
        ) p ON e.enterprise_id COLLATE utf8mb4_unicode_ci = p.enterprise_id AND p.rn = 1

        LEFT JOIN (
        SELECT
        SUM(deal_amount) AS totalDealAmount,
        SUM(pay_fee) AS totalDealFee,
        enterprise_id,
        project_enterprise_id
        FROM platform_deal_fee_dtl
        WHERE is_delete = 0
        GROUP BY enterprise_id, project_enterprise_id
        ) pd ON e.enterprise_id COLLATE utf8mb4_unicode_ci = pd.enterprise_id
        OR e.enterprise_id COLLATE utf8mb4_unicode_ci = pd.project_enterprise_id

        WHERE 1=1
        AND e.is_delete = 0
        AND e.is_supplier = 2
        <if test="dto.enterpriseName != null and dto.enterpriseName != ''">
            AND e.enterprise_name LIKE CONCAT('%', #{dto.enterpriseName}, '%')
        </if>
        <if test="dto.contractNo != null and dto.contractNo != ''">
            AND c.contract_no LIKE CONCAT('%', #{dto.contractNo}, '%')
        </if>
        <if test="dto.keywords != null and dto.keywords != ''">
            and (e.enterprise_name LIKE CONCAT('%',#{dto.keywords},'%')
                 or c.contract_no LIKE CONCAT('%',#{dto.keywords},'%')
                or s.shop_name LIKE CONCAT('%',#{dto.keywords},'%'))
        </if>
        <if test="dto.orderBy == 1">
            order by e.sort, e.gmt_create DESC
        </if>
        <if test="dto.orderBy == 2">
            order by e.gmt_create DESC
        </if>
        <if test="dto.orderBy == 3">
            order by e.gmt_modified DESC
        </if>
    </select>
</mapper>