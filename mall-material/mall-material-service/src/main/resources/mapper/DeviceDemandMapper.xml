<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.DeviceDemandMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.DeviceDemand" id="DeviceDemandMap">
        <result property="demandId" column="demand_id"/>
        <result property="name" column="name"/>
        <result property="spec" column="spec"/>
        <result property="keyword" column="keyword"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="classId" column="class_id"/>
        <result property="className" column="class_name"/>
        <result property="brandId" column="brand_id"/>
        <result property="brandName" column="brand_name"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="county" column="county"/>
        <result property="address" column="address"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="num" column="num"/>
        <result property="demandType" column="demand_type"/>
        <result property="numUnit" column="num_unit"/>
        <result property="duration" column="duration"/>
        <result property="enterDate" column="enter_date"/>
        <result property="linkman" column="linkman"/>
        <result property="linkmanPhone" column="linkman_phone"/>
        <result property="releaseDate" column="release_date"/>
        <result property="stopDate" column="stop_date"/>
        <result property="budgetAmount" column="budget_amount"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="checkState" column="check_state"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="releaseType" column="release_type"/>
    </resultMap>

    <select id="listHomeDemandPage" resultType="scrbg.meplat.mall.vo.product.website.demand.DeviceDemandHomeVO"
            parameterType="Map">
        select
        dd.demand_id,
        dd.`name`,
        dd.spec,
        dd.keyword,
        dd.relevance_id,
        dd.class_id,
        dd.class_name,
        dd.brand_id,
        dd.brand_name,
        dd.province,
        dd.city,
        dd.county,
        dd.address,
        dd.longitude,
        dd.latitude,
        dd.num,
        dd.demand_type,
        dd.num_unit,
        dd.duration,
        dd.enter_date,
        dd.release_date,
        dd.stop_date,
        dd.budget_amount,
        dd.sort,
        dd.state,
        dd.release_type,
        dd.launch,
        dd.start_address,
        dd.end_address,
        u.nick_name  releaseName
        from device_demand dd
        inner join user u on dd.relevance_id = u.user_id and dd.is_delete = 0 and u.is_delete = 0
         and dd.release_type = #{dto.releaseType}
        <where>
            dd.`state` != 0 and dd.`check_state` = 1 and dd.`demand_type` = #{dto.demandType}
             and dd.`release_type` = #{dto.releaseType}
            <if test="dto.classId != null and dto.classId != ''">
                and dd.`class_id` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (dd.`name` LIKE CONCAT('%',#{dto.keywords},'%') or dd.`launch` LIKE
                CONCAT('%',#{dto.keywords},'%') or dd.`spec` LIKE CONCAT('%',#{dto.keywords},'%') or dd.`keyword` LIKE
                CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.brandId != null and dto.brandId != ''">
                and dd.`brand_id` = #{dto.brandId}
            </if>
            <if test="dto.city != null and dto.city != ''">
                and dd.`city` = #{dto.city}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and dd.`budget_amount` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and dd.`budget_amount` &gt;= #{dto.abovePrice}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by dd.`sort` desc , dd.`release_date` desc
        </if>
        <if test="dto.orderBy == 10">
            order by dd.`budget_amount` asc
        </if>
        <if test="dto.orderBy == 11">
            order by dd.`budget_amount` desc
        </if>
        <if test="dto.orderBy == 20">
            order by dd.`release_date` asc
        </if>
        <if test="dto.orderBy == 21">
            order by dd.`release_date` desc
        </if>
    </select>

    <select id="listHomeDemandPageCount" resultType="int"
            parameterType="Map">
        select
        count(*)
        from device_demand dd
        inner join user u on dd.relevance_id = u.user_id and dd.is_delete = 0 and u.is_delete = 0
        and dd.release_type = #{dto.releaseType}
        <where>
            dd.`state` != 0 and dd.`check_state` = 1 and dd.`demand_type` = #{dto.demandType}
            and dd.`release_type` = #{dto.releaseType}
            <if test="dto.classId != null and dto.classId != ''">
                and dd.`class_id` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (dd.`name` LIKE CONCAT('%',#{dto.keywords},'%') or dd.`launch` LIKE
                CONCAT('%',#{dto.keywords},'%') or dd.`spec` LIKE CONCAT('%',#{dto.keywords},'%') or dd.`keyword` LIKE
                CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.brandId != null and dto.brandId != ''">
                and dd.`brand_id` = #{dto.brandId}
            </if>
            <if test="dto.city != null and dto.city != ''">
                and dd.`city` = #{dto.city}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and dd.`budget_amount` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and dd.`budget_amount` &gt;= #{dto.abovePrice}
            </if>
        </where>
    </select>
    <select id="attendList" resultType="scrbg.meplat.mall.vo.product.website.demand.DeviceDemandHomeVO">

        select
            dd.demand_id,
            dd.`name`,
            dd.spec,
            dd.keyword,
            dd.relevance_id,
            dd.class_id,
            dd.class_name,
            dd.brand_id,
            dd.brand_name,
            dd.province,
            dd.city,
            dd.county,
            dd.address,
            dd.longitude,
            dd.latitude,
            dd.num,
            dd.demand_type,
            dd.num_unit,
            dd.duration,
            dd.enter_date,
            dd.release_date,
            dd.stop_date,
            dd.budget_amount,
            dd.sort,
            dd.state,
            dd.release_type,
            dd.launch
        from device_demand dd
        where   dd.mall_type=1 and  dd.demand_id in  (select relevance_id   from ask_answer
                                                      where founder_id= #{dto.userId} and is_delete=0
                                                      UNION ALL
                                                      select relevance_id from negotiated_price
                                                      where founder_id= #{dto.userId} and is_delete=0	 )

        <if test="dto.mallType != null and dto.mallType != ''">
            and dd.`mall_type` =#{dto.mallType}
        </if>
         order by dd.`release_date` desc
        limit #{dto.start},#{dto.limit}

    </select>
    <select id="attendLists" resultType="scrbg.meplat.mall.entity.DeviceDemand">
        select
        dd.demand_id,
        dd.`name`,
        dd.spec,
        dd.keyword,
        dd.relevance_id,
        dd.class_id,
        dd.class_name,
        dd.brand_id,
        dd.brand_name,
        dd.province,
        dd.city,
        dd.county,
        dd.address,
        dd.longitude,
        dd.latitude,
        dd.num,
        dd.demand_type,
        dd.num_unit,
        dd.duration,
        dd.enter_date,
        dd.release_date,
        dd.stop_date,
        dd.budget_amount,
        dd.sort,
        dd.state,
        dd.release_type,
        dd.launch
        from device_demand dd
        where   dd.mall_type=1 and  dd.demand_id in  (select relevance_id   from ask_answer
        where founder_id= #{dto.userId} and is_delete=0
        UNION ALL
        select relevance_id from negotiated_price
        where founder_id= #{dto.userId} and is_delete=0	 )

        <if test="dto.mallType != null and dto.mallType != ''">
            and dd.`mall_type` =#{dto.mallType}
        </if>
        order by dd.`release_date` desc
    </select>
</mapper>
