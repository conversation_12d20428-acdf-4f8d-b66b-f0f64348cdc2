<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.MaterialMasterPlanDtlMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.MaterialMasterPlanDtl" id="MaterialMasterPlanDtlMap">
        <result property="dtlId" column="dtl_id"/>
        <result property="billId" column="bill_id"/>
        <result property="projectPlanDtlId" column="project_plan_dtl_id"/>
        <result property="materialId" column="material_id"/>
        <result property="materialName" column="material_name"/>
        <result property="materialClassId" column="material_class_id"/>
        <result property="materialClassName" column="material_class_name"/>
        <result property="spec" column="spec"/>
        <result property="texture" column="texture"/>
        <result property="unit" column="unit"/>
        <result property="planQuantity" column="plan_quantity"/>
        <result property="designQuantity" column="design_quantity"/>
        <result property="budgetPrice" column="budget_price"/>
        <result property="budgetAmount" column="budget_amount"/>
        <result property="marketPrice" column="market_price"/>
        <result property="marketAmount" column="market_amount"/>
        <result property="purchasingUnitId" column="purchasing_unit_id"/>
        <result property="purchasingUnitName" column="purchasing_unit_name"/>
        <result property="purchaseType" column="purchase_type"/>
        <result property="receivedQuantity" column="received_quantity"/>
        <result property="summarizedQuantity" column="summarized_quantity"/>
        <result property="usedQuantityTender" column="used_quantity_tender"/>
        <result property="usedQuantityContract" column="used_quantity_contract"/>
        <result property="isSummarized" column="is_summarized"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
        <result property="productType" column="product_type"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>