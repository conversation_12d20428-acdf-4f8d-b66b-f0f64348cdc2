<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.CategoryBrandRelationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.CategoryBrandRelation" id="CategoryBrandRelationMap">
        <result property="categoryBrandRelationId" column="category_brand_relation_id"/>
        <result property="brandId" column="brand_id"/>
        <result property="classId" column="class_id"/>
        <result property="brandName" column="brand_name"/>
        <result property="className" column="class_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="productType" column="product_type"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>