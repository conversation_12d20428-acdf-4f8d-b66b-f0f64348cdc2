<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ProcessInstanceMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ProcessInstance" id="ProcessInstanceMap">
        <result property="processInstanceId" column="process_instance_id" />
        <result property="processId" column="process_id"/>
        <result property="currentNodeId" column="current_node_id"/>
        <result property="businessKey" column="business_key"/>
        <result property="status" column="status"/>
        <result property="operaterId" column="operater_id"/>
        <result property="operaterName" column="operater_name"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="modifyId" column="modify_id"/>
        <result property="modifyName" column="modify_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>
    
    <!-- 根据流程配置id获取流程配置详情 -->
    <select id="getProcessInstanceByKey" resultMap="ProcessInstanceMap">
    SELECT pi.*
	FROM process_instance pi
	<where>
		pi.is_delete = 0
        <if test="businessKey != null">
            and pi.business_key = #{businessKey}
        </if>
     </where>
    </select>

</mapper>