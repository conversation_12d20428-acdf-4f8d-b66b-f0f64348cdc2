<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.StationMessageReceiveMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.StationMessageReceive" id="StationMessageReceiveMap">
        <result property="stationMessageReceiveId" column="station_message_receive_id"/>
        <result property="receiveId" column="receive_id"/>
        <result property="receiveName" column="receive_name"/>
        <result property="receiveType" column="receive_type"/>
        <result property="receiveCode" column="receive_code"/>
        <result property="isRead" column="is_read"/>
        <result property="readDate" column="read_date"/>
        <result property="sort" column="sort"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="stationMessageId" column="station_message_id"/>
    </resultMap>
    <select id="findByCondition" resultType="scrbg.meplat.mall.vo.product.material.StationMessageReceiveVo">
        SELECT r.station_message_receive_id,
               r.receive_name,
               r.receive_type,
               r.receive_code,
               r.is_read,
               r.read_date,
               r.sort,
               s.send_date,
               s.title as title,
               r.station_message_id
        FROM `station_message_receive` r
                 LEFT JOIN station_message s
                           on r.station_message_id=s.station_message_id
where
            ${ew.sqlSegment}
    </select>
    <select id="getRemindMessage" resultType="scrbg.meplat.mall.vo.message.RemindMessageVo">
        SELECT
        DISTINCT
        m.station_message_id,
        c.receive_name,
        c.receive_id,
        m.content,
        m.title,
        m.remind,
        m.send_date,
        m.send_name,
        c.is_read,
        c.station_message_receive_id

        FROM
        station_message_receive c
        RIGHT JOIN station_message m ON c.station_message_id = m.station_message_id
        and m.is_delete = 0 and c.is_delete = 0 and m.remind=1 and c.is_read = 0
        <where>
            <if test="userId!=''">
                c.receive_id = #{userId}
            </if>
            <if test="shopId!=''">
                or  c.receive_id = #{shopId}
            </if>
        </where>
        ORDER BY m.gmt_create desc


    </select>
    <select id="getRemindMessageCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        station_message_receive c
        RIGHT JOIN station_message m ON c.station_message_id = m.station_message_id
        and m.is_delete = 0 and c.is_delete = 0 and m.remind=1 and c.is_read = 0
        <where>
            <if test="userId!=''">
                c.receive_id = #{userId}
            </if>
            <if test="shopId!=''">
                or  c.receive_id = #{shopId}
            </if>
        </where>
        ORDER BY m.gmt_create desc
    </select>


</mapper>
