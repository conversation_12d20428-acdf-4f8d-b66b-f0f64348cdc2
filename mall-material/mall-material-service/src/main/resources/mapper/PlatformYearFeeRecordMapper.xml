<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PlatformYearFeeRecordMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.PlatformYearFeeRecord" id="PlatformYearFeeRecordMap">
        <result property="paymentRecordId" column="payment_record_id"/>
        <result property="paymentRecordUn" column="payment_record_un"/>
        <result property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="payType" column="pay_type"/>
        <result property="paymentDuration" column="payment_duration"/>
        <result property="paymentDurationType" column="payment_duration_type"/>
        <result property="auditOpenTime" column="audit_open_time"/>
        <result property="recordType" column="record_type"/>
        <result property="state" column="state"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="version" column="version"/>
    </resultMap>

    <select id="myQueryPayFreeListByEntity" resultType="scrbg.meplat.mall.dto.free.MyQueryPayFreeListByEntity">
        SELECT * FROM (
        SELECT payment_record_id as paymentRecordId,
        payment_record_un as paymentRecordUn,
        shop_id as shopId,
        shop_name as shopName,
        enterprise_id as enterpriseId,
        enterprise_name as enterpriseName,
        pay_amount as payAmount,
        pay_type as payType,
        payment_duration as paymentDuration,
        payment_duration_type as paymentDurationType,
        audit_open_time as auditOpenTime,
        record_type as recordType,
        state as state,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        FROM `platform_year_fee_record` where is_delete = 0
        UNION ALL
        SELECT deal_fee_record_id as paymentRecordId,
        deal_fee_record_un as paymentRecordUn,
        shop_id as shopId,
        shop_name as shopName,
        enterprise_id as enterpriseId,
        enterprise_name as enterpriseName,
        pay_amount as payAmount,
        pay_type as payType,
        NULL as paymentDuration,
        NULL as paymentDurationType,
        audit_open_time as auditOpenTime,
        record_type as recordType,
        state as state,
        gmt_create as gmtCreate,
        gmt_modified as gmtModified
        FROM `platform_deal_fee_record` where is_delete = 0
        ) AS combined_result
        <where>
            <if test="dto.states != null">
                and `state` in
                <foreach item="item" collection="dto.states" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.enterpriseId != null and dto.enterpriseId != ''">
                and enterpriseId = #{dto.enterpriseId}
            </if>
            <if test="dto.recordType != null">
                <if test="dto.recordType == 1 or dto.recordType == 2">
                    and recordType = #{dto.recordType} and paymentDurationType is not null
                </if>
                <if test="dto.recordType == 3 or dto.recordType == 4">
                    and paymentDurationType is null
                    <if test="dto.recordType == 3">
                        and recordType = 1
                    </if>
                    <if test="dto.recordType == 4">
                        and recordType = 2
                    </if>
                </if>
            </if>
            <if test="dto.state != null and dto.state != ''">
                and `state` = #{dto.state}
            </if>
            <if test="dto.neState != null">
                <![CDATA[
                and `state` != 0
                 ]]>
            </if>
            <if test="dto.shopName != null and  dto.shopName != ''">
                and shopName = #{dto.shopName}
            </if>
            <if test="dto.paymentRecordUn != null and  dto.paymentRecordUn != ''">
                and paymentRecordUn = #{dto.paymentRecordUn}
            </if>
            <if test="dto.enterpriseName != null and  dto.enterpriseName != ''">
                and enterpriseName = #{dto.enterpriseName}
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                shopName LIKE CONCAT('%',#{dto.keywords},'%')
                or paymentRecordUn LIKE CONCAT('%',#{dto.keywords},'%')
                or enterpriseName LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startGmtCreate != null and dto.startGmtCreate != ''">
                <![CDATA[
                AND gmtCreate >= #{dto.startGmtCreate}
                 ]]>
            </if>
            <if test="dto.endGmtCreate != null and dto.endGmtCreate != ''">
                <![CDATA[
                AND gmtCreate <= #{dto.endGmtCreate}
                ]]>
            </if>
            <if test="dto.startGmtModified != null and dto.startGmtModified != ''">
                <![CDATA[
                AND gmtModified >= #{dto.startGmtModified}
                 ]]>
            </if>
            <if test="dto.endGmtModified != null and dto.endGmtModified != ''">
                <![CDATA[
                AND gmtModified <= #{dto.endGmtModified}
                ]]>
            </if>
            <if test="dto.startAuditTime != null and dto.startAuditTime != ''">
                <![CDATA[
                AND auditOpenTime >= #{dto.startAuditTime}
                 ]]>
            </if>
            <if test="dto.endAuditTime != null and dto.endAuditTime != ''">
                <![CDATA[
                AND auditOpenTime <= #{dto.endAuditTime}
                ]]>
            </if>
        </where>
        <if test="dto.orderBy !=null">
            <if test="dto.orderBy == 0">
                order by gmtCreate DESC
            </if>
            <if test="dto.orderBy == 1">
                order by gmtModified DESC
            </if>
            <if test="dto.orderBy == 2">
                order by auditOpenTime DESC
            </if>
        </if>
    </select>

    <!-- 缴费管理查询接口 - 查询所有企业的交易服务费和年费 -->
    <select id="getFeeManagementList" resultType="scrbg.meplat.mall.dto.fee.FeeManagementResultVO">
        SELECT * FROM (
            SELECT
                payment_record_un as paymentRecordUn,
                enterprise_name as enterpriseName,
                1 as serviceType,
                '店铺年度服务费' as serviceTypeName,
                shop_name as shopName,
                pay_type as payType,
                CASE pay_type
                    WHEN 1 THEN '线下'
                    WHEN 2 THEN '线上'
                    WHEN 3 THEN '其他'
                    ELSE '未知'
                END as payTypeName,
                payment_duration as paymentDuration,
                payment_duration_type as paymentDurationType,
                CASE payment_duration_type
                    WHEN 1 THEN '天'
                    WHEN 2 THEN '周'
                    WHEN 3 THEN '月'
                    WHEN 4 THEN '年'
                    ELSE '未知'
                END as paymentDurationTypeName,
                pay_amount as payAmount,
                state as state,
                CASE state
                    WHEN 0 THEN '草稿'
                    WHEN 1 THEN '待审核'
                    WHEN 2 THEN '审核通过'
                    WHEN 3 THEN '审核未通过'
                    ELSE '未知'
                END as stateName,
                audit_open_time as auditOpenTime,
                gmt_create as gmtCreate,
                gmt_modified as gmtModified
            FROM platform_year_fee_record
            WHERE is_delete = 0

            UNION ALL

            SELECT
                deal_fee_record_un as paymentRecordUn,
                enterprise_name as enterpriseName,
                2 as serviceType,
                '店铺交易服务费' as serviceTypeName,
                shop_name as shopName,
                pay_type as payType,
                CASE pay_type
                    WHEN 1 THEN '线下'
                    WHEN 2 THEN '线上'
                    WHEN 3 THEN '其他'
                    ELSE '未知'
                END as payTypeName,
                NULL as paymentDuration,
                NULL as paymentDurationType,
                NULL as paymentDurationTypeName,
                pay_amount as payAmount,
                state as state,
                CASE state
                    WHEN 0 THEN '待确认'
                    WHEN 1 THEN '确认中'
                    WHEN 2 THEN '确认成功'
                    WHEN 3 THEN '确认失败'
                    WHEN 4 THEN '审核中'
                    WHEN 5 THEN '审核通过'
                    WHEN 6 THEN '审核未通过'
                    ELSE '未知'
                END as stateName,
                audit_open_time as auditOpenTime,
                gmt_create as gmtCreate,
                gmt_modified as gmtModified
            FROM platform_deal_fee_record
            WHERE is_delete = 0
        ) AS combined_result
        <where>
            <if test="dto.paymentRecordUn != null and dto.paymentRecordUn != ''">
                AND paymentRecordUn LIKE CONCAT('%', #{dto.paymentRecordUn}, '%')
            </if>
            <if test="dto.enterpriseName != null and dto.enterpriseName != ''">
                AND enterpriseName LIKE CONCAT('%', #{dto.enterpriseName}, '%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                AND shopName LIKE CONCAT('%', #{dto.shopName}, '%')
            </if>
            <if test="dto.serviceType != null">
                AND serviceType = #{dto.serviceType}
            </if>
            <if test="dto.payTimeStart != null">
                AND gmtCreate >= #{dto.payTimeStart}
            </if>
            <if test="dto.payTimeEnd != null">
                AND gmtCreate &lt;= #{dto.payTimeEnd}
            </if>
            <if test="dto.modifyTimeStart != null">
                AND gmtModified >= #{dto.modifyTimeStart}
            </if>
            <if test="dto.modifyTimeEnd != null">
                AND gmtModified &lt;= #{dto.modifyTimeEnd}
            </if>
            <if test="dto.auditTimeStart != null">
                AND auditOpenTime >= #{dto.auditTimeStart}
            </if>
            <if test="dto.auditTimeEnd != null">
                AND auditOpenTime &lt;= #{dto.auditTimeEnd}
            </if>
            <if test="dto.states != null and dto.states.size() > 0">
                AND (
                    <!-- 年费记录状态映射：1审核通过->2，2审核不通过->3 -->
                    (serviceType = 1 AND (
                        <foreach item="state" collection="dto.states" separator=" OR ">
                            <choose>
                                <when test="state == 1">state = 2</when>
                                <when test="state == 2">state = 3</when>
                            </choose>
                        </foreach>
                    ))
                    OR
                    <!-- 交易费记录状态映射：1审核通过->5，2审核不通过->6 -->
                    (serviceType = 2 AND (
                        <foreach item="state" collection="dto.states" separator=" OR ">
                            <choose>
                                <when test="state == 1">state = 5</when>
                                <when test="state == 2">state = 6</when>
                            </choose>
                        </foreach>
                    ))
                )
            </if>
        </where>
        <choose>
            <when test="dto.orderBy != null">
                <choose>
                    <when test="dto.orderBy == 1">
                        ORDER BY gmtCreate
                        <choose>
                            <when test="dto.orderDirection != null and dto.orderDirection == 'ASC'">ASC</when>
                            <otherwise>DESC</otherwise>
                        </choose>
                    </when>
                    <when test="dto.orderBy == 2">
                        ORDER BY gmtModified
                        <choose>
                            <when test="dto.orderDirection != null and dto.orderDirection == 'ASC'">ASC</when>
                            <otherwise>DESC</otherwise>
                        </choose>
                    </when>
                    <when test="dto.orderBy == 3">
                        ORDER BY auditOpenTime
                        <choose>
                            <when test="dto.orderDirection != null and dto.orderDirection == 'ASC'">ASC</when>
                            <otherwise>DESC</otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        ORDER BY gmtCreate DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY gmtCreate DESC
            </otherwise>
        </choose>
    </select>


</mapper>