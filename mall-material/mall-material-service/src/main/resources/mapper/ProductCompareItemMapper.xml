<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ProductCompareItemMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ProductCompareItem" id="ProductCompareItemMap">
        <result property="compareItemId" column="compare_item_id"/>
        <result property="compareId" column="compare_id"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="skuId" column="sku_id"/>
        <result property="skuName" column="sku_name"/>
        <result property="shopId" column="shop_id"/>
        <result property="skuImg" column="sku_img"/>
        <result property="sellPrice" column="sell_price"/>
        <result property="unit" column="unit"/>
        <result property="brandId" column="brand_id"/>
        <result property="brandName" column="brand_name"/>
        <result property="supperBy" column="supper_by"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>