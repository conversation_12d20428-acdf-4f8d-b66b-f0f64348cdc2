<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.FileRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.FileRecord" id="FileRecordMap">
        <result property="recordId" column="record_id"/>
        <result property="objectName" column="object_name"/>
        <result property="objectPath" column="object_path"/>
        <result property="nonIpObjectPath" column="non_ip_object_path"/>
        <result property="bucketName" column="bucket_name"/>
        <result property="objectSizeKb" column="object_size_kb"/>
        <result property="objectSizeMb" column="object_size_mb"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>