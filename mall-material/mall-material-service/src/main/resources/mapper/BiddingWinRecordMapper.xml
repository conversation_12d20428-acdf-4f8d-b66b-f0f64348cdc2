<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.BiddingWinRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.BiddingWinRecord" id="BiddingWinRecordMap">
        <result property="winRecordId" column="win_record_id"/>
        <result property="bidRecordId" column="bid_record_id"/>
        <result property="biddingId" column="bidding_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="bidAmount" column="bid_amount"/>
        <result property="bidRateAmount" column="bid_rate_amount"/>
        <result property="winTime" column="win_time"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="rejectReason" column="reject_reason"/>
    </resultMap>


</mapper>