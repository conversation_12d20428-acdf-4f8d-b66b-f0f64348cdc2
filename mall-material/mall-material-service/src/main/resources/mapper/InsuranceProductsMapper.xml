<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.InsuranceProductsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.InsuranceProducts" id="InsuranceProductsMap">
        <result property="insuranceId" column="insurance_id"/>
        <result property="describes" column="describes"/>
        <result property="type" column="type"/>
        <result property="details" column="details"/>
        <result property="company" column="company"/>
        <result property="shopId" column="shop_id"/>
        <result property="projectArea" column="project_area"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="name" column="name"/>
        <result property="insure" column="insure"/>
        <result property="years" column="years"/>
        <result property="age" column="age"/>
        <result property="occupation" column="occupation"/>
        <result property="notice" column="notice"/>
        <result property="commonProblem" column="common_problem"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="gmtRelease" column="gmt_release"/>
        <result property="isDelete" column="is_delete"/>
        <result property="remarks" column="remarks"/>
    </resultMap>


</mapper>