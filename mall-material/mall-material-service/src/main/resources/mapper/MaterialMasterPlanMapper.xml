<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.MaterialMasterPlanMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.MaterialMasterPlan" id="MaterialMasterPlanMap">
        <result property="billId" column="bill_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="workId" column="work_id"/>
        <result property="billNo" column="bill_no"/>
        <result property="planDate" column="plan_date"/>
        <result property="projectPlanId" column="project_plan_id"/>
        <result property="amount" column="amount"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="taxAmount" column="tax_amount"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="preparerId" column="preparer_id"/>
        <result property="preparer" column="preparer"/>
        <result property="currencyId" column="currency_id"/>
        <result property="currency" column="currency"/>
        <result property="purchaseType" column="purchase_type"/>
        <result property="orgId" column="org_id"/>
        <result property="orgName" column="org_name"/>
        <result property="remarks" column="remarks"/>
        <result property="baseCurId" column="base_cur_id"/>
        <result property="baseCurName" column="base_cur_name"/>
        <result property="baseCurRate" column="base_cur_rate"/>
        <result property="baseCurAmount" column="base_cur_amount"/>
        <result property="baseCurTaxAmount" column="base_cur_tax_amount"/>
        <result property="baseCurTotalAmount" column="base_cur_total_amount"/>
        <result property="rmbId" column="rmb_id"/>
        <result property="rmbName" column="rmb_name"/>
        <result property="rmbRate" column="rmb_rate"/>
        <result property="rmbAmount" column="rmb_amount"/>
        <result property="rmbTaxAmount" column="rmb_tax_amount"/>
        <result property="rmbTotalAmount" column="rmb_total_amount"/>
        <result property="orderId" column="order_id"/>
        <result property="isDelete" column="is_delete"/>
        <result property="productType" column="product_type"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>