<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.SysOrgMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.SysOrg" id="SysOrgMap">
        <result property="orgId" column="org_id"/>
        <result property="shortCode" column="short_code"/>
        <result property="orgName" column="org_name"/>
        <result property="orgType" column="org_type"/>
        <result property="pid" column="pid"/>
        <result property="pName" column="p_name"/>
        <result property="state" column="state"/>
        <result property="orgUserId" column="org_user_id"/>
        <result property="sort" column="sort"/>
        <result property="userName" column="user_name"/>
        <result property="userPhone" column="user_phone"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>