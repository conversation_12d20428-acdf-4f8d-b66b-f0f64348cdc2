<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.AdPictureMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.AdPicture" id="AdPictureMap">
        <result property="pictureId" column="picture_id"/>
        <result property="pictureUrlId" column="picture_url_id"/>
        <result property="pictureUrl" column="picture_url"/>
        <result property="pictureType" column="picture_type"/>
        <result property="pictureLinkAddress" column="picture_link_address"/>
        <result property="useType" column="use_type"/>
        <result property="sort" column="sort"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>