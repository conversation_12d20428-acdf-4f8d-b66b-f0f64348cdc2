<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ContractMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.Contract" id="ContractMap">
        <result property="contractId" column="contract_id"/>
        <result property="contractNo" column="contract_no"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="partyAOrgId" column="party_a_org_id"/>
        <result property="partyAOrgName" column="party_a_org_name"/>
        <result property="partyBOrgId" column="party_b_org_id"/>
        <result property="partyBOrgName" column="party_b_org_name"/>
        <result property="sort" column="sort"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="remarks" column="remarks"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="contractJson" column="contract_json"/>
        <result property="contractYear" column="contract_year"/>
        <result property="contractYearIndex" column="contract_year_index"/>
    </resultMap>
    <select id="getContractNoList" resultType="scrbg.meplat.mall.vo.payment.UnUsedContractDto">
        SELECT
            contract.*
        FROM
            contract
                left JOIN platform_year_fee_record item ON item.contract_no = contract.contract_no
                and item.is_delete = 0
                and contract.is_delete = 0
        WHERE
            item.contract_no IS NULL
          and contract.contract_year = #{year}
          and contract.type=#{cType}
        ORDER BY contract.contract_year_index asc
    </select>


</mapper>