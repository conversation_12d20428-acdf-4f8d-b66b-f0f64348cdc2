<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ShopSupplierReleMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ShopSupplierRele" id="ShopSupplierReleMap">
        <result property="shopSupplierReleId" column="shop_supplier_rele_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <select id="listShopListBySupplierId" resultType="scrbg.meplat.mall.entity.ShopSupplierRele">
        select s.shop_id,
        s.shop_Name
        from shop_supplier_rele ss
        inner join shop s on ss.shop_id = s.shop_id
        <where>
            ss.is_delete = 0 and ss.supplier_id = #{innerMap.enterpriseId}
            <if test="innerMap.shopName != null and innerMap.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{innerMap.shopName},'%')
            </if>
            <if test="innerMap.shopIds!= null and innerMap.shopIds.size() > 0">
                and s.`shop_id` not in
                <foreach item="shopId" collection="innerMap.shopIds" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
        </where>
        GROUP BY s.shop_name, s.shop_id
    </select>

    <select id="listShopListBySupplierIdCount" resultType="int">
        SELECT
        count(*)
        FROM
        (select s.shop_name
        from shop_supplier_rele ss
        inner join shop s on ss.shop_id = s.shop_id
        <where>
            ss.is_delete = 0 and ss.supplier_id = #{innerMap.enterpriseId}
            <if test="innerMap.shopName != null and innerMap.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{innerMap.shopName},'%')
            </if>
        </where>
        GROUP BY s.shop_name) total
    </select>

    <select id="listShopList" resultType="scrbg.meplat.mall.entity.ShopSupplierRele">
        select s.shop_id,
        s.shop_Name
        from shop s
        <where>
            s.is_supplier = 1 and s.is_delete = 0
            <if test="innerMap.shopName != null and innerMap.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{innerMap.shopName},'%')
            </if>
        </where>
        GROUP BY s.shop_name, s.shop_id
    </select>

    <select id="listShopListCount" resultType="int">
        SELECT
        count(*)
        FROM
        (select s.shop_name
        from shop s
        <where>
            s.is_supplier = 1 and s.is_delete = 0
            <if test="innerMap.shopName != null and innerMap.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{innerMap.shopName},'%')
            </if>
        </where>
        GROUP BY s.shop_name) total
    </select>

    <!-- 根据订单条件筛选供应商分页查询 - 使用IN子查询优化 -->
    <select id="querySuppliersByOrderConditions" resultMap="ShopSupplierReleMap">
        SELECT DISTINCT
            ssr.shop_supplier_rele_id,
            ssr.shop_id,
            ssr.supplier_id,
            ssr.supplier_name,
            ssr.list_permissions,
            ssr.permissions_commodities,
            ssr.permissions_low_value,
            ssr.sort,
            ssr.mall_type,
            ssr.gmt_create,
            ssr.gmt_modified,
            ssr.founder_name,
            ssr.founder_id,
            ssr.modify_name,
            ssr.modify_id,
            ssr.remarks,
            ssr.is_delete
        FROM shop_supplier_rele ssr
        WHERE 
            <if test="innerMap.shopId != null and innerMap.shopId != ''">
                ssr.shop_id = #{innerMap.shopId} AND
            </if>
            ssr.is_delete = 0
            AND ssr.supplier_id IN (
                <!-- 基于订单反推供应商ID -->
                SELECT DISTINCT supplier_id
                FROM (
                    <!-- 发货单对应的供应商 -->
                    SELECT o.supplier_id
                    FROM order_ship_dtl osd
                    INNER JOIN order_ship os ON osd.bill_id = os.bill_Id
                    INNER JOIN orders o ON osd.order_id = o.parent_order_id
                    WHERE 
                        <if test="innerMap.startDate != null and innerMap.startDate != ''">
                            os.confirm_time >= #{innerMap.startDate} AND
                        </if>
                        <if test="innerMap.endDate != null and innerMap.endDate != ''">
                            os.confirm_time &lt; #{innerMap.endDate} AND
                        </if>
                        osd.is_reconciliation = 0
                        AND osd.is_delete = 0
                        AND os.is_delete = 0
                        AND o.is_delete = 0
                        AND os.confirm_time IS NOT NULL
                        <if test="innerMap.shopId != null and innerMap.shopId != ''">
                            AND o.shop_id = #{innerMap.shopId}
                        </if>
                        <!-- 产品类型筛选 -->
                        <if test="innerMap.productTypeList != null and innerMap.productTypeList.size() > 0">
                            <choose>
                                <when test="innerMap.productTypeList.size() > 1">
                                    AND o.product_type IN
                                    <foreach item="type" collection="innerMap.productTypeList" open="(" separator="," close=")">
                                        #{type}
                                    </foreach>
                                </when>
                                <otherwise>
                                    AND o.product_type = #{innerMap.productTypeList[0]}
                                </otherwise>
                            </choose>
                        </if>
                        <!-- 价格类型筛选 -->
                        <if test="innerMap.billType != null">
                            AND o.bill_type = #{innerMap.billType}
                        </if>

                    UNION

                    <!-- 退货单对应的供应商 -->
                    SELECT ret.supplier_id
                    FROM order_return_item ori
                    INNER JOIN order_return ret ON ori.order_return_id = ret.order_return_id
                    WHERE 
                        <if test="innerMap.startDate != null and innerMap.startDate != ''">
                            ret.gmt_create >= #{innerMap.startDate} AND
                        </if>
                        <if test="innerMap.endDate != null and innerMap.endDate != ''">
                            ret.gmt_create &lt; #{innerMap.endDate} AND
                        </if>
                        ori.is_reconciliation = 0
                        AND ret.state = 3  <!-- 退款成功 -->
                        AND ori.is_delete = 0
                        AND ret.is_delete = 0
                        <if test="innerMap.shopId != null and innerMap.shopId != ''">
                            AND ret.shop_id = #{innerMap.shopId}
                        </if>
                        <!-- 产品类型筛选 - 对应业务类型 -->
                        <if test="innerMap.productTypeList != null and innerMap.productTypeList.size() > 0">
                            <choose>
                                <when test="innerMap.productTypeList.contains('2') and innerMap.productTypeList.contains('13')">
                                    AND ret.business_type IN (2, 6)
                                </when>
                                <when test="innerMap.productTypeList.contains('2')">
                                    AND ret.business_type = 2
                                </when>
                                <when test="innerMap.productTypeList.contains('13')">
                                    AND ret.business_type = 6
                                </when>
                                <otherwise>
                                    AND 1=0 <!-- 如果没有匹配的产品类型，则不返回退货记录 -->
                                </otherwise>
                            </choose>
                        </if>
                        <!-- 价格类型筛选 -->
                        <if test="innerMap.billType != null">
                            AND ret.bill_type = #{innerMap.billType}
                        </if>
                ) supplier_orders
            )
            <!-- 供应商名称关键字筛选 -->
            <if test="innerMap.keywords != null and innerMap.keywords != ''">
                AND ssr.supplier_name LIKE CONCAT('%', #{innerMap.keywords}, '%')
            </if>
        ORDER BY ssr.supplier_name
    </select>

    <!-- 根据订单条件筛选供应商总数查询 - 使用UNION优化 -->
    <select id="querySuppliersByOrderConditionsCount" resultType="int">
        SELECT COUNT(DISTINCT supplier_id) 
        FROM (
            <!-- 查询有未对账发货单的供应商 -->
            SELECT ssr.supplier_id
            FROM shop_supplier_rele ssr
            WHERE 
                <if test="innerMap.shopId != null and innerMap.shopId != ''">
                    ssr.shop_id = #{innerMap.shopId} AND
                </if>
                ssr.is_delete = 0
                AND EXISTS (
                    SELECT 1
                    FROM order_ship_dtl osd
                    INNER JOIN order_ship os ON osd.bill_id = os.bill_Id
                    INNER JOIN orders o ON osd.order_id = o.parent_order_id
                    WHERE o.supplier_id = ssr.supplier_id
                        <!-- 收料员收料时间范围 -->
                        <if test="innerMap.startDate != null and innerMap.startDate != ''">
                            AND os.confirm_time >= #{innerMap.startDate}
                        </if>
                        <if test="innerMap.endDate != null and innerMap.endDate != ''">
                            AND os.confirm_time &lt; #{innerMap.endDate}
                        </if>
                        AND osd.is_reconciliation = 0
                        AND osd.is_delete = 0
                        AND os.is_delete = 0
                        AND o.is_delete = 0
                        AND os.confirm_time IS NOT NULL
                        <if test="innerMap.shopId != null and innerMap.shopId != ''">
                            AND o.shop_id = #{innerMap.shopId}
                        </if>
                        <!-- 产品类型筛选 -->
                        <if test="innerMap.productTypeList != null and innerMap.productTypeList.size() > 0">
                            <choose>
                                <when test="innerMap.productTypeList.size() > 1">
                                    AND o.product_type IN
                                    <foreach item="type" collection="innerMap.productTypeList" open="(" separator="," close=")">
                                        #{type}
                                    </foreach>
                                </when>
                                <otherwise>
                                    AND o.product_type = #{innerMap.productTypeList[0]}
                                </otherwise>
                            </choose>
                        </if>
                        <!-- 价格类型筛选 -->
                        <if test="innerMap.billType != null">
                            AND o.bill_type = #{innerMap.billType}
                        </if>
                )
                <if test="innerMap.keywords != null and innerMap.keywords != ''">
                    AND ssr.supplier_name LIKE CONCAT('%', #{innerMap.keywords}, '%')
                </if>
                
            UNION
            
            <!-- 查询有未对账退货单的供应商 -->
            SELECT ssr.supplier_id
            FROM shop_supplier_rele ssr
            WHERE 
                <if test="innerMap.shopId != null and innerMap.shopId != ''">
                    ssr.shop_id = #{innerMap.shopId} AND
                </if>
                ssr.is_delete = 0
                AND EXISTS (
                    SELECT 1
                    FROM order_return_item ori
                    INNER JOIN order_return ret ON ori.order_return_id = ret.order_return_id
                    WHERE ret.supplier_id = ssr.supplier_id
                        <!-- 退货时间范围 -->
                        <if test="innerMap.startDate != null and innerMap.startDate != ''">
                            AND ret.gmt_create >= #{innerMap.startDate}
                        </if>
                        <if test="innerMap.endDate != null and innerMap.endDate != ''">
                            AND ret.gmt_create &lt; #{innerMap.endDate}
                        </if>
                        AND ori.is_reconciliation = 0
                        AND ret.state = 3  <!-- 退款成功 -->
                        AND ori.is_delete = 0
                        AND ret.is_delete = 0
                        <if test="innerMap.shopId != null and innerMap.shopId != ''">
                            AND ret.shop_id = #{innerMap.shopId}
                        </if>
                        <!-- 产品类型筛选 - 对应业务类型 -->
                        <if test="innerMap.productTypeList != null and innerMap.productTypeList.size() > 0">
                            <choose>
                                <when test="innerMap.productTypeList.contains('2') and innerMap.productTypeList.contains('13')">
                                    AND ret.business_type IN (2, 6)
                                </when>
                                <when test="innerMap.productTypeList.contains('2')">
                                    AND ret.business_type = 2
                                </when>
                                <when test="innerMap.productTypeList.contains('13')">
                                    AND ret.business_type = 6
                                </when>
                                <otherwise>
                                    AND 1=0 <!-- 如果没有匹配的产品类型，则不返回退货记录 -->
                                </otherwise>
                            </choose>
                        </if>
                        <!-- 价格类型筛选 -->
                        <if test="innerMap.billType != null">
                            AND ret.bill_type = #{innerMap.billType}
                        </if>
                )
                <if test="innerMap.keywords != null and innerMap.keywords != ''">
                    AND ssr.supplier_name LIKE CONCAT('%', #{innerMap.keywords}, '%')
                </if>
        ) AS combined_suppliers
    </select>
</mapper>
