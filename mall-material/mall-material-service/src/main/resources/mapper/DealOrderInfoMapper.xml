<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.DealOrderInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.DealOrderInfo" id="DealOrderInfoMap">
        <result property="dealOrderInfoId" column="deal_order_info_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="buyOrgId" column="buy_org_id"/>
        <result property="bugOrgName" column="bug_org_name"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="orderSn" column="order_sn"/>
        <result property="orderId" column="order_id"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="number" column="number"/>
        <result property="amount" column="amount"/>
        <result property="costAmount" column="cost_amount"/>
        <result property="finishDate" column="finish_date"/>
        <result property="skuName" column="sku_name"/>
        <result property="unit" column="unit"/>
        <result property="dealType" column="deal_type"/>
        <result property="orderFinishDate" column="order_finish_date"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <select id="platformList" resultType="scrbg.meplat.mall.entity.DealOrderInfo">
        SELECT t.*, mr.reconciliation_product_type AS productType
        FROM deal_order_info t
        LEFT JOIN material_reconciliation mr ON mr.reconciliation_id = t.reconciliation_id
        WHERE t.is_delete = 0

        <!-- 按供应商名称搜索 -->
        <if test="params.supplierName != null and params.supplierName != ''">
            AND t.supplier_name LIKE CONCAT('%', #{params.supplierName}, '%')
        </if>

        <!-- 按客户名称搜索 -->
        <if test="params.bugOrgName != null and params.bugOrgName != ''">
            AND t.bug_org_name LIKE CONCAT('%', #{params.bugOrgName}, '%')
        </if>

        <!-- 按店铺名称搜索 -->
        <if test="params.shopName != null and params.shopName != ''">
            AND t.shop_name LIKE CONCAT('%', #{params.shopName}, '%')
        </if>

        <!-- 按商品名称搜索 -->
        <if test="params.productName != null and params.productName != ''">
            AND t.product_name LIKE CONCAT('%', #{params.productName}, '%')
        </if>

        <!-- 按关键词模糊搜索 -->
        <if test="params.keywords != null and params.keywords != ''">
            AND (
            t.product_name LIKE CONCAT('%', #{params.keywords}, '%')
            OR t.supplier_name LIKE CONCAT('%', #{params.keywords}, '%')
            OR t.shop_name LIKE CONCAT('%', #{params.keywords}, '%')
            OR t.bug_org_name LIKE CONCAT('%', #{params.keywords}, '%')
            )
        </if>

        <!-- 金额区间 -->
        <if test="params.abovePrice != null and params.abovePrice != ''">
            AND t.amount >= #{params.abovePrice}
        </if>
        <if test="params.belowPrice != null and params.belowPrice != ''">
            AND t.amount &lt;= #{params.belowPrice}
        </if>

        <!-- 时间范围 -->
        <if test="params.startFinishDate != null and params.endFinishDate != null and params.startFinishDate != '' and params.endFinishDate != ''">
            AND t.gmt_create BETWEEN #{params.startFinishDate} AND #{params.endFinishDate}
        </if>

        <!-- productType 条件 -->
        <if test="params.productType != null">
            AND mr.reconciliation_product_type = #{params.productType}
        </if>

        <!-- dealType 条件 -->
        <if test="params.dealType != null">
            AND mr.reconciliation_product_type = #{params.dealType}
        </if>
        ORDER BY t.finish_date DESC
    </select>
    <select id="selCountAmount" resultType="java.math.BigDecimal">
        select sum(b.totalAmount) from (
        SELECT t.amount as totalAmount
        FROM deal_order_info t
        LEFT JOIN material_reconciliation mr ON mr.reconciliation_id = t.reconciliation_id
        WHERE t.is_delete = 0

        <!-- 按供应商名称搜索 -->
        <if test="params.supplierName != null and params.supplierName != ''">
            AND t.supplier_name LIKE CONCAT('%', #{params.supplierName}, '%')
        </if>

        <!-- 按客户名称搜索 -->
        <if test="params.bugOrgName != null and params.bugOrgName != ''">
            AND t.bug_org_name LIKE CONCAT('%', #{params.bugOrgName}, '%')
        </if>

        <!-- 按店铺名称搜索 -->
        <if test="params.shopName != null and params.shopName != ''">
            AND t.shop_name LIKE CONCAT('%', #{params.shopName}, '%')
        </if>

        <!-- 按商品名称搜索 -->
        <if test="params.productName != null and params.productName != ''">
            AND t.product_name LIKE CONCAT('%', #{params.productName}, '%')
        </if>

        <!-- 按关键词模糊搜索 -->
        <if test="params.keywords != null and params.keywords != ''">
            AND (
            t.product_name LIKE CONCAT('%', #{params.keywords}, '%')
            OR t.supplier_name LIKE CONCAT('%', #{params.keywords}, '%')
            OR t.shop_name LIKE CONCAT('%', #{params.keywords}, '%')
            OR t.bug_org_name LIKE CONCAT('%', #{params.keywords}, '%')
            )
        </if>

        <!-- 金额区间 -->
        <if test="params.abovePrice != null and params.abovePrice != ''">
            AND t.amount >= #{params.abovePrice}
        </if>
        <if test="params.belowPrice != null and params.belowPrice != ''">
            AND t.amount &lt;= #{params.belowPrice}
        </if>

        <!-- 时间范围 -->
        <if test="params.startFinishDate != null and params.endFinishDate != null and params.startFinishDate != '' and params.endFinishDate != ''">
            AND t.gmt_create BETWEEN #{params.startFinishDate} AND #{params.endFinishDate}
        </if>

        <!-- productType 条件 -->
        <if test="params.productType != null">
            AND mr.reconciliation_product_type = #{params.productType}
        </if>

        <!-- dealType 条件 -->
        <if test="params.dealType != null">
            AND mr.reconciliation_product_type = #{params.dealType}
        </if>
        ORDER BY t.finish_date DESC
        ) b
    </select>
    <select id="selNoRateCountAmount" resultType="java.math.BigDecimal">
        select sum(b.noRateCountAmount) from (
        SELECT
        t.amount * (1 - t.tax_rate/100) AS noRateCountAmount
        FROM deal_order_info t
        LEFT JOIN material_reconciliation mr ON mr.reconciliation_id = t.reconciliation_id
        WHERE t.is_delete = 0

        <!-- 按供应商名称搜索 -->
        <if test="params.supplierName != null and params.supplierName != ''">
            AND t.supplier_name LIKE CONCAT('%', #{params.supplierName}, '%')
        </if>

        <!-- 按客户名称搜索 -->
        <if test="params.bugOrgName != null and params.bugOrgName != ''">
            AND t.bug_org_name LIKE CONCAT('%', #{params.bugOrgName}, '%')
        </if>

        <!-- 按店铺名称搜索 -->
        <if test="params.shopName != null and params.shopName != ''">
            AND t.shop_name LIKE CONCAT('%', #{params.shopName}, '%')
        </if>

        <!-- 按商品名称搜索 -->
        <if test="params.productName != null and params.productName != ''">
            AND t.product_name LIKE CONCAT('%', #{params.productName}, '%')
        </if>

        <!-- 按关键词模糊搜索 -->
        <if test="params.keywords != null and params.keywords != ''">
            AND (
            t.product_name LIKE CONCAT('%', #{params.keywords}, '%')
            OR t.supplier_name LIKE CONCAT('%', #{params.keywords}, '%')
            OR t.shop_name LIKE CONCAT('%', #{params.keywords}, '%')
            OR t.bug_org_name LIKE CONCAT('%', #{params.keywords}, '%')
            )
        </if>

        <!-- 金额区间 -->
        <if test="params.abovePrice != null and params.abovePrice != ''">
            AND t.amount >= #{params.abovePrice}
        </if>
        <if test="params.belowPrice != null and params.belowPrice != ''">
            AND t.amount &lt;= #{params.belowPrice}
        </if>

        <!-- 时间范围 -->
        <if test="params.startFinishDate != null and params.endFinishDate != null and params.startFinishDate != '' and params.endFinishDate != ''">
            AND t.gmt_create BETWEEN #{params.startFinishDate} AND #{params.endFinishDate}
        </if>

        <!-- productType 条件 -->
        <if test="params.productType != null">
            AND mr.reconciliation_product_type = #{params.productType}
        </if>

        <!-- dealType 条件 -->
        <if test="params.dealType != null">
            AND mr.reconciliation_product_type = #{params.dealType}
        </if>
        ORDER BY t.finish_date DESC
        ) b
    </select>
</mapper>