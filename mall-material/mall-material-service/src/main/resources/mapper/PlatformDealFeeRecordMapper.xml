<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PlatformDealFeeRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.PlatformDealFeeRecord" id="PlatformDealFeeRecordMap">
        <result property="dealFeeRecordId" column="deal_fee_record_id"/>
        <result property="dealFeeRecordUn" column="deal_fee_record_un"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="payType" column="pay_type"/>
        <result property="auditOpenTime" column="audit_open_time"/>
        <result property="recordType" column="record_type"/>
        <result property="state" column="state"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="version" column="version"/>
    </resultMap>


</mapper>