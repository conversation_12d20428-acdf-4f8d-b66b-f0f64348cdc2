<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.NegotiatedPriceMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.NegotiatedPrice" id="NegotiatedPriceMap">
        <result property="negotiatedPriceId" column="negotiated_price_id"/>
        <result property="enquiryAmount" column="enquiry_amount"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="type" column="type"/>
        <result property="illustrate" column="illustrate"/>
        <result property="state" column="state"/>
        <result property="sort" column="sort"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>
    <select id="listByUser" resultType="scrbg.meplat.mall.vo.user.userCenter.NegotiatedPriceVo">
        select n.*,u.user_img,u.nick_name from negotiated_price n
             LEFT JOIN `user` u
             on  n.founder_id=u.user_id
        where  ${ew.sqlSegment}


    </select>


</mapper>
