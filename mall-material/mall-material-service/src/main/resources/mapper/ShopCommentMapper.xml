<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ShopCommentMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ShopComment" id="ShopCommentMap">
        <result property="shopCommentId" column="shop_comment_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="commentServiceScore" column="comment_service_score"/>
        <result property="commentLevel" column="comment_level"/>
        <result property="commentSupply" column="comment_supply"/>
        <result property="commentIntegrity" column="comment_intgetProductCommentByShopIdegrity"/>
        <result property="commentService" column="comment_service"/>
        <result property="month" column="month"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <!--二级供应商列表-->
    <select id="findShopComment" resultType="scrbg.meplat.mall.entity.ShopComment"
            parameterType="Map">
        SELECT sc.`shop_comment_id`,
        sc.`shop_id`,
        sc.`shop_name`,
        sc.`main_business`,
        sc.`comment_service_score`,
        sc.`comment_level`,
        sc.`comment_supply`,
        sc.`comment_integrity`,
        sc.`comment_service`,
        sc.`month`,
        sc.`comment_start`,
        sc.`comment_end`,
        sc.`sort`,
        sc.`mall_type`,
        sc.`gmt_create`,
        sc.`gmt_modified`,
        sc.`founder_name`,
        sc.`founder_id`,
        sc.`modify_name`,
        sc.`modify_id`,
        sc.`remarks`,
        sc.`is_delete`
        from shop_comment sc left join
        ( SELECT shop_id,open_date FROM shop WHERE audit_status = '1' AND is_delete = '0' AND state = '1' AND is_supplier = '1' )s
        on sc.shop_id=s.shop_id
        <where>
            sc.is_delete = 0 and s.shop_id is not null
            <if test="dto.keywords != null and dto.keywords != ''">
                and sc.`shop_name` LIKE CONCAT('%',#{dto.keywords},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and sc.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.commentLevel != null and dto.commentLevel != ''">
                and sc.`comment_level` = #{dto.commentLevel}
            </if>
            <if test="dto.commentSupply != null and dto.commentSupply == 1">
                and sc.`comment_supply` = #{dto.commentSupply}
            </if>
            <if test="dto.commentIntegrity != null and dto.commentIntegrity == 1">
                and sc.`comment_integrity` = #{dto.commentIntegrity}
            </if>
            <if test="dto.commentService != null and dto.commentService == 1">
                and sc.`comment_service` = #{dto.commentService}
            </if>
            <if test="dto.commentStart != null">
                and sc.`gmt_create` &gt;= #{dto.commentStart}
            </if>
            <if test="dto.commentEnd != null">
                and sc.`gmt_create` &lt;= #{dto.commentEnd}
            </if>
        </where>
        <if test="dto.isComment == 1">
            ORDER BY sc.comment_service_score desc ,s.open_date asc
        </if>
        <if test="dto.isComment == null or dto.isComment == ''">
            ORDER BY s.open_date asc
        </if>
    </select>
    <!--二级供应商总数-->
    <select id="findShopCommentCount" resultType="int" parameterType="Map">
        select count(*)
        from shop_comment sc left join
        ( SELECT shop_id,open_date FROM shop WHERE audit_status = '1' AND is_delete = '0' AND state = '1' AND is_supplier = '1' )s
        on sc.shop_id=s.shop_id
        <where>
            sc.is_delete = 0 and s.shop_id is not null
            <if test="dto.keywords != null and dto.keywords != ''">
                and sc.`shop_name` LIKE CONCAT('%',#{dto.keywords},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and sc.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.commentLevel != null and dto.commentLevel != ''">
                and sc.`comment_level` = #{dto.commentLevel}
            </if>
            <if test="dto.commentSupply != null and dto.commentSupply == 1">
                and sc.`comment_supply` = #{dto.commentSupply}
            </if>
            <if test="dto.commentIntegrity != null and dto.commentIntegrity == 1">
                and sc.`comment_integrity` = #{dto.commentIntegrity}
            </if>
            <if test="dto.commentService != null and dto.commentService == 1">
                and sc.`comment_service` = #{dto.commentService}
            </if>
            <if test="dto.commentStart != null">
                and sc.`gmt_create` &gt;= #{dto.commentStart}
            </if>
            <if test="dto.commentEnd != null">
                and sc.`gmt_create` &lt;= #{dto.commentEnd}
            </if>
        </where>
    </select>
</mapper>