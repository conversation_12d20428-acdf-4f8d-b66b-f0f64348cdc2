<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ErrorInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ErrorInfo" id="ErrorInfoMap">
        <result property="errorInfoId" column="error_info_id"/>
        <result property="businessType" column="business_type"/>
        <result property="errorRqJson" column="error_rq_json"/>
        <result property="createTime" column="create_time"/>
        <result property="isDispose" column="is_dispose"/>
        <result property="methodName" column="method_name"/>
        <result property="errorInfo" column="error_info"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
    </resultMap>


</mapper>