<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.OrderPaymentInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.OrderPaymentInfo" id="OrderPaymentInfoMap">
        <result property="paymentId" column="payment_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="orderId" column="order_id"/>
        <result property="alipayTradeNo" column="alipay_trade_no"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="subject" column="subject"/>
        <result property="paymentStatus" column="payment_status"/>
        <result property="callbackContent" column="callback_content"/>
        <result property="callbackTime" column="callback_time"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
        <result property="productType" column="product_type"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>