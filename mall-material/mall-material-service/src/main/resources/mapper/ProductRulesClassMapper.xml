<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ProductRulesClassMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ProductRulesClass" id="ProductRulesClassMap">
        <result property="warningRuleId" column="warning_rule_id"/>
        <result property="classId" column="class_id"/>
        <result property="classPathName" column="class_path_name"/>
        <result property="brandName" column="brand_name"/>
        <result property="materialId" column="material_id"/>
        <result property="materialName" column="material_name"/>
        <result property="skuName" column="sku_name"/>
    </resultMap>
    <select id="selcetDataByClassIdAndMaterialId" resultType="scrbg.meplat.mall.entity.ProductRulesClass">
        select * from product_rules_class  cr left join
                      product_warning_rules  wr
                          on wr.warning_rule_id = cr.warning_rule_id
            where
            cr.class_id = #{classId} and cr.material_id = #{relevanceId} and wr.state = 1
    </select>


</mapper>