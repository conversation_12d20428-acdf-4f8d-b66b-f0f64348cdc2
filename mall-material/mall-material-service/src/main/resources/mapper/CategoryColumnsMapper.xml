<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.CategoryColumnsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.CategoryColumns" id="CategoryColumnsMap">
        <result property="columnId" column="column_id"/>
        <result property="columnNumber" column="column_number"/>
        <result property="columnName" column="column_name"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="gmtRelease" column="gmt_release"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


    <resultMap id="categoryColumnsResultMap" type="scrbg.meplat.mall.vo.floor.website.WColumnFloorVO">
        <id property="columnId" column="column_id"/>
        <id property="columnNumber" column="column_number"/>
        <id property="columnName" column="column_name"/>
        <collection property="floorVOS" ofType="scrbg.meplat.mall.vo.floor.website.WFloorVO">
        <id property="floorId" column="floor_id"/>
        <result property="columnId" column="column_id"/>
            <result property="floorId" column="floor_id"/>
            <result property="columnId" column="column_id"/>
            <result property="floorName" column="floor_name"/>
            <result property="floorNameText" column="floor_name_text"/>
            <result property="classId" column="class_id"/>
            <result property="className" column="class_name"/>
            <result property="classPath" column="class_path"/>
            <result property="imgUrl" column="img_url"/>
            <result property="backgroundUrl" column="background_url"/>
            <result property="mainImgUrl" column="main_img_url"/>
            <result property="imgUrlId" column="img_url_id"/>
        </collection>



    </resultMap>
    <select id="wColumnFloorVOsList" resultMap="categoryColumnsResultMap">
        SELECT
            cc.column_id, cc.column_number, cc.column_name,
            f.floor_id, f.column_id,f.floor_name,f.floor_name_text ,f.img_url_id,
            f.img_url,f.class_id,f.class_path,f.class_name,f.background_url,f.main_img_url
        FROM
            category_columns cc
                LEFT JOIN
            floor f ON cc.column_id = f.column_id
        WHERE
            cc.is_delete = 0 AND cc.state = 1 AND cc.is_fixed = 0
          AND f.is_delete = 0 AND f.state = 1 AND f.mall_type = 0
        ORDER BY
            cc.sort DESC, cc.gmt_create DESC, f.sort DESC, f.gmt_create DESC;
    </select>


</mapper>