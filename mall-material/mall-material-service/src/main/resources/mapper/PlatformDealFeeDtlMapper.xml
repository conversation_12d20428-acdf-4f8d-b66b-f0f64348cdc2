<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PlatformDealFeeDtlMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.PlatformDealFeeDtl" id="PlatformDealFeeDtlMap">
        <result property="platformDealFeeDtlId" column="platform_deal_fee_dtl_id"/>
        <result property="platformDealFeeId" column="platform_deal_fee_id"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="relevanceNu" column="relevance_nu"/>
        <result property="relevanceType" column="relevance_type"/>
        <result property="serveType" column="serve_type"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="serveFee" column="serve_fee"/>
        <result property="freeRatio" column="free_ratio"/>
        <result property="dealAmount" column="deal_amount"/>
        <result property="payFee" column="pay_fee"/>
        <result property="dealFeeAmount" column="deal_fee_amount"/>
        <result property="exceedQuota" column="exceed_quota"/>
        <result property="arrearage" column="arrearage"/>
        <result property="arrearageAmount" column="arrearage_amount"/>
        <result property="dealFeeQuota" column="deal_fee_quota"/>
        <result property="dealFeeQuotaTime" column="deal_fee_quota_time"/>
        <result property="finishPayFee" column="finish_pay_fee"/>
        <result property="payFeeState" column="pay_fee_state"/>
        <result property="state" column="state"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
        <result property="version" column="version"/>
    </resultMap>


</mapper>