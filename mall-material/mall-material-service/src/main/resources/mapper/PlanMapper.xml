<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.plan.PlanMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.plan.Plan" id="PlanMap">
        <result property="billId" column="bill_id"/>
        <result property="billNo" column="bill_no"/>
        <result property="state" column="state"/>
        <result property="billDate" column="bill_date"/>
        <result property="remark" column="remark"/>
        <result property="orgId" column="org_id"/>
        <result property="orgName" column="org_name"/>
        <result property="recorderId" column="recorder_id"/>
        <result property="recorderName" column="recorder_name"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="planAmount" column="plan_amount"/>
        <result property="taxPlanAmount" column="tax_plan_amount"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="taxAmount" column="tax_amount"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="modifyName" column="modify_name"/>
        <result property="modifyId" column="modify_id"/>
    </resultMap>


</mapper>