<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.OrderShipMapper">



    <resultMap id="OrderShipInfoVo" type="scrbg.meplat.mall.vo.ship.OrderShipInfoVo">
        <id column="bill_id" property="billId"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="receive_org_name" property="receiveOrgName"/>
        <result column="confirm_time" property="confirmTime"/>
        <result column="bill_sn" property="billSn"/>
        <result column="order_sn" property="orderSn"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="ship_enterprise_id" property="shipEnterpriseId"/>
        <result column="ship_enterprise_name" property="shipEnterpriseName"/>
        <result column="remarks" property="remarks"/>
        <result column="ship_data" property="shipData"/>
        <result column="rate_amount" property="rateAmount"/>
        <collection property="dtls" ofType="scrbg.meplat.mall.entity.OrderShipDtl">
            <id column="bill_id" property="billId"/>
            <result column="product_name" property="productName"/>
            <result column="sku_name" property="skuName"/>
            <result column="unit" property="unit"/>
            <result column="ship_counts" property="shipCounts"/>
            <result column="ship_num" property="shipNum"/>
            <result column="product_price" property="productPrice"/>
            <result column="ship_counts" property="shipCounts"/>
            <result column="order_item_id" property="orderItemId"/>
            <result column="total_amount" property="totalAmount"/>
            <result column="remarks" property="remarks"/>
        </collection>
    </resultMap>
	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.OrderShip" id="OrderShipMap">
        <result property="billId" column="bill_Id"/>
        <result property="orderId" column="order_id"/>
        <result property="billSn" column="bill_sn"/>
        <result property="shipData" column="ship_data"/>
        <result property="shipUserId" column="ship_user_id"/>
        <result property="supplierId" column="supplier_Id"/>
        <result property="otherRateAmount" column="other_rate_amount"/>
        <result property="noRateAmount" column="no_rate_amount"/>
        <result property="rateAmount" column="rate_amount"/>
        <result property="otherTotalPrice" column="other_total_price"/>

        <result property="supplierName" column="supplier_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="type" column="type"/>
        <result property="shipAddress" column="ship_address"/>
<!--        <result property="orgName" column="org_name"/>-->
        <result property="deliveryFlowId" column="delivery_flow_id"/>
        <result property="logisticsCompany" column="logistics_company"/>
    </resultMap>
    <select id="exportDataByBillId" resultMap="OrderShipInfoVo">
        select s.bill_sn,s.supplier_name,s.receive_org_name,s.confirm_time,
              s.ship_data ,s.rate_amount,s.supplier_id,s.ship_enterprise_id,
               s.ship_enterprise_name,
               s.order_sn
        from order_ship s
        where s.bill_id=#{billId}
    </select>
    <select id="selectOtherItemsByOrderId" resultType="scrbg.meplat.mall.vo.ship.OrderReturnItemPCWPVo">
        select os.order_id,os.order_sn,os.other_order_sn,os.other_order_id,
               oi.order_item_id,oi.parent_order_item_id as  otherOrderItemId,oi.product_id,oi.product_sn,
               oi.product_name,oi.product_img,oi.sku_id,oi.sku_name,oi.buy_counts,
               osd.product_price,oi.brand_id,oi.brand_name,osd.other_product_price,
               other_no_rate_price,osd.no_rate_price,os.ship_enterprise_id,os.ship_enterprise_name,
               os.shop_id,os.shop_name,osd.dtl_Id
        FROM order_ship_dtl osd
                 left join order_ship os
                           on osd.bill_id =os.bill_id
                 left join order_item oi
                           on osd.order_item_id=oi.parent_order_item_id
        where  os.type=2
          and osd.is_delete=0
          and oi.is_delete=0
          and os.is_delete=0
          and osd.dtl_id in
        <foreach collection="dtlIdList" item="dtlId" open="(" close=")" separator=",">
        #{dtlId}
       </foreach>

    </select>


</mapper>
