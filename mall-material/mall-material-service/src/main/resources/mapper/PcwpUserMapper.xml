<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PcwpUserMapper">

    <select id="queryUserOrg" resultType="scrbg.meplat.mall.entity.PcwpUser">
        SELECT DISTINCT
            pp.*,
            ppp.org_name AS orgName,
            ppp.user_name AS userName
        FROM pcwp_person_permissions ppp
                 LEFT JOIN (
            SELECT
                xmbm,
                pname,
                SUBSTRING_INDEX(GROUP_CONCAT(pnumber ORDER BY id), ',', 1) AS pnumber
            FROM pcwp_rzjlinfos
            GROUP BY xmbm, pname, pnumber
        ) pr ON ppp.short_code = pr.xmbm AND ppp.user_name = pr.pname
                 left join pcwp_personinfos pp ON pp.pnumber = pr.pnumber
        where ${ew.sqlSegment}
    </select>
</mapper>