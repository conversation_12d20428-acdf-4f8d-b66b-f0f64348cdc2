<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.SysContrMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.SysContr" id="SysContrMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="myDate" column="my_date"/>
        <result property="jsonStr" column="json_str"/>
    </resultMap>


</mapper>