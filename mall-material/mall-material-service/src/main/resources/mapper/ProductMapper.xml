<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ProductMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.Product" id="ProductMap">
        <result property="productId" column="product_id"/>
        <result property="serialNum" column="serial_num"/>
        <result property="productName" column="product_name"/>
        <result property="shopId" column="shop_id"/>
        <result property="classId" column="class_id"/>
        <result property="productDescribe" column="product_describe"/>
        <result property="productType" column="product_type"/>
        <result property="state" column="state"/>
        <result property="productKeyword" column="product_keyword"/>
        <result property="productTransportType" column="product_transport_type"/>
        <result property="productMinPrice" column="product_min_price"/>
        <result property="productAvePrice" column="product_ave_price"/>
        <result property="isDelete" column="is_delete"/>
        <result property="sort" column="sort"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="productInventoryId" column="product_inventory_id"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="brandId" column="brand_id"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="putawayDate" column="putaway_date"/>
        <result property="soldNum" column="sold_num"/>
        <result property="productVisitNum" column="product_visit_num"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="county" column="county"/>
        <result property="detailedAddress" column="detailed_address"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="shopSort" column="shop_sort"/>
        <result property="synthesisSort" column="synthesis_sort"/>
        <result property="productMinImg" column="product_min_img"/>
        <result property="relevanceName" column="relevance_name"/>
        <result property="relevanceNo" column="relevance_no"/>
    </resultMap>
    <resultMap type="scrbg.meplat.mall.vo.product.device.PlatformDeviceListVO" id="PlatformProductMap">
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="shopId" column="shop_id"/>
        <result property="classId" column="class_id"/>
        <result property="productType" column="product_type"/>
        <result property="state" column="state"/>
        <result property="productTransportType" column="product_transport_type"/>
        <result property="productMinPrice" column="product_min_price"/>
        <result property="sort" column="sort"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="productInventoryId" column="product_inventory_id"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="brandId" column="brand_id"/>
        <result property="mallType" column="mall_type"/>
        <result property="shopName" column="shop_name"/>
        <result property="putawayDate" column="putaway_date"/>
    </resultMap>


    <select id="listPlatformProductPageByClassId" resultMap="PlatformProductMap" resultType="list"
            parameterType="scrbg.meplat.mall.dto.product.ListPlatformProductPageByClassIdDTO">
        select p.product_id,p.product_name,p.shop_id,
        s.shop_name,p.class_id,p.product_type,p.state,p.product_transport_type,p.product_min_price
        ,p.sort,p.gmt_create,p.gmt_modified,p.founder_id,p.founder_name,p.product_inventory_id,p.relevance_id,p.brand_id,p.mall_type,p.putaway_date
        from product p
        left join shop s on p.shop_id = s.shop_id
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` = #{dto.state}
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and p.`gmt_create` between #{dto.startDate} and #{dto.endDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 1">
            order by p.`sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by p.`gmt_create` desc
        </if>
        limit #{dto.start},#{dto.limit}
    </select>

    <select id="countPlatformProductPageByClassId" resultType="int"
            parameterType="scrbg.meplat.mall.dto.product.ListPlatformProductPageByClassIdDTO">
        select count(*)
        from product p
        left join shop s on p.shop_id = s.shop_id
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` = #{dto.state}
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and p.`gmt_create` between #{dto.startDate} and #{dto.endDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 1">
            order by p.`sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by p.`gmt_create` desc
        </if>
    </select>


    <resultMap type="scrbg.meplat.mall.vo.product.device.PlatformDeviceListVO" id="PlatformDeviceMap">
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="shopId" column="shop_id"/>
        <result property="classId" column="class_id"/>
        <result property="productType" column="product_type"/>
        <result property="state" column="state"/>
        <result property="productTransportType" column="product_transport_type"/>
        <result property="productMinPrice" column="product_min_price"/>
        <result property="sort" column="sort"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="productInventoryId" column="product_inventory_id"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="brandId" column="brand_id"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="shopName" column="shop_name"/>
        <result property="putawayDate" column="putaway_date"/>
        <result property="productMinImg" column="product_min_Img"/>
    </resultMap>

    <!--    查询平台设备列表-->
    <select id="listPlatformDevicePage" resultMap="PlatformDeviceMap" resultType="list" parameterType="Map">
        select p.product_id,p.product_name,p.shop_id,
        s.shop_name,p.class_id,p.product_type,p.state,p.product_transport_type,p.product_min_price
        ,p.sort,p.gmt_create,p.gmt_modified,p.founder_id,p.founder_name,p.product_inventory_id,p.relevance_id,p.brand_id,p.mall_type,p.putaway_date,
        p.product_min_Img
        from product p
        left join shop s on p.shop_id = s.shop_id
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.productTypes != null">
                and p.`product_type` in
                <foreach item="item" collection="dto.productTypes" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 1">
            order by p.`sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by p.`gmt_create` desc
        </if>
        <if test="dto.orderBy == 3">
            order by p.`gmt_modified` desc
        </if>
    </select>

    <select id="listPlatformDevicePageCount" resultType="int" parameterType="Map">
        select count(*)
        from product p
        left join shop s on p.shop_id = s.shop_id
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.productTypes != null">
                and p.`product_type` in
                <foreach item="item" collection="dto.productTypes" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
    </select>

    <!--    物资-->
    <resultMap type="scrbg.meplat.mall.vo.product.material.PlatformMaterialListVO" id="PlatformMaterialMap">
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="shopId" column="shop_id"/>
        <result property="classId" column="class_id"/>
        <result property="productType" column="product_type"/>
        <result property="state" column="state"/>
        <result property="productTransportType" column="product_transport_type"/>
        <result property="productMinPrice" column="product_min_price"/>
        <result property="productAvePrice" column="product_ave_price"/>
        <result property="isOpenImport" column="is_open_import"/>
        <result property="isDelete" column="is_delete"/>
        <result property="sort" column="sort"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="productInventoryId" column="product_inventory_id"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="brandId" column="brand_id"/>
        <result property="brandName" column="brand_name"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="shopName" column="shop_name"/>
        <result property="putawayDate" column="putaway_date"/>
        <result property="productMinImg" column="product_min_Img"/>
    </resultMap>

    <!--    查询平台设备列表-->
    <select id="listPlatformMaterialPage" resultMap="PlatformMaterialMap" resultType="list" parameterType="Map">
        select p.product_id,p.product_name,p.shop_id,p.serial_num,pc.class_path,p.relevance_name,p.mark_up,p.jc_state,
        s.shop_name,p.class_id,p.product_type,p.state,p.product_transport_type,p.product_min_price,p.sku_name
        ,p.sort,p.gmt_create,p.gmt_modified,p.founder_id,p.founder_name,p.product_inventory_id,p.relevance_id,p.brand_id,p.brand_name,p.mall_type,p.putaway_date,
        p.product_min_Img,sku.original_price,p.tax_Rate
        from product p
        left join shop s on p.shop_id = s.shop_id
        left join product_sku sku on p.product_id = sku.product_id
        left join product_category pc on p.class_id = pc.class_id

        <where>
            p.`is_delete` =0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or s.`shop_name` LIKE  CONCAT('%',#{dto.keywords},'%')
                or p.`serial_num` LIKE  CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.serialNum != null and dto.serialNum != ''">
                and p.`serial_num` LIKE CONCAT('%',#{dto.serialNum},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 1">
            order by p.`sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by p.`gmt_create` desc
        </if>
        <if test="dto.orderBy == 3">
            order by p.`gmt_modified` desc
        </if>
    </select>

    <select id="listPlatformMaterialPageCount" resultType="int" parameterType="Map">
        select count(*)
        from product p
        left join shop s on p.shop_id = s.shop_id
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or s.`shop_name` LIKE  CONCAT('%',#{dto.keywords},'%')
                or p.`serial_num` LIKE  CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.serialNum != null and dto.serialNum != ''">
                and p.`serial_num` LIKE CONCAT('%',#{dto.serialNum},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 1">
            order by p.`sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by p.`gmt_create` desc
        </if>
        <if test="dto.orderBy == 3">
            order by p.`gmt_modified` desc
        </if>
    </select>

    <!--    查询平台设备列表-->
    <select id="listPlatformMaterialPagePVP" resultMap="PlatformMaterialMap" resultType="list" parameterType="Map">
        select p.product_id,p.product_name,p.shop_id,p.serial_num,pc.class_path,p.relevance_name,
        s.shop_name,p.class_id,p.product_type,p.state,p.product_transport_type,p.product_min_price,p.product_ave_price,p.is_open_import
        ,p.sort,p.gmt_create,p.gmt_modified,p.founder_id,p.founder_name,p.product_inventory_id,p.relevance_id,p.brand_id,p.brand_name,p.mall_type,p.putaway_date,
        p.product_min_Img,sku.original_price,p.tax_Rate
        from product p
        left join shop s on p.shop_id = s.shop_id
        left join product_sku sku on p.product_id = sku.product_id
        left join product_category pc on p.class_id = pc.class_id

        <where>
            p.`is_delete` =0 and p.`product_type` = 0 and ((p.is_open_import = 0 and p.`state` = 1) or (p.is_open_import = 1))
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or s.`shop_name` LIKE  CONCAT('%',#{dto.keywords},'%')
                or p.`serial_num` LIKE  CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.serialNum != null and dto.serialNum != ''">
                and p.`serial_num` LIKE CONCAT('%',#{dto.serialNum},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.isOpenImport != null">
                and p.`is_open_import` = #{dto.isOpenImport}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
            <if test="dto.belowPriceAve != null and dto.belowPriceAve != ''">
                and p.`product_ave_price` &lt;= #{dto.belowPriceAve}
            </if>
            <if test="dto.abovePriceAve != null and dto.abovePriceAve != ''">
                and p.`product_ave_price` &gt;= #{dto.abovePriceAve}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 1">
            order by p.`sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by p.`gmt_create` desc
        </if>
        <if test="dto.orderBy == 3">
            order by p.`gmt_modified` desc
        </if>
    </select>

    <select id="listPlatformMaterialPageCountPVP" resultType="int" parameterType="Map">
        select count(*)
        from product p
        left join shop s on p.shop_id = s.shop_id
        <where>
            p.is_delete = 0 and p.`product_type` = 0 and ((p.is_open_import = 0 and p.`state` = 1) or (p.is_open_import = 1))
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or s.`shop_name` LIKE  CONCAT('%',#{dto.keywords},'%')
                or p.`serial_num` LIKE  CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.serialNum != null and dto.serialNum != ''">
                and p.`serial_num` LIKE CONCAT('%',#{dto.serialNum},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
            <if test="dto.belowPriceAve != null and dto.belowPriceAve != ''">
                and p.`product_ave_price` &lt;= #{dto.belowPriceAve}
            </if>
            <if test="dto.abovePriceAve != null and dto.abovePriceAve != ''">
                and p.`product_ave_price` &gt;= #{dto.abovePriceAve}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 1">
            order by p.`sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by p.`gmt_create` desc
        </if>
        <if test="dto.orderBy == 3">
            order by p.`gmt_modified` desc
        </if>
    </select>


    <resultMap type="scrbg.meplat.mall.vo.product.ProductFullInfoVO" id="ProductFullInfoVOMap">
        <result property="productId" column="product_id"/>
        <result property="serialNum" column="serial_num"/>
        <result property="productName" column="product_name"/>
        <result property="shopId" column="shop_id"/>
        <result property="classId" column="class_id"/>
        <result property="brandId" column="brand_id"/>
        <result property="putawayDate" column="putaway_date"/>
        <result property="productMinImg" column="product_min_img"/>
        <result property="skuName" column="sku_name"/>
        <result property="originalPrice" column="original_price"/>
        <result property="sellPrice" column="sell_price"/>
        <result property="stock" column="stock"/>
        <result property="unit" column="unit"/>
        <result property="shopName" column="shop_name"/>
    </resultMap>
    <select id="listProductFullInfoVOPage" resultMap="ProductFullInfoVOMap" parameterType="Map">
        select p.product_id
        , p.serial_num
        , p.product_name
        , p.shop_id
        , p.class_id
        , p.brand_id
        , p.putaway_date
        , p.product_min_img
        , ps.sku_name
        , ps.original_price
        , ps.sell_price
        , ps.stock
        , ps.unit
        , s.shop_name
        from product p
        left join shop s on p.shop_id = s.shop_id
        left join product_sku ps on p.product_id = ps.product_id and ps.is_delete = 0
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%') or ps.`sku_name` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.skuName != null and dto.skuName != ''">
                and ps.`sku_name` LIKE CONCAT('%',#{dto.skuName},'%')
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 1">
            order by p.`sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by p.`gmt_create` desc
        </if>
        <if test="dto.orderBy == 3">
            order by p.`gmt_modified` desc
        </if>
    </select>

    <select id="listProductFullInfoVOPageCount" resultType="int" parameterType="Map">
        select count(*)
        from product p
        left join shop s on p.shop_id = s.shop_id
        left join product_sku ps on p.product_id = ps.product_id and ps.is_delete = 0
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%') or ps.`sku_name` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.skuName != null and dto.skuName != ''">
                and ps.`sku_name` LIKE CONCAT('%',#{dto.skuName},'%')
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 1">
            order by p.`sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by p.`gmt_create` desc
        </if>
        <if test="dto.orderBy == 3">
            order by p.`gmt_modified` desc
        </if>
    </select>


    <!--    前台物资列表-->
    <select id="listHomeMaterialPage" resultType="scrbg.meplat.mall.vo.product.website.material.WMaterialVO"
            parameterType="Map">
        select  p.product_id
        ,p.serial_num
        , p.product_name
        , p.shop_id
        , p.class_id
        , p.product_transport_type
        , p.product_min_price
        , p.brand_id
        , p.brand_name
        , p.putaway_date
        , p.sold_num
        , p.product_visit_num
        , p.province
        , p.city
        , p.county
        , p.detailed_address
        , p.product_min_img
        , p.relevance_name
        , ps.sku_name
        , ps.sold_num
        , ps.original_price
        , ps.sell_price
        , s.shop_name
        , s.is_business
        from shop s
        left join product p on p.shop_id = s.shop_id and s.state = 1 and
        s.audit_status = 1
        left join product_sku ps on p.product_id = ps.product_id and ps.is_delete = 0
        <if test="dto.orderBy == 40 or dto.orderBy == 41">
        left join (select comment_service_score,shop_id from  shop_comment where is_delete='0') sc ON sc.shop_id = p.shop_id
        </if>
        <where>
            p.`state` = 1 and p.is_delete = 0 and s.is_delete = 0 and p.show_state = 0
            <if test="dto.isBusinessShip!= null and dto.isBusinessShip.size() > 0">
                and s.shop_id NOT IN
                <foreach item="shopId" collection="dto.isBusinessShip" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.serialNum != null and dto.serialNum != ''">
                and p.`serial_num` LIKE CONCAT('%',#{dto.serialNum},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or p.`serial_num` LIKE CONCAT('%',#{dto.keywords},'%')
                or s.`shop_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or ps.`sku_name` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.city != null and dto.city != ''">
                and p.`city` = #{dto.city}
            </if>
            <if test="dto.province != null and dto.province != ''">
                and p.`province` = #{dto.province}
            </if>
            <if test="dto.brandName != null and dto.brandName != ''">
                and p.`brand_name` = #{dto.brandName}
            </if>
            <if test="dto.shopId != null and dto.shopId != ''">
                and s.`shop_id` = #{dto.shopId}
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                <choose>
                    <when test="dto.productType == 99">
                        and ( p.`product_type` = 0 or p.`product_type` = 1 or p.`product_type` = 2)
                    </when>
                    <otherwise>
                        and p.`product_type` = #{dto.productType}
                    </otherwise>
                </choose>
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 1">
                and s.`is_business` = 1
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 2">
                and s.`is_internal_shop` = 1
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 3">
                and s.`is_internal_shop` = 0 and s.`is_business` = 0
            </if>
        </where>

        <if test="dto.orderBy == 0">
            order by p.shop_type ASC , p.`synthesis_sort` desc, p.`putaway_date` desc, p.`product_id`
        </if>
        <if test="dto.orderBy == 10">
            order by p.shop_type ASC , p.`product_min_price` asc
        </if>
        <if test="dto.orderBy == 11">
            order by  p.shop_type ASC ,p.`product_min_price` desc
        </if>
        <if test="dto.orderBy == 20">
            order by  p.shop_type ASC ,p.`putaway_date` asc, p.`product_id`
        </if>
        <if test="dto.orderBy == 21">
            order by  p.shop_type ASC ,p.`putaway_date` desc, p.`product_id`
        </if>
        <if test="dto.orderBy == 30">
            order by p.shop_type ASC ,  ps.sold_num asc
        </if>
        <if test="dto.orderBy == 31">
            order by  p.shop_type ASC , ps.sold_num desc
        </if>
        <if test="dto.orderBy == 40">
            order by p.shop_type ASC ,  sc.comment_service_score asc, p.`product_id`
        </if>
        <if test="dto.orderBy == 41">
            order by p.shop_type ASC , sc.comment_service_score desc, p.`product_id`
        </if>
    </select>

    <select id="listHomeMaterialPageCount" resultType="int" parameterType="Map">
        select count(*)
        from shop s
        left join product p on p.shop_id = s.shop_id and s.state = 1 and s.audit_status = 1
        left join product_sku ps on p.product_id = ps.product_id and ps.is_delete = 0
        <where>
            p.`state` = 1 and p.is_delete = 0 and s.is_delete = 0 and p.show_state = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%') or ps.`sku_name` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.city != null and dto.city != ''">
                and p.`city` = #{dto.city}
            </if>
            <if test="dto.province != null and dto.province != ''">
                and p.`province` = #{dto.province}
            </if>
            <if test="dto.brandId != null and dto.brandId != ''">
                and p.`brand_id` = #{dto.brandId}
            </if>
            <if test="dto.shopId != null and dto.shopId != ''">
                and s.`shop_id` = #{dto.shopId}
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                <choose>
                    <when test="dto.productType == 99">
                        and  (p.`product_type` = 0 or p.`product_type` = 1 or p.`product_type` = 2)
                    </when>
                    <otherwise>
                        and p.`product_type` = #{dto.productType}
                    </otherwise>
                </choose>
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 1">
                and s.`is_business` = 1
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 2">
                and s.`is_internal_shop` = 1
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 3">
                and s.`is_internal_shop` = 0 and s.`is_business` = 0
            </if>
        </where>
    </select>

    <select id="listSelfMaterialPage" resultType="scrbg.meplat.mall.entity.SelfOperatedStoreRecord"
            parameterType="Map">
        select  p.product_id,
        p.product_name,
        p.serial_num,
        p.class_id,
        p.brand_name,
        p.product_min_img,
        ps.stock,
        ps.sku_name,
        p.tax_rate,
        p.product_describe,
        ps.cost_price,
        ps.original_price,
        ps.sell_price,
        ps.unit
        from shop s
        left join product p on p.shop_id = s.shop_id and s.state = 1 and s.audit_status = 1
        left join product_sku ps on p.product_id = ps.product_id and ps.is_delete = 0
        <where>
            p.`state` = 1 and p.is_delete = 0 and s.is_delete = 0 and p.show_state = 0
            and p.`shop_id` = #{dto.shopId} and s.`is_business` = 1
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or p.`serial_num` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
        </where>
        order by p.gmt_create desc
    </select>

    <select id="listSelfMaterialPageCount" resultType="int" parameterType="Map">
        select count(*)
        from shop s
        left join product p on p.shop_id = s.shop_id and s.state = 1 and s.audit_status = 1
        left join product_sku ps on p.product_id = ps.product_id and ps.is_delete = 0
        <where>
            p.`state` = 1 and p.is_delete = 0 and s.is_delete = 0 and p.show_state = 0
            and p.`shop_id` = #{dto.shopId} and s.`is_business` = 1
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or p.`serial_num` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
        </where>
    </select>

    <select id="listHomeDevicePage" resultType="scrbg.meplat.mall.vo.product.website.device.WDeviceVO"
            parameterType="Map">
        select p.product_id
        , p.product_name
        , p.product_intro
        , p.shop_id
        , p.class_id
        , p.product_transport_type
        , p.product_min_price
        , p.brand_id
        , p.putaway_date
        , p.sold_num
        , p.product_visit_num
        , p.province
        , p.city
        , p.county
        , p.detailed_address
        , p.product_min_img
        , p.relevance_name
        , p.leave_factory
        , ps.sku_name
        , ps.original_price
        , ps.sell_price
        , s.shop_name
        , s.is_business
        from shop s
        left join product p on p.shop_id = s.shop_id and s.state = 1 and
        s.audit_status = 1
        left join product_sku ps on p.product_id = ps.product_id and ps.is_delete = 0
        <where>
            p.`state` = 1 and p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.skuName != null and dto.skuName != ''">
                and ps.`sku_name` LIKE CONCAT('%',#{dto.skuName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%') or ps.`sku_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or p.`relevance_name` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.city != null and dto.city != ''">
                and p.`city` = #{dto.city}
            </if>
            <if test="dto.province != null and dto.province != ''">
                and p.`province` = #{dto.province}
            </if>
            <if test="dto.brandId != null and dto.brandId != ''">
                and p.`brand_id` = #{dto.brandId}
            </if>
            <if test="dto.shopId != null and dto.shopId != ''">
                and s.`shop_id` = #{dto.shopId}
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.quality != null">
                and p.`quality` = #{dto.quality}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.productTypes != null">
                and p.`product_type` in
                <foreach item="item" collection="dto.productTypes" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
            <if test="dto.aboveYear != null and dto.aboveYear != ''">
                and year(p.`leave_factory`) &lt;= #{dto.aboveYear}
            </if>
            <if test="dto.belowYear != null and dto.belowYear != ''">
                and year(p.`leave_factory`) &gt;= #{dto.belowYear}
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 1">
                and s.`is_business` = 1
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 2">
                and s.`is_internal_shop` = 1
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 3">
                and s.`is_internal_shop` = 0 and s.`is_business` = 0
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`synthesis_sort` desc, p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 10">
            order by p.`product_min_price` asc
        </if>
        <if test="dto.orderBy == 11">
            order by p.`product_min_price` desc
        </if>
        <if test="dto.orderBy == 20">
            order by p.`putaway_date` asc
        </if>
        <if test="dto.orderBy == 21">
            order by p.`putaway_date` desc
        </if>
    </select>

    <select id="listHomeDevicePageCount" resultType="int" parameterType="Map">
        select count(*)
        from shop s
        left join product p on p.shop_id = s.shop_id and s.state = 1 and
        s.audit_status = 1
        left join product_sku ps on p.product_id = ps.product_id and ps.is_delete = 0
        <where>
            p.`state` = 1 and p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.skuName != null and dto.skuName != ''">
                and ps.`sku_name` LIKE CONCAT('%',#{dto.skuName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%') or ps.`sku_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or p.`relevance_name` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.city != null and dto.city != ''">
                and p.`city` = #{dto.city}
            </if>
            <if test="dto.province != null and dto.province != ''">
                and p.`province` = #{dto.province}
            </if>
            <if test="dto.brandId != null and dto.brandId != ''">
                and p.`brand_id` = #{dto.brandId}
            </if>
            <if test="dto.shopId != null and dto.shopId != ''">
                and s.`shop_id` = #{dto.shopId}
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.quality != null">
                and p.`quality` = #{dto.quality}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.productTypes != null">
                and p.`product_type` in
                <foreach item="item" collection="dto.productTypes" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
            <if test="dto.aboveYear != null and dto.aboveYear != ''">
                and year(p.`leave_factory`) &lt;= #{dto.aboveYear}
            </if>
            <if test="dto.belowYear != null and dto.belowYear != ''">
                and year(p.`leave_factory`) &gt;= #{dto.belowYear}
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 1">
                and s.`is_business` = 1
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 2">
                and s.`is_internal_shop` = 1
            </if>
            <if test="dto.isBusiness != null and dto.isBusiness == 3">
                and s.`is_internal_shop` = 0 and s.`is_business` = 0
            </if>
        </where>
    </select>


    <select id="otherServicesPage" resultType="scrbg.meplat.mall.vo.product.website.restsServe.WOtherServicesVO"
            parameterType="Map">
        select p.product_id
        , p.product_name
        , p.product_intro
        , p.product_min_price
        , p.product_min_img
        from shop s
        left join product p on p.shop_id = s.shop_id and s.state = 1 and
        s.audit_status = 1
        left join product_sku ps on p.product_id = ps.product_id and ps.is_delete = 0
        <where>
            p.`state` = 1 and p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
            </if>
            <if test="dto.city != null and dto.city != ''">
                and p.`city` = #{dto.city}
            </if>
            <if test="dto.province != null and dto.province != ''">
                and p.`province` = #{dto.province}
            </if>
            <if test="dto.shopId != null and dto.shopId != ''">
                and s.`shop_id` = #{dto.shopId}
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        <if test="dto.orderBy == 100">
            order by p.`synthesis_sort` asc, p.`putaway_date` asc
        </if>
        <if test="dto.orderBy == 101">
            order by p.`synthesis_sort` desc, p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 0">
            order by p.`synthesis_sort` desc, p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 10">
            order by p.`product_min_price` asc
        </if>
        <if test="dto.orderBy == 11">
            order by p.`product_min_price` desc
        </if>
        <if test="dto.orderBy == 20">
            order by p.`putaway_date` asc
        </if>
        <if test="dto.orderBy == 21">
            order by p.`putaway_date` desc
        </if>
    </select>

    <select id="otherServicesPageCount" resultType="int"
            parameterType="Map">
        select count(*)
        from shop s
        left join product p on p.shop_id = s.shop_id and s.state = 1 and
        s.audit_status = 1
        left join product_sku ps on p.product_id = ps.product_id and ps.is_delete = 0
        <where>
            p.`state` = 1 and p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
            </if>
            <if test="dto.city != null and dto.city != ''">
                and p.`city` = #{dto.city}
            </if>
            <if test="dto.province != null and dto.province != ''">
                and p.`province` = #{dto.province}
            </if>
            <if test="dto.shopId != null and dto.shopId != ''">
                and s.`shop_id` = #{dto.shopId}
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
    </select>
    <select id="listShopManageMaterialSupplierPage"
            resultType="scrbg.meplat.mall.vo.product.MaterialSupplierListVO" parameterType="Map">
        select p.product_id,
        p.mark_up,
        p.mark_up_num,
        p.relevance_name,
        p.product_name,
        p.serial_num,
        p.shop_id,
        s.shop_name,
        s.state as shopState,
        p.class_id,
        p.product_type,
        p.state,
        p.product_transport_type,
        p.product_min_price,
        p.sort,
        p.gmt_create,
        p.gmt_modified,
        p.founder_id,
        p.founder_name,
        p.product_inventory_id,
        p.relevance_id,
        p.brand_id,
        p.product_texture,
        p.mall_type,
        p.putaway_date,
        p.product_min_Img,
        p.gmt_create,
        p.gmt_modified,
        p.shop_sort,
        p.supplier_submit_state,
        p.supplier_submit_error,
        p.fail_reason,
        p.tax_rate,
        p.account_period,
        p.purchase_price,
        p.product_material,
        p.annualized_rate,
        pc.class_path,
        ps.stock,
        ps.s_cost_price as costPrice,
        ps.original_price,
        ps.s_sell_price as sellPrice,
        ps.s_sell_price - ps.s_cost_price as profitPrice
        from product p
        inner join shop s on p.shop_id = s.shop_id
        left join product_category pc on p.class_id = pc.class_id
        inner join product_sku ps on p.product_id = ps.product_id
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.shopId != null and dto.shopId != ''">
                and p.`shop_Id` = #{dto.shopId}
            </if>
            <if test="dto.enterpriseId != null and dto.enterpriseId != ''">
                and p.`supper_by` = #{dto.enterpriseId}
            </if>
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.serial_num != null and dto.serial_num != ''">
                and p.`serial_num` LIKE CONCAT('%',#{dto.serial_num},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or s.`shop_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or p.`serial_num` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.supplierSubmitStates != null">
                and p.`supplier_submit_state` in
                <foreach item="item" collection="dto.supplierSubmitStates" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 1">
            order by p.`shop_sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by p.`gmt_create` desc
        </if>
        <if test="dto.orderBy == 3">
            order by p.`gmt_modified` desc
        </if>
    </select>

    <select id="listShopManageMaterialSupplierPageCount" resultType="int" parameterType="Map">
        select count(*)
        from product p
        left join shop s on p.shop_id = s.shop_id
        left join product_sku ps on p.product_id = ps.product_id
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.shopId != null and dto.shopId != ''">
                and p.`shop_Id` = #{dto.shopId}
            </if>
            <if test="dto.enterpriseId != null and dto.enterpriseId != ''">
                and p.`supper_by` = #{dto.enterpriseId}
            </if>
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.supplierSubmitStates != null">
                and p.`supplier_submit_state` in
                <foreach item="item" collection="dto.supplierSubmitStates" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
    </select>
    <select id="listSupplierMaterialSupplierPage"
            resultType="scrbg.meplat.mall.vo.product.MaterialSupplierListVO" parameterType="Map">
        select p.product_id,
        p.mark_up,
        p.mark_up_num,
        p.relevance_name,
        p.product_name,
        p.serial_num,
        p.shop_id,
        s.shop_name,
        p.class_id,
        p.product_type,
        p.state,
        p.product_transport_type,
        p.product_min_price,
        p.sort,
        p.gmt_create,
        p.gmt_modified,
        p.founder_id,
        p.founder_name,
        p.product_inventory_id,
        p.relevance_id,
        p.brand_id,
        p.mall_type,
        p.putaway_date,
        p.product_min_Img,
        p.gmt_create,
        p.gmt_modified,
        p.shop_sort,
        p.supplier_submit_state,
        p.supplier_name,
        pc.class_path,
        ps.stock,
        ps.cost_price,
        ps.original_price,
        ps.sell_price,
        ps.sell_price - ps.cost_price as profitPrice
        from product p
        inner join shop s on p.shop_id = s.shop_id
        inner join product_sku ps on p.product_id = ps.product_id
        left join product_category pc on p.class_id = pc.class_id
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and p.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.serialNum != null and dto.serialNum != ''">
                and p.`serial_num` LIKE CONCAT('%',#{dto.serialNum},'%')
            </if>
            <if test="dto.shopId != null and dto.shopId != ''">
                and p.`shop_Id` = #{dto.shopId}
            </if>
            <if test="dto.enterpriseId != null and dto.enterpriseId != ''">
                and p.`supper_by` = #{dto.enterpriseId}
            </if>
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or p.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or p.`serial_num` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.supplierSubmitStates != null">
                and p.`supplier_submit_state` in
                <foreach item="item" collection="dto.supplierSubmitStates" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        <if test="dto.orderBy == 0">
            order by p.`putaway_date` desc
        </if>
        <if test="dto.orderBy == 1">
            order by p.`shop_sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by p.`gmt_create` desc
        </if>
        <if test="dto.orderBy == 3">
            order by p.`gmt_modified` desc
        </if>
    </select>



    <select id="listSupplierMaterialSupplierPageCount" resultType="int" parameterType="Map">
        select count(*)
        from product p
        left join shop s on p.shop_id = s.shop_id
        left join product_sku ps on p.product_id = ps.product_id
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and p.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.shopId != null and dto.shopId != ''">
                and p.`shop_Id` = #{dto.shopId}
            </if>
            <if test="dto.enterpriseId != null and dto.enterpriseId != ''">
                and p.`supper_by` = #{dto.enterpriseId}
            </if>
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.serialNum != null and dto.serialNum != ''">
                and p.`serial_num` LIKE CONCAT('%',#{dto.serialNum},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or p.`supplier_name` LIKE  CONCAT('%',#{dto.keywords},'%')
                or p.`serial_num` LIKE  CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.supplierSubmitStates != null">
                and p.`supplier_submit_state` in
                <foreach item="item" collection="dto.supplierSubmitStates" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
    </select>
    <select id="productFromList" resultType="scrbg.meplat.mall.vo.shopManage.reportForms.ProductFromVo">
        select p.product_id, p.product_id ,p.supplier_name,p.serial_num,p.product_name,ps.cost_price,ps.sell_price,ps.original_price,(ps.sell_price-ps.cost_price) as profitPrice, p.putaway_date , p.gmt_create
        from  product  p
                  INNER JOIN product_sku ps
                             on p.product_id=ps.product_id
        where  ${ew.sqlSegment}
    </select>





    <select id="batchAffirmSupplierProductQueryIds" resultType="java.lang.String">
        select p.product_id
        from product p
        inner join shop s on p.shop_id = s.shop_id
        inner join product_sku ps on p.product_id = ps.product_id
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and p.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.shopId != null and dto.shopId != ''">
                and p.`shop_Id` = #{dto.shopId}
            </if>
            <if test="dto.enterpriseId != null and dto.enterpriseId != ''">
                and p.`supper_by` = #{dto.enterpriseId}
            </if>
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`supplier_name` LIKE
                CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.supplierSubmitStates != null">
                and p.`supplier_submit_state` in
                <foreach item="item" collection="dto.supplierSubmitStates" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
    </select>
    <select id="platformProductFromList"
            resultType="scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo">
        select  p.product_id, sh.shop_name,en.enterprise_name, p.serial_num,p.product_name,ps.sell_price, p.putaway_date , p.gmt_create
        from  product  p
                  INNER JOIN product_sku ps
                             on p.product_id=ps.product_id
                  INNER JOIN shop sh
                             on p.shop_id=sh.shop_id
                  INNER JOIN enterprise_info en
                             on sh.enterprise_id=en.enterprise_id
        where  ${ew.sqlSegment}
    </select>
    <select id="platformProductFromList2"
            resultType="scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo">
        select sh.shop_name,p.supplelierName, p.serial_num,p.product_name,ps.sell_price, p.putaway_date , p.gmt_create
        from  product  p
                  INNER JOIN product_sku ps
                             on p.product_id=ps.product_id
                  INNER JOIN shop sh
                             on p.shop_id=sh.shop_id
        where  ${ew.sqlSegment}

    </select>
    <select id="productFromListLoad2" resultType="scrbg.meplat.mall.vo.shopManage.reportForms.ProductFromVo">
        select  p.product_id,p.serial_num,p.product_name,ps.cost_price,ps.sell_price,ps.original_price,(ps.sell_price-ps.cost_price) as profitPrice, p.putaway_date , p.gmt_create
        from  product  p
                  INNER JOIN product_sku ps
                             on p.product_id=ps.product_id
        where  ${ew.sqlSegment}
    </select>
    <select id="productFromListLoad3" resultType="java.util.Map">
        select  p.product_id,p.serial_num,p.product_name,ps.cost_price,ps.sell_price,ps.original_price,(ps.sell_price-ps.cost_price) as profitPrice, p.putaway_date , p.gmt_create
        from  product  p
                  INNER JOIN product_sku ps
                             on p.product_id=ps.product_id
        where  ${ew.sqlSegment}
    </select>
    <select id="productFromListLoad" resultType="scrbg.meplat.mall.vo.shopManage.reportForms.ProductFromVo">
        select  p.serial_num,p.product_name,ps.cost_price,p.supplier_name,
                ps.sell_price,ps.original_price,(ps.sell_price-ps.cost_price) as profitPrice,
                DATE_FORMAT(p.putaway_date, '%Y-%m-%d %H:%i:%s') AS putawayDateStr,
                DATE_FORMAT(p.gmt_create, '%Y-%m-%d %H:%i:%s') AS gmtCreateStr
        from  product  p
                  INNER JOIN product_sku ps
                             on p.product_id=ps.product_id
        where  ${ew.sqlSegment}
    </select>
    <select id="platformProductFromListLoad"
            resultType="scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo">
        select p.product_id, sh.shop_name,en.enterprise_name, p.serial_num,
               p.product_name,ps.sell_price,
               DATE_FORMAT(p.putaway_date, '%Y-%m-%d %H:%i:%s') AS putawayDateStr ,
               p.gmt_create
        from  product  p
                  INNER JOIN product_sku ps
                             on p.product_id=ps.product_id
                  INNER JOIN shop sh
                             on p.shop_id=sh.shop_id
                  INNER JOIN enterprise_info en
                             on sh.enterprise_id=en.enterprise_id
        where  ${ew.sqlSegment}

    </select>
    <select id="getDataByClassIdParentPath" resultType="java.lang.String">
        select class_path from product where class_id=#{classId} GROUP BY class_path
    </select>

    <select id="listPlatformMaterialProductIds" resultType="java.lang.String">
        select p.product_id
        from product p
        left join shop s on p.shop_id = s.shop_id
        <where>
            p.is_delete = 0 and s.is_delete = 0
            <if test="dto.classId != null and dto.classId != ''">
                and p.`class_path` LIKE CONCAT('%',#{dto.classId},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (p.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and p.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.mallType != null">
                and p.`mall_Type` = #{dto.mallType}
            </if>
            <if test="dto.productType != null">
                and p.`product_type` = #{dto.productType}
            </if>
            <if test="dto.state != null">
                and p.`state` in
                <foreach item="item" collection="dto.state" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startCreateDate != null and dto.endCreateDate != null ">
                and p.`gmt_create` between #{dto.startCreateDate} and #{dto.endCreateDate}
            </if>
            <if test="dto.startPutawayDate != null and dto.endPutawayDate != null ">
                and p.`putaway_date` between #{dto.startPutawayDate} and #{dto.endPutawayDate}
            </if>
            <if test="dto.startModifiedDate != null and dto.endModifiedDate != null ">
                and p.`gmt_modified` between #{dto.startModifiedDate} and #{dto.endModifiedDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and p.`product_min_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and p.`product_min_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
    </select>
    <select id="findAllProduct" resultType="scrbg.meplat.mall.entity.Product">
        SELECT p.serial_num, p.product_name, p.relevance_name, p.brand_name, p.sku_name, pc.class_path,
               sh.shop_name, pk.cost_price, pk.stock,
               DATE_FORMAT(p.putaway_date, '%Y-%m-%d %H:%i:%s') AS putawayDateStr,
               DATE_FORMAT(p.gmt_create, '%Y-%m-%d %H:%i:%s') AS gmtCreateStr,
               pk.unit,pk.original_price,pk.sell_price
        FROM product p
                 LEFT JOIN product_category pc ON p.class_id = pc.class_id
                 LEFT JOIN shop sh ON p.shop_id = sh.shop_id
                 LEFT JOIN product_sku pk ON p.product_id = pk.product_id
        where    ${ew.sqlSegment}
    </select>
    <select id="calculateProductAveragePrice" resultType="scrbg.meplat.mall.entity.Product">
        SELECT brand_id, sku_name, relevance_name, class_path,AVG(product_min_price) AS product_ave_price
        FROM product where class_path like '%1683780842626412546%' and is_delete = '0'
        and product_type = 0 and ((is_open_import = 0 and state = 1) or (is_open_import = 1))
        GROUP BY brand_id, sku_name, relevance_name, class_path;
    </select>
    <!--Integer selectTypeCount(LambdaQueryWrapper<Product> q);-->
    <select id="selectTypeCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM product p
        WHERE
            ${ew.sqlSegment}
        <if test="type != null">
            and p.`product_type` = #{type}
        </if>

    </select>

    <!-- 统计店铺商品类型及供应商维度的商品状态分布 -->
    <select id="statisticsByShopAndProductTypeWithSupplier"
            resultType="scrbg.meplat.mall.vo.product.ProductStatisticsVO">
        SELECT
        p.shop_id AS shopId,
        p.product_type AS productType,
        p.supplier_name AS supplierName,
        SUM(CASE WHEN p.state = 1 THEN 1 ELSE 0 END) AS onShelfCount,
        SUM(CASE WHEN p.state = 2 THEN 1 ELSE 0 END) AS offShelfCount,
        SUM(CASE WHEN p.state = 4 THEN 1 ELSE 0 END) AS rejectedCount,
        SUM(CASE WHEN p.state IN (1, 2) THEN 1 ELSE 0 END) AS totalOnAndOffShelfCount
        FROM product p
        WHERE p.is_delete = 0 and p.mall_type = 0
        <if test="params.startCreateDate != null and params.endCreateDate != null ">
            and p.`gmt_create` between #{params.startCreateDate} and #{params.endCreateDate}
        </if>
        <if test="params.shopId != null and params.shopId != ''">
            AND p.shop_id = #{params.shopId}
        </if>
        <if test="params.productType != null">
            AND p.product_type = #{params.productType}
        </if>
        <if test="params.supplierName != null and params.supplierName != ''">
            AND p.supplier_name LIKE CONCAT('%', #{params.supplierName}, '%')
        </if>
        GROUP BY p.shop_id, p.product_type, p.supplier_name
        ORDER BY p.shop_id, p.product_type
    </select>
    <select id="selectStatisticsTypeVoCount"
            resultType="java.lang.Integer">
    SELECT COUNT(1) from (
        SELECT
        p.shop_id AS shopId,
        p.product_type AS productType,
        p.supplier_name AS supplierName,
        SUM(CASE WHEN p.state = 1 THEN 1 ELSE 0 END) AS onShelfCount,
        SUM(CASE WHEN p.state = 2 THEN 1 ELSE 0 END) AS offShelfCount,
        SUM(CASE WHEN p.state = 4 THEN 1 ELSE 0 END) AS rejectedCount,
        SUM(CASE WHEN p.state IN (1, 2) THEN 1 ELSE 0 END) AS totalOnAndOffShelfCount
        FROM product p
        WHERE p.is_delete = 0 and p.mall_type = 0
        <if test="params.startCreateDate != null and params.endCreateDate != null ">
            and p.`gmt_create` between #{params.startCreateDate} and #{params.endCreateDate}
        </if>
        <if test="params.shopId != null and params.shopId != ''">
            AND p.shop_id = #{params.shopId}
        </if>
        <if test="type != null">
            AND p.product_type = #{type}
        </if>
        <if test="params.supplierName != null and params.supplierName != ''">
            AND p.supplier_name LIKE CONCAT('%', #{params.supplierName}, '%')
        </if>
        GROUP BY p.shop_id, p.product_type, p.supplier_name
        ORDER BY p.shop_id, p.product_type
        ) t
    </select>
</mapper>
