<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PrivateKeySupplierMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.PrivateKeySupplier" id="PrivateKeySupplierMap">
        <result property="privateId" column="private_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="supplierType" column="supplier_type"/>
        <result property="privateKey" column="private_key"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>