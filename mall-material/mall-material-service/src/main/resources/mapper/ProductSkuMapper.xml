<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ProductSkuMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ProductSku" id="ProductSkuMap">
        <result property="skuId" column="sku_id"/>
        <result property="productId" column="product_id"/>
        <result property="skuName" column="sku_name"/>
        <result property="skuImg" column="sku_img"/>
        <result property="costPrice" column="cost_price"/>
        <result property="originalPrice" column="original_price"/>
        <result property="sellPrice" column="sell_price"/>
        <result property="discounts" column="discounts"/>
        <result property="stock" column="stock"/>
        <result property="state" column="state"/>
        <result property="unit" column="unit"/>
        <result property="soldNum" column="sold_num"/>
        <result property="skuTitle" column="sku_title"/>
        <result property="skuSubtitle" column="sku_subtitle"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="brandId" column="brand_id"/>
        <result property="sort" column="sort"/>
        <result property="productType" column="product_type"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="leaseNum" column="lease_num"/>
        <result property="leaseUnit" column="lease_unit"/>
        <result property="settlePrice" column="settle_price"/>
    </resultMap>


</mapper>