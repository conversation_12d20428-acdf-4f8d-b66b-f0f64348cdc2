<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.FloorGoodsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.FloorGoods" id="FloorGoodsMap">
        <result property="floorGoodsId" column="floor_goods_id"/>
        <result property="orderValue" column="order_value"/>
        <result property="floorId" column="floor_id"/>
        <result property="goodsId" column="goods_id"/>
        <result property="goodsPictureUrl" column="goods_picture_url"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="state" column="state"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>
    <select id="wFloorGoodsVOByList" resultType="scrbg.meplat.mall.vo.floor.website.WFloorGoodsVO">
        SELECT f.goods_id as productId,p.serial_num,p.product_name,p.product_min_img ,
               sku.sku_name,sku.sell_price,p.shop_id,sku.original_price
        FROM floor_goods f
                 left join product p
                           on f.goods_id=p.product_id
                 LEFT JOIN product_sku sku
                           on f.goods_id=sku.product_id
        where  ${ew.sqlSegment}
    </select>


</mapper>