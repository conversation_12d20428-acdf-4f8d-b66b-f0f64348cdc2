<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.StatisticalNumMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.StatisticalNum" id="StatisticalNumMap">
        <result property="id" column="id"/>
        <result property="statisticalDate" column="statistical_Date"/>
        <result property="supplierNum" column="supplier_num"/>
        <result property="shopNum" column="shop_num"/>
        <result property="inEnterprise" column="in_enterPrise"/>
        <result property="inUserCounts" column="in_user_counts"/>
        <result property="userNum" column="user_num"/>
        <result property="ordersTotalAmount" column="orders_total_amount"/>
        <result property="productNum" column="product_num"/>
        <result property="upProductNum" column="up_product_num"/>
        <result property="ordersNum" column="orders_num"/>
        <result property="orderShipAmount" column="order_ship_amount"/>
    </resultMap>


</mapper>
