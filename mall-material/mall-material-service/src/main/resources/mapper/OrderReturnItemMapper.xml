<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.OrderReturnItemMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.OrderReturnItem" id="OrderReturnItemMap">
        <result property="orderReturnItemId" column="order_return_item_id"/>
        <result property="orderReturnId" column="order_return_id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="productId" column="product_id"/>
        <result property="productSn" column="product_sn"/>
        <result property="productName" column="product_name"/>
        <result property="productImg" column="product_img"/>
        <result property="skuId" column="sku_id"/>
        <result property="skuName" column="sku_name"/>
        <result property="costPrice" column="cost_price"/>
        <result property="productPrice" column="product_price"/>
        <result property="count" column="count"/>
        <result property="flishTime" column="flish_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderName" column="founder_name"/>
        <result property="founderId" column="founder_id"/>
        <result property="remarks" column="remarks"/>
        <result property="productType" column="product_type"/>
        <result property="state" column="state"/>
        <result property="version" column="version"/>
        <result property="returnState" column="return_state"/>

        <result property="noRatePrice" column="no_rate_price"/>
        <result property="noRateAmount" column="no_rate_amount"/>
        <result property="taxRate" column="tax_rate"/>
    </resultMap>
    <select id="selectRetuenList" resultType="scrbg.meplat.mall.entity.OrderReturnItem">
        select oi.* from order_return_item oi
          left join order_return  o
         on oi.order_return_id=o.order_return_id
        where
            ${ew.sqlSegment}
    </select>
    <select id="selectCounts" resultType="java.math.BigDecimal">
        select sum(oi.count) from order_return_item oi
                                 left join order_return  o
                                           on oi.order_return_id=o.order_return_id
        where
            ${ew.sqlSegment}
    </select>


</mapper>
