<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.FinancialProductsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.FinancialProducts" id="FinancialProductsMap">
        <result property="financialId" column="financial_id"/>
        <result property="financialName" column="financial_name"/>
        <result property="pictureUrl" column="picture_url"/>
        <result property="productsType" column="products_type"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="interestRate" column="interest_rate"/>
        <result property="financingPeriod" column="financing_period"/>
        <result property="repaymentType" column="repayment_type"/>
        <result property="lendingDuration" column="Lending_duration"/>
        <result property="applicationMaterials" column="application_materials"/>
        <result property="applicationConditions" column="application_conditions"/>
        <result property="productIntroduction" column="product_introduction"/>
        <result property="useArea" column="use_area"/>
        <result property="productFeatures" column="product_features"/>
        <result property="applicationScenario" column="application_scenario"/>
        <result property="shopId" column="shop_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="isDelete" column="is_delete"/>
        <result property="state" column="state"/>
        <result property="sort" column="sort"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>