<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.SupplierReconciliationDtlMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.SupplierReconciliationDtl" id="SupplierReconciliationDtlMap">
        <result property="dtlId" column="dtl_id"/>
        <result property="billId" column="bill_id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="materialId" column="material_id"/>
        <result property="materialName" column="material_name"/>
        <result property="spec" column="spec"/>
        <result property="unit" column="unit"/>
        <result property="texture" column="texture"/>
        <result property="noRatePrice" column="no_rate_price"/>
        <result property="price" column="price"/>
        <result property="quantity" column="quantity"/>
        <result property="noRateAmount" column="no_rate_amount"/>
        <result property="rateAmount" column="rate_amount"/>
        <result property="state" column="state"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="receivingDate" column="receiving_date"/>
        <result property="materialClassId" column="material_class_id"/>
        <result property="materialClassName" column="material_class_name"/>
    </resultMap>
    <select id="findByCondition" resultType="scrbg.meplat.mall.entity.SupplierReconciliationDtl">
        SELECT
            res.order_id,
            res.source_type,
            res.bill_type,
            res.order_sn,
            res.supplierOrgId,
            res.receivingDate,
            res.twoSupplierOrgId,
            res.materialId,
            res.product_sn,
            res.reconciliation_type,
            res.product_name,
            p.product_texture as texture,
            res.unit,
            res.sourceDtlId,
            res.spec,
            res.quantity,
            res.is_reconciliation,
            res.noRateAmount,
            res.totalAmount,
            res.price,
            res.no_rate_price,
            res.order_item_id,
            p.tax_rate
        FROM (
        -- 发货数据子查询
        SELECT
            ors.order_id,
            ors.source_type,
            ors.bill_type,
            ors.order_sn,
            ors.supplier_id as supplierOrgId,
            ors.confirm_time as receivingDate,
            ors.ship_enterprise_id as twoSupplierOrgId,
            dtl.product_id as materialId,
            dtl.product_sn,
            1 as reconciliation_type,
            dtl.product_name,
            dtl.unit,
            dtl.dtl_id as sourceDtlId,
            dtl.sku_name as spec,
            dtl.ship_num as quantity,
            dtl.is_reconciliation,
            dtl.other_no_rate_amount as noRateAmount,
            dtl.other_total_amount as totalAmount,
            dtl.other_product_price as price,
            dtl.other_no_rate_price AS no_rate_price,
            dtl.order_item_id
            FROM order_ship ors
            INNER JOIN order_ship_dtl dtl ON ors.bill_Id = dtl.bill_id
        WHERE ors.type = 2
            AND ors.order_class != 1
            AND ors.is_delete = 0
            AND dtl.is_delete = 0
        UNION ALL
        -- 退货数据子查询
        SELECT
            ore.order_id,
            ore.source_type,
            ore.bill_type,
            ore.order_sn,
            ore.supplier_id as supplierOrgId,
            ore.flish_time as receivingDate,
            ore.ship_enterprise_id as twoSupplierOrgId,
            ori.product_id as materialId,
            ori.product_sn,
            2 as reconciliation_type,
            ori.product_name,
            ori.unit,
            ori.order_return_item_id as sourceDtlId,
            ori.sku_name as spec,
            -ori.count as quantity,
            ori.is_reconciliation,
            ori.other_no_rate_amount as noRateAmount,
            ori.other_total_amount as totalAmount,
            ori.other_product_price as price,
            ori.other_no_product_price AS no_rate_price,
            ori.order_item_id
            FROM order_return ore
            INNER JOIN order_return_item ori ON ore.order_return_id = ori.order_return_id
        WHERE ore.state = 3
            AND ore.is_out = 1
            AND ore.is_delete = 0
            AND ori.is_delete = 0
        ) res
        LEFT JOIN product p ON res.materialId = p.product_id
        where ${ew.sqlSegment}
        ORDER BY receivingDate DESC
    </select>

    <!-- 根据对账单ID查询收料明细（用于导出） -->
    <select id="getReceiptDetailByBillId" resultType="scrbg.meplat.mall.entity.SupplierReconciliationDtlExcel">
        SELECT distinct
        srd.order_sn,
        mrd.receipt_bill_sn,
        srd.material_name,
        srd.product_name,
        srd.spec AS spec,
        srd.unit AS unit,
        srd.texture,
        srd.quantity,
        srd.price as rate_price,
        srd.no_rate_price,
        srd.rate_amount,
        srd.no_rate_amount,
        srd.remarks AS remarks
        FROM supplier_reconciliation_dtl srd
        INNER JOIN supplier_reconciliation sr ON srd.bill_id = sr.bill_id
        LEFT JOIN material_reconciliation_dtl mrd ON srd.order_item_id = mrd.order_item_id
        WHERE sr.is_delete = 0
        AND srd.is_delete = 0
        AND srd.bill_id = #{billId}
        ORDER BY srd.order_sn DESC,
        srd.quantity DESC,
        srd.material_name DESC,
        srd.rate_amount DESC
    </select>
</mapper>
