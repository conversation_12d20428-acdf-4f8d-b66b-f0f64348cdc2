<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.OperateLogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.OperateLog" id="OperateLogMap">
        <result property="operateLogId" column="operate_log_id"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="relevanceType" column="relevance_type"/>
        <result property="explainInfo" column="explain_info"/>
        <result property="beforeUpdate" column="before_update"/>
        <result property="afterUpdate" column="after_update"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>