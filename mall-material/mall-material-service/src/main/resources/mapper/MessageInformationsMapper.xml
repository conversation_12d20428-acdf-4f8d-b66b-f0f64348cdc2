<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.MessageInformationsMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.MessageInformations" id="MessageInformationsMap">
        <result property="messageInformationsId" column="message_informations_id"/>
        <result property="messageId" column="message_id"/>
        <result property="messageName" column="message_name"/>
        <result property="respondId" column="respond_id"/>
        <result property="respondName" column="respond_name"/>
        <result property="messagTitle" column="messag_title"/>
        <result property="messagContent" column="messag_content"/>
        <result property="messagType" column="messag_type"/>
        <result property="isFile" column="is_file"/>
        <result property="messagDate" column="messag_date"/>
        <result property="respondTitle" column="respond_title"/>
        <result property="respondContent" column="respond_content"/>
        <result property="respondDate" column="respond_date"/>
        <result property="state" column="state"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>
</mapper>