<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.AskAnswerMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.AskAnswer" id="AskAnswerMap">
        <result property="askAnswerId" column="ask_answer_id"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="sendId" column="send_id"/>
        <result property="sendName" column="send_name"/>
        <result property="sendType" column="send_type"/>
        <result property="content" column="content"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="sendDate" column="send_date"/>
        <result property="acceptIds" column="accept_ids"/>
        <result property="state" column="state"/>
        <result property="sort" column="sort"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="isFile" column="is_file"/>
    </resultMap>
    <select id="listByDeviceDemand" resultType="scrbg.meplat.mall.vo.user.userCenter.AskAnswerVo">
        select a.*,u.user_img,u.nick_name from ask_answer a
                                                   LEFT JOIN `user` u
                                                             on  a.founder_id=u.user_id
        where  ${ew.sqlSegment}



    </select>


</mapper>
