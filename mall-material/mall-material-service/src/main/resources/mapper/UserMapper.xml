<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.UserMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.User" id="UserMap">
        <result property="userId" column="user_id"/>
        <result property="interiorId" column="interior_id"/>
        <result property="userNumber" column="user_number"/>
        <result property="account" column="account"/>
        <result property="password" column="password"/>
        <result property="userMobile" column="user_mobile"/>
        <result property="email" column="email"/>
        <result property="userImg" column="user_img"/>
        <result property="userImgId" column="user_img_id"/>
        <result property="realName" column="real_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="userSex" column="user_sex"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="county" column="county"/>
        <result property="detailAddress" column="detail_address"/>
        <result property="userBirth" column="user_birth"/>
        <result property="state" column="state"/>
        <result property="isAdmin" column="is_admin"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="gmtLogin" column="gmt_login"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isMaterial" column="is_material"/>
        <result property="isDevice" column="is_device"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="isInternalUser" column="is_internal_user"/>
        <result property="platformAdmin" column="platform_admin"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>