<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.SynthesizeTemporaryMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.SynthesizeTemporary" id="SynthesizeTemporaryMap">
        <result property="synthesizeTemporaryId" column="synthesize_temporary_id"/>
        <result property="synthesizeTemporarySn" column="synthesize_temporary_sn"/>
        <result property="orgFarId" column="org_far_id"/>
        <result property="orgId" column="org_id"/>
        <result property="orgName" column="org_name"/>
        <result property="supplierOrgFarId" column="supplier_org_far_id"/>
        <result property="supplierOrgId" column="supplier_org_id"/>
        <result property="supplierOrgName" column="supplier_org_name"/>
        <result property="supplierCreditCode" column="supplier_credit_code"/>
        <result property="supplierShortCode" column="supplier_short_code"/>
        <result property="receiverAddress" column="receiver_address"/>
        <result property="referenceSumAmount" column="reference_sum_amount"/>
        <result property="synthesizeSumAmount" column="synthesize_sum_amount"/>
        <result property="submitTime" column="submit_time"/>
        <result property="auditTime" column="audit_time"/>
        <result property="state" column="state"/>
        <result property="orgIsDelete" column="org_is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>