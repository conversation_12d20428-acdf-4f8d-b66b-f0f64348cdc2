<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.BiddingPurchaseMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.BiddingPurchase" id="BiddingPurchaseMap">
        <result property="biddingId" column="bidding_id"/>
        <result property="biddingSn" column="bidding_sn"/>
        <result property="biddingSourceType" column="bidding_source_type"/>
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="linkName" column="link_name"/>
        <result property="linkPhone" column="link_phone"/>
        <result property="biddingExplain" column="bidding_explain"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="biddingState" column="bidding_state"/>
        <result property="referencePrice" column="reference_price"/>
        <result property="publicityState" column="publicity_state"/>
    </resultMap>
    <resultMap id="biddingMyList" type="scrbg.meplat.mall.vo.bidding.MyBiddingVo">
        <result property="title" column="title"/>
        <result property="biddingSn" column="bidding_sn"/>
        <result property="type" column="type"/>
        <result property="state" column="state"/>
        <result property="publicityState" column="publicity_state"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
    </resultMap>

    <select id="getMyBidList" resultMap="biddingMyList">
        SELECT
        p.title,
        p.type,
        p.bidding_sn,
        p.state,
        p.start_time,
        p.end_time,
        p.publicity_state
        FROM
        bidding_purchase p JOIN
        bidding_suppliers s
        on p.bidding_id = s.bidding_id
        <where>
            s.supplier_id ='${bid.supplierId}'
            <if test="bid.keyWords != null and bid.keyWords != ''">
                and (p.`title` LIKE CONCAT('%',#{bid.keyWords},'%') )
            </if>
        </where>
        order by p.start_time
    </select>
    <select id="selectByBiddingSn" resultType="scrbg.meplat.mall.vo.bidding.HitBidVo">
        SELECT
            i.bidding_id,
            b.product_sn,
            r.bill_type,
            i.bid_rate_price,
            i.bid_price,
            r.tax_rate,
            i.net_price,
            i.fixation_price,
            i.out_factory_price,
            i.transport_price,
            r.supplier_id,
            r.supplier_name
        FROM
            bidding_purchase p
                INNER JOIN bidding_bid_record r ON p.bidding_id = r.bidding_id  AND r.is_delete = 0

                RIGHT JOIN bidding_bid_record_item i ON r.bid_record_id = i.bid_record_id AND i.is_delete = 0
                inner join  bidding_product b on i.bidding_product_id = b.bidding_product_id and b.is_delete = 0
        WHERE
           r.state = 6
          AND p.is_delete = 0


          AND p.synthesize_temporary_sn = #{biddingSn};
    </select>

</mapper>