<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.SysRoleMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.SysRole" id="SysRoleMap">
        <result property="roleId" column="role_id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
        <result property="keyword" column="keyword"/>
        <result property="orgSearch" column="org_search"/>
    </resultMap>

    <resultMap id="roleMenuMap" type="scrbg.meplat.mall.dto.user.MallRole">
        <id column="role_id" property="roleId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="org_search" property="orgSearch"/>
        <result column="category_type" property="categoryType"/>
        <collection property="mallSysMenus" ofType="scrbg.meplat.mall.dto.user.MallSysMenu">
            <id column="menu_id" property="menuId"/>
            <result column="smCode" property="code"/>
            <result column="title" property="title"/>
            <result column="type" property="type"/>
            <result column="auth_label" property="authLabel"/>
            <result column="icon" property="icon"/>
            <result column="path_url" property="pathUrl"/>
            <result column="class_code" property="classCode"/>
            <result column="parent_menu_id" property="parentMenuId"/>
            <result column="level" property="level"/>
        </collection>
    </resultMap>
<!--    根据pcwp角色列表查询角色以及对应-->
    <select id="getRoleMenuListByRoleNames" resultMap="roleMenuMap">
        SELECT DISTINCT
        sr.role_id,
        sr.code,
        sr.name,
        sr.org_search,
        sr.category_type,
        sm.menu_id,
        sm.code as smCode,
        sm.title,
        sm.type,
        sm.auth_label,
        sm.icon,
        sm.path_url,
        sm.class_code,
        sm.parent_menu_id,
        sm.level
        FROM
        sys_role sr
        LEFT JOIN sys_menu_role smr ON sr.role_id = smr.role_id and smr.is_delete = 0
        LEFT JOIN sys_menu sm ON sm.menu_id = smr.menu_id AND sm.is_delete = 0 AND sm.state = 1
        WHERE
        sr.is_delete = 0 and sr.state = 1
        and sr.`name` in
        <foreach item="item" collection="roleNames" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
<!--    根据角色名称和所属平台获取菜单列表-->
    <select id="getMenuListByRoleNames" resultType="scrbg.meplat.mall.entity.SysMenu">
        SELECT DISTINCT
        sm.menu_id as menuId,
        sm.code as code,
        sm.title as title,
        sm.type as type,
        sm.icon as icon,
        sm.path_url as pathUrl,
        sm.class_code as classCode,
        sm.parent_menu_id as parentMenuId,
        sm.level as level,
        sm.sort as sort,
        sm.gmt_create
        FROM
        sys_role sr
        INNER JOIN sys_menu_role smr ON sr.role_id = smr.role_id
        INNER JOIN sys_menu sm ON sm.menu_id = smr.menu_id
        WHERE
        sr.is_delete = 0 and smr.is_delete = 0 and sm.is_delete = 0 and sr.state = 1 and sm.state = 1 and
        sr.category_type = #{categoryType}
        <if test="showDev != null">
            and sm.`show_dev` = #{showDev}
        </if>
        and sr.`name` in
        <foreach item="item" collection="roleNames" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by sm.`sort` desc , sm.`gmt_create` asc
    </select>
<!--    根据pcwp角色列表查询角色以及对应(禁止直接调用mapper，不包含菜单)-->
    <select id="getRoleMenuListByRoleNameLists" resultType="scrbg.meplat.mall.dto.user.MallRole">
        SELECT
        sr.role_id,
        sr.code,
        sr.name,
        sr.org_search,
        sr.category_type
        FROM
        sys_role sr
        WHERE
        sr.is_delete = 0 and sr.state = 1
        and sr.`name` in
        <foreach item="item" collection="roleNames" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>