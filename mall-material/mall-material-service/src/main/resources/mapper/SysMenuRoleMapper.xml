<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.SysMenuRoleMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.SysMenuRole" id="SysMenuRoleMap">
        <result property="sysMenuRoleId" column="sys_menu_role_id"/>
        <result property="menuId" column="menu_id"/>
        <result property="roleId" column="role_id"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>