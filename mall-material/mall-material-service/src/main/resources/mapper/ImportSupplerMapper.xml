<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ImportSupplerMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ImportSuppler" id="ImportSupplerMap">
        <result property="importSupplerId" column="import_suppler_id"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="socialCreditCode" column="social_credit_code"/>
        <result property="legalRepresentative" column="legal_representative"/>
        <result property="adminPhone" column="admin_phone"/>
        <result property="state" column="state"/>
        <result property="fail" column="fail"/>
    </resultMap>


</mapper>