<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.FloorMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.Floor" id="FloorMap">
        <result property="floorId" column="floor_id"/>
        <result property="columnId" column="column_id"/>
        <result property="columnName" column="column_name"/>
        <result property="floorName" column="floor_name"/>
        <result property="floorNameText" column="floor_name_text"/>
        <result property="imgUrlId" column="img_url_id"/>
        <result property="imgUrl" column="img_url"/>
        <result property="mainImgUrl" column="main_img_url"/>
        <result property="floorProductType" column="floor_product_type"/>
        <result property="usePage" column="use_page"/>
        <result property="gmtRelease" column="gmt_release"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="state" column="state"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>