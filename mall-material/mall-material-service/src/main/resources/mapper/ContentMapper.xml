<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ContentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.Content" id="ContentMap">
        <result property="contentId" column="content_id"/>
        <result property="fileName" column="file_name"/>
        <result property="content" column="content"/>
        <result property="source" column="source"/>
        <result property="state" column="state"/>
        <result property="comments" column="comments"/>
        <result property="sort" column="sort"/>
        <result property="defaultPicturePath" column="default_picture_path"/>
        <result property="bigPicturePath" column="big_picture_path"/>
        <result property="smallPicturePath" column="small_picture_path"/>
        <result property="tinyPicturePath" column="tiny_picture_path"/>
        <result property="home" column="home"/>
        <result property="top" column="top"/>
        <result property="title" column="title"/>
        <result property="infoType" column="info_type"/>
        <result property="subtitle" column="subtitle"/>
        <result property="author" column="author"/>
        <result property="filePath" column="file_path"/>
        <result property="homePicturePath" column="home_picture_path"/>
        <result property="viewCount" column="view_count"/>
        <result property="programaId" column="programa_id"/>
        <result property="programaKey" column="programa_key"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="gmtRelease" column="gmt_release"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>