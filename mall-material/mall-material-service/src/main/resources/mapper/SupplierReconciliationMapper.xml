<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.SupplierReconciliationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.SupplierReconciliation" id="SupplierReconciliationMap">
        <result property="billId" column="bill_id"/>
        <result property="billNo" column="bill_no"/>
        <result property="twoSupplierOrgId" column="two_supplier_org_id"/>
        <result property="twoSupplierName" column="two_supplier_name"/>
        <result property="supplierOrgId" column="supplier_org_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="noRateAmount" column="no_rate_amount"/>
        <result property="rateAmount" column="rate_amount"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createType" column="create_type"/>
        <result property="twoSupplierIsAffirm" column="two_supplier_is_affirm"/>
        <result property="twoSupplierAffirmTime" column="two_supplier_affirm_time"/>
        <result property="supplierAffirmTime" column="supplier_affirm_time"/>
        <result property="supplierIsAffirm" column="supplier_is_affirm"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="nullifyReason" column="nullify_reason"/>
        <result property="nullifyCreatorOrgId" column="nullify_creator_org_id"/>
        <result property="nullifyCreatorId" column="nullify_creator_id"/>
        <result property="nullifyCreator" column="nullify_creator"/>
        <result property="nullifyCreated" column="nullify_created"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="state" column="state"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
        <result property="orderSn" column="order_sn"/>
        <result property="orderId" column="order_id"/>
    </resultMap>



</mapper>
