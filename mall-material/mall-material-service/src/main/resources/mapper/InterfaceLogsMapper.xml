<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.InterfaceLogsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.InterfaceLogs" id="InterfaceLogsMap">
        <result property="logId" column="log_id"/>
        <result property="secretKey" column="secret_key"/>
        <result property="methodName" column="method_name"/>
        <result property="localArguments" column="local_arguments"/>
        <result property="farArguments" column="far_arguments"/>
        <result property="isSuccess" column="is_success"/>
        <result property="result" column="result"/>
        <result property="errorInfo" column="error_info"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="gmtCreate" column="gmt_create"/>
    </resultMap>


</mapper>