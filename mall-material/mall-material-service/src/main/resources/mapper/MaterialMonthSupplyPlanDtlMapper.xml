<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.MaterialMonthSupplyPlanDtlMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.MaterialMonthSupplyPlanDtl" id="MaterialMonthSupplyPlanDtlMap">
        <result property="planDtlId" column="plan_dtl_id"/>
        <result property="materialName" column="material_name"/>
        <result property="spec" column="spec"/>
        <result property="unit" column="unit"/>
        <result property="texture" column="texture"/>
        <result property="thisPlanQty" column="this_plan_qty"/>
        <result property="sourceQty" column="source_qty"/>
        <result property="state" column="state"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>
    <update id="closePushDtl">
        update material_month_supply_plan_dtl set two_supplier_id=null , two_supplier_name=null where plan_dtl_id=#{planDtlId}

    </update>


</mapper>
