<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.plan.PlanDetailMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.plan.PlanDetail" id="PlanDetailMap">
        <result property="dtlId" column="dtl_id"/>
        <result property="billId" column="bill_id"/>
        <result property="matterId" column="matter_id"/>
        <result property="matterName" column="matter_name"/>
        <result property="spec" column="spec"/>
        <result property="matterUnit" column="matter_unit"/>
        <result property="matterUse" column="matter_use"/>
        <result property="classId" column="class_id"/>
        <result property="className" column="class_name"/>
        <result property="tradeName" column="trade_name"/>
        <result property="tradeId" column="trade_id"/>
        <result property="number" column="number"/>
        <result property="price" column="price"/>
        <result property="taxPrice" column="tax_price"/>
        <result property="amount" column="amount"/>
        <result property="taxAmount" column="tax_amount"/>
        <result property="stockNumber" column="stock_number"/>
        <result property="purchaseType" column="purchase_type"/>
        <result property="topClassId" column="top_class_id"/>
        <result property="topClassName" column="top_class_name"/>
        <result property="storageId" column="storage_id"/>
        <result property="storageName" column="storage_name"/>
        <result property="storageOrgId" column="storage_org_id"/>
        <result property="orgShort" column="org_short"/>
        <result property="creditCode" column="credit_code"/>
        <result property="consumeAmount" column="consume_amount"/>
        <result property="notConsumeAmount" column="not_consume_amount"/>
        <result property="consumeNumber" column="consume_number"/>
        <result property="notConsumeNumber" column="not_consume_number"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="modifyName" column="modify_name"/>
        <result property="modifyId" column="modify_id"/>
    </resultMap>


</mapper>