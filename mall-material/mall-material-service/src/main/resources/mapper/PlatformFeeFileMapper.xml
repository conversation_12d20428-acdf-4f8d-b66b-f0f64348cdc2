<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PlatformFeeFileMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.PlatformFeeFile" id="PlatformFeeFileMap">
        <result property="fileId" column="file_Id"/>
        <result property="name" column="name"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="relevanceType" column="relevance_type"/>
        <result property="url" column="url"/>
        <result property="fileFarId" column="file_far_id"/>
        <result property="fileType" column="file_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>