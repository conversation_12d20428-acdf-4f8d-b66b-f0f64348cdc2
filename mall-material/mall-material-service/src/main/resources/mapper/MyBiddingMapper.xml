<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.MyBiddingMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.BiddingPurchase" id="BiddingPurchaseMap">
        <result property="biddingId" column="bidding_id"/>
        <result property="biddingSn" column="bidding_sn"/>
        <result property="biddingSourceType" column="bidding_source_type"/>
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="linkName" column="link_name"/>
        <result property="linkPhone" column="link_phone"/>
        <result property="biddingExplain" column="bidding_explain"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="biddingState" column="bidding_state"/>
        <result property="referencePrice" column="reference_price"/>
        <result property="publicityState" column="publicity_state"/>
    </resultMap>
    <resultMap id="biddingMyList" type="scrbg.meplat.mall.vo.bidding.MyBiddingVo">
        <result property="title" column="title"/>
        <result property="biddingSn" column="bidding_sn"/>
        <result property="type" column="type"/>
        <result property="state" column="state"/>
        <result property="publicityState" column="publicity_state"/>
        <result property="createOrgName" column="create_org_name"/>
        <result property="bidState" column="bstate"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
    </resultMap>

    <select id="getMyBidList" resultMap="biddingMyList">
        SELECT
        p.title,
        p.type,
        p.bidding_sn,
        p.state,
        p.start_time,
        p.end_time,
        p.publicity_state,
        p.product_type,
        p.create_org_name
     ,   r.state as bstate
        FROM
            bidding_suppliers s

        inner join bidding_purchase p  ON p.bidding_id = s.bidding_id
        and s.is_delete=0
        and p.is_delete=0
      left  JOIN bidding_bid_record r ON p.bidding_id = r.bidding_id
        and  r.supplier_id = s.supplier_id
      and r.is_delete=0
        <where>
            s.supplier_id =#{bid.supplierId}
            <if test="bid.keyWords != null and bid.keyWords != ''">
                and (p.`title` LIKE CONCAT('%',#{bid.keyWords},'%') )
            </if>
            <if test="bid.title != null and bid.title != ''">
                and (p.`title` LIKE CONCAT('%',#{bid.title},'%') )
            </if>
            <if test="bid.type != null and bid.type != ''">
                and (p.`type` = #{bid.type} )
            </if>
            <if test="bid.bidState != null">
                and (r.`state` = #{bid.bidState} )
            </if>
            <if test="bid.productType != null">
                and (p.`product_type` = #{bid.productType} )
            </if>
            <if test="bid.publicityState != null and bid.publicityState != ''">
                and (p.`publicity_state`= #{bid.publicityState}  )
            </if>
            <if test="bid.startTime != null and bid.endTime != ''">
                and (p.`start_time` between #{bid.startTime} and #{bid.endTime}  )
            </if>
        </where>
        <if test="bid.orderBy !=null">
            <if test="bid.orderBy == 1">
              order by p.start_time	DESC
            </if>
            <if test="bid.orderBy == 2">
                order by p.end_time	DESC
            </if>
        </if>
    </select>
    <select id="getMyBidListCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM
        bidding_suppliers s
        inner join bidding_purchase p  ON p.bidding_id = s.bidding_id
        and s.is_delete=0
        and p.is_delete=0
        left  JOIN bidding_bid_record r ON p.bidding_id = r.bidding_id
        and  r.supplier_id = s.supplier_id
        and r.is_delete=0
        <where>
            s.supplier_id =#{bid.supplierId}
            <if test="bid.keyWords != null and bid.keyWords != ''">
                and (p.`title` LIKE CONCAT('%',#{bid.keyWords},'%') )
            </if>
            <if test="bid.title != null and bid.title != ''">
                and (p.`title` LIKE CONCAT('%',#{bid.title},'%') )
            </if>
            <if test="bid.type != null and bid.type != ''">
                and (p.`type` = #{bid.type} )
            </if>
            <if test="bid.bidState != null">
                and (r.`state` = #{bid.bidState} )
            </if>
            <if test="bid.productType != null">
                and (p.`product_type` = #{bid.productType} )
            </if>
            <if test="bid.publicityState != null and bid.publicityState != ''">
                and (p.`publicity_state`= #{bid.publicityState}  )
            </if>
            <if test="bid.startTime != null and bid.endTime != ''">
                and (p.`start_time` between #{bid.startTime} and #{bid.endTime}  )
            </if>
        </where>
    </select>

</mapper>