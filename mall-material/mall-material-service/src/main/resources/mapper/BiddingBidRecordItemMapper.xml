<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.BiddingBidRecordItemMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.BiddingBidRecordItem" id="BiddingBidRecordItemMap">
        <result property="bidRecordItemId" column="bid_record_item_id"/>
        <result property="bidRecordId" column="bid_record_id"/>
        <result property="biddingProductId" column="bidding_product_id"/>
        <result property="biddingId" column="bidding_id"/>
        <result property="bidPrice" column="bid_price"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="bidRatePrice" column="bid_rate_price"/>
        <result property="bidRateAmount" column="bid_rate_amount"/>
        <result property="bidAmount" column="bid_amount"/>
        <result property="bidTime" column="bid_time"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="bidingRemarks" column="biding_remarks"/>
    </resultMap>

	<select id="getQuotationsBySynTempSn" resultType="scrbg.meplat.mall.vo.bidding.GetBidingRecordItemVO">
		SELECT bp.bidding_sn,bpro.product_name,bpro.spec,bpro.product_texture,bpro.brand,bpro.unit,bpro.num ,bbri.net_price,bbri.fixation_price,bbri.out_factory_price,bbri.transport_price,bbri.bid_amount,bbri.bid_rate_price,bbri.bid_rate_amount from bidding_bid_record_item bbri
		LEFT JOIN bidding_purchase bp on bbri.bidding_id = bp.bidding_id
		LEFT JOIN bidding_product bpro on bbri.bidding_product_id = bpro.bidding_product_id
		where bp.synthesize_temporary_sn = #{synTemSn};
    </select>

</mapper>