<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.InvoiceDtlMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.InvoiceDtl" id="InvoiceDtlMap">
        <result property="invoiceDtlId" column="invoice_dtl_id"/>
        <result property="invoiceId" column="invoice_id"/>
        <result property="invoiceNo" column="invoice_no"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="productSn" column="product_sn"/>
        <result property="reconciliationId" column="reconciliation_id"/>
        <result property="reconciliationDtlId" column="reconciliation_dtl_id"/>
        <result property="rateAmouns" column="rate_amouns"/>
        <result property="noRateAmouns" column="no_rate_amouns"/>
        <result property="productPrice" column="product_price"/>
        <result property="productNoPrice" column="product_no_price"/>
        <result property="skuName" column="sku_name"/>
        <result property="texture" column="texture"/>
        <result property="quantity" column="quantity"/>
        <result property="unit" column="unit"/>
        <result property="reconciliationType" column="reconciliation_type"/>
    </resultMap>


</mapper>
