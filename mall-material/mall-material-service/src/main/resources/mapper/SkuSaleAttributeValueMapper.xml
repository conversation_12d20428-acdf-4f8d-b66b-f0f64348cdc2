<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.SkuSaleAttributeValueMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.SkuSaleAttributeValue" id="SkuSaleAttributeValueMap">
        <result property="skuSaleAttributeId" column="sku_sale_attribute_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="attributeId" column="attribute_id"/>
        <result property="attributeName" column="attribute_name"/>
        <result property="attributeValue" column="attribute_value"/>
        <result property="sort" column="sort"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="state" column="state"/>
        <result property="remarks" column="remarks"/>
        <result property="productType" column="product_type"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>