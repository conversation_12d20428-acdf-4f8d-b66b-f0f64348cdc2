<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.MaterialReconciliationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.MaterialReconciliation" id="MaterialReconciliationMap">
        <result property="reconciliationId" column="reconciliation_id"/>
        <result property="reconciliationNo" column="reconciliation_no"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="businessType" column="business_type"/>
        <result property="contractId" column="contract_id"/>
        <result property="contractSn" column="contract_sn"/>
        <result property="planId" column="plan_id"/>
        <result property="planSn" column="plan_sn"/>
        <result property="sourceBillId" column="source_bill_id"/>
        <result property="sourceBillNo" column="source_bill_no"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierEnterpriseId" column="supplier_enterprise_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="purchaserId" column="purchaser_id"/>
        <result property="purchaserLocalId" column="purchaser_local_id"/>
        <result property="purchaserName" column="purchaser_name"/>
        <result property="purchasingOrgId" column="purchasing_org_id"/>
        <result property="purchasingLocalOrgId" column="purchasing_local_org_id"/>
        <result property="purchasingOrgName" column="purchasing_org_name"/>
        <result property="acceptancerId" column="acceptancer_id"/>
        <result property="acceptancerName" column="acceptancer_name"/>
        <result property="reconciliationAmount" column="reconciliation_amount"/>
        <result property="settleAmount" column="settle_amount"/>
        <result property="stratTime" column="strat_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createType" column="create_type"/>
        <result property="purchaseIsAffirm" column="purchase_is_affirm"/>
        <result property="supplierIsAffirm" column="supplier_is_affirm"/>
        <result property="isPush" column="is_push"/>
        <result property="freight" column="freight"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="nullifyReason" column="nullify_reason"/>
        <result property="nullifyCreatorLocalId" column="nullify_creator_local_id"/>
        <result property="nullifyCreatorId" column="nullify_creator_id"/>
        <result property="nullifyCreator" column="nullify_creator"/>
        <result property="nullifyCreated" column="nullify_created"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="state" column="state"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
        <result property="orgName" column="org_name"/>
        <result property="orgId" column="org_id"/>
        <result property="enterpriseId" column="enterprise_id"/>
    </resultMap>

    <select id="listLedgerCount" resultType="java.lang.Integer">
        SELECT count(*) FROM (
        SELECT mr.reconciliation_id
        FROM material_reconciliation_dtl mrd
        INNER JOIN material_reconciliation mr ON mrd.reconciliation_id = mr.reconciliation_id
        INNER JOIN orders o ON mrd.order_id = o.order_id
        AND o.is_delete = 0
        AND mr.is_delete = 0
        AND mrd.is_delete = 0
        <where>
            <if test="dto.shopId != null ">
                and o.`shop_id` = #{dto.shopId}
            </if>
            <if test="dto.orgId != null ">
                and mr.`purchasing_org_id` = #{dto.orgId}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and mr.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.productType != null ">
                and mr.`reconciliation_product_type` = #{dto.productType}
            </if>
            <if test="dto.assetType != null ">
                and mr.`reconciliation_product_type` = #{dto.assetType}
            </if>
            <if test="dto.ledgerType != null ">
                and mr.`reconciliation_product_type` = #{dto.ledgerType}
            </if>
            <if test="dto.reconciliationNo != null ">
                and mr.`reconciliation_no` = #{dto.reconciliationNo}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and mrd.`material_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.purchasingOrgName != null and dto.purchasingOrgName != ''">
                and mr.`purchasing_org_name` LIKE CONCAT('%',#{dto.purchasingOrgName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                mrd.`material_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or mr.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and mr.`end_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        GROUP BY mr.reconciliation_id, mr.reconciliation_no
        ) count_result
    </select>
    <select id="ledgerList" resultType="scrbg.meplat.mall.vo.platform.ReconciliationLedgerListVo">
        select * from (
        SELECT
        mr.purchasing_org_id as purchasingOrgId,
        mr.purchasing_org_name as purchasingOrgName,
        mr.supplier_id as supplierId,
        mr.supplier_name as supplierName,
        mr.reconciliation_product_type as reconciliationProductType,
        mr.reconciliation_product_type as ReconciliationType,
        mr.reconciliation_id as reconciliationId,
        mr.reconciliation_no as reconciliationNo,
        GROUP_CONCAT(mrd.material_id) as materialId,
        GROUP_CONCAT(mrd.material_name) as materialName,
        GROUP_CONCAT(mrd.spec) as spec,
        GROUP_CONCAT(mrd.unit) as unit,
        SUM(mrd.quantity) as quantity,
        AVG(mrd.price) as price,
        AVG(mrd.no_rate_price) as noRatePrice,
        SUM(mrd.acceptance_amount) as acceptanceAmount,
        SUM(mrd.tax_amount) as taxAmount,
        SUM(mrd.acceptance_no_rate_amount) as acceptanceNoRateAmount,
        mr.start_time as startTime,
        mr.end_time as endTime,
        mr.state as state
        FROM material_reconciliation_dtl mrd
        INNER JOIN material_reconciliation mr ON mrd.reconciliation_id = mr.reconciliation_id
        INNER JOIN orders o ON mrd.order_id = o.order_id
        AND o.is_delete = 0
        AND mr.is_delete = 0
        AND mrd.is_delete = 0
        <where>
            <if test="dto.shopId != null ">
                and o.`shop_id` = #{dto.shopId}
            </if>
            <if test="dto.orgId != null ">
                and mr.`purchasing_org_id` = #{dto.orgId}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and mr.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.productType != null ">
                and mr.`reconciliation_product_type` = #{dto.productType}
            </if>
            <if test="dto.assetType != null ">
                and mr.`reconciliation_product_type` = #{dto.assetType}
            </if>
            <if test="dto.ledgerType != null ">
                and mr.`reconciliation_product_type` = #{dto.ledgerType}
            </if>
            <if test="dto.reconciliationNo != null ">
                and mr.`reconciliation_no` = #{dto.reconciliationNo}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and mrd.`material_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.purchasingOrgName != null and dto.purchasingOrgName != ''">
                and mr.`purchasing_org_name` LIKE CONCAT('%',#{dto.purchasingOrgName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                mrd.`material_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or mr.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and mr.`end_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        GROUP BY
        mr.reconciliation_id,
        mr.reconciliation_no,
        mr.purchasing_org_id,
        mr.purchasing_org_name,
        mr.supplier_id,
        mr.supplier_name,
        mr.reconciliation_product_type,
        mr.start_time,
        mr.end_time,
        mr.state
        ) b order by b.endTime desc
    </select>
    <select id="selCountAmount" resultType="java.math.BigDecimal">
        select sum(b.totalAmount) from (
        SELECT mrd.acceptance_amount as totalAmount
        FROM material_reconciliation mr
        INNER JOIN material_reconciliation_dtl mrd ON mrd.reconciliation_id = mr.reconciliation_id
        INNER JOIN orders o ON mrd.order_id = o.order_id
        AND o.is_delete = 0
        AND mr.is_delete = 0
        AND mrd.is_delete = 0
        <where>
            <if test="dto.shopId != null ">
                and o.`shop_id` = #{dto.shopId}
            </if>
            <if test="dto.orgId != null ">
                and mr.`purchasing_org_id` = #{dto.orgId}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and mr.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.productType != null ">
                and mr.`reconciliation_product_type` = #{dto.productType}
            </if>
            <if test="dto.assetType != null ">
                and mr.`reconciliation_product_type` = #{dto.assetType}
            </if>
            <if test="dto.ledgerType != null ">
                and mr.`reconciliation_product_type` = #{dto.ledgerType}
            </if>
            <if test="dto.reconciliationNo != null ">
                and mr.`reconciliation_no` = #{dto.reconciliationNo}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and mrd.`material_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.purchasingOrgName != null and dto.purchasingOrgName != ''">
                and mr.`purchasing_org_name` LIKE CONCAT('%',#{dto.purchasingOrgName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                mrd.`material_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or mr.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and mr.`end_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        ) b
    </select>
    <select id="selCountNoRateAmount" resultType="java.math.BigDecimal">
        select sum(b.countNoRateAmount) from (
        SELECT mrd.acceptance_no_rate_amount as countNoRateAmount
        FROM material_reconciliation mr
        left JOIN material_reconciliation_dtl mrd ON mrd.reconciliation_id = mr.reconciliation_id
        left JOIN orders o ON mrd.order_id = o.order_id
        AND o.is_delete = 0
        AND mr.is_delete = 0
        AND mrd.is_delete = 0
        <where>
            <if test="dto.shopId != null ">
                and o.`shop_id` = #{dto.shopId}
            </if>
            <if test="dto.orgId != null ">
                and mr.`purchasing_org_id` = #{dto.orgId}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and mr.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.productType != null ">
                and mr.`reconciliation_product_type` = #{dto.productType}
            </if>
            <if test="dto.assetType != null ">
                and mr.`reconciliation_product_type` = #{dto.assetType}
            </if>
            <if test="dto.ledgerType != null ">
                and mr.`reconciliation_product_type` = #{dto.ledgerType}
            </if>
            <if test="dto.reconciliationNo != null ">
                and mr.`reconciliation_no` = #{dto.reconciliationNo}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and mrd.`material_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.purchasingOrgName != null and dto.purchasingOrgName != ''">
                and mr.`purchasing_org_name` LIKE CONCAT('%',#{dto.purchasingOrgName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                mrd.`material_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or mr.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and mr.`end_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        ) b
    </select>
</mapper>