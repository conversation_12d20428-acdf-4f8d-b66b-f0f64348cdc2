<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PlatformBalanceOperateMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.PlatformBalanceOperate" id="PlatformBalanceOperateMap">
        <result property="platformBalanceOperateId" column="platform_balance_operate_id"/>
        <result property="title" column="title"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="relevanceType" column="relevance_type"/>
        <result property="amount" column="amount"/>
        <result property="beforeAmount" column="before_amount"/>
        <result property="afterAmount" column="after_amount"/>
        <result property="operateId" column="operate_id"/>
        <result property="operateUn" column="operate_un"/>
        <result property="operateType" column="operate_type"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>