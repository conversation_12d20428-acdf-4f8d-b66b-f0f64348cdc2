<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ProcessConfigMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ProcessConfig" id="ProcessConfigMap">
        <result property="processId" column="process_id"/>
        <result property="processName" column="process_name"/>
        <result property="systemName" column="system_name"/>
        <result property="systemNo" column="system_no"/>
        <result property="remark" column="remark"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="modifyId" column="modify_id"/>
        <result property="modifyName" column="modify_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>
    
    <resultMap id="getProcessConfigDtlMap" type="scrbg.meplat.mall.vo.processConfig.ProcessConfigDtlVO">
        <result column="process_id" property="processId"/>
        <result column="process_name" property="processName"/>
        <result column="system_name" property="systemName"/>
        <result column="system_no" property="systemNo"/>
        <result column="remark" property="remark"/>
        <collection property="processConfigDtlItemVOs" ofType="scrbg.meplat.mall.vo.processConfig.ProcessConfigDtlItemVO">
            <result column="process_node_id" property="processNodeId"/>
	        <result column="node_no" property="nodeNo"/>
	        <result column="node_name" property="nodeName"/>
	        <result column="process_role_id" property="processRoleId"/>
	        <result column="role_no" property="roleNo"/>
	        <result column="role_name" property="roleName"/>
	        <result column="process_user_id" property="processUserId"/>
	        <result column="user_id" property="userId"/>
	        <result column="user_name" property="userName"/>
        </collection>
    </resultMap>
    
    <!-- 根据流程配置id获取流程配置详情 -->
    <select id="getProcessConfigDtlById" resultMap="getProcessConfigDtlMap">
    SELECT pc.process_id,pc.process_name,pn.process_node_id,pn.node_no,pn.node_name,pr.process_role_id,pr.role_no,pr.role_name,pu.process_user_id,pu.user_id,pu.user_name
	FROM process_config pc
	LEFT JOIN process_node pn on pc.process_id = pn.process_id
	LEFT JOIN process_role pr on pn.process_node_id = pr.process_node_id
	LEFT JOIN process_user pu on pr.process_role_id = pu.process_role_id
	<where>
		pc.is_delete = 0
        <if test="processId != null">
            and pc.process_id = #{processId}
        </if>
     </where>
	ORDER BY pc.process_id asc,pn.sort asc,pr.sort asc,pu.sort asc
    </select>

    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT MAX(sort) FROM process_config
    </select>
</mapper>