<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.StationMessageMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.StationMessage" id="StationMessageMap">
        <result property="stationMessageId" column="station_message_id"/>
        <result property="sendId" column="send_id"/>
        <result property="sendName" column="send_name"/>
        <result property="sendType" column="send_type"/>
        <result property="sendCode" column="send_code"/>
        <result property="allRead" column="all_read"/>
        <result property="isFile" column="is_file"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="sendDate" column="send_date"/>
        <result property="messageType" column="message_type"/>
        <result property="state" column="state"/>
        <result property="sort" column="sort"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>

    <select id="listStationMessageReceiveById" resultMap="StationMessageMap" resultType="list">
        select sm.*,smr.station_message_id
        from station_message_receive smr
        left join station_message sm on sm.station_message_id = smr.station_message_id
        <where>
            sm.is_delete = 0
            <if test="dto.keywords != null and dto.keywords != ''">
                and (sm.`send_name` LIKE CONCAT('%',#{dto.keywords},'%') or sm.`title` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.title != null and dto.title != ''">
                and (sm.`title` LIKE CONCAT('%',#{dto.title},'%'))
            </if>
            <if test="dto.sendName != null and dto.sendName != ''">
                and (sm.`send_name` LIKE CONCAT('%',#{dto.sendName},'%'))
            </if>
            <if test="dto.state != null">
                and sm.`state` = #{dto.state}
            </if>
        </where>
        <if test="dto.orderBy == 1">
            order by  sm.`sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by  sm.`gmt_create` desc
        </if>
        limit #{dto.start},#{dto.limit}
    </select>

    <select id="countStationMessageReceiveById" resultType="int" parameterType="scrbg.meplat.mall.dto.mail.StationMessageReceiveDTO">
        select count(*)
        from station_message_receive smr
        left join station_message sm on sm.station_message_id = smr.station_message_id
        <where>
            sm.is_delete = 0
            <if test="dto.keywords != null and dto.keywords != ''">
                and (sm.`send_name` LIKE CONCAT('%',#{dto.keywords},'%') or sm.`title` LIKE CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.state != null">
                and sm.`state` = #{dto.state}
            </if>
        </where>
        <if test="dto.orderBy == 1">
            order by  sm.`sort` desc
        </if>
        <if test="dto.orderBy == 2">
            order by  sm.`gmt_create` desc
        </if>
    </select>
    <select id="findByCondition" resultType="scrbg.meplat.mall.vo.product.material.StationMessageVo">
        select s.*,r.is_read AS isRead,
               r.gmt_create as  gmtCreate ,
               r.station_message_receive_id as stationMessageReceiveId
        from  station_message  s
        left join station_message_receive r
         on s.station_message_id=r.station_message_id
        where  ${ew.sqlSegment}

    </select>


</mapper>
