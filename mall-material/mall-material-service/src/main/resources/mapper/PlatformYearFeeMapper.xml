<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PlatformYearFeeMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.PlatformYearFee" id="PlatformYearFeeMap">
        <result property="platformYearFeeId" column="platform_year_fee_id"/>
        <result property="platformYearFeeNu" column="platform_year_fee_nu"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="serveType" column="serve_type"/>
        <result property="paymentAmount" column="payment_amount"/>
        <result property="serveStartTime" column="serve_start_time"/>
        <result property="serveEndTime" column="serve_end_time"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
        <result property="version" column="version"/>
    </resultMap>

    <select id="totalCountFree" resultType="scrbg.meplat.mall.dto.free.TotalCountFreeVO">
        SELECT * FROM (
        SELECT enterprise_name,arrearage,
        (SELECT serve_end_time
        FROM platform_year_fee
        WHERE enterprise_id = ei.enterprise_id COLLATE utf8mb4_0900_ai_ci
        AND is_delete = 0
        AND serve_type = 1) AS shopEndTime,
        (SELECT serve_end_time
        FROM platform_year_fee
        WHERE enterprise_id = ei.enterprise_id COLLATE utf8mb4_0900_ai_ci
        AND is_delete = 0
        AND serve_type = 2) AS bidEndTime,
        (SELECT COALESCE(sum( residue_pay_fee ),0)
        FROM platform_deal_fee_dtl
        WHERE enterprise_id = ei.enterprise_id COLLATE utf8mb4_0900_ai_ci
        AND state = 0
        AND is_delete = 0
        AND serve_type = 1) AS shopPayFree,
        (SELECT COALESCE(sum( residue_pay_fee ),0)
        FROM platform_deal_fee_dtl
        WHERE enterprise_id = ei.enterprise_id COLLATE utf8mb4_0900_ai_ci
        AND state = 0
        AND is_delete = 0
        AND serve_type = 2) AS contractPayFree,
        CASE WHEN (SELECT COALESCE(sum( residue_pay_fee ),0)
        FROM platform_deal_fee_dtl
        WHERE enterprise_id = ei.enterprise_id COLLATE utf8mb4_0900_ai_ci
        AND state = 0
        AND is_delete = 0
        AND serve_type = 1) > arrearage THEN 1 ELSE 0 END AS isShopExceedArrearage,
        CASE WHEN (SELECT COALESCE(sum( residue_pay_fee ),0)
        FROM platform_deal_fee_dtl
        WHERE enterprise_id = ei.enterprise_id COLLATE utf8mb4_0900_ai_ci
        AND state = 0
        AND is_delete = 0
        AND serve_type = 2) > arrearage THEN 1 ELSE 0 END AS isBidExceedArrearage
        FROM enterprise_info ei where is_delete = 0
        AND short_code IS NULL AND shu_dao_flag = 0 AND is_supplier = 2
        <![CDATA[
            AND enterprise_type <> 2
                 ]]>
        ) as resultTable
        <where>
            <if test="dto.startShopAuditTime != null and dto.startShopAuditTime != ''">
                <![CDATA[
                AND shopEndTime >= #{dto.startShopAuditTime}
                 ]]>
            </if>
            <if test="dto.endShopAuditTime != null and dto.endShopAuditTime != ''">
                <![CDATA[
                AND shopEndTime <= #{dto.endShopAuditTime}
                ]]>
            </if>
            <if test="dto.startBidAuditTime != null and dto.startBidAuditTime != ''">
                <![CDATA[
                AND bidEndTime >= #{dto.startBidAuditTime}
                 ]]>
            </if>
            <if test="dto.endBidAuditTime != null and dto.endBidAuditTime != ''">
                <![CDATA[
                AND bidEndTime <= #{dto.endBidAuditTime}
                ]]>
            </if>
            <if test="dto.enterpriseNames != null and !dto.enterpriseNames.isEmpty()">
                and enterprise_name in
                <foreach item="item" collection="dto.enterpriseNames" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and enterprise_name LIKE CONCAT('%',#{dto.keywords},'%')
            </if>
            <if test="dto.enterpriseName != null and  dto.enterpriseName != ''">
                and enterprise_name = #{dto.enterpriseName}
            </if>
            <if test="dto.isArrearage != null and dto.isArrearage == 1">
                and (contractPayFree + shopPayFree) > 0
            </if>
            <if test="dto.isArrearage != null and dto.isArrearage == 0">
                and (contractPayFree + shopPayFree) = 0
            </if>
            <if test="dto.isShopExceedArrearage != null and dto.isShopExceedArrearage == 1">
                and isShopExceedArrearage = 1
            </if>
            <if test="dto.isShopExceedArrearage != null and dto.isShopExceedArrearage == 0">
                and isShopExceedArrearage = 0
            </if>
            <if test="dto.isBidExceedArrearage != null and dto.isBidExceedArrearage == 1">
                and isBidExceedArrearage = 1
            </if>
            <if test="dto.isBidExceedArrearage != null and dto.isBidExceedArrearage == 0">
                and isBidExceedArrearage = 0
            </if>

            <if test="dto.isShopOut != null">
                <if test="dto.isShopOut == 0">
                    and shopEndTime > NOW()
                </if>
                <if test="dto.isShopOut == 1">
                    <![CDATA[
                    and (shopEndTime <= NOW() or shopEndTime is null)
                    ]]>
                </if>
            </if>
            <if test="dto.isBidOut != null">
                <if test="dto.isBidOut == 0">
                    and bidEndTime > NOW()
                </if>
                <if test="dto.isBidOut == 1">
                    <![CDATA[
                    and (bidEndTime <= NOW() or bidEndTime is null)
                    ]]>
                </if>
            </if>
        </where>

    </select>


</mapper>