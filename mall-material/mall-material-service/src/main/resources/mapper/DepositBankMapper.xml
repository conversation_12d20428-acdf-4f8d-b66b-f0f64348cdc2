<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.DepositBankMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.DepositBank" id="DepositBankMap">
        <result property="bankId" column="bank_id"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankCount" column="bank_count"/>
        <result property="phone" column="phone"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="isDefault" column="is_default"/>
        <result property="isDelete" column="is_delete"/>
        <result property="state" column="state"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>