<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PlatformYearFeeDtlMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.PlatformYearFeeDtl" id="PlatformYearFeeDtlMap">
        <result property="platformYearFeeDtlId" column="platform_year_fee_dtl_id"/>
        <result property="relevanceId" column="relevance_id"/>
        <result property="relevanceType" column="relevance_type"/>
        <result property="serveType" column="serve_type"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="serveAmount" column="serve_amount"/>
        <result property="serveStartTime" column="serve_start_time"/>
        <result property="serveEndTime" column="serve_end_time"/>
        <result property="paymentDuration" column="payment_duration"/>
        <result property="paymentDurationType" column="payment_duration_type"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
        <result property="version" column="version"/>
    </resultMap>


</mapper>