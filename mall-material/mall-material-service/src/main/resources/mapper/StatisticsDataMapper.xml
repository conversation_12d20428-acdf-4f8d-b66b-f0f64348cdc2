<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.StatisticsDataMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.StatisticsData" id="StatisticsDataMap">
        <result property="statisticsId" column="statistics_id"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="shopNum" column="shop_num"/>
        <result property="orderShipTotalAmount" column="order_ship_total_amount"/>
        <result property="ordersNum" column="orders_num"/>
        <result property="enterpriseNum" column="enterprise_num"/>
        <result property="upProductNum" column="up_product_num"/>
        <result property="productNum" column="product_num"/>
        <result property="ordersTotalAmount" column="orders_total_amount"/>
        <result property="userNum" column="user_num"/>
    </resultMap>


</mapper>
