<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ProductCollectMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ProductCollect" id="ProductCollectMap">
        <result property="collectId" column="collect_id"/>
        <result property="userId" column="user_id"/>
        <result property="productId" column="product_id"/>
        <result property="productType" column="product_type"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>
    <delete id="deleteBycollectId">
        DELETE
        FROM product_collect
        WHERE collect_id = #{collectId};
    </delete>
    <!--    根据商品id批量删除自身收藏商品-->
    <delete id="delCollectByProductIds">
        DELETE FROM product_collect WHERE 1=1
        <if test="userId!=null and userId!=''">
            AND user_id =#{userId}
        </if>

        <if test="collectType!=null and collectType!=''">
            AND collect_type =#{collectType}
        </if>
        <if test="ids!=null and ids!=''">
            AND  product_id  IN
            <foreach collection='ids' open='(' item='id_' separator=',' close=')'>#{id_}</foreach>
        </if>
    </delete>


</mapper>
