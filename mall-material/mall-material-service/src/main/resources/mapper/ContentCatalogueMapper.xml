<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ContentCatalogueMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ContentCatalogue" id="ContentCatalogueMap">
        <result property="catalogueId" column="catalogue_id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="state" column="state"/>
        <result property="comments" column="comments"/>
        <result property="sort" column="sort"/>
        <result property="programaCode" column="programa_code"/>
        <result property="isHome" column="Is_home"/>
        <result property="isNavigation" column="Is_navigation"/>
        <result property="pageUrl" column="page_url"/>
        <result property="parentId" column="parent_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="remarks" column="remarks"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>