package ${package.Controller};
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import ${package.Service}.${table.serviceName};
import ${package.Entity}.${entity};
import ${cfg.dtoPackage}.${entity}DTO;

import javax.validation.Valid;
import java.util.List;

/**
 * @描述：$!{table.comment}控制类
 * @作者: ${author}
 * @日期: ${date}
 */
@RestController
@RequestMapping("/${table.entityPath}")
@Api(tags = "$!{table.comment}")
public class ${table.controllerName}{

        @Autowired
        public ${table.serviceName} ${table.entityPath}Service;

        @GetMapping("/page")
        @ApiOperation(value = "根据实体属性分页查询")
        public PageR<${entity}> listByPage(@RequestParam ${entity}DTO dto) {
            PageUtils page = ${table.entityPath}Service.queryPage(dto);
            return PageR.success(page);
        }

        @GetMapping("/{id}")
        @ApiOperation(value = "根据主键查询")
        @ApiImplicitParams({
                @ApiImplicitParam(name = "id", value = "ID", required = true,
                        dataType = "String", paramType = "path")
        })
        public R<${entity}> findById(@PathVariable String id) {
            ${entity} ${table.entityPath} =${table.entityPath}Service.getById(id);
            return R.success(${table.entityPath});
        }

        @PostMapping
        @ApiOperation(value = "新增")
        @LogRecord(title = "xx管理", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)

        public R save(@RequestBody @Valid ${entity} ${table.entityPath}) {
                ${table.entityPath}Service.create(${table.entityPath});
            return R.success();
        }

        @PutMapping
        @ApiOperation(value = "修改")
        @LogRecord(title = "xx管理", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)

        public R update(@RequestBody @Valid ${entity} ${table.entityPath}) {
                ${table.entityPath}Service.update(${table.entityPath});
            return R.success();
        }

        @DeleteMapping("/{id}")
        @ApiOperation(value = "根据主键删除")
        @ApiImplicitParams({
                @ApiImplicitParam(name = "id", value = "ID", required = true,
                        dataType = "String", paramType = "path")
        })
        @LogRecord(title = "xx管理", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
        public R delete(@PathVariable String id) {
                ${table.entityPath}Service.delete(id);
            return R.success();
        }


        @DeleteMapping("/batch")
        @ApiOperation(value = "根据主键批量删除")
        @LogRecord(title = "xx管理", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)

        public R deleteBatch(@RequestBody List<String> ids) {
                ${table.entityPath}Service.deleteBatch(ids);
            return R.success();
        }
        }

