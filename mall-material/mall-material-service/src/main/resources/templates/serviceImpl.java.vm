package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${cfg.dtoPackage}.${entity}DTO;
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import com.scrbg.common.utils.PageUtils;
import java.util.List;

/**
 * @描述：$!{table.comment} 服务类
 * @作者: ${author}
 * @日期: ${date}
 */
@Service
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName}{

        @Override
        public PageUtils queryPage(${entity}DTO dto) {
            QueryWrapper<${entity}> queryWrapper = new QueryWrapper<>();
            //dto传值给queryWrapper作为查询条件
            IPage<${entity}> page = this.page(
                    new Page<>(dto.getPage(), dto.getLimit()),
                    queryWrapper
            );
            return new PageUtils(page);
        }

        @Override
        @Transactional(rollbackFor = Exception.class)
        @NotResubmit
        public void create(${entity} ${table.entityPath}) {
            super.save(${table.entityPath});
        }

        @Override
        @Transactional(rollbackFor = Exception.class)
        @NotResubmit
        public void update(${entity} ${table.entityPath}) {
            super.updateById(${table.entityPath});
        }

        @Override
        public ${entity} getById(String id) {
            return super.getById(id);
        }

        @Override
        @Transactional(rollbackFor = Exception.class)
        @NotResubmit
        public void delete(String id) {
            super.removeById(id);
        }

        @Override
        @Transactional(rollbackFor = Exception.class)
        @NotResubmit
        public void deleteBatch(List<String> ids) {
            super.removeByIds(ids);
        }
}
