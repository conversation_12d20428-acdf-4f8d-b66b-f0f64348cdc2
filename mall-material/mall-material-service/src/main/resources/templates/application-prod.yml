spring:
  rabbitmq:
    #    host: ************* # 主机名
    #    port: 5672 # 端口
    #    virtual-host: / # 虚拟主机
    #    username: root # 用户名
    #    password: root # 密码
    host: ************** # 主机名
    port: 5672 # 端口
    virtual-host: / # 虚拟主机
    username: mmcp # 用户名
    password: Ms1q2w3e # 密码
  redis:
    host: ************
    port: 6380
#    host: localhost
#    port: 6379
    password: tl@o23T#19N#
  cache:
    type: redis # 使用redis作为缓存
    redis:
      #      time-to-live: 3600s # 过期时间
      # key-prefix: CACHE_ # 会导致自己在@Cacheable里设置的名字失效，
      use-key-prefix: true # key值加前缀，不声明默认是使用区域作为前缀
      cache-null-values: true # 缓存控制
  #数据源配置
  datasource:
    username: root
    password: qwert@1234aa%.*
#    username: mall
#    password: Qwert@1234aa%.*
#    url: *******************************************************************************************************************************************************************
    url: *******************************************************************************************************************************************************************
#    url: *************************************************************************************************************************************************************************

#    password: 123456789
#    url: ******************************************************************************************************************************************************************
#    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    driver-class-name: com.mysql.cj.jdbc.Driver
  #云配置
  cloud:
    nacos:
      discovery:
        server-addr: ************:9848
        register-enabled: true
        ip: ************
redisson:
  singleserverconfig:
    address: "redis://${spring.redis.host}:${spring.redis.port}"
    password: ${spring.redis.password}
    database: 0

mall:
  # 访问TT登录接口
  prodPcwp2Url: "https://pcwp2-api.scrbg.com"
  #  访问pcwp2业务接口
  prodPcwp2Url02: "https://pcwp2.scrbg.com/thirdApi"
  #  访问pcwp2Token
  thirdApiToken: "RHI5YW44ek4sMTY4MTEzMDQ1OQ.lWswCMpG4b2Cr2Bz1er--a-joeFSmdJDw9rPIBB3Q2o8WvGwjAbhmg"
  #  isDPlatformAdminOrgId: "1575170728938962947"
  #  装备平台管理员组织id
  isDPlatformAdminOrgId: "af1f01246cc6-ab00-084c-79e7-76f6022e"
## 物资平台管理员组织id
  isMPlatformAdminOrgId: "aa5422473bfb-aeea-9146-8ce7-04ff3c5e"
# # pcwp1 业务合同接口地址
  pcwp1ContactUrl: "http://cpms_cbm.scrbg.com"
  # pcwp1 业务计划接口地址
  pcwp1PlanUrl: "http://cpms_material.scrbg.com"
  #//pcwp1收料单推送
  pcwpPurchase: "http://cpms_material.scrbg.com/json.rpc"
  isApiImportProductBradDispose: 2 # 1品牌自动新增（品牌是必传）2无品牌不保存，有品牌判断是否存在不存在报错
#  正式
  profilesActive: "prod"
#  服务器模板文件地址
  templateFormUrl: "/templateForm"
  isShowCode: 1 # 是否显示开发中的代码
  isCountPlanOrderNum: 0 # 是否使用计划数量统计
  isNotRateAmount: 1 # 是否使用累加不含税总金额
  isLoginAuthQuery: 0 # 是否查询登陆权限
  isContractConsumeNum: 1 # 是否使用pcwp记录商城合同数量
  isPlatformFee: 0 # 是否使用平台计费功能
  selectMaterialMonthSupplyPlan: 1 # 是否使用完结计划  1 使用  0不使用
  ossPrefix: "https://mmcp.scrbg.com"
  changAmountAndTaxPlanAmount: 1 # //2024年05月15号  应pcwp要求 Amount传不含税，NoTaxPlanAmount也要改成含税TaxPlanAmount
  isPCWPLogin: 1
#  templateFormUrl: "D://work//templateFormUrl"
knife4j:
  production: true

seata:
  enabled: false
  application-id: mall-material
  tx-service-group: my_test_tx_group
  enable-auto-data-source-proxy: true
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: 192.168.90.181:8848
      group: SEATA_GROUP
      cluster: default
      namespace: eeba1f43-adcf-449f-bc82-754550cce91b
  config:
    type: nacos
    nacos:
      server-addr: 192.168.90.181:8848
      group: SEATA_GROUP
      namespace: eeba1f43-adcf-449f-bc82-754550cce91b
  service:
    vgroup-mapping:
      my_test_tx_group: default
    disable-global-transaction: false
  client:
    rm:
      report-success-enable: false
      lock:
        retry-interval: 20 #校验或占用全局锁重试间隔 默认10,单位毫秒
        retry-times: 60 #校验或占用全局锁重试次数 默认30
minio:
  endpoint: http://192.168.91.1:9000 #Minio服务所在地址
  accessKey: ALJLSOSNNFSD #访问的key
  secretKey: wLSDJFLJA!@!dlfjalHlsdj12 #访问的秘钥
#日志配置
#logging:
#  level:
#    srig.dcmp.base: error

#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#seata:
#  enabled: true
#  application-id: demo
#  tx-service-group: my_test_tx_group
#  enable-auto-data-source-proxy: true
#  registry:
#    type: nacos
#    nacos:
#      application: seata-server
#      server-addr: 192.168.100.100:8848
#      group: SEATA_GROUP
#      cluster: default
#  config:
#    type: nacos
#    nacos:
#      server-addr: 192.168.100.100:8848
#      group: SEATA_GROUP
#      namespace: eeba1f43-adcf-449f-bc82-754550cce91b
#  service:
#    vgroup-mapping:
#      my_test_tx_group: default
#    disable-global-transaction: false
#  client:
#    rm:
#      report-success-enable: false
#      lock:
#        retry-interval: 20 #校验或占用全局锁重试间隔 默认10,单位毫秒
#        retry-times: 60 #校验或占用全局锁重试次数 默认30
#
#spring:
#  #数据源配置
#  datasource:
#    username: root
#    password: asdw@123
#    url: ***********************************************************************************************************************************************************
#    driver-class-name: com.mysql.cj.jdbc.Driver
#  #云配置
#  cloud:
#    nacos:
#      discovery:
#        register-enabled: true
#        server-addr: 192.168.100.100:8848

