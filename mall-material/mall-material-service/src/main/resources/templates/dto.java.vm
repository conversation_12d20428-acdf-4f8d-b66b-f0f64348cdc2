package ${cfg.dtoPackage};

import com.scrbg.common.utils.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.math.BigDecimal;

/**
 * @描述：$!{table.comment}查询参数
 * @作者: ${author}
 * @日期: ${date}
 */
@ApiModel(value = "$!{table.comment}查询参数")
@Data
@EqualsAndHashCode(callSuper = true)
public class ${entity}DTO extends PageParam implements Serializable {

    private static final long serialVersionUID = 1L;

    #foreach($field in ${table.fields})
        #if("$!field.comment" != "")
            #if(!(${field.propertyName}=="sort"||${field.propertyName}=="mallType"||${field.propertyName}=="gmtCreate"||${field.propertyName}=="gmtModified"||${field.propertyName}=="founderName"||${field.propertyName}=="founderId"||${field.propertyName}=="remarks"||${field.propertyName}=="isDelete"||($!field.propertyName.indexOf('Id') != -1 && ${foreach.index} == '0')))
    @ApiModelProperty(value = "${field.comment}")
    private ${field.propertyType} ${field.propertyName};#if($foreach.hasNext)#end

            #end
        #end
    #end
}