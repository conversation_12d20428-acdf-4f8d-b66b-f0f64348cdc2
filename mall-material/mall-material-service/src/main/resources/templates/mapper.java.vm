package ${package.Mapper};

import ${package.Entity}.${entity};
import ${cfg.dtoPackage}.${entity}DTO;
import ${superMapperClassPackage};
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;

/**
 * @描述：$!{table.comment} Mapper 接口
 * @作者: ${author}
 * @日期: ${date}
 */
@Mapper
@Repository
public interface ${table.mapperName} extends ${superMapperClass}<${entity}> {

}