<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="${package.Mapper}.${table.mapperName}">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="${package.Entity}.${entity}" id="${entity}Map">
#foreach($column in ${table.fields})
        <result property="${column.propertyName}" column="${column.columnName}"/>
#end
    </resultMap>


</mapper>