/*
 Navicat Premium Data Transfer

 Source Server         : **************
 Source Server Type    : MySQL
 Source Server Version : 80023
 Source Host           : **************:3306
 Source Schema         : mall

 Target Server Type    : MySQL
 Target Server Version : 80023
 File Encoding         : 65001

 Date: 03/02/2023 08:58:00
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for category_columns
-- ----------------------------
DROP TABLE IF EXISTS `category_columns`;
CREATE TABLE `category_columns`  (
                                     `column_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '栏目id',
                                     `column_number` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目编号',
                                     `column_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目名称',
                                     `sort` int NULL DEFAULT NULL COMMENT '排序值',
                                     `state` tinyint NULL DEFAULT NULL COMMENT '栏目状态（1：启用 0：停用）',
                                     `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                     `gmt_modified` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                     `gmt_release` datetime NULL DEFAULT NULL COMMENT '发布时间',
                                     `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                                     `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                     `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注信息',
                                     `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除（-1：删除  0：未删除）',
                                     `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型（0：物资 1：装备）',
                                     PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of category_columns
-- ----------------------------
INSERT INTO `category_columns` VALUES ('1602592383261327362', 'lm1602592383261327361', '大宗材料', 55, 1, '2022-12-13 17:12:54', '2022-12-30 10:37:52', '2022-12-22 16:26:34', NULL, NULL, NULL, 0, 0);
INSERT INTO `category_columns` VALUES ('1621083634654572546', 'lm1621083634654572545', '低值易耗品', 2, 1, '2023-02-02 17:50:31', '2023-02-02 17:50:31', NULL, NULL, NULL, NULL, 0, 0);
INSERT INTO `category_columns` VALUES ('1621311516027977730', 'lm1621311516007006209', '办公用品', 3, 1, '2023-02-03 08:56:03', '2023-02-03 08:56:03', NULL, NULL, NULL, NULL, 0, 0);

SET FOREIGN_KEY_CHECKS = 1;
