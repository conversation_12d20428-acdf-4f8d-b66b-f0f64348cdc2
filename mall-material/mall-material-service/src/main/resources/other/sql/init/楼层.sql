/*
 Navicat Premium Data Transfer

 Source Server         : **************
 Source Server Type    : MySQL
 Source Server Version : 80023
 Source Host           : **************:3306
 Source Schema         : mall

 Target Server Type    : MySQL
 Target Server Version : 80023
 File Encoding         : 65001

 Date: 03/02/2023 08:58:43
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for floor
-- ----------------------------
DROP TABLE IF EXISTS `floor`;
CREATE TABLE `floor`  (
                          `floor_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '楼层id',
                          `column_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目id',
                          `column_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '栏目名称',
                          `floor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '楼层名称',
                          `floor_name_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '楼层名称后小字',
                          `img_url_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主图id',
                          `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '楼层主图链接',
                          `main_img_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '点击主图链接地址',
                          `floor_product_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品类别',
                          `use_page` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '使用页面（枚举类型）待讨论',
                          `gmt_release` datetime NULL DEFAULT NULL COMMENT '发布时间',
                          `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
                          `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
                          `state` tinyint NULL DEFAULT NULL COMMENT '楼层状态（1：显示  0：不显示）',
                          `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
                          `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
                          `sort` int NULL DEFAULT NULL COMMENT '排序',
                          `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
                          `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
                          `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
                          PRIMARY KEY (`floor_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '客户端商品展示楼层' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of floor
-- ----------------------------
INSERT INTO `floor` VALUES ('1621081910745296897', '1602592383261327362', '大宗材料', '电线电缆2', '精细挑选 为您选购', '1621081719976947714', '/mall/material/SRBC/20230202/d5eaaec5cb5153f5e957abeac1fdf00-20230202174254.png', NULL, NULL, NULL, NULL, '2023-02-02 17:43:40', '2023-02-03 08:57:01', 1, NULL, NULL, 2, NULL, 0, 0);
INSERT INTO `floor` VALUES ('1621082395095134209', '1602592383261327362', '大宗材料', '电线电缆', '精细挑选 为您选购', '1621082344768860161', '/mall/material/SRBC/20230202/d5eaaec5cb5153f5e957abeac1fdf00-20230202174523.png', NULL, NULL, NULL, NULL, '2023-02-02 17:45:36', '2023-02-03 08:57:01', 1, NULL, NULL, 3, NULL, 0, 0);
INSERT INTO `floor` VALUES ('1621083906701324290', '1602592383261327362', '大宗材料', '电线电缆3', '精细挑选 为您选购', '1621083836741513217', '/mall/material/SRBC/20230202/d5eaaec5cb5153f5e957abeac1fdf00-20230202175119.png', NULL, NULL, NULL, NULL, '2023-02-02 17:51:36', '2023-02-03 08:57:01', 1, NULL, NULL, 1, NULL, 0, 0);

SET FOREIGN_KEY_CHECKS = 1;
