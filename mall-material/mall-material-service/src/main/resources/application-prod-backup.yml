spring:
  rabbitmq:
    host: ************** # 主机名
    port: 5672 # 端口
    virtual-host: / # 虚拟主机
    username: mmcp # 用户名
    password: Ms1q2w3e # 密码
  redis:
    port: 6379
    host: ************* # 主机名
    sentinel:
      master: mymaster
      nodes:
        - *************:26379
        - *************:26379
        - *************:26379
      password: 9ImAtEIE  # sentinel 认证密码（如果有）
    password: 9ImAtEIE       # Redis 服务器密码
  cache:
    type: redis # 使用redis作为缓存
    redis:
      #      time-to-live: 3600s # 过期时间
      # key-prefix: CACHE_ # 会导致自己在@Cacheable里设置的名字失效，
      use-key-prefix: true # key值加前缀，不声明默认是使用区域作为前缀
      cache-null-values: true # 缓存控制
  #数据源配置
  datasource:
    username: root
    password: 9ImAtEIE
#    username: mall
#    password: Qwert@1234aa%.*
    #    url: *******************************************************************************************************************************************************************
    url: ***************************************************************************************************************************************************************************************
    #    url: *************************************************************************************************************************************************************************

    #    password: 123456789
    #    url: ******************************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      db-type: mysql
      # 下面为连接池的补充设置，应用到上面所有数据源中
      # 初始化大小，最小，最大
      initial-size: 5
      # 最小连接池数量
      min-idle: 5
      # 最大连接池数量
      max-active: 50
      # 配置获取连接等待超时的时间
      max-wait: 60000
      #申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
      test-while-idle: true
      #既作为检测的间隔时间又作为testWhileIdel执行的依据
      time-between-eviction-runs-millis: 60000
      #销毁线程时检测当前连接的最后活动时间和当前时间差大于该值时，关闭当前连接
      min-evictable-idle-time-millis: 30000
      #用来检测连接是否有效的sql 必须是一个查询语句
      #mssql中为 select 1
      #mysql中为 select 'x'
      #oracle中为 select 1 from dual
      validation-query: select 'x'
      #申请连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
      test-on-borrow: false
      #归还连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
      test-on-return: false
      #当数据库抛出不可恢复的异常时,抛弃该连接
      #exception-sorter: true
      #是否缓存preparedStatement,mysql5.5+建议开启
      pool-prepared-statements: true
      #当值大于0时poolPreparedStatements会自动修改为true
      max-pool-prepared-statement-per-connection-size: 20
      #配置扩展插件
      #配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      #通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      #合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true
      #设置访问druid监控页的账号和密码,默认没有。（通过http://host:port/druid/index.html进入druid监控界面）
      stat-view-servlet:
        login-username: admin
        login-password: lqlRRjEqVSU5H5n2
        enabled: true
        url-pattern: /druidBoard/*
        allow: ""
        deny: ""
      #开启web filter
      web-stat-filter:
        enabled: true
  #云配置
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        username: nacos # Nacos 用户名
        password: 9ImAtEIE # Nacos 密码
        register-enabled: false
        ip: *************
redisson:
  singleserverconfig:
#    address: "redis://${spring.redis.host}:${spring.redis.port}"
    address: "redis://*************:6379"
#    password: ${spring.redis.password}
    password: 9ImAtEIE
    database: 0

mall:
  # 访问TT登录接口
  prodPcwp2Url: "https://pcwp2-api.scrbg.com"
  #  访问pcwp2业务接口
  prodPcwp2Url02: "https://pcwp2.scrbg.com/thirdApi"
  #  访问pcwp2Token
  thirdApiToken: "RHI5YW44ek4sMTY4MTEzMDQ1OQ.lWswCMpG4b2Cr2Bz1er--a-joeFSmdJDw9rPIBB3Q2o8WvGwjAbhmg"
  #  isDPlatformAdminOrgId: "1575170728938962947"
  #  装备平台管理员组织id
  isDPlatformAdminOrgId: "af1f01246cc6-ab00-084c-79e7-76f6022e"
  ## 物资平台管理员组织id
  isMPlatformAdminOrgId: "aa5422473bfb-aeea-9146-8ce7-04ff3c5e"
  # # pcwp1 业务合同接口地址
  pcwp1ContactUrl: "http://cpms_cbm.scrbg.com"
  # pcwp1 业务计划接口地址
  pcwp1PlanUrl: "http://cpms_material.scrbg.com"
  #//pcwp1收料单推送
  pcwpPurchase: "http://cpms_material.scrbg.com/json.rpc"
  isApiImportProductBradDispose: 2 # 1品牌自动新增（品牌是必传）2无品牌不保存，有品牌判断是否存在不存在报错
  #  正式
  profilesActive: "prod"
  #  服务器模板文件地址
  templateFormUrl: "/templateForm"
  isShowCode: 1 # 是否显示开发中的代码
  isCountPlanOrderNum: 0 # 是否使用计划数量统计
  isNotRateAmount: 1 # 是否使用累加不含税总金额
  isLoginAuthQuery: 0 # 是否查询登陆权限
  isContractConsumeNum: 1 # 是否使用pcwp记录商城合同数量
  isPlatformFee: 0 # 是否使用平台计费功能
  selectMaterialMonthSupplyPlan: 1 # 是否使用完结计划  1 使用  0不使用
  ossPrefix: "https://mmcp.scrbg.com"
  changAmountAndTaxPlanAmount: 1 # //2024年05月15号  应pcwp要求 Amount传不含税，NoTaxPlanAmount也要改成含税TaxPlanAmount
  isPCWPLogin: 1
  isBusinessOrg: 1
  miniProgram: "http://*************:9050/pages/sheet/sheet?isExternal=true&id="
  #  物资子公司自营店数据
  businessShopId: "1858777977810120705"
#  templateFormUrl: "D://work//templateFormUrl"
knife4j:
  production: true

seata:
  enabled: false
  application-id: mall-material
  tx-service-group: my_test_tx_group
  enable-auto-data-source-proxy: true
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: *************:8848
      group: SEATA_GROUP
      cluster: default
      namespace: 93f82b2f-1d61-4007-adb3-040a8e12b3b5
  config:
    type: nacos
    nacos:
      server-addr: *************:8848
      group: SEATA_GROUP
      namespace: 93f82b2f-1d61-4007-adb3-040a8e12b3b5
  service:
    vgroup-mapping:
      my_test_tx_group: default
    disable-global-transaction: false
  client:
    rm:
      report-success-enable: false
      lock:
        retry-interval: 20 #校验或占用全局锁重试间隔 默认10,单位毫秒
        retry-times: 60 #校验或占用全局锁重试次数 默认30
minio:
  endpoint: http://192.168.90.68:9000,http://192.168.90.69:9000,http://192.168.90.70:9000 #Minio服务所在地址
  accessKey: minioadmin #访问的key
  secretKey: 9ImAtEIE #访问的秘钥
  cluster: true  # 设置为 true 表示使用集群模式


